#include "ActorBody.h"

#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"

#include "ModelItemMesh.h"
#include "GameMode.h"
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
#include "ModelView.h"
#endif
#include "OgreUtils.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"

#include "GunUseComponent.h"
#include "CustomMotionMgr.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"

#include "PlayerAttrib.h"
#include "VillagerAttrib.h"
#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "ParticlesComponent.h"
#include "EffectComponent.h"
#include "ActorSandworm.h"
#include "ActorHorse.h"

#include "LegacyOgreSkeletonData.h"
#include "ActorSnowMan.h"
#include "BlockMaterialMgr.h"
#include "ActorVehicleAssemble.h"

#include "Optick/optick.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "SoundComponent.h"
#include "PlayerLocoMotion.h"

#include "ActorBoat.h"
#include "ActionIdleStateGunAdvance.h"
#include "SandboxGameDef.h"
#if HUD_TEST
#include "HUDUI/Render/HUDLevitationFontRender.h"
#include "HUDUI/Render/HUDTitleRender.h"
#endif
#if (defined(DEBUG) || defined(PROFILE_MODE)) && GIZMO_DRAW_ENGABLE
#include "Gizmo/DebugUtility.h"
#endif
#include "CustomGunUseComponent.h"
#include "OgreEntity.h"
using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

namespace MNSandbox
{
	static int s_SeqIDsNpc[] =
	{
		100100, //SEQ_STAND
		100101, //SEQ_WALK
		100105, //SEQ_ATTACK
		100106, //SEQ_DIE
		100109, //SEQ_JUMP
		130001, //SEQ_SKATEBOARD_DRIFT,
		130002,//SEQ_SKATEBOARD_SHOW1,
		130003,//SEQ_SKATEBOARD_SHOW2,
		130004,//SEQ_SKATEBOARD_SHOW3,
		130005,//SEQ_SKATEBOARD_IDLE2WALK,
		130006,//SEQ_SKATEBOARD_WALK2IDLE,
		130007,//SEQ_SKATEBOARD_JUMP2BLOCK,
		130008, //SEQ_SKATEBOARD_ONBLOCKFRONT,
		130010, //SEQ_SKATEBOARD_DRIFT2
		130011, //SEQ_SKATEBOARD_JUMP2BLOCK2,
		100108, //SEQ_IDLEACTION
		100166, //SEQ_IDLEACTION2
		102001, //SEQ_IDLEACTIONINVITE 20210926 codeby:wangyu 互动装扮动画id
		100107, //SEQ_BEHIT
		100102, //SEQ_LAYDOWN
		100113, //SEQ_SITDOWN
		101158, //SEQ_HELM
		100104, //SEQ_SWIM
		100111, //SEQ_RUN
		100114, //SEQ_MOBEAT
		1060006, //SEQ_MOBDRINK
		100102, //SEQ_MOBSLEEP
		100156,	//SEQ_MOBDANCE
		100156, //SEQ_MOBTOPPLEOVER
		100153, //SEQ_MOBLOGGERHEAD
		100170, //SEQ_MOBSCROLL
		100160,	//SEQ_MOBKICK
		100161, //SEQ_MOBDODGE
		100155, //SEQ_MOBCONCEAL
		100257, //SEQ_MOBHOWL
		100165,	//SEQ_MOBMAKETROUBLE
		100171, //SEQ_MOBBREATHE
		100172, //SEQ_MOBHOLD
		100166, //SEQ_MOBREVERSE
		100116, //SEQ_SHOOTARROW
		100117, //SEQ_EAT
		1060006,//SEQ_DRINK
		100118, //SEQ_SHOOTARROW_WALK
		100119, //SEQ_TAMED
		100121, //SEQ_FLY
		100123, //SEQ_JUMPTWO
		100114, //SEQ_SKINFLY
		100115, //SEQ_SKINSTAND
		100103, //SEQ_SITCHAIR
		100124, //SEQ_DISAPPEAR
		100130, //SEQ_SHOWTIME
		100147, //SEQ_DIG_MULTI
		100148, //SEQ_DIG_CHARGE
		100105, //SEQ_SKINNING
		100116, //SEQ_TOOL_LOOP
		100105, //SEQ_TOOL_ATTACK
		100105, //SEQ_ITEMSKILL_START
		100105, //SEQ_ITEMSKILL_ATTACK
		100105, //SEQ_GUN_IDLE
		100105, //SEQ_GUN_FIRE
		100105, //SEQ_GUN_RELOAD
		100160, //SEQ_KICK_BALL
		100161, //SEQ_TACKLE
		100200, //SEQ_WIZARDMAGIC
		100201, //SEQ_BUMP_PRE
		100162, //SEQ_THINK
		100112, //SEQ_RUB_HAND
		100163, //SEQ_GET_UP
		100164, //SEQ_HUNGER_SIT
		100182, //SEQ_SCREAM
		100181, //SEQ_TIRED
		100184, //SEQ_HALFGIANT_RIGHTHANDATK,
		100185, //SEQ_HALFGIANT_LEFTHANDATK,
		100186, //SEQ_HALFGIANT_BOTHHANDSATK,
		100187, //SEQ_HALFGIANT_BORN
		100188, //SEQ_HALFGIANT_STUN
		100195, //SEQ_HALFGIANT_TRANSFORM
		100189, //SEQ_GIANT_BOW
		100190, //SEQ_GIANT_ATKGROUND
		100187, //SEQ_GIANT_BORN
		100188, //SEQ_GIANT_STUN
		100191, //SEQ_GIANT_ROCKWAVE_READY
		100192, //SEQ_GIANT_ROCKWAVE_CAST
		100193, //SEQ_GIANT_REVENGEROAR
		100194, //SEQ_GIANT_ATKAIR
		100195, //SEQ_GIANT_TRANSFORM
		100100, //SEQ_PLAY_ACT
		100148, //SEQ_CATCH_GRAVITYACTOR
		100180, //SEQ_SWIM_DIVING
		100181, //SEQ_SWIM_RUSH
		100182, //SEQ_SWIM_IDLE
		100191,	//SEQ_BASKETBALL_OBSTRUCT
		100192, //SEQ_BASKETBALL_BLOCK_SHOT
		100193,  //SEQ_BASKETBALL_SHOOT_AND_PASS
		100190,  //SEQ_BASKETBALL_DRIBBLE
		100100,	//SEQ_BASKETBALL_GRAB_BEFORE
		100143,	//SEQ_BASKETBALL_GRAB
		100400, //SEQ_SHAPE_SHIFT
		100401, //SEQ_RE_SHAPE_SHIFT
		100105, //SEQ_SPITFIRE_LAND
		100114, //SEQ_SPITFIRE_AIR
		100125, //SEQ_SPITFIRE_LAND_WAIT_CD
		100126,  //SEQ_SPITFIRE_AIR_WAIT_CD
		200100, //SEQ_SAVAGE_DANCE
		200101, //SEQ_SAVAGE_CRUEL
		200102, //SEQ_SAVAGE_SHOCK
		200105, //SEQ_SAVAGE_SING
		200201,	//SEQ_CARRIED
		200204, //SEQ_SHAKE_HEAD
		200205, //SEQ_SAVAGE_CELEBRATE
		100502, //SEQ_CARRYING
		200108,	//SEQ_EXTREMIS_WALK
		200208,	//SEQ_EXTREMIS_STAND_1
		200109,	//SEQ_EXTREMIS_STAND_2
		200200,	//SEQ_EXTREMIS_STAND_3
		200202, //SEQ_NOD
		200207, //SEQ_STAND_HUNGER
		200104, //SEQ_FLEE
		100159, //SEQ_CAY
		200206, //SEQ_CAY
		100105, //SEQ_STARFIRE
		100125, //SEQ_STARFIRE_CD
		100121, //SEQ_MOONFLY
		100140,//SEQ_SAYHELLO
		100158,//SEQ_ANGRY
		100131,//SEQ_THANKS
		100133,//SEQ_POSE
		100500,//SEQ_FORTUNEMOOSKILL
		100200,//SEQ_HOMELAND_PET_STANDBY
		100201,//SEQ_HOMELAND_PET_IDLE
		100202,//SEQ_HOMELAND_PET_SITDOWN
		100203,//SEQ_HOMELAND_PET_LYINGDOWN
		100204,//SEQ_HOMELAND_PET_HAPPY
		100205,//SEQ_HOMELAND_PET_SHAKEINGHEAD
		100200,//SEQ_HOMECHEST_PLANT_STANDBY
		100202,//SEQ_HOMECHEST_PLANT_FAWN
		100100,//SEQ_VACANT2_ANNIM1,//虚空boss二阶段 10个动作
		100515,//SEQ_VACANT2_ANNIM2,
		100516,//SEQ_VACANT2_ANNIM3,
		100517,//SEQ_VACANT2_ANNIM4,
		100518,//SEQ_VACANT2_ANNIM5,
		100519,//SEQ_VACANT2_ANNIM6,
		100600,//SEQ_VACANT2_ANNIM7,
		100601,//SEQ_VACANT2_ANNIM8,
		100602,//SEQ_VACANT2_ANNIM9,
		100107,//SEQ_VACANT2_ANNIM10,
		100700,//SEQ_VACANT2_ANNIM11,
		100100,//SEQ_VACANT1_ANNIM1,//虚空boss一阶段 18个动作
		100504,//SEQ_VACANT1_ANNIM2
		100505,//SEQ_VACANT1_ANNIM3
		100506,//SEQ_VACANT1_ANNIM4
		100507,//SEQ_VACANT1_ANNIM5
		100107,//SEQ_VACANT1_ANNIM6
		100508,//SEQ_VACANT1_ANNIM7
		100509,//SEQ_VACANT1_ANNIM8
		100510,//SEQ_VACANT1_ANNIM9
		100511,//SEQ_VACANT1_ANNIM10
		100512,//SEQ_VACANT1_ANNIM11
		100513,//SEQ_VACANT1_ANNIM12
		100514,//SEQ_VACANT1_ANNIM13
		100101,//SEQ_VACANT1_ANNIM14
		100607,//SEQ_VACANT1_ANNIM15
		100608,//SEQ_VACANT1_ANNIM16
		100609,//SEQ_VACANT1_ANNIM17
		100610,//SEQ_VACANT1_ANNIM18
		100606,//SEQ_EARTHMAN_ANNIM

		100606, //SEQ_BUTTERFLY_CHANGEBLOCK
		100605, //SEQ_BUTTERFLY_FLYSTAY
		100164, //SEQ_STAND_SLEEP,//站着睡觉
		100154, //SEQ_FLYMOB_FALLGROUND
		100129, //SEQ_COOK
		100801, //SEQ_FLASH
		100101,	//SEQ_HOVER
		100809, //SEQ_HAPPLYFLY,
		100810, //SEQ_UPSETFLY

		100833, //SEQ_SCORPION_HIDE
		100834, //SEQ_SCORPION_DIRLLOUT
		100838, //SEQ_SCORPION_TAIL
		100839, //SEQ_SCORPION_NAIL
		100100, //SEQ_SCORPION_STANDBY
		100101, //SEQ_SCORPION_WALK
		100107, //SEQ_SCORPION_HIT
		100106, //SEQ_SCORPION_DIE
		100105, //SEQ_SCORPION_ATTACK
		100840, //SEQ_SCORPION_DEADLY

		100833,//SEQ_SANDMAN_HIDE
		100842,//SEQ_SANDMAN_RANGEDATK
		100843,//SEQ_SANDMAN_FAKEDEATH
		100844,//SEQ_SANDMAN_REVIVE
		100846,//SEQ_SANDMAN_ABSORB

		100841,//SEQ_CAMEL_DRINK
		100842,//SEQ_CAMEL_RANGEDATK

		100100,//SEQ_SANDWORM_STANBY
		100101,//SEQ_SANDWORM_WALK
		100107,//SEQ_SANDWORM_HURT
		100106,//SEQ_SANDWORM_DIE
		100824,//SEQ_SANDWORM_ROAR
		100105,//SEQ_SANDWORM_ATTACK
		100830,//SEQ_SANDWORM_VERTIGO
		100831,//SEQ_SANDWORM_BITE1
		100832,//SEQ_SANDWORM_BITE2
		100842,//SEQ_SANDWORM_MARACAS
		100833,//SEQ_SANDWORM_DRILLING_IN
		100834,//SEQ_SANDWORM_DRILLING_OUT
		100835,//SEQ_SANDWORM_TOW
		100836,//SEQ_SANDWORM_DASH
		100837,//SEQ_SANDWORM_DRILLING_IN_STATE
		100845,//SEQ_SANDWORM_NIBBLE

		600100,//SEQ_DANCE1//跳舞动作1~5
		600101,//SEQ_DANCE2
		600102,//SEQ_DANCE3
		600103,//SEQ_DANCE4
		600104,//SEQ_DANCE5
		100812,//SEQ_WAKEUP

		100100, //SEQ_MUSIC_IDLE
		100100, //SEQ_MUSIC_PLAY
		200202, //SEQ_GUARD_NOD
		100827, //SEQ_DESERTBUSSINESS_CRYDOWN
		100110, //SEQ_DESERTBUSSINESS_GEST
		100828, //SEQ_DESERTBUSSINESS_REFUSE
		100829, //SEQ_DESERTBUSSINESS_COLLECT
		100848, //SEQ_DJUMP 二段跳

		100860,	//SEQ_ENTERWORLD, //进入地图
		100859,	//SEQ_REVIVE,    // 复活
		100889,// SEQ_HIPPOCAMPUS_BIND 海马缠缚
		100893,	//SEQ_CRAB_DIG_BLOCK,//螃蟹扒土动作
		100875,	//SEQ_CRAB_STRUGGLE,//螃蟹挣扎动作
		100892,//	SEQ_CRAB_CLAMB,//螃蟹夹手动作
		100105,//SEQ_CRAB_SHOW_HAND,//螃蟹挥舞蟹钳
		100925,//SEQ_CRAB_CLIMB //螃蟹攀爬动作
		100101,	//SEQ_FLYINGFISH_SWIM, // 飞鱼 游泳
		100111,	//SEQ_FLYINGFISH_ESCAPA, // 飞鱼飞
		100900,	//SEQ_FLYINGFISH_FLY, // 飞鱼飞
		100901, //SEQ_FLYINGFISH_TAKE_OFF,	// 飞鱼起飞
		100912, //SEQ_FLYINGFISH_ENTRY_WATER,	// 飞鱼入水
		100884, //SEQ_JELLYFISH_SHAKE_1, // 水母 抖动挣扎
		100887,	//SEQ_JELLYFISH_SHAKE_2,	  // 水母 震动
		100888,	//SEQ_JELLYFISH_EXHAUST,	  // 水母 排气泡
		100914, //SEQ_JELLYFISH_FLOOD_GAS,    //  水母 充斥气体
		100874,  //SEQ_SHARK_BITE_1,    // 鲨鱼撕咬方块
		100926,  //SEQ_SHARK_BITE_2，    // 鲨鱼撕咬方块
		100874, //SEQ_MOUBULA_TAIL_ATTACK, //魔鬼鱼尾刺攻击 荧光棒藻鱼摇晃动作
		100883, //SEQ_FISHCONCEAL,	// 荧光棒藻鱼潜伏动作
		100890, //SEQ_MOUBULA_REST_DOWN, //魔鬼鱼趴着休息
		100891, //SEQ_MOUBULA_REST_UP, //魔鬼鱼竖着休息
		100137,	//SEQ_ENDING_FISHING，// 钓鱼收杆动作，用的冲锋枪待机
		100152, //SEQ_LAYEGG     //嘟嘟鸟下蛋孵蛋
		100864,	//SEQ_BATREVERSE,	// 蝙蝠倒挂
		100114, //SEQ_BATATTACH,	// 蝙蝠抱脸

		100862,	//SEQ_MOBCONCEAL_NEW  //生物新潜伏动作
		100928,//SEQ_AVATAT_SUMMON //皮肤召唤动作

		//新手引导机器人
		100100, //SEQ_BOT_IDLE
		100101, //SEQ_BOT_MOVE
		100940,	//SEQ_BOT_ENTER
		100942,	//SEQ_BOT_ENTER_FAST
		100929,	//SEQ_BOT_LEAVE
		100943,	//SEQ_BOT_LEAVE_FAST
		100930,	//SEQ_BOT_AWAIT1
		100931,	//SEQ_BOT_AWAIT2
		100932,	//SEQ_BOT_PROJECTION1
		100933,	//SEQ_BOT_PROJECTION2
		100934,	//SEQ_BOT_PROJECTION_END
		100935,	//SEQ_BOT_ANXIOUS
		100936,	//SEQ_BOT_TALK
		100937,	//SEQ_BOT_POINT_RIGHTUP
		100938,	//SEQ_BOT_POINT_DOWN
		100939,	//SEQ_BOT_POINT_LEFTUP
		100941, //SEQ_BOT_AWAKE

		100946, //SEQ_GLISSADE  //冰面滑行
		100953, //SEQ_DOG_PADDLE //狗刨

		100979, //SEQ_SNOWMAN_STAND_PANIC  //雪人待机-恐慌
		100980, //SEQ_SNOWMAN_MOVE_PANIC  //雪人移动-恐慌
		100130, //SEQ_SNOWMAN_STAND  //雪人小休闲

		100130, //SEQ_SNOWHARE_LOOKAROUND,  //雪兔张望
		100944, //SEQ_SNOWHARE_TEMPT,  //雪兔被吸引
		100945, //SEQ_SNOWHARE_HIDE,  //雪兔躲藏

		100956, //SEQ_IB_ATTACK1
		100957, //SEQ_IB_FMOVE_BEGIN
		100958,	//SEQ_IB_FMOVE_LOOP
		100959,	//SEQ_IB_FMOVE_END
		100960,	//SEQ_IB_ICEROCK
		100961, //SEQ_IB_ICEBALL
		100962, //SEQ_IB_AWAKE
		100963, //SEQ_IB_IDLE2
		100964, //SEQ_IB_ATTACK2
		100965, //SEQ_IB_JUMP_BEGIN
		100966, //SEQ_IB_JUMP_LOOP
		100967, //SEQ_IB_JUMP_END
		100968,	//SEQ_IB_ICEROCK2
		100969, //SEQ_IB_ICEBALL2
		100970, //SEQ_IB_SKILL1_BEGIN
		100971, //SEQ_IB_SKILL1_LOOP
		100972, //SEQ_IB_BEATEN2
		100973, //SEQ_IB_SLEEP
		100974, //SEQ_IB_JUMPIN
		100975, //SEQ_IB_JUMPOUT
		100976, //SEQ_IB_ATTACK2_B
		100977, //SEQ_IB_ATTACK2_L
		100978, //SEQ_IB_ATTACK2_E
		100984, //SEQ_IB_SKILL2
		100985, //SEQ_IB_SKILL3
		100980, //SEQ_IB_SKILL1_END
		100987,	//SEQ_IB_INJURED1
		100988,	//SEQ_IB_INJURED2

		600169, //SEQ_COMBOATK_IDLE_BODY, //手持武器-待机时-全身
		600142, //SEQ_COMBOATK_MOVE_BODY, //手持武器-移动时-全身

	#ifdef CHINA_SEQ_USED
		600123, //SEQ_PIANO_PLAY
		100170, //SEQ_TRANSFER 变身
	#endif

		100955,	//SEQ_IB_ATTACK2_F	//冰野人祭司的特殊攻击
		200214, //SEQ_HUNT_ATTACK
		600134,	//SEQ_RAISE_SHIELD
		600141, //SEQ_BROKEN_TOUGHNESS
		600161, //SEQ_DOUBLEWEAPON_DIGING
		100991,	//SEQ_VACANTVORTEX_CREATE //创建虚空团子
		100992, //SEQ_VACANTVORTEX_END //创建虚空团子结束
		140000,	//SEQ_DUDU_THROB, //嘟嘟鸟 扑腾
		140001,	//SEQ_ELK_PANIC,  //角鹿 小跳走
		140002,	//SEQ_FOX_FIERCE,//狼 龇牙
		600167, //SEQ_LEFTRAISE_SHIELD 左手持盾防御
		140003, //SEQ_ATTTACT_POWER, //引力蓄力
		140004, //SEQ_ATTTACT_BOMB, //爆炸

		100996,	//SEQ_REVIVE_BASIC,    // 复活(基础)
		120066,	//SEQ_FOLLOW_MOVE_BODY,    // 跟随移动(基础)
		120068,	//SEQ_FOLLOW_MOVE_BODY1,    // 进阶跟随移动1
		110032, //SEQ_CLIMB 爬行动作
		110033, //SEQ_MOB_FLY,飞行动作
		600187, //SEQ_THROW, 丢人动作
		600188, //SEQ_CRAWL,爬行动作
		600189, //SEQ_CRAWL_STOP,爬行停止
		110028, //挣扎
	};

	static int s_SeqIDs[] =
	{
		1000123, //SEQ_STAND
		1000201, //SEQ_WALK
		1010141, //SEQ_ATTACK
		1000122, //SEQ_DIE
		1000231, //SEQ_JUMP
		130001, //SEQ_SKATEBOARD_DRIFT,
		130002,//SEQ_SKATEBOARD_SHOW1,
		130003,//SEQ_SKATEBOARD_SHOW2,
		130004,//SEQ_SKATEBOARD_SHOW3,
		130005,//SEQ_SKATEBOARD_IDLE2WALK,
		130006,//SEQ_SKATEBOARD_WALK2IDLE,
		130007,//SEQ_SKATEBOARD_JUMP2BLOCK,
		130008, //SEQ_SKATEBOARD_ONBLOCKFRONT,
		130010, //SEQ_SKATEBOARD_DRIFT2
		130011, //SEQ_SKATEBOARD_JUMP2BLOCK2,
		100108, //SEQ_IDLEACTION
		100166, //SEQ_IDLEACTION2
		102001, //SEQ_IDLEACTIONINVITE 20210926 codeby:wangyu 互动装扮动画id
		100107, //SEQ_BEHIT
		1000000, //SEQ_LAYDOWN
		100113, //SEQ_SITDOWN
		101158, //SEQ_HELM
		100104, //SEQ_SWIM
		1000211, //SEQ_RUN
		100114, //SEQ_MOBEAT
		1060006, //SEQ_MOBDRINK
		100102, //SEQ_MOBSLEEP
		100156,	//SEQ_MOBDANCE
		100156, //SEQ_MOBTOPPLEOVER
		100153, //SEQ_MOBLOGGERHEAD
		100170, //SEQ_MOBSCROLL
		100160,	//SEQ_MOBKICK
		100161, //SEQ_MOBDODGE
		100155, //SEQ_MOBCONCEAL
		100257, //SEQ_MOBHOWL
		100165,	//SEQ_MOBMAKETROUBLE
		100171, //SEQ_MOBBREATHE
		100172, //SEQ_MOBHOLD
		100166, //SEQ_MOBREVERSE
		100116, //SEQ_SHOOTARROW
		100117, //SEQ_EAT
		1060006,//SEQ_DRINK
		100118, //SEQ_SHOOTARROW_WALK
		100119, //SEQ_TAMED
		100121, //SEQ_FLY
		100123, //SEQ_JUMPTWO
		100114, //SEQ_SKINFLY
		100115, //SEQ_SKINSTAND
		100103, //SEQ_SITCHAIR
		100124, //SEQ_DISAPPEAR
		100130, //SEQ_SHOWTIME
		100147, //SEQ_DIG_MULTI
		100148, //SEQ_DIG_CHARGE
		100105, //SEQ_SKINNING
		100116, //SEQ_TOOL_LOOP
		100105, //SEQ_TOOL_ATTACK
		100105, //SEQ_ITEMSKILL_START
		100105, //SEQ_ITEMSKILL_ATTACK
		100105, //SEQ_GUN_IDLE
		100105, //SEQ_GUN_FIRE
		100105, //SEQ_GUN_RELOAD
		100160, //SEQ_KICK_BALL
		100161, //SEQ_TACKLE
		100200, //SEQ_WIZARDMAGIC
		100201, //SEQ_BUMP_PRE
		100162, //SEQ_THINK
		100112, //SEQ_RUB_HAND
		100163, //SEQ_GET_UP
		100164, //SEQ_HUNGER_SIT
		100182, //SEQ_SCREAM
		100181, //SEQ_TIRED
		100184, //SEQ_HALFGIANT_RIGHTHANDATK,
		100185, //SEQ_HALFGIANT_LEFTHANDATK,
		100186, //SEQ_HALFGIANT_BOTHHANDSATK,
		100187, //SEQ_HALFGIANT_BORN
		100188, //SEQ_HALFGIANT_STUN
		100195, //SEQ_HALFGIANT_TRANSFORM
		100189, //SEQ_GIANT_BOW
		100190, //SEQ_GIANT_ATKGROUND
		100187, //SEQ_GIANT_BORN
		100188, //SEQ_GIANT_STUN
		100191, //SEQ_GIANT_ROCKWAVE_READY
		100192, //SEQ_GIANT_ROCKWAVE_CAST
		100193, //SEQ_GIANT_REVENGEROAR
		100194, //SEQ_GIANT_ATKAIR
		100195, //SEQ_GIANT_TRANSFORM
		100100, //SEQ_PLAY_ACT
		100148, //SEQ_CATCH_GRAVITYACTOR
		100180, //SEQ_SWIM_DIVING
		100181, //SEQ_SWIM_RUSH
		100182, //SEQ_SWIM_IDLE
		100191,	//SEQ_BASKETBALL_OBSTRUCT
		100192, //SEQ_BASKETBALL_BLOCK_SHOT
		100193,  //SEQ_BASKETBALL_SHOOT_AND_PASS
		100190,  //SEQ_BASKETBALL_DRIBBLE
		100100,	//SEQ_BASKETBALL_GRAB_BEFORE
		100143,	//SEQ_BASKETBALL_GRAB
		100400, //SEQ_SHAPE_SHIFT
		100401, //SEQ_RE_SHAPE_SHIFT
		100105, //SEQ_SPITFIRE_LAND
		100114, //SEQ_SPITFIRE_AIR
		100125, //SEQ_SPITFIRE_LAND_WAIT_CD
		100126,  //SEQ_SPITFIRE_AIR_WAIT_CD
		200100, //SEQ_SAVAGE_DANCE
		200101, //SEQ_SAVAGE_CRUEL
		200102, //SEQ_SAVAGE_SHOCK
		200105, //SEQ_SAVAGE_SING
		200201,	//SEQ_CARRIED
		200204, //SEQ_SHAKE_HEAD
		200205, //SEQ_SAVAGE_CELEBRATE
		100502, //SEQ_CARRYING
		200108,	//SEQ_EXTREMIS_WALK
		200208,	//SEQ_EXTREMIS_STAND_1
		200109,	//SEQ_EXTREMIS_STAND_2
		200200,	//SEQ_EXTREMIS_STAND_3
		200202, //SEQ_NOD
		200207, //SEQ_STAND_HUNGER
		200104, //SEQ_FLEE
		100159, //SEQ_CAY
		200206, //SEQ_CAY
		100105, //SEQ_STARFIRE
		100125, //SEQ_STARFIRE_CD
		100121, //SEQ_MOONFLY
		100140,//SEQ_SAYHELLO
		100158,//SEQ_ANGRY
		100131,//SEQ_THANKS
		100133,//SEQ_POSE
		100500,//SEQ_FORTUNEMOOSKILL
		100200,//SEQ_HOMELAND_PET_STANDBY
		100201,//SEQ_HOMELAND_PET_IDLE
		100202,//SEQ_HOMELAND_PET_SITDOWN
		100203,//SEQ_HOMELAND_PET_LYINGDOWN
		100204,//SEQ_HOMELAND_PET_HAPPY
		100205,//SEQ_HOMELAND_PET_SHAKEINGHEAD
		100200,//SEQ_HOMECHEST_PLANT_STANDBY
		100202,//SEQ_HOMECHEST_PLANT_FAWN
		100100,//SEQ_VACANT2_ANNIM1,//虚空boss二阶段 10个动作
		100515,//SEQ_VACANT2_ANNIM2,
		100516,//SEQ_VACANT2_ANNIM3,
		100517,//SEQ_VACANT2_ANNIM4,
		100518,//SEQ_VACANT2_ANNIM5,
		100519,//SEQ_VACANT2_ANNIM6,
		100600,//SEQ_VACANT2_ANNIM7,
		100601,//SEQ_VACANT2_ANNIM8,
		100602,//SEQ_VACANT2_ANNIM9,
		100107,//SEQ_VACANT2_ANNIM10,
		100700,//SEQ_VACANT2_ANNIM11,
		100100,//SEQ_VACANT1_ANNIM1,//虚空boss一阶段 18个动作
		100504,//SEQ_VACANT1_ANNIM2
		100505,//SEQ_VACANT1_ANNIM3
		100506,//SEQ_VACANT1_ANNIM4
		100507,//SEQ_VACANT1_ANNIM5
		100107,//SEQ_VACANT1_ANNIM6
		100508,//SEQ_VACANT1_ANNIM7
		100509,//SEQ_VACANT1_ANNIM8
		100510,//SEQ_VACANT1_ANNIM9
		100511,//SEQ_VACANT1_ANNIM10
		100512,//SEQ_VACANT1_ANNIM11
		100513,//SEQ_VACANT1_ANNIM12
		100514,//SEQ_VACANT1_ANNIM13
		100101,//SEQ_VACANT1_ANNIM14
		100607,//SEQ_VACANT1_ANNIM15
		100608,//SEQ_VACANT1_ANNIM16
		100609,//SEQ_VACANT1_ANNIM17
		100610,//SEQ_VACANT1_ANNIM18
		100606,//SEQ_EARTHMAN_ANNIM

		100606, //SEQ_BUTTERFLY_CHANGEBLOCK
		100605, //SEQ_BUTTERFLY_FLYSTAY
		100164, //SEQ_STAND_SLEEP,//站着睡觉
		100154, //SEQ_FLYMOB_FALLGROUND
		100129, //SEQ_COOK
		100801, //SEQ_FLASH
		100101,	//SEQ_HOVER
		100809, //SEQ_HAPPLYFLY,
		100810, //SEQ_UPSETFLY

		100833, //SEQ_SCORPION_HIDE
		100834, //SEQ_SCORPION_DIRLLOUT
		100838, //SEQ_SCORPION_TAIL
		100839, //SEQ_SCORPION_NAIL
		100100, //SEQ_SCORPION_STANDBY
		100101, //SEQ_SCORPION_WALK
		100107, //SEQ_SCORPION_HIT
		100106, //SEQ_SCORPION_DIE
		100105, //SEQ_SCORPION_ATTACK
		100840, //SEQ_SCORPION_DEADLY

		100833,//SEQ_SANDMAN_HIDE
		100842,//SEQ_SANDMAN_RANGEDATK
		100843,//SEQ_SANDMAN_FAKEDEATH
		100844,//SEQ_SANDMAN_REVIVE
		100846,//SEQ_SANDMAN_ABSORB

		100841,//SEQ_CAMEL_DRINK
		100842,//SEQ_CAMEL_RANGEDATK

		100100,//SEQ_SANDWORM_STANBY
		100101,//SEQ_SANDWORM_WALK
		100107,//SEQ_SANDWORM_HURT
		100106,//SEQ_SANDWORM_DIE
		100824,//SEQ_SANDWORM_ROAR
		100105,//SEQ_SANDWORM_ATTACK
		100830,//SEQ_SANDWORM_VERTIGO
		100831,//SEQ_SANDWORM_BITE1
		100832,//SEQ_SANDWORM_BITE2
		100842,//SEQ_SANDWORM_MARACAS
		100833,//SEQ_SANDWORM_DRILLING_IN
		100834,//SEQ_SANDWORM_DRILLING_OUT
		100835,//SEQ_SANDWORM_TOW
		100836,//SEQ_SANDWORM_DASH
		100837,//SEQ_SANDWORM_DRILLING_IN_STATE
		100845,//SEQ_SANDWORM_NIBBLE

		600100,//SEQ_DANCE1//跳舞动作1~5
		600101,//SEQ_DANCE2
		600102,//SEQ_DANCE3
		600103,//SEQ_DANCE4
		600104,//SEQ_DANCE5
		100812,//SEQ_WAKEUP

		100100, //SEQ_MUSIC_IDLE
		100100, //SEQ_MUSIC_PLAY
		200202, //SEQ_GUARD_NOD
		100827, //SEQ_DESERTBUSSINESS_CRYDOWN
		100110, //SEQ_DESERTBUSSINESS_GEST
		100828, //SEQ_DESERTBUSSINESS_REFUSE
		100829, //SEQ_DESERTBUSSINESS_COLLECT
		100848, //SEQ_DJUMP 二段跳

		100860,	//SEQ_ENTERWORLD, //进入地图
		100859,	//SEQ_REVIVE,    // 复活
		100889,// SEQ_HIPPOCAMPUS_BIND 海马缠缚
		100893,	//SEQ_CRAB_DIG_BLOCK,//螃蟹扒土动作
		100875,	//SEQ_CRAB_STRUGGLE,//螃蟹挣扎动作
		100892,//	SEQ_CRAB_CLAMB,//螃蟹夹手动作
		100105,//SEQ_CRAB_SHOW_HAND,//螃蟹挥舞蟹钳
		100925,//SEQ_CRAB_CLIMB //螃蟹攀爬动作
		100101,	//SEQ_FLYINGFISH_SWIM, // 飞鱼 游泳
		100111,	//SEQ_FLYINGFISH_ESCAPA, // 飞鱼飞
		100900,	//SEQ_FLYINGFISH_FLY, // 飞鱼飞
		100901, //SEQ_FLYINGFISH_TAKE_OFF,	// 飞鱼起飞
		100912, //SEQ_FLYINGFISH_ENTRY_WATER,	// 飞鱼入水
		100884, //SEQ_JELLYFISH_SHAKE_1, // 水母 抖动挣扎
		100887,	//SEQ_JELLYFISH_SHAKE_2,	  // 水母 震动
		100888,	//SEQ_JELLYFISH_EXHAUST,	  // 水母 排气泡
		100914, //SEQ_JELLYFISH_FLOOD_GAS,    //  水母 充斥气体
		100874,  //SEQ_SHARK_BITE_1,    // 鲨鱼撕咬方块
		100926,  //SEQ_SHARK_BITE_2，    // 鲨鱼撕咬方块
		100874, //SEQ_MOUBULA_TAIL_ATTACK, //魔鬼鱼尾刺攻击 荧光棒藻鱼摇晃动作
		100883, //SEQ_FISHCONCEAL,	// 荧光棒藻鱼潜伏动作
		100890, //SEQ_MOUBULA_REST_DOWN, //魔鬼鱼趴着休息
		100891, //SEQ_MOUBULA_REST_UP, //魔鬼鱼竖着休息
		100137,	//SEQ_ENDING_FISHING，// 钓鱼收杆动作，用的冲锋枪待机
		100152, //SEQ_LAYEGG     //嘟嘟鸟下蛋孵蛋
		100864,	//SEQ_BATREVERSE,	// 蝙蝠倒挂
		100114, //SEQ_BATATTACH,	// 蝙蝠抱脸

		100862,	//SEQ_MOBCONCEAL_NEW  //生物新潜伏动作
		100928,//SEQ_AVATAT_SUMMON //皮肤召唤动作

		//新手引导机器人
		100100, //SEQ_BOT_IDLE
		100101, //SEQ_BOT_MOVE
		100940,	//SEQ_BOT_ENTER
		100942,	//SEQ_BOT_ENTER_FAST
		100929,	//SEQ_BOT_LEAVE
		100943,	//SEQ_BOT_LEAVE_FAST
		100930,	//SEQ_BOT_AWAIT1
		100931,	//SEQ_BOT_AWAIT2
		100932,	//SEQ_BOT_PROJECTION1
		100933,	//SEQ_BOT_PROJECTION2
		100934,	//SEQ_BOT_PROJECTION_END
		100935,	//SEQ_BOT_ANXIOUS
		100936,	//SEQ_BOT_TALK
		100937,	//SEQ_BOT_POINT_RIGHTUP
		100938,	//SEQ_BOT_POINT_DOWN
		100939,	//SEQ_BOT_POINT_LEFTUP
		100941, //SEQ_BOT_AWAKE

		100946, //SEQ_GLISSADE  //冰面滑行
		100953, //SEQ_DOG_PADDLE //狗刨

		100979, //SEQ_SNOWMAN_STAND_PANIC  //雪人待机-恐慌
		100980, //SEQ_SNOWMAN_MOVE_PANIC  //雪人移动-恐慌
		100130, //SEQ_SNOWMAN_STAND  //雪人小休闲

		100130, //SEQ_SNOWHARE_LOOKAROUND,  //雪兔张望
		100944, //SEQ_SNOWHARE_TEMPT,  //雪兔被吸引
		100945, //SEQ_SNOWHARE_HIDE,  //雪兔躲藏

		100956, //SEQ_IB_ATTACK1
		100957, //SEQ_IB_FMOVE_BEGIN
		100958,	//SEQ_IB_FMOVE_LOOP
		100959,	//SEQ_IB_FMOVE_END
		100960,	//SEQ_IB_ICEROCK
		100961, //SEQ_IB_ICEBALL
		100962, //SEQ_IB_AWAKE
		100963, //SEQ_IB_IDLE2
		100964, //SEQ_IB_ATTACK2
		100965, //SEQ_IB_JUMP_BEGIN
		100966, //SEQ_IB_JUMP_LOOP
		100967, //SEQ_IB_JUMP_END
		100968,	//SEQ_IB_ICEROCK2
		100969, //SEQ_IB_ICEBALL2
		100970, //SEQ_IB_SKILL1_BEGIN
		100971, //SEQ_IB_SKILL1_LOOP
		100972, //SEQ_IB_BEATEN2
		100973, //SEQ_IB_SLEEP
		100974, //SEQ_IB_JUMPIN
		100975, //SEQ_IB_JUMPOUT
		100976, //SEQ_IB_ATTACK2_B
		100977, //SEQ_IB_ATTACK2_L
		100978, //SEQ_IB_ATTACK2_E
		100984, //SEQ_IB_SKILL2
		100985, //SEQ_IB_SKILL3
		100980, //SEQ_IB_SKILL1_END
		100987,	//SEQ_IB_INJURED1
		100988,	//SEQ_IB_INJURED2

		600169, //SEQ_COMBOATK_IDLE_BODY, //手持武器-待机时-全身
		600142, //SEQ_COMBOATK_MOVE_BODY, //手持武器-移动时-全身

	#ifdef CHINA_SEQ_USED
		600123, //SEQ_PIANO_PLAY
		100170, //SEQ_TRANSFER 变身
	#endif

		100955,	//SEQ_IB_ATTACK2_F	//冰野人祭司的特殊攻击
		200214, //SEQ_HUNT_ATTACK
		600134,	//SEQ_RAISE_SHIELD
		600141, //SEQ_BROKEN_TOUGHNESS
		600161, //SEQ_DOUBLEWEAPON_DIGING
		100991,	//SEQ_VACANTVORTEX_CREATE //创建虚空团子
		100992, //SEQ_VACANTVORTEX_END //创建虚空团子结束
		140000,	//SEQ_DUDU_THROB, //嘟嘟鸟 扑腾
		140001,	//SEQ_ELK_PANIC,  //角鹿 小跳走
		140002,	//SEQ_FOX_FIERCE,//狼 龇牙
		600167, //SEQ_LEFTRAISE_SHIELD 左手持盾防御
		140003, //SEQ_ATTTACT_POWER, //引力蓄力
		140004, //SEQ_ATTTACT_BOMB, //爆炸

		100996,	//SEQ_REVIVE_BASIC,    // 复活(基础)
		120066,	//SEQ_FOLLOW_MOVE_BODY,    // 跟随移动(基础)
		120068,	//SEQ_FOLLOW_MOVE_BODY1,    // 进阶跟随移动1
		110032, //SEQ_CLIMB 爬行动作
		110033, //SEQ_MOB_FLY,飞行动作

		1000101,  // SEQ_UI_STAND
		1000220,	// SEQ_SNEAK_IDLE,     // 潜行
		1000221,	// SEQ_SNEAK_WALK,     // 潜行移动
		1000111,    //SEQ_CRAWL_FORWARD, //重伤状态前进爬行
		1000112,    //SEQ_CRAWL_BACKWARD, //重伤状态后退爬行
		1000113,    //SEQ_CRAWL_LEFT, //重伤状态左爬行
		1000114,    //SEQ_CRAWL_RIGHT, //重伤状态右爬行
		1000124,     //SEQ_LAYDOWN_IDLE, //仰头躺底倒地

		1000123, //SEQ_TOOL_DOWNBODY_IDLE
		1000201, //SEQ_TOOL_DOWNBODY_WALK
		1000211, //SEQ_TOOL_DOWNBODY_RUN
	};

	static unordered_set<int> s_IdleAnimSet = {		// 判断算不算待机动作，用于意识丧失buff。******** shitengkai
	SEQ_STAND,
	SEQ_DIE,
	SEQ_BEHIT,
	SEQ_LAYDOWN,
	SEQ_SITDOWN,
	SEQ_MOBSLEEP, //动物睡觉
	SEQ_MOBTOPPLEOVER,//动物趴下
	SEQ_SKINSTAND, //皮肤带的空中站立
	SEQ_SITCHAIR,
	//半身石巨人
	SEQ_HALFGIANT_STUN, //晕眩动作（虚弱状态）
	//全身石巨人
	SEQ_GIANT_STUN, //晕眩动作（虚弱状态）

	SEQ_SWIM_IDLE, //水中待机

	SEQ_CARRIED,		//被扛起

	SEQ_EXTREMIS_WALK,	//濒死走路
	SEQ_EXTREMIS_STAND_1, //濒死站立1
	SEQ_EXTREMIS_STAND_2, //濒死站立2
	SEQ_EXTREMIS_STAND_3, //濒死站立3

	SEQ_HOMELAND_PET_STANDBY,//家园宠物待机
	SEQ_HOMELAND_PET_IDLE,//家园宠物休闲
	SEQ_HOMELAND_PET_SITDOWN,//家园宠物坐下
	SEQ_HOMELAND_PET_LYINGDOWN,//家园宠物趴着

	SEQ_HOMECHEST_PLANT_STANDBY,	//家园植物待机

	SEQ_VACANT1_ANNIM6,//受击

	SEQ_STAND_SLEEP,//站着睡觉

	SEQ_SCORPION_STANDBY,//蝎子待机
	SEQ_SCORPION_HIT,//蝎子受击
	SEQ_SCORPION_DIE,//蝎子死亡

	SEQ_SANDWORM_STANBY,//沙虫-待机
	SEQ_SANDWORM_HURT,//沙虫-受击
	SEQ_SANDWORM_DIE,//沙虫-死亡
	SEQ_SANDWORM_VERTIGO,//沙虫-眩晕

	SEQ_GUARD_NOD, //打瞌睡
	SEQ_HELM,
	};

	bool isAnimIdle(int anim)
	{
		return s_IdleAnimSet.find(anim) != s_IdleAnimSet.end();
	}
}

EXPORT_SANDBOXGAME int SeqType2ID(int seqtype, bool ispayer)
{
	if (ispayer)
		return s_SeqIDs[seqtype];
	else
		return s_SeqIDsNpc[seqtype];
}

EXPORT_SANDBOXGAME int SeqID2Type(int seqId, bool isplayer)
{
	int* pSeqId = s_SeqIDsNpc;
	if (isplayer) pSeqId = s_SeqIDs;
	for (int i = 0; i < sizeof(s_SeqIDs) / sizeof(int); ++i)
	{
		if (pSeqId[i] == seqId)
		{
			return i;
		}
	}
	return -1;
}

bool ActorBody::hasAnimSeq(int seqId)
{
	if (m_Entity)
	{
		Rainbow::Model* mainModel = m_Entity->GetMainModel();
		if (mainModel)
			return mainModel->HasAnim(seqId);
	}
	return false;
}

void ActorBody::resetAnim(int seqId)
{
	if (m_Entity)
	{
		m_Entity->ResetAnim(seqId);
	}
}

bool ActorBody::hasAnimPlayingById(int seqId)
{
	if (m_Entity)
	{
		return m_Entity->HasAnimPlaying(seqId);
	}
	return false;
}

bool ActorBody::hasAnimPlayEnd(int seqId)
{
	if (m_Entity)
	{
		return m_Entity->HasAnimPlayEnd(seqId);
	}
	return false;
}

bool ActorBody::addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId)
{
	if (m_Entity && m_Entity->GetMainModel())
	{
		Rainbow::Model* mainModel = m_Entity->GetMainModel();
		if (mainModel->IsKindOf<ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(mainModel);
			if (legacymodel != nullptr && legacymodel->GetModelData() && legacymodel->GetAnimPlayer())
			{
				AnimationPlayer* aniPlayer = legacymodel->GetAnimPlayer();
				aniPlayer->SetOwnerObjId(objId);

				SharePtr<ModelData> modelData = legacymodel->GetModelData();
				for (size_t i = 0; i < modelData->GetNumAnim(); i++)
				{
					SharePtr<AnimationData> pAnimData = modelData->getAnimation(i);
					if (pAnimData && pAnimData->hasSequence(seqId) && pAnimData->m_BoneTracks.size() > 0)
					{
						return pAnimData->addFrameEventByKey(seqId, keyFrame * 33, eventName, 1, objId);
					}
				}
			}
		}
		else if (mainModel->IsKindOf<ModelNew>())
		{
			Rainbow::ModelNew* newModel = static_cast<Rainbow::ModelNew*>(mainModel);
			if (newModel && newModel->GetAnimationPlayer())
			{
				ModelAnimationPlayer* aniPlayer = newModel->GetAnimationPlayer();
				float clipLen = aniPlayer->GetAnimClipLenth(seqId);
				float keyFrameTime = Rainbow::Clamp<float>(keyFrame * 33.0f * 0.001f, 0, clipLen);
				aniPlayer->RegistAnimKeyFrameEvent(seqId, keyFrameTime, eventName);
			}
			return true;
		}
	}
	return false;
}

void ActorBody::clearAnimFrameEvent(long long objid)
{
	if (m_Entity && m_Entity->GetMainModel())
	{
		Rainbow::Model* mainModel = m_Entity->GetMainModel();
		if (mainModel->IsKindOf<ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(mainModel);
			if (legacymodel != nullptr && legacymodel->GetModelData())
			{
				SharePtr<ModelData> modelData = legacymodel->GetModelData();
				for (const auto& pAnimData : modelData->m_Anims)
				{
					if (pAnimData.anim)
					{
						std::vector<LegacyAnimationFrameEventData>& frameEvents = pAnimData.anim->m_FrameEvents;
						if (objid > 0)
						{
							for (auto it = frameEvents.begin(); it != frameEvents.end();)
							{
								if ((*it).objId == objid)
									it = frameEvents.erase(it);
								else
									++it;
							}
						}
						else
						{
							frameEvents.clear();
						}
					}
				}
			}
		}
		else if (mainModel->IsKindOf<ModelNew>())
		{
			Rainbow::ModelNew* newModel = static_cast<Rainbow::ModelNew*>(mainModel);
			if (newModel && newModel->GetAnimationPlayer())
			{
				ModelAnimationPlayer* aniPlayer = newModel->GetAnimationPlayer();
				aniPlayer->ClearAllAnimKeyFrameEvent();
			}
		}
	}
}

void ActorBody::clearPendingAnimEvents(int seqId /*= -1*/)
{
	if (m_Entity)
	{
		Rainbow::Model* model = m_Entity->GetMainModel();
		if (model && model->IsKindOf<ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
			if (legacymodel != nullptr && legacymodel->GetAnimPlayer())
			{
				auto& events = legacymodel->GetAnimPlayer()->m_FrameEvents;
				if (seqId < 0)
				{
					events.clear();
				}
				else
				{
					for (auto it = events.begin(); it != events.end();)
					{
						if ((*it).id == seqId)
							it = events.erase(it);
						else
							++it;
					}
				}
			}
		}
	}
}

void ActorBody::SetModelAnimPlayerCullingMode(int mode)
{
	if (getModel() && getModel()->GetModelAnimationPlayer())
		getModel()->GetModelAnimationPlayer()->SetCullingMode((Rainbow::IModelAnimationPlayer::CullingMode)mode);
}

void ActorBody::addAnimModel(int fileId)
{
#ifndef IWORLD_SERVER_BUILD	
	if (fileId != 2 && fileId != 3)
		return;



	char path[256];
	sprintf(path, "entity/player/player12/body");

	char animpath[256] = { 0 };
	sprintf(animpath, "entity/player/body0%d.oanim", fileId);

	Rainbow::Model* model = LoadModel(path, animpath);

	if (model == NULL)
		return;
	if (nullptr != m_Entity)
	{
		m_Entity->Load(model);
	}

	setCurAnim(0, 0);
#endif		
}

void ActorBody::setNeedUpdateAnim(bool b)
{
	m_NeedUpdateAnim = b;
}

void ActorBody::resetSeqAnimDesc()
{
	if (m_suSeqMustPlayDesc)
	{
		m_suSeqMustPlayDesc->reset();
	}
	m_bIsCheckSeqList = false;
}

void ActorBody::playWeaponAnim(int seqId, int loopMode, float speed, int layer, int playType)
{
	if ((playType == 0 || playType == 2))
	{
		if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
		{
			BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel;
			if (weaponItemMesh)
			{
				weaponItemMesh->playAnim(seqId, loopMode, speed, layer);
			}
		}

	}
	if ((playType == 1 || playType == 2))
	{
		if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left)
		{
			BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel_left;
			if (weaponItemMesh)
			{
				weaponItemMesh->playAnim(seqId, loopMode, speed, layer);
			}
		}
	}
}

void ActorBody::stopWeaponAnim(int seqId)
{
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
	{
		BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel;
		if (weaponItemMesh && weaponItemMesh->hasAnimPlaying(seqId))
		{
			weaponItemMesh->stopAnim(seqId);
		}
	}
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left)
	{
		BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel_left;
		if (weaponItemMesh && weaponItemMesh->hasAnimPlaying(seqId))
		{
			weaponItemMesh->stopAnim(seqId);
		}
	}
}

bool ActorBody::hasWeaponPlaying(int seqId, int playType)
{

	if ((playType == 0 || playType == 2))
	{
		if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
		{
			BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel;
			if (weaponItemMesh)
			{
				return weaponItemMesh->hasAnimPlaying(seqId);
			}
		}
	}
	if ((playType == 1 || playType == 2))
	{
		if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left)
		{
			BaseItemMesh* weaponItemMesh = m_EquipComponent->m_WeaponModel_left;
			if (weaponItemMesh)
			{
				return weaponItemMesh->hasAnimPlaying(seqId);
			}
		}
	}
	return false;
}

void ActorBody::playWeaponMotion(const char* motion, bool reset_play, int motion_class, float motionScale, int playType)
{
    OPTICK_EVENT();
    OPTICK_TAG("motion", motion);
#ifndef IWORLD_SERVER_BUILD		
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel && (playType == 0 || playType == 2))
	{
		m_EquipComponent->m_WeaponModel->playMotion(0,motion, reset_play, motion_class, motionScale);
	}

	if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left && (playType == 1 || playType == 2))
	{
		m_EquipComponent->m_WeaponModel_left->playMotion(0,motion, reset_play, motion_class, motionScale);
	}
#endif		
}

void ActorBody::stopWeaponMotion(int motion_class)
{
#ifndef IWORLD_SERVER_BUILD			
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
	{
		m_EquipComponent->m_WeaponModel->stopMotion(motion_class);
	}
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel_left)
	{
		m_EquipComponent->m_WeaponModel_left->stopMotion(motion_class);
	}
#endif			
}

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
void ActorBody::playEffectParticle(int efType, const char* efName)
#else
void ActorBody::playEffectParticle(int efType, const char* efName, ModelView* modelview, float x, float y, float z)
#endif
{
#ifndef IWORLD_SERVER_BUILD		
	bool loadResult = false;
	if (efType && efName)
	{
		int index = -1;
		for (int i = 0; i < (int)m_pEffectID.size(); i++)
		{
			if (efName == m_pEffectID[i])
			{
				index = i;
				break;
			}
		}

		if (index == -1 && m_Entity)
		{
			core::string efpath;
			switch (efType)
			{
			case 1:
				efpath = Format("particles/%s.ent", efName);
#if ENTITY_MODIFY_MODEL_ASYNC
				loadResult = m_Entity->LoadAsync(efpath, true);
#else
				loadResult = m_Entity->Load(efpath, true);
#endif
				break;
			case 2:
				break;
			case 3:
			{
				int checkcode = 1;
				MINIW::ScriptVM::game()->callFunction("AvtCallTable", "ii>i", 1, atoi(efName), &checkcode);
				if (checkcode != 0)
					return;

				int avatarmodel = atoi(efName);
				efpath = Format("entity/avatar/1000_%d/%d.ent", avatarmodel, avatarmodel);

#if ENTITY_MODIFY_MODEL_ASYNC
				loadResult = m_Entity->LoadAsync(efpath);
#else
				if (efpath.ends_with(ENTITY_EXT))
				{
					SharePtr<EntityData> entityData = GetAssetManager().LoadAsset<EntityData>(efpath.c_str());
					if (entityData) {
						m_Entity->Load(entityData);
						loadResult = true;
					}
				}
				if (efpath.ends_with(MODEL_DATA_EXT))
				{
					SharePtr<ModelData> model = GetAssetManager().LoadAsset<ModelData>(efpath.c_str());
					if (model) {
						m_Entity->Load(model);
						loadResult = true;
					}
			}
#endif
			}
			break;
			default:
				return;
			}

			if (loadResult)
			{
				m_pEffectID.push_back(StrDup(kMemGame, efName));
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
#else
				m_Entity->attachToScene(modelview->getScene());
				SetPosition(WorldPos::initVector3f(Rainbow::Vector3f(x, y, z)));
#endif
				//m_Entity->setMotionScale(0.7);
				//m_Entity->SetPosition(wp);
				//m_Entity->updateWorldCache();
				//m_Entity->update(0);
			}
		}
		else if (index >= 0 && m_Entity)
		{
			m_Entity->PlayMotion(index);
		}
	}
#endif		
}

void ActorBody::stopEffectParticle(bool isDetach)
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_Entity && isDetach)
	{
		m_Entity->DetachFromScene();
		m_Entity->StopMotion();
	}
	else if (m_Entity)
	{
		m_Entity->StopMotion();
	}
#endif		
}

void ActorBody::playAttack()
{
	if (!m_bIsShow)
	{
		return;
	}
	playAnim(SEQ_ATTACK);
	if (m_OwnerPlayer && GetDefManagerProxy()->isFishNeedUp(m_OwnerPlayer->getCurToolID()))
	{
		setCurAnim(SEQ_ATTACK, 1);
	}
}

void ActorBody::setFreeAnimLayer(bool free)
{
	m_bFreeAnimLayer = free;
}

void ActorBody::setCurAnimId(int animId, int layer)
{
	//setCurAnim(SeqID2Type(animId), layer);
	m_CurSeqID[layer] = animId;
	ModelData* md = getModelData();
	if (!md)
	{
		return;
	}
	SharePtr<AnimationData> ad = md->findAnimationDataBySequenceId(animId);
	if (!ad)
	{
		return;
	}
	ad->setPriority(animId, layer);
}

void ActorBody::setCurAnim(int anim, int layer)
{
	if (!m_ablePlayerOtherAnim || m_ownerActorTriggerProjecttile)
	{
		return;
	}

	if (anim == 255) anim = -1;
	//如果相同类型的道具，比如枪支，放在一起，但是待机的动作不一样，切换的话就不会改变动作，所以加了个判断m_curToolID
	bool sneak = m_OwnerPlayer ? m_OwnerPlayer->getSneaking() : false;
	if (m_CurAnim[layer] != anim|| (m_OwnerPlayer && m_CurToolID != m_OwnerPlayer->getCurToolID()) || m_LastSneak != sneak)
	{
		LOG_INFO("ActorBody::setCurAnim %d %d", anim, layer);
		if (anim < 0)
		{
			if (m_Entity && m_CurSeqID[layer] >= 0)
			{
				stopActorActionEffect(m_NowPlaySeqID);
				m_NowPlaySeqID = -1;
				m_NowPlayAnimLayer = -1;
				int stopAnim = actionIDConversion(m_CurSeqID[layer]);
				if (m_CurSeqID[(layer + 1) % 2] != stopAnim) //不可影响另一层的动画
				{
					m_Entity->StopAnim(stopAnim);
				}
				m_CurSeqID[layer] = -1;
			}
		}
		else
		{
			if (anim == SEQ_TACKLE)
			{
				stopAnim(SEQ_JUMP);
			}
			else if (anim == SEQ_DIE)
			{
				stopAllAnim();
			}
			else
			{
				bool directSwitchAni = m_OwnerActor != nullptr && m_OwnerActor->getDirectSwitchAni();
				if (!directSwitchAni && m_Entity && m_CurSeqID[layer] >= 0)
				{
					stopActorActionEffect(m_NowPlaySeqID);
					m_NowPlaySeqID = -1;
					m_NowPlayAnimLayer = -1;
					m_Entity->StopAnim(actionIDConversion(m_CurSeqID[layer]));
				}
			}
				
			int ret = playAnimInner(anim);
			if (ret < 0) return;

			m_CurSeqID[layer] = ret;
			
			// 动画同步逻辑：当播放持枪动作时，确保上下半身动画时间同步
			// if (m_OwnerPlayer && (anim == SEQ_GUN_IDLE || anim == SEQ_GUN_FIRE || anim == SEQ_GUN_RELOAD))
			// {
			// 	syncUpperLowerBodyAnimation(layer, ret);
			// }
			
			//上身发生变化，可能影响主身
// #ifndef DEDICATED_SERVER
// 			if (layer == 1)
// 			{
// 				m_CurAnim[0] = -1;
// 			}
// #endif
		}

		m_CurAnim[layer] = anim;
	}

	// 更新当前工具id和潜行状态 不能在layer是0的时候更新，否则layer1的动画可能不会更新
	if (m_OwnerPlayer && layer == 1) 
	{
		m_CurToolID = m_OwnerPlayer->getCurToolID();
		m_LastSneak = m_OwnerPlayer->getSneaking();
	}
}

//State driven method to animate player.
void ActorBody::updatePlayAnim(float runwalkfactor)
{
    OPTICK_EVENT();
	if (m_bFreeAnimLayer)
	{
		return;
	}
	if (m_OwnerActor == NULL) return;
	if (hasAnimPlaying(SEQ_SHAPE_SHIFT) || hasAnimPlaying(SEQ_RE_SHAPE_SHIFT) || hasAnimPlaying(SEQ_EARTHMAN_ANNIM)
		|| hasAnimPlaying(SEQ_BROKEN_TOUGHNESS))
	{//code-by:hanyunqiang SEQ_EARTHMAN_ANNIM动作播放时 中断刷新循环动作
		return;
	}

	LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
	std::vector<StatusAttInfo> vAttrtForbidOpValue;

	// 意识丧失buff时，只能播待机类型的动作。。******** shitengkai
	bool bForceIdleByBuffLOC = false;

	if (pAttrib && pAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	{
		if (m_OwnerActor->getObjType() == OBJ_TYPE_SANDMAN)
		{
			//什么都不做
		}
		else
		{
			pAttrib->getStatusAddAttInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE, vAttrtForbidOpValue);
			if (vAttrtForbidOpValue.size() == 1 && vAttrtForbidOpValue[0].iStatusId == LOSS_OF_CONSCIOUSNESS_BUFF)
			{
				bForceIdleByBuffLOC = true;
			}
			else {
				if (!pAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_STOP_ACTION))
				{
					stopAllAnim();
				}
				return;
			}
		}
	}


	int animBody = SEQ_STAND;
	int animUpBody = -1;
	int animWeapon = -1;

	ClientPlayer* player = NULL;
	player = dynamic_cast<ClientPlayer*>(m_OwnerActor);

	if (player && player->isShapeShift()) //变形为坐骑的时候，装扮是隐藏的 动作不用播放
	{
		if (m_nAct > 0 || m_nActTrigger > 0)
		{
			m_nAct = -1;
			m_nActTrigger = -1;
			clearAction();
		}
		return;
	}

	auto functionWrapper = m_OwnerActor->getFuncWrapper();
	
	bool isflying = false;
	if (player) isflying = player->isFlying();
	else isflying = functionWrapper ? functionWrapper->getCanFly() : false;

	if (m_OwnerActor && m_OwnerActor->isDead() && (!player || !player->isInSpectatorMode()))
	{
		animBody = SEQ_DIE;
	}
	else if (player != NULL)
	{
		auto RidComp = player->getRiddenComponent();
		auto playerAttrib = player->getPlayerAttrib();
		//全身的部分
		if (player->isCurrentActionState("Sleep")) animBody = SEQ_LAYDOWN;
		else if (player->isCoconutHit()) animBody = SEQ_LAYDOWN;//椰子打中
		else if (player->getSitting()) animBody = SEQ_SITCHAIR;
		else if (RidComp && RidComp->isRiding()) animBody = SEQ_SITDOWN;
		else if (playerAttrib&& playerAttrib->isPlayerDowned())
		{
			LivingLocoMotion* loc = dynamic_cast<LivingLocoMotion*>(player->getLocoMotion());
			if (loc) {
				if (loc->m_InWater || loc->m_InLava || loc->m_InHoney) {
					animBody = SEQ_SWIM;
				}
				else if (loc->m_OnGround) {
					if (loc->m_MoveForward > 0) {
						animBody = SEQ_CRAWL_FORWARD;
					} else if (loc->m_MoveForward < 0) {
						animBody = SEQ_CRAWL_BACKWARD;
					} else if (loc->m_MoveStrafing > 0) {
						animBody = SEQ_CRAWL_RIGHT;
					} else if (loc->m_MoveStrafing < 0) {
						animBody = SEQ_CRAWL_LEFT;
					} else {
						animBody = SEQ_LAYDOWN_IDLE;
					}
				} else {
					animBody = SEQ_LAYDOWN_IDLE;
				}
			} else {
				animBody = SEQ_LAYDOWN_IDLE;
			}
		}
		auto CarryComp = player->getCarryComponent();

		//上半身部分
		if (player->getCurOperate() == PLAYEROP_ATTACK_BOW || player->getCurOperate() == PLAYEROP_USE_ITEM_SKILL)
		{
			//animUpBody = SEQ_SHOOTARROW;
			animUpBody = SEQ_TOOL_LOOP;
		}
		else if (player->getCurOperate() == PLAYEROP_EATFOOD)
		{
			if (player->getCurToolID() == ITEM_BANDAGE)
				animUpBody = SEQ_RUB_HAND;
			else
				animUpBody = SEQ_EAT;
		}
		else if (player->getCurOperate() == PLAYEROP_DRINKWATER)
		{
			animUpBody = SEQ_DRINK;
		}
		else if (player->getCurOperate() == PLAYEROP_DIG && player->getOperateData() == DIG_METHOD_CHARGE)
		{
			//animUpBody = SEQ_DIG_CHARGE;
			animUpBody = SEQ_TOOL_LOOP;
		}
		else if (player->getCurDorsumID() == ITEM_FIRE_ROCKET)
		{
			animBody = SEQ_SWIM;
		}
		else if (player->getHookObj() && player->getCurToolID() == ITEM_HOOK && !player->getLocoMotion()->m_OnGround)
		{
			animBody = SEQ_SWIM;
		}
		else if (player->getCurOperate() == PLAYEROP_TACKLE)
		{
			animBody = SEQ_TACKLE;
		}
		else if (player->getCurOperate() == PLAYEROP_CATCH_GRAVITYACTOR)
		{
			animUpBody = SEQ_CATCH_GRAVITYACTOR;
		}
		else if (player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			animUpBody = SEQ_BASKETBALL_OBSTRUCT;
		}
		else if (player->getCurOperate() == PLAYEROP_BASKETBALL_PASS
			|| player->getCurOperate() == PLAYEROP_BASKETBALL_SHOOT)
		{
			animBody = SEQ_BASKETBALL_SHOOT_AND_PASS;
		}
		else if (player->getCurOperate() == PLAYEROP_BASKETBALL_GRAB)
		{
			if (!m_suSeqMustPlayDesc)
			{
				m_suSeqMustPlayDesc = SANDBOX_NEW(SeqMustAnimDesc);
			}

			if (m_suSeqMustPlayDesc && !m_suSeqMustPlayDesc->_animIdList.size())
			{
				m_bIsCheckSeqList = true;
				if (m_Entity)
				{
					m_Entity->StopAnim(m_CurSeqID[0]);
				}
				m_CurAnim[0] = SEQ_BASKETBALL_GRAB_BEFORE;
				m_CurAnim[1] = -1;
				m_CurSeqID[0] = seqType2ID(SEQ_BASKETBALL_GRAB_BEFORE);
				m_CurSeqID[1] = -1;

				m_suSeqMustPlayDesc->_curIdx = 0;
				m_suSeqMustPlayDesc->addAnimId(SEQ_BASKETBALL_GRAB_BEFORE, GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.grab_before_time);
				m_suSeqMustPlayDesc->addAnimId(SEQ_BASKETBALL_GRAB, 1);
				m_suSeqMustPlayDesc->_pAnimPlayTrack = m_Entity->PlayerAnimReturnTrack(m_CurSeqID[0]);
			}
			return;
		}
		else if (player->getCurOperate() == PLAYEROP_SHEILD_DEFENCE_BEGIN)
		{
			if (isDoubleWeapon(player->getCurToolID()))
				animUpBody = SEQ_LEFTRAISE_SHIELD;
			else
				animUpBody = SEQ_RAISE_SHIELD;
		}
		else if (player->getCurDorsumID() == ITEM_SNAKEGOD_WING && animBody != SEQ_LAYDOWN)
		{
			if (player->getLocoMotion()->m_OnGround)
			{
				animBody = SEQ_STAND;
			}
			else
				animBody = SEQ_SWIM;
		}
		else if (CarryComp && CarryComp->isCarrying())
		{
			animUpBody = SEQ_CARRYING;
		}

		CustomGunUseComponent* gunUseComponent = player->getCustomGunComponent();
		const CustomGunDef* modGunDef = player->getCustomGunDef();
		if (gunUseComponent && modGunDef)
		{
			auto riddenComponent = player->getRiddenComponent();
			auto gunAdvanceIdleState = player->GetIdleStateGunAdvance();
			if (((!riddenComponent) || (riddenComponent && !riddenComponent->isVehicleController())) && gunAdvanceIdleState)
			{
				if (gunAdvanceIdleState->IsFireState())
				{
					animUpBody = SEQ_GUN_FIRE;
					animWeapon = modGunDef->gunFire;
				}
				else if (gunAdvanceIdleState->IsReloadState())
				{
					animUpBody = SEQ_GUN_RELOAD;
				}
				else
				{
					animUpBody = SEQ_GUN_IDLE;
					animWeapon = gunUseComponent->getMagazine() == 0 ? modGunDef->gunFireLastBullet : modGunDef->gunIdle;
				}
			}
		}
		else if (GetDefManagerProxy()->getGunDef(player->getCurToolID()))
		{
			auto riddenComponent = player->getRiddenComponent();
			if ((!riddenComponent) || (riddenComponent && !riddenComponent->isVehicleController()))
			{
				if (player->getGunLogical()->isFire())
				{
					animUpBody = SEQ_GUN_FIRE;
					
				}
				else if (player->getGunLogical()->isReload())
				{
					animUpBody = SEQ_GUN_RELOAD;
				}
				else
				{
					animUpBody = SEQ_GUN_IDLE;
				}
			}
		}

		if (player->isEndingFishing())
		{
			animUpBody = SEQ_ENDING_FISHING;
		}

		if (GetDefManagerProxy()->getMusicalDef(player->getCurToolID()))
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("IsMusicItemUseState", SandboxContext(nullptr));

			if (result.IsExecSuccessed() && (result.GetData_Bool("state") || result.GetData_Bool("openview")))
			{
				animUpBody = SEQ_MUSIC_PLAY;
			}
			else {
				animUpBody = SEQ_MUSIC_IDLE;
			}
		}
	}
	else
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		if (mob && !m_OwnerActor->IsExtremis())
		{
			if (mob->isSleeping())
				animBody = SEQ_LAYDOWN;
			else if (mob->getSitting())
				animBody = SEQ_SITCHAIR;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_NOD))
				animBody = SEQ_NOD;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_EAT))
				animUpBody = SEQ_MOBEAT;  // = SEQ_EAT;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_SIT_BY_HUNGER))
				animBody = SEQ_HUNGER_SIT;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_STAND_BY_HUNGER))
				animBody = SEQ_STAND_HUNGER;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_FLEE))
				animBody = SEQ_FLEE;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_SIT))
				animBody = SEQ_SITCHAIR;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_DANCE))
				animBody = SEQ_SAVAGE_DANCE;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_SING))
				animBody = SEQ_SAVAGE_SING;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_THINK))
				animUpBody = SEQ_THINK; // animBody = SEQ_THINK;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_CAY))
				animBody = SEQ_CAY;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_RUB_HAND))
				animBody = SEQ_RUB_HAND;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_TALK))
				animBody = SEQ_TALK;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_SHOOT))
				animUpBody = SEQ_SHOOTARROW;
			//animBody = SEQ_SHOOTARROW;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_WAKEUP))
				animBody = SEQ_WAKEUP;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_DEFENSE))
				animUpBody = SEQ_RAISE_SHIELD;
			else if (m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_LEFTDEFENSE))
				animUpBody = SEQ_LEFTRAISE_SHIELD;
			else
				isflying |= m_OwnerActor->getFlying();
		}
		else
			isflying |= m_OwnerActor->getFlying();
	}

	if (animBody == SEQ_STAND && m_OwnerActor->getSitting())
	{
		animBody = SEQ_SITDOWN;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_SLEEP))
	{
		animBody = SEQ_MOBSLEEP;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getAnimFlagBit(ACTOR_ANIM_FLAG_STAND_SLEEP))
	{
		animBody = SEQ_STAND_SLEEP;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_NPCSLEEP))
	{
		animBody = SEQ_LAYDOWN;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_CRAFT))
	{
		animBody = SEQ_RUB_HAND;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_HUNGERSIT))
	{
		animBody = SEQ_HUNGER_SIT;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_DANCING))
	{
		int iDefId = m_OwnerActor ? m_OwnerActor->getDefID() : 0;
		if (iDefId == 3101 || iDefId == 3102 || iDefId == 3105)
			animBody = SEQ_SAVAGE_DANCE;
		else
			animBody = SEQ_MOBDANCE;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_SCROLL))
	{
		animBody = SEQ_MOBSCROLL;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_BUMP))
	{
		animBody = SEQ_BUMP_PRE;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_DIE))
	{
		animBody = SEQ_DIE;
	}

	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_FLOATAGE))
	{
		animBody = SEQ_FLY;
	}

	auto RidComp = m_OwnerActor->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{// 20210716：获取坐骑的骑乘动作  codeby： keguanqiang
		animBody = SEQ_SITDOWN;
		if (player)
		{
			auto playerRidComp = player->getRiddenComponent();
			ActorHorse* horse = NULL;
			if (playerRidComp)
			{
				horse = dynamic_cast<ActorHorse*>(playerRidComp->getRidingActor());
			}
			if (horse)
				animBody = horse->getRiddenAnim();

			ActorBoat* boat = NULL;
			if (playerRidComp)
			{
				boat = dynamic_cast<ActorBoat*>(playerRidComp->getRidingActor());
			}
			if (boat)
			{
				animBody = boat->getRiddenAnim();
			}

			ActorVehicleAssemble* veh = dynamic_cast<ActorVehicleAssemble*>(playerRidComp->getRidingActor());
			if (veh && veh->canDig())
			{
				animBody = SEQ_HELM;
			}
		}
	}


	bool ismove = false;
	bool inair = false;
	float animspeed = 1.0f;
	if (animBody == SEQ_STAND && !(RidComp && RidComp->isRiding()))	// 20210716：骑乘时不改变动作  codeby： keguanqiang
	{
		LivingLocoMotion* loc = dynamic_cast<LivingLocoMotion*>(m_OwnerActor->getLocoMotion());
		if (loc)
		{
			//if (loc->m_MoveForward != 0 || loc->m_MoveStrafing != 0 || loc->m_TickPosition.m_LastTickPos.x != loc->m_Position.x || loc->m_TickPosition.m_LastTickPos.z != loc->m_Position.z)
			if (loc->m_MoveForward != 0 || loc->m_MoveStrafing != 0 || 
				((!m_OwnerPlayer || (!m_OwnerPlayer->IsOnPlatform() && !loc->isSlopeFalling())) && (loc->m_TickPosition.m_LastTickPos.x != loc->m_Position.x || loc->m_TickPosition.m_LastTickPos.z != loc->m_Position.z)))
			{
				if (loc->m_InWater || loc->m_InLava || loc->m_InHoney)
				{
					if (loc->canWalkOnLiquid(loc->m_InWater))
						animBody = SEQ_RUN;
					else
						animBody = SEQ_SWIM;
				}
				else if (loc->m_OnGround || m_CheckAnim == SEQ_JUMP)
				{
					const float RUN_BEGIN = 1.2f;
					if (runwalkfactor >= RUN_BEGIN)
					{
						animBody = SEQ_RUN;
						animspeed = 1.0f + runwalkfactor - RUN_BEGIN;
					}
					else
					{
						//滑板自定义动作----判断是否在方块k边缘,如果在边缘使用滑板特殊滑行动作
						if (m_OwnerPlayer) {
							if (m_OwnerPlayer->getRun())
							{
								animBody = SEQ_RUN;
							}
							else
							{
								PlayerLocoMotion* playerLoc = static_cast<PlayerLocoMotion*> (m_OwnerPlayer->getLocoMotion());
								if (playerLoc->m_OnBlockLeftEdge && playerLoc->m_CanDriftWithSkateboard)
								{
									animBody = SEQ_SKATEBOARD_DRIFT2;
								}
								else if (playerLoc->m_OnBlockRightEdge && playerLoc->m_CanDriftWithSkateboard)
								{
									animBody = SEQ_SKATEBOARD_DRIFT;
								}
								else
								{
									animBody = SEQ_WALK;
								}
							}
						}
						else
						{
							animBody = SEQ_WALK;
						}
					}
					ActorHorse* horse = dynamic_cast<ActorHorse*>(m_OwnerActor);
					if (horse && (m_CurAnim[0] != SEQ_RUN && m_CurAnim[0] != SEQ_WALK))	//坐骑播放移动特效
						horse->playMoveEffect();
				}
				else
				{
					animBody = isflying ? SEQ_FLY : SEQ_STAND;
					inair = true;
				}

				ismove = true;
			}
			else
			{
				animBody = SEQ_STAND;
				if (!loc->m_OnGround)
				{
					inair = true;
					if (isflying) animBody = SEQ_FLY;
				}
			}

			if ((m_CurAnim[0] == SEQ_RUN || m_CurAnim[0] == SEQ_WALK) && (animBody != SEQ_RUN && animBody != SEQ_WALK))  //坐骑停止播放移动特效
			{
				ActorHorse* horse = dynamic_cast<ActorHorse*>(m_OwnerActor);
				if (horse)
					horse->stopMoveEffect();
			}
		}

		if (inair && m_PlayerIndex > 0 && getSkinID() > 0)
		{
			RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(getSkinID());
			if (skindef && skindef->EffectType2 == 2)
			{
				animBody = ismove ? SEQ_SKINFLY : SEQ_SKINSTAND;
			}
		}
	}
	if (m_OwnerPlayer && GetDefManagerProxy()->isFishNeedUp(m_OwnerPlayer->getCurToolID()))//举鱼时不改变动作 chenkaite
	{
		animUpBody = SEQ_CARRYING;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_EAT))
	{
		animBody = SEQ_MOBEAT;
	}
	if (animBody == SEQ_WALK && m_OwnerActor->getFlagBit(ACTORFLAG_AI_MATE))
	{
		animBody = SEQ_RUN;
	}
	if ((animBody == SEQ_STAND || animBody == SEQ_WALK) && m_OwnerActor->getObjType() == OBJ_TYPE_MONSTER && static_cast<ClientMob*>(m_OwnerActor)->isInHate())
	{
		animBody = SEQ_MOBLOGGERHEAD;
	}
	if ((animBody == SEQ_RUN || animBody == SEQ_WALK) && m_OwnerActor->getFlagBit(ACTORFLAG_AI_CONCEAL))
	{
		animBody = SEQ_MOBCONCEAL_NEW;
	}
	if ((animBody == SEQ_STAND) && m_OwnerActor->getFlagBit(ACTORFLAG_AI_BREATHE))
	{
		animBody = SEQ_MOBBREATHE;
	}
	if ((animBody == SEQ_RUN || animBody == SEQ_WALK || animBody == SEQ_STAND) && m_OwnerActor->getFlagBit(ACTORFLAG_AI_HOLD))
	{
		animBody = SEQ_MOBHOLD;
	}
	if ((animBody == SEQ_STAND) && m_OwnerActor->getReverse() && !m_OwnerActor->getLocoMotion()->getJumping())
	{
		animBody = SEQ_MOBREVERSE;
	}
	if (animBody == SEQ_STAND && m_OwnerActor->getFlagBit(ACTORFLAG_AI_TOPPLEOVER))
	{
		animBody = SEQ_MOBTOPPLEOVER;
	}
	if (animBody == SEQ_FLY && m_OwnerActor->getFlagBit(ACTORFLAG_AI_TIRED))
	{
		animBody = SEQ_TIRED;
	}
	if (m_OwnerActor->getAnimBodyId() >= 0)
	{
		animBody = m_OwnerActor->getAnimBodyId();
	}

	//滑板自定义动作，判断是否在block边缘
	if (m_OwnerPlayer) {
		PlayerLocoMotion* playerLoc = static_cast<PlayerLocoMotion*> (m_OwnerPlayer->getLocoMotion());
		if (animBody == SEQ_STAND && playerLoc->m_OnBlockFrontEdge)
		{
			animBody = SEQ_SKATEBOARD_ONBLOCKFRONT;
		}
	}

	//if (player && player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	//{
	//	ActorBasketBall* pBasketBall = dynamic_cast<ActorBasketBall*>(player->getCatchBall());
	//	if (pBasketBall)
	//	{
	//		LivingLocoMotion* loc = dynamic_cast<LivingLocoMotion*>(m_OwnerActor->getLocoMotion());
	//		if (loc && !loc->m_InWater && !isflying)
	//		{
	//			animUpBody = SEQ_BASKETBALL_DRIBBLE;
	//			pBasketBall->playDribbleMotion();
	//		}
	//		else
	//		{
	//			stopAnim(SEQ_BASKETBALL_DRIBBLE);
	//			pBasketBall->stopDribbleMotion();
	//		}
	//	}
	//	else
	//	{
	//		stopAnim(SEQ_BASKETBALL_DRIBBLE);
	//	}
	//}

	//if (m_OwnerPlayer && m_OwnerPlayer->isSkinning())
	//{
	//	animBody = SEQ_SKINNING;
	//}

	if (animBody == SEQ_STAND)
	{
		if (m_CurAnim[0] == SEQ_STAND && !isInPlayerAction() && animUpBody == -1)
		{
			bool play = false;
			if (GenRandomInt(100 * 20) == 0)
			{
				play = true;
			}

			if (play)
			{
				if (m_OwnerPlayer)
				{

				}
				else
				{
					animBody = (NULL != dynamic_cast<ActorHorse*>(m_OwnerActor)) ? SEQ_SHOWTIME : SEQ_IDLEACTION;
					if (m_FaceMesh != nullptr && m_FaceTexs && m_FaceTexs[EXPRESSION_IDLEACTION])
						m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_IDLEACTION]);
				}
			}
			else
			{
				auto animals = m_vIdleAnimals.begin();
				while (animals != m_vIdleAnimals.end())
				{
					if (GenRandomInt(animals->second) == 0)
					{
						animBody = animals->first;
						break;
					}
					animals++;
				}
			}
		}
		else if (m_CurAnim[0] == SEQ_IDLEACTION || m_CurAnim[0] == SEQ_SHOWTIME || m_CurAnim[0] == SEQ_IDLEACTION2)
		{
			if (isInPlayerAction() || (m_Entity && !m_Entity->HasAnimPlaying(seqType2ID(m_CurAnim[0]))))
			{
				animBody = SEQ_STAND;
				if (!isAvatarModel())
				{
					setFaceExpression(EXPRESSION_STAND);
				}
			}
			else
			{
				animBody = m_CurAnim[0];
			}
		}

		if (m_nActTrigger > 0)
		{
			//20211020 codeby:chenwei 使用新的触发器动作接口
			if (updateTriggerAction(m_nActTrigger))
				animBody = SEQ_PLAY_ACT;
			else
				m_nActTrigger = -1;
		}
		else if (m_nAct > 0)
		{
			//20210929 codeby:chenwei 根据接口参数修改，修改逻辑
			if (updateAction(m_nAct))
				animBody = SEQ_PLAY_ACT;
			else {
				m_nAct = -1;
				m_bSideAct = false;
				clearAction(); //2021-10-20 codeby:wangyu 清除互动动作
			}
		}
	}
	else if (m_nAct > 0 || m_nActTrigger > 0)
	{
		m_nAct = -1;
		m_nActTrigger = -1;
		m_bSideAct = false; //2021-09-14 codeby:chenwei 重置副动作状态
		clearAction();
	}

	if ((animUpBody == -1 || animUpBody == SEQ_GUN_IDLE) && player)
	{
		// 非枪械的tool 控制下半身动作
		int curToolId = player->getCurToolID();
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(curToolId);
		if (tooldef)
		{
			switch (animBody)
			{
			case SEQ_STAND:
				if (animUpBody == -1)
					animUpBody = SEQ_TOOL_UPBODY_IDLE;
				break;
			case SEQ_WALK:
				animUpBody = SEQ_TOOL_UPBODY_WALK;
				break;
			case SEQ_RUN:
				animUpBody = SEQ_TOOL_UPBODY_RUN;
				break;
			default:
				break;
			}
		}
	}

	if (bForceIdleByBuffLOC && !isAnimIdle(animBody))
	{
		animBody = SEQ_STAND;
		animUpBody = -1;
	}

	if (player && player->IsOffline())
	{
		animBody = SEQ_LAYDOWN;
		animUpBody = -1;
	}

	if (m_OwnerActor->getObjType() == OBJ_TYPE_VILLAGER)
	{
		auto CarryComp = m_OwnerActor->getCarryComponent();
		if (CarryComp && CarryComp->isCarried())
			animBody = SEQ_CARRIED;
		else
			animBody = convertExtremisAnim(animBody);
	}
	if (m_OwnerPlayer && GetDefManagerProxy()->isFishNeedUp(m_OwnerPlayer->getCurToolID()))
	{
		if (animBody == SEQ_SWIM)
		{
			animBody = SEQ_CARRYING;
		}
	}
	if (m_OwnerActor->getObjType() == OBJ_TYPE_SNOWMAN)
	{
		ActorSnowMan* snowman = dynamic_cast<ActorSnowMan*>(m_OwnerActor);
		if (snowman && snowman->getPanicStatu())
		{
			if (animBody == SEQ_WALK)
			{
				animBody = SEQ_SNOWMAN_MOVE_PANIC;
			}
			else if (animBody == SEQ_STAND)
			{
				animBody = SEQ_SNOWMAN_STAND_PANIC;
			}
		}		
	}
	if (RidComp && RidComp->isRiding())
	{
		ClientActor* riding = RidComp->getRidingActor();
		if (riding)
		{
			ActorHorse* horse = dynamic_cast<ActorHorse*>(riding);
			if (horse)
				horse->updateRiddenBodyAnim(animBody, m_OwnerActor);
		}
	}

	if (RidComp && RidComp->isRidden())
	{
		ActorHorse* horse = dynamic_cast<ActorHorse*>(m_OwnerActor);
		/*	if (horse && horse->getBamboDragonFlyState() && horse->getDragonFlyIsFly())
				animBody = SEQ_FLY;*/
		if (horse)
			horse->updateHorseBodyAnim(animBody);
	}

	//当前是否持有武器 这里需要增加配置表，配置不同的武器的战力、移动、等动作
	// 不使用原来的动作分层
	// if (player && player->isComboWeaponInHand())
	// {
	// 	if (animBody == SEQ_STAND)
	// 		animBody = SEQ_COMBOATK_IDLE_BODY;
	// 	else if (animBody == SEQ_WALK || animBody == SEQ_RUN)
	// 		animBody = SEQ_COMBOATK_MOVE_BODY;
	// }

	if (m_iFollowingActId != 0 && (animBody == SEQ_WALK || animBody == SEQ_RUN || animBody == SEQ_COMBOATK_MOVE_BODY))
	{
		animBody = m_iFollowingActId;
	}

	if (m_CheckPlaySeq > 0)
	{
		if (!hasSeqPlaying(m_CheckPlaySeq))
		{
			m_CheckPlaySeq = -1;
			m_CheckAnim = -1;
			m_CurAnim[0] = -1;
			m_CurAnim[1] = -1;
		}
	}

	setCurAnim(animBody, 0);
	setCurAnimWeapon(animWeapon);
	setCurAnim(animUpBody, 1);
}

void ActorBody::setCurAnimWeapon(int anim)
{
	if (!m_ablePlayerOtherAnim || m_ownerActorTriggerProjecttile)
	{
		return;
	}

	if (m_CurWeaponSeqID != anim || (m_OwnerPlayer && m_CurToolID != m_OwnerPlayer->getCurToolID()))
	{
		if (m_CurWeaponSeqID > 0)
			stopWeaponAnim(m_CurWeaponSeqID);

		if (anim >= 0)
			playWeaponAnim(anim, 0);

		m_CurWeaponSeqID = anim;
	}
}

int ActorBody::convertExtremisAnim(int animBody)
{
	if (m_OwnerActor->getObjType() != OBJ_TYPE_VILLAGER)
		return animBody;

	VillagerAttrib* attrib = dynamic_cast<VillagerAttrib*>(m_OwnerActor->getAttrib());
	if (attrib)
	{
		int stage = attrib->getExtremisStage();
		if (stage == 3)
		{
			if (animBody == SEQ_STAND)
			{
				return SEQ_EXTREMIS_STAND_3;
			}
		}
		else if (stage == 2)
		{
			if (animBody == SEQ_STAND)
			{
				return SEQ_EXTREMIS_STAND_2;
			}
		}
		else if (stage == 1)
		{
			if (animBody == SEQ_STAND)
			{
				return SEQ_EXTREMIS_STAND_1;
			}
			else if (animBody == SEQ_WALK)
			{
				return SEQ_EXTREMIS_WALK;
			}
		}
	}

	return animBody;
}


//20211020 codeby:chenwei 分离触发动作到独立函数实现
bool ActorBody::updateTriggerAction(int act)
{
	auto def = GetDefManagerProxy()->getTriggerActDef(act);
	if (!m_OwnerActor || !m_Entity || !def)
		return false;

	int actid = def->ActID;
	const std::string face = def->Face;

	int conversionActid = actionIDConversion(actid);
#ifdef DEDICATED_SERVER
	if (hasAnimPlaying(actid))
#else
	if (m_Entity->HasAnimPlaying(conversionActid))
#endif
	{
		//s_SeqIDs[SEQ_PLAY_ACT] = actid;
		setActSeqID(actid);
		if (!isAvatarModel())
		{
			setFaceExpression(face);
		}
		return true;
	}

	if (m_CurAnim[0] != SEQ_PLAY_ACT || seqType2ID(SEQ_PLAY_ACT) != actid)
	{
		m_Entity->PlayMotion(def->Effect.c_str(), true, 30000);
#ifdef DEDICATED_SERVER
		m_ActivePlaySeqList[actid] = Rainbow::Timer::getSystemTick();
#endif

		//s_SeqIDs[SEQ_PLAY_ACT] = actid;
		setActSeqID(actid);
		if (!isAvatarModel())
		{
			setFaceExpression(face);
		}
		return true;
	}

	m_Entity->StopMotion(30000);
	if (!isAvatarModel())
	{
		setFaceExpression(-1);
	}

	// PlayerControl
	PlayerControl* playerCtrl = dynamic_cast<PlayerControl*>(m_OwnerActor);
	if (playerCtrl)
	{
		playerCtrl->recoverActView();
	}

	return false;
}


//20210929 codeby:chenwei 更新接口参数，新增装扮互动动作逻辑
bool ActorBody::updateAction(int act)
{
	auto def = GetDefManagerProxy()->getPlayActDef(act); //20211013 codeby:chenwei 修复用错变量影响到触发器动作的问题
	if (!m_OwnerActor || !m_Entity)
		return false;
	if (!def)//20240104 codeby:wangyu 新动作不走配置表，直接传动作id
	{
		//int conversionActid = actionIDConversion(act);
		setActSeqID(act);
		return true;
	}
	//20210929 codeby:chenwei 新增装扮互动动作判定逻辑
	int actid = def->ActID;
	bool isSkinAct = def->SkinID > 0 && def->SkinID2 > 0;
	const std::string face = def->Face;

	if (isSkinAct && m_bSideAct) //如果是装扮动作，并且是副动作，则播副动作ID
	{
		actid = def->ActID * 10 + 1;
	}


	int conversionActid = actionIDConversion(actid);
	if (m_Entity->HasAnimPlaying(conversionActid))
	{
		//s_SeqIDs[SEQ_PLAY_ACT] = actid;
		setActSeqID(actid);
		if (!isAvatarModel())
		{
			setFaceExpression(face);
		}
		return true;
	}

	if (m_CurAnim[0] != SEQ_PLAY_ACT || seqType2ID(SEQ_PLAY_ACT) != actid)
	{
		//20210929 codeby:chenwei 装扮互动特效
		if (isSkinAct)
		{
			playSkinActMotion(act, 30000);
		}
		else
		{
			m_Entity->PlayMotion(def->Effect.c_str(), true, 30000);
		}
		//s_SeqIDs[SEQ_PLAY_ACT] = actid;
		setActSeqID(actid);
		//if (!isAvatarModel())
		//{
			//setFaceExpression(face);
		//}
		return true;
	}

	m_Entity->StopMotion(30000);
	if (!isAvatarModel())
	{
		setFaceExpression(-1);
	}

	// PlayerControl
	PlayerControl* playerCtrl = dynamic_cast<PlayerControl*>(m_OwnerActor);
	if (playerCtrl)
	{
		playerCtrl->recoverActView();
	}

	return false;
}

void ActorBody::clearAction()
{
	if (m_CurAnim[0] == SEQ_PLAY_ACT)
		setCurAnim(-1, 0);

	if (m_Entity)
		m_Entity->StopMotion(30000);
	if (!isAvatarModel())
	{
		setFaceExpression(-1);
	}

	// PlayerControl
	PlayerControl* playerCtrl = dynamic_cast<PlayerControl*>(m_OwnerActor);
	if (playerCtrl)
	{
		playerCtrl->recoverActView();
	}

	//20210927 codeby：chenwei 新增动作中断回调函数并重置序列号ID
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player) player->onAnimInterrupt();
}

void ActorBody::setAct(int act)
{
	m_nAct = act;
	if (act > 0)
	{
		auto def = GetDefManagerProxy()->getPlayActDef(m_nAct);
		if (def)
		{
			//2021-09-14 codeby:chenwei 如果是装扮动作，并且是副动作，则播副动作ID
			int actID = def->ActID;
			if (def->SkinID2 > 0 && m_bSideAct == true) {
				actID = def->ActID * 10 + 1;
			}

			//s_SeqIDs[SEQ_PLAY_ACT] = def->ActID;
			setActSeqID(actID);
		}
		else
		{
			if (m_bSideAct == true)
			{
				setActSeqID(m_nAct * 10 + 1);
			}
			else
			{
				setActSeqID(m_nAct);
			}
		}

		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (player && m_World && !m_World->isRemoteMode())
		{
			player->playActionOnTrigger(m_nAct);
		}
	}
}

void ActorBody::setActTrigger(int act)
{
	m_nActTrigger = act;
	if (act > 0)
	{
		auto def = GetDefManagerProxy()->getTriggerActDef(act);
		if (def)
		{
			//s_SeqIDs[SEQ_PLAY_ACT] = def->ActID;
			setActSeqID(def->ActID);

			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
			if (player && m_World && !m_World->isRemoteMode())
			{
				player->playActionOnTrigger(0, m_nActTrigger);
			}
		}
	}
}

bool ActorBody::playAct(int act)
{
	//auto def = GetDefManagerProxy()->getPlayActDef(act);
	//if (def)
	//{
		if (m_CurAnim[0] == SEQ_PLAY_ACT)
			setCurAnim(-1, 0);
		if (m_Entity)
			m_Entity->StopMotion(30000);
		m_nAct = act;
		m_nActTrigger = -1;
		m_bSideAct = false; //2021-09-14 codeby:chenwei 重置副动作状态	
		if (!isAvatarModel())
		{
			setFaceExpression(-1);
		}
		return true;
	//}
	//return false;
}

void ActorBody::stopAct()
{
	m_nAct = -1;
	m_nActTrigger = -1;
	clearAction();
}

//判断角色是否处于上半身的动作
bool ActorBody::isInPlayerAction()
{
	if (m_OwnerPlayer == NULL)
	{
		return false;
	}

	return  m_OwnerPlayer->getCurOperate() != PLAYEROP_NULL || m_OwnerPlayer->getGunLogical()->getGunDef() != NULL/*m_OwnerPlayer->getGunLogical()->isInGunAction()*/;
}

void ActorBody::playSkinEffect(int skin_id)
{
	RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(skin_id);
	if (skindef == NULL) return;

	int anim = getCurAnim(0);
	const char* effname = skindef->getEffect(1);
	auto RidComp = m_OwnerActor->getRiddenComponent();

	int effectId = getAvatarListModelId(AVATAR_PART_TYPE::FOOTPRINT);
	
	if ((anim == SEQ_WALK || anim == SEQ_RUN || anim == SEQ_COMBOATK_MOVE_BODY) && !(RidComp && RidComp->isRiding()) && effname  != NULL && m_OwnerActor->IsVisible() && effectId <= 0)
	{
		WCoord dp = m_OwnerActor->getPosition() - m_LastSkinEffectPos;
		if (dp.length() >= BLOCK_FSIZE * 2.0f)
		{
			char path[256];
			sprintf(path, "%s.ent", effname);


			Quaternionf rot = AxisAngleToQuaternionf(Vector3f::yAxis, Deg2Rad(m_RenderYawOffset));
			Vector3f offset(0.0f, 0.0f, 0.0f);
			offset.x = (m_SkinEffectCount % 2) == 0 ? 20.0f : -20.0f;
			offset = RotateVectorByQuat(rot, offset);

			WCoord offsetcoord(offset);
			ParticlesComponent::playParticles(m_OwnerActor, path, 60, &offsetcoord);

			m_LastSkinEffectPos = m_OwnerActor->getPosition();
			m_SkinEffectCount++;
		}
	}
	else if ((anim == SEQ_SKINSTAND || anim == SEQ_SKINFLY) && (effname = skindef->getEffect(2)) != NULL)
	{
		if (!m_SkinFlyingEffect)
		{
			if (m_Entity) m_Entity->PlayMotion(effname, false);
			m_SkinFlyingEffect = true;
		}
	}
	else if (m_SkinFlyingEffect)
	{
		if (m_Entity) m_Entity->StopMotion(skindef->getEffect(2));
		m_SkinFlyingEffect = false;
	}

	const char* born_effect = skindef->getEffect(3);
	if (born_effect)
	{
		bool shouldplay = true;
		if (m_OwnerActor->isDead() || m_OwnerActor->needClear()) shouldplay = false;
		else if (m_OwnerActor == g_pPlayerCtrl && g_pPlayerCtrl->getCamera()->getMode() == 0) shouldplay = false;

		if (m_SkinEffect3Playing && !shouldplay)
		{
			if (m_Entity) m_Entity->StopMotion(born_effect);
		}
		else if (!m_SkinEffect3Playing && shouldplay)
		{
			if (m_Entity) m_Entity->PlayMotion(born_effect);
		}
		m_SkinEffect3Playing = shouldplay;
	}

	const char* trailing_effect = skindef->getEffect(4);
	if (trailing_effect)
	{
		bool shouldplay = true;
		if (m_OwnerActor->isDead() || m_OwnerActor->needClear()) shouldplay = false;
		else if (m_OwnerActor == g_pPlayerCtrl && g_pPlayerCtrl->getCamera()->getMode() == 0) shouldplay = false;

		if (m_SkinEffect4Playing && !shouldplay)
		{
			if (m_Entity) m_Entity->StopMotion(trailing_effect);
		}
		else if (!m_SkinEffect4Playing && shouldplay)
		{
			if (!GetAvatarPartModelShow(AVATAR_PART_TYPE::TRAILING_EFFECT))
			{
				if (m_Entity) m_Entity->PlayMotion(trailing_effect);
			}
		}
		m_SkinEffect4Playing = shouldplay;
	}
}

void ActorBody::updateSkinEffect()
{
	int anim = getCurAnim(0);

	if (m_CustomSkins.size() > 0)
	{
		//data/http/productions/1000_25/140021_2.emo
		ClientPlayer* player = NULL;
		player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (player)
		{
			//int uin = player->getUin();
			int effectId = getAvatarListModelId(AVATAR_PART_TYPE::FOOTPRINT);
			//int partID = 0;
			//MINIW::ScriptVM::game()->callFunction("GetAvatarEffectID", "i>ii", uin, &partID, &effectId);

			//if (partID == 9 && effectId > 0)
			if (effectId > 0 && m_AvatarComponent && m_AvatarComponent->m_bAvaterEffectShow)
			{
				if ((anim == SEQ_WALK || anim == SEQ_RUN || anim == SEQ_COMBOATK_MOVE_BODY) && m_OwnerActor->IsVisible())
				{
					WCoord dp = m_OwnerActor->getPosition() - m_LastSkinEffectPos;
					if (dp.length() >= BLOCK_FSIZE * 2.0f)
					{
						Quaternionf rot = AxisAngleToQuaternionf(Vector3f::yAxis, Deg2Rad(m_RenderYawOffset));
						Vector3f offset(0.0f, 0.0f, 0.0f);
						offset.x = (m_SkinEffectCount % 2) == 0 ? 20.0f : -20.0f;
						offset = RotateVectorByQuat(rot, offset);

						WCoord offsetcoord(offset);
						ParticlesComponent::playAvatarParticles(m_OwnerActor, effectId, 60, &offsetcoord);
						m_LastSkinEffectPos = m_OwnerActor->getPosition();
						m_SkinEffectCount++;
					}
				}

				if (!m_SkinEffect3Playing)
				{
					//m_Entity->playMotion(25);
				}
				m_SkinEffect3Playing = true;
				m_SkinEffect4Playing = true;
			}

			if (player->IsVisible() && m_Entity)
			{
				Rainbow::Model* model = m_Entity->GetMainModel();
				if (GetAvatarPartModelShow(AVATAR_PART_TYPE::TRAILING_EFFECT))
				{
					PlayerAttrib* attr = static_cast<PlayerAttrib*>(m_OwnerActor->getAttrib());
					if (m_AvatarComponent && m_AvatarComponent->m_bAvaterEffectShow && attr && !attr->isStill())
					{
						model->ShowAvatar(AVATAR_PART_TYPE::TRAILING_EFFECT, true);
					}
					else
					{
						model->ShowAvatar(AVATAR_PART_TYPE::TRAILING_EFFECT, false);
					}
				}
			}

			if (m_CustomSkins.size() > 0)
			{
				int skin_id = (m_PlayerIndex > 0) ? getSkinID() : 0;
				if (skin_id > 0)
				{
					//使用皮肤模型的avt，同样需要显示周身特效
					playSkinEffect(skin_id);
				}
			}
		}
	}
	else
	{
		if (m_OwnerActor->getObjType() != OBJ_TYPE_ROLE) { return; }
		if (m_PlayerIndex <= 0 || getSkinID() == 0) return;
		playSkinEffect(getSkinID());
	}
}


void ActorBody::stopAnimBySeqId(int seq,bool reset)
{
	if (m_NowPlaySeqID == seq)
	{
		stopActorActionEffect(m_NowPlaySeqID);
		m_NowPlaySeqID = -1;
		m_NowPlayAnimLayer = -1;
	}

#ifdef DEDICATED_SERVER
	m_ActivePlaySeqList.erase(seq);
#endif

	if (m_Entity) {
		m_Entity->StopAnim(seq, reset);
	}
}
void ActorBody::skillStopAnimBySeqId(int seq, bool reset)
{
	if (m_Entity) {
		m_Entity->StopAnim(seq, reset);
		setCurAnimId(-1, 0);
		setCurAnimId(-1, 1);
	}
}

void ActorBody::playAnimBySeqId(int seq, int inputloopmode/* =-1 */, int playLayer)
{
	// LogStringMsg("ActorBody::playAnimBySeqId %d %d", seq, playLayer);

	if (seq == 100105 && m_OwnerPlayer && m_OwnerPlayer->getSkinID() == 52)
	{
		auto effectComponent = m_OwnerPlayer->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("140050_2");
		}
	}

	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (playLayer >= 0 && playLayer <= 1)
	{
		m_CurAnim[playLayer] = -1;
	}
	else if (playLayer == 2)
	{
		m_CurAnim[0] = -1;
		m_CurAnim[1] = -1;
	}

	if (model && !model->HasAnim(seq))
	{
		seq = seqType2ID(SEQ_STAND);
	}

	if (seq == seqType2ID(SEQ_IDLEACTION))
	{
		if (m_FaceMesh != NULL && m_FaceTexs && m_FaceTexs[EXPRESSION_IDLEACTION])
		{
			m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_IDLEACTION]);
		}
		// 		if(m_Entity) m_Entity->playAnim(seq);
		playAnimCheck(seq);
		setCurAnim(SEQ_IDLEACTION, 0);
		return;
	}
	else if (seq == seqType2ID(SEQ_SHOWTIME))
	{
		if (m_FaceMesh != NULL && m_FaceTexs && m_FaceTexs[EXPRESSION_SHOWTIME])
		{
			m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_SHOWTIME]);
		}
		// 		if(m_Entity) m_Entity->playAnim(seq);
		playAnimCheck(seq);
		setCurAnim(SEQ_SHOWTIME, 0);
		return;
	}

	if (m_Entity) {
		// 		m_Entity->playAnim(seq);
		playAnimCheck(seq, inputloopmode);
	}
}

void ActorBody::skillplayAnimBySeqId(int seq, int inputloopmode, int playLayer)
{
	setCurAnimId(-1, 0);
	setCurAnimId(-1, 1);
	if (seq == 100105 && m_OwnerPlayer && m_OwnerPlayer->getSkinID() == 52)
	{
		auto effectComponent = m_OwnerPlayer->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("140050_2");
		}
	}
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (playLayer >= 0 && playLayer <= 1)
	{
		m_CurAnim[playLayer] = -1;
	}
	else if (playLayer == 2)
	{
		m_CurAnim[0] = -1;
		m_CurAnim[1] = -1;
	}

	if (model && !model->HasAnim(seq))
	{
		seq = seqType2ID(SEQ_STAND);
	}

	if (seq == seqType2ID(SEQ_IDLEACTION))
	{
		if (m_FaceMesh != NULL && m_FaceTexs && m_FaceTexs[EXPRESSION_IDLEACTION])
		{
			m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_IDLEACTION]);
		}
		// 		if(m_Entity) m_Entity->playAnim(seq);
		playAnimCheck(seq, inputloopmode, 1.0, playLayer);
		setCurAnim(SEQ_IDLEACTION, 0);
		return;
	}
	else if (seq == seqType2ID(SEQ_SHOWTIME))
	{
		if (m_FaceMesh != NULL && m_FaceTexs && m_FaceTexs[EXPRESSION_SHOWTIME])
		{
			m_FaceMesh->SetTexture("g_DiffuseTex", m_FaceTexs[EXPRESSION_SHOWTIME]);
		}
		// 		if(m_Entity) m_Entity->playAnim(seq);
		playAnimCheck(seq, inputloopmode, 1.0, playLayer);
		setCurAnim(SEQ_SHOWTIME, 0);
		return;
	}

	if (m_Entity) {
		// 		m_Entity->playAnim(seq);
		playAnimCheck(seq, inputloopmode,1.0, playLayer);
	}

}


bool ActorBody::playAnimCheck(int seqId, int eInputLoopMode /* = -1 */, float speed /*= 1.f*/, int layer /*= -1*/)
{
	LOG_INFO("ActorBody::playAnimCheck %d %d %f %d", seqId, eInputLoopMode, speed, layer);
	bool ismob = false;
	ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);

	ismob = mob ? true : false;
	// if (ismob)
	// {
	// 	LOG_PRIVATE("hx playAnimCheck %d layer %d", seqId, layer);
	// }

	if (!m_ablePlayerOtherAnim)
	{
		return false;
	}
#ifdef IWORLD_SERVER_BUILD
	m_NowPlaySeqID = seqId;
	m_NowPlayAnimLayer = layer;
	m_ActivePlaySeqList[m_NowPlaySeqID] = Rainbow::Timer::getSystemTick();
	return true;
#endif
	//不必播放相同的循环动画 code by 李元星
	int rInputLoopMode = -1;
	int customActSeqId = actionIDConversion(seqId);
	// 默认为true不影响原来的逻辑
	bool hasCustomActAnim = true;	
	if (m_Entity && m_Entity->GetMainModel())
	{
		Rainbow::Model* model = m_Entity->GetMainModel();
		if (model && hasAnimSeq(seqId))
		{
			hasCustomActAnim = hasAnimSeq(customActSeqId);
			if (model->IsKindOf<ModelLegacy>())
			{
				ModelLegacy* modelLegacy = static_cast<ModelLegacy*>(model);
				if (modelLegacy && modelLegacy->GetModelData())
				{
					SharePtr<AnimationData> panimdata;
					for (size_t i = 0; i < modelLegacy->GetModelData()->GetNumAnim(); i++)
					{
						SharePtr<AnimationData> pdata = modelLegacy->GetModelData()->getAnimation(i);
						if (pdata && pdata->hasSequence(seqId) && pdata->m_BoneTracks.size())
						{
							panimdata = pdata;
							break;
						}
					}
					if (!panimdata) return false;
					SequenceDesc* sd = panimdata->getSequence(panimdata->getSequenceIndex(seqId));
					if (!sd) return false;

					if (eInputLoopMode >= 0)
					{
						rInputLoopMode = eInputLoopMode;
					}
					else
					{
						SequenceMap::SeqDesc* smsd = GetSequenceMap().findSequenceDesc(seqId);
						if (eInputLoopMode < 0)
							rInputLoopMode = sd->loopmode == ANIE_MODE_EDIT ? ANIM_MODE_ONCE_STOP : (smsd ? smsd->loopmode : sd->loopmode);
					}
				}
				else
					return false;
			}
			else if (model->IsKindOf<ModelNew>())
			{
				if (eInputLoopMode >= 0)
				{
					rInputLoopMode = eInputLoopMode;
				}
				else
				{
					rInputLoopMode = ANIM_MODE_ONCE;
					SequenceMap::SeqDesc* smsd = GetSequenceMap().findSequenceDesc(seqId);
					if (smsd)
						rInputLoopMode = smsd->loopmode;
				}
			}
		}
		else
			return false;
	}
	else
	{
		return false;
	}

	// 判断动作打断
	if (seqId == m_NowPlaySeqID && m_NowPlayAnimLayer == layer && m_Entity->GetCurAnimLoopMode() == eInputLoopMode && hasAnimIdPlaying(seqId))
	{
		// 如果是循环动画且正在播放相同的动画，则不允许打断
		if (rInputLoopMode == ANIM_MODE_LOOP )
			return true;

		// 特定动画不允许打断
		static std::map<int, int> map_seq_id = {
			{TPS_DOUBLEWEAPON_ATTACK, 1},
			{100105, 1},{1010141, 1}, {FPS_DOUBLEWEAPON_ATTACK, 1}
		};
		if (map_seq_id.find(seqId) != map_seq_id.end())
		{
			return false;
		}
	}

	if (m_OwnerActor)
	{
		LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
		if (pAttrib && pAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_STOP_ACTION))
		{
			return false;
		}
	}
	
	if (!CheckModelNewAnimLoad(seqId)) return false;

	int srcSeq = seqId;
	seqId = customActSeqId;
	// 模型里不存在自定义动作对应的ID，还是使用原ID
	if (!hasCustomActAnim)
	{
		seqId = srcSeq;
	}

	if (seqId != srcSeq)
	{
		GetSequenceMap().copySequenceDesc(srcSeq, seqId);
	}

	if (m_Entity)
	{
		if ((m_Entity->IsShow() || m_nActTrigger > 0)&&  m_Entity->PlayAnim(seqId, eInputLoopMode, speed, layer))//第一人称entity是隐藏的
		{
			if (m_MutateMob == 0)
			{
				playActorActionEffect(srcSeq);
				playSkinActCsvEffect(srcSeq);
			}
			m_NowPlaySeqID = srcSeq;
			m_NowPlayAnimLayer = layer;
		}
	}
	else
	{
		return false;
	}

	return true;
}

bool ActorBody::CheckModelNewAnimLoad(int& seqId)
{
	if (m_Entity && m_Entity->IsShow())
	{
		Rainbow::Model* model = m_Entity->GetMainModel();
		if (model && model->IsKindOf<ModelNew>())
		{
			ModelNew* modelnew = static_cast<ModelNew*>(model);
			if (modelnew)
			{
				if (modelnew->GetIsPlayer() && modelnew->HasInitiallized())
				{
					if (m_vLoadedAnimSeqs.find(seqId) != m_vLoadedAnimSeqs.end())
					{
						return true;
					}

					// 战斗相关动态加载动作，有状态，默认关联空动作
					if (modelnew->HasAnimState(seqId))
					{
						if (m_vLoadedAnimSeqs.find(seqId) == m_vLoadedAnimSeqs.end())
						{
							auto stateClip = modelnew->GetStateClip(seqId);
							if (stateClip.IsValid())
							{
								SharePtr<SkeletonAnimationClip> skeletonClip = stateClip.CastTo<SkeletonAnimationClip>();
								if (skeletonClip.IsValid())
								{
									if (skeletonClip->IsEmpty())
									{
										if (!loadAnimToModel(seqId, seqId))
										{
											return false;
										}
									}

									m_vLoadedAnimSeqs.insert(seqId);
								}
							}
						}
					}
					// 自定义表情动作，使用1000001 - 1000003 三个临时的状态播放动作
					else if (!modelnew->HasAnimState(seqId) && actionIDConversionReverse(seqId) == seqId)
					{
						core::string motionStr = Format("%d", seqId);
						addCustomAnimMap(modelnew->GetNextMotionSeqId(), motionStr);
						if (!loadCusAnim())
						{
							return false;
						}
					}
				}
			}
		}
	}

	return true;
}

int ActorBody::getCurPlayAnimIds(std::vector<int>& list)
{
	//只获取第一个活动中的动作的seqid
	int ret = 1;
	if (!m_Entity) return ret;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (!model)
		return ret;

	int result = model->getCurPlayAnimIds(list);
	if (result > 0)
	{
		ret = result;
	}
	else
	{
		list.push_back(m_NowPlaySeqID);
	}

	return ret;

}

int ActorBody::playAnim(int anim, int inputloopmode, float speed)
{
	int seq = playAnimInner(anim, inputloopmode, speed);
	if (seq != -1)
	{
		m_CheckPlaySeq = seq;
		m_CheckAnim = anim;
	}
	return seq;
}

int ActorBody::playAnimInner(int anim, int inputloopmode/* =-1 */, float speed /*= 1.f*/)
{
	assert(anim >= 0 && anim < MAX_SEQ);
	if (!m_Entity) return -1;
	OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Animation);
	// LOG_INFO("ActorBody::playAnim %d %d", anim, inputloopmode);

	if (m_IsInUI && anim == 0) anim = SEQ_UI_STAND;

	if (m_OwnerActor)
	{
		//ActorSandworm* sandworm = dynamic_cast<ActorSandworm*>(this->m_OwnerActor);
		//if ((anim == SEQ_SANDWORM_WALK || anim == SEQ_WALK) && sandworm && sandworm->getDrillingState())
		//{
		//	return -1;
		//}
		//if (m_OwnerActor->getObjType() == OBJ_TYPE_SANDMAN && anim == SEQ_SANDMAN_FAKEDEATH)
		//{

		//}
		//else
		{
			LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
			if (pAttrib)
			{
				if (pAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
				{
					std::vector<StatusAttInfo> vAttrtForbidOpValue;
					pAttrib->getStatusAddAttInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE, vAttrtForbidOpValue);
					if (vAttrtForbidOpValue.size() == 1 && vAttrtForbidOpValue[0].iStatusId == LOSS_OF_CONSCIOUSNESS_BUFF) {
						if (!isAnimIdle(anim)) {
							return -1;
						}
					}
					else {
						return -1;
					}
				}
				if (pAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_STOP_ACTION))
				{
					return -1;
				}
			}
		}
	}

	Rainbow::Model* model = m_Entity->GetMainModel();
	resetSeqAnimDesc();
	if (!(anim >= 0 && anim < MAX_SEQ))
	{
		return -1;
	}

	int seq = seqType2ID(anim);

	//没找到seq的处理
	if (model && !model->HasAnim(seq))
	{
		if (anim == SEQ_SWIM || anim == SEQ_RUN)
			seq = seqType2ID(SEQ_WALK);
		else
			seq = seqType2ID(SEQ_STAND);
	}

	int aniLayer = -1;
	if (m_OwnerPlayer)
	{
		int curToolId = m_OwnerPlayer->getCurToolID();
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(curToolId);
		if (!tooldef || tooldef->PlayerIdleSeq == 0)
		{
			tooldef = GetDefManagerProxy()->getToolDef(1); // 获取空手
		}

		switch (anim)
		{
		case SEQ_STAND:
		{
			inputloopmode = 0;

			if (tooldef && m_OwnerPlayer->getSneaking() && tooldef->PlayerSneakSeq > 0)
			{
				seq = tooldef->PlayerSneakSeq;
			}
			else if (tooldef && tooldef->DownbodyIdleSeq > 0)
			{
				seq = tooldef->DownbodyIdleSeq;
			}
		}break;
		case SEQ_WALK:
		{
			inputloopmode = 0;

			if (tooldef && m_OwnerPlayer->getSneaking() && tooldef->PlayerSneakWalkSeq > 0)
			{
				seq = tooldef->PlayerSneakWalkSeq;
			}
			else if (tooldef && tooldef->DownbodyWalkSeq > 0)
			{
				seq = tooldef->DownbodyWalkSeq;
			}
		}break;
		case SEQ_JUMP:
		{
			if (tooldef && tooldef->PlayerJumpSeq > 0)
			{
				seq = tooldef->PlayerJumpSeq;
			}
		}break;
		case SEQ_RUN:
		{
			inputloopmode = 0;
			if (tooldef && tooldef->DownbodyRunSeq > 0)
			{
				seq = tooldef->DownbodyRunSeq;
			}
		}break;
			
		// case SEQ_COMBOATK_IDLE_BODY:
		// {
		// 	if (tooldef && tooldef->PlayerIdleSeq > 0)
		// 	{
		// 		inputloopmode = 0;
		// 		seq = tooldef->PlayerIdleSeq;
		// 	}
		// }
		// break;
		// case SEQ_COMBOATK_MOVE_BODY:
		// {
		// 	if (tooldef && tooldef->PlayerWalkSeq > 0)
		// 	{
		// 		inputloopmode = 0;
		// 		seq = tooldef->PlayerWalkSeq;
		// 	}
		// }
		// break;
		case SEQ_TOOL_LOOP:
		case SEQ_TOOL_ATTACK:
		{
			seq = 0;
			if (tooldef)
			{
				if (anim == SEQ_TOOL_LOOP) seq = tooldef->BodyLoopSeq;
				else if (anim == SEQ_TOOL_ATTACK) seq = tooldef->BodyAtkSeq;
			}

			if (m_OwnerPlayer->getCurItemSkillID())
			{
				const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_OwnerPlayer->getCurItemSkillID());
				if (skilldef)
				{
					if (anim == SEQ_TOOL_LOOP) seq = skilldef->TpsChargeAct;
				}
			}

			if (seq == 0) return -1;
		}
		break;
		case SEQ_ITEMSKILL_ATTACK:
		{
			if (m_OwnerPlayer->getCurItemSkillID())
			{
				const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_OwnerPlayer->getCurItemSkillID());
				if (skilldef && skilldef->TpsAttackAct)
				{
					seq = skilldef->TpsAttackAct;
				}
			}
		}
		break;
		case SEQ_ITEMSKILL_START:
		{
			if (m_OwnerPlayer->getCurItemSkillID())
			{
				const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_OwnerPlayer->getCurItemSkillID());
				if (skilldef && skilldef->TpsStartAct)
				{
					seq = skilldef->TpsStartAct;
				}
			}
		}
		break;
		
		case SEQ_MUSIC_PLAY:
		{
			if (GetDefManagerProxy()->getMusicalDef(curToolId))
			{
				//seq = GetDefManagerProxy()->getMusicalDef(curToolId)->IdleAnimTps;
				bool isAvt = m_OwnerPlayer->getBody()->isAvatarModel();
				if (isAvt) {
					seq = GetDefManagerProxy()->getMusicalDef(curToolId)->Thirdpersonplayingaction2;
				}
				else {
					seq = GetDefManagerProxy()->getMusicalDef(curToolId)->Thirdpersonplayingaction1;
				}

				//seq = 100117;
			}
		}
		break;
		case SEQ_MUSIC_IDLE:
		{
			if (GetDefManagerProxy()->getMusicalDef(curToolId))
			{
				//seq = GetDefManagerProxy()->getMusicalDef(curToolId)->IdleAnimTps;
				bool isAvt = m_OwnerPlayer->getBody()->isAvatarModel();
				if (isAvt) {
					seq = GetDefManagerProxy()->getMusicalDef(curToolId)->Thirdpersonholdingaction2;
				}
				else {
					seq = GetDefManagerProxy()->getMusicalDef(curToolId)->Thirdpersonholdingaction1;
				}

				if (seq == 0) {
					seq = 100100;
				}
			}

			//seq = 100117;
		}
		break;
		case SEQ_TOOL_UPBODY_IDLE:
		{
			if (tooldef && tooldef->PlayerIdleSeq > 0)
			{
				seq = tooldef->PlayerIdleSeq;
			}
		}
		break;
		case SEQ_TOOL_UPBODY_WALK:
		{
			if (tooldef && tooldef->PlayerWalkSeq > 0)
			{
				seq = tooldef->PlayerWalkSeq;
			}
		}
		break;
		case SEQ_TOOL_UPBODY_RUN:
		{
			if (tooldef && tooldef->PlayerRunSeq > 0)
			{
				seq = tooldef->PlayerRunSeq;
			}
		}
		break;
		case SEQ_GUN_IDLE:
		{
			CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(curToolId);
			if (originCustomGunDef)
			{
				seq = originCustomGunDef->tpsHipHandIdle;
				inputloopmode = 0;
				aniLayer = 3;
			}
			else if (GetDefManagerProxy()->getGunDef(curToolId))
			{
				seq = GetDefManagerProxy()->getGunDef(curToolId)->IdleAnimTps;
				aniLayer = 3;
			}
		}
		break;
		case SEQ_GUN_FIRE:
		{
			CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(curToolId);
			if (originCustomGunDef)
			{
				seq = originCustomGunDef->tpsHipHandFire;
				inputloopmode = 0;
				aniLayer = 3;
			}
			else if (GetDefManagerProxy()->getGunDef(curToolId))
			{
				seq = GetDefManagerProxy()->getGunDef(curToolId)->ShootAnimTps;
			}
		}
		break;
		case SEQ_GUN_RELOAD:
		{
			CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(curToolId);
			if (originCustomGunDef)
			{
				seq = originCustomGunDef->tpsHipHandReloadPhase1;
				inputloopmode = 1;
				aniLayer = 3;
			}
			else if (GetDefManagerProxy()->getGunDef(curToolId))
			{
				seq = GetDefManagerProxy()->getGunDef(curToolId)->ReloadAnimTps;
			}
		}
		break;
		case SEQ_CATCH_GRAVITYACTOR:
		{
			if (m_OwnerPlayer->getCurOperate() == PLAYEROP_CATCH_GRAVITYACTOR)
			{
				seq = m_OwnerPlayer->getOperateData();
			}
		}
		break;
		}
	}
	else {
		if (anim == SEQ_DIE) {
			inputloopmode = 2;
		}
	}

#ifdef CHINA_SEQ_USED
	if (anim == SEQ_PIANO_PLAY && m_OwnerPlayer != NULL)
	{
		bool isAvt = m_OwnerPlayer->getBody()->isAvatarModel();
		if (isAvt) {
			seq = 600123;
		}
	}
#endif

	if (playAnimCheck(seq, inputloopmode, speed, aniLayer))
	{
		return seq;
	}
	else
	{
		return -1;
	}
}

void ActorBody::playAnimByMotionId(int motionId, bool isLoop) {
	if (!m_Entity) return;
	Rainbow::Model* model = nullptr;
	Rainbow::Model* mainModel = m_Entity->GetMainModel();
	if (!mainModel) {
		m_CurMotionId = motionId;
		m_CurMotionIdIsLoop = isLoop;
		return;
	}
	m_CurMotionId = 0;

	if (mainModel->IsKindOf<Rainbow::ModelLegacy>())
	{
		if (CustomMotionMgr::GetInstancePtr())
		{
			model = CustomMotionMgr::GetInstancePtr()->getCusMotionAct();
		}

		if (NULL == model)
			return;

		Rainbow::ModelData* modelData = nullptr;
		if (model->IsKindOf<Rainbow::ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
			if (legacymodel != nullptr && legacymodel->GetModelData())
				modelData = legacymodel->GetModelData().Get();
		}

		if (NULL == modelData)
			return;

		const SharePtr<AnimationData>& anim = modelData->getAnimationSeq(motionId);
		if (!anim)
			return;

		for (int j = 0; j < (int)anim->m_Sequences.size(); ++j) {
			if (anim->m_Sequences[j].id == motionId) {
				if (isLoop)
					anim->getSequence(j)->loopmode = ANIM_MODE_LOOP;
				else
					anim->getSequence(j)->loopmode = ANIM_MODE_ONCE_STOP;
			}
		}

		if (mainModel->IsKindOf<Rainbow::ModelLegacy>()) //有崩溃，先加个判空
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(mainModel);
			if (legacymodel != nullptr && legacymodel->GetModelData())
			{
				legacymodel->GetModelData()->AddAnimation(anim);
			}
		}
	}

	if (m_Entity) {
		m_Entity->ResetUpdate(false);
	}
	playAnimCheck(motionId);
}

void ActorBody::stopAnim(int anim)
{
	assert(anim >= 0 && anim < MAX_SEQ);
	int seq = seqType2ID(anim);

#ifdef DEDICATED_SERVER
	m_ActivePlaySeqList.erase(seq);
	m_NowPlaySeqID = -1;
	m_NowPlayAnimLayer = -1;
#else
	if (m_Entity)
		m_Entity->StopAnim(seq);
#endif
}

void ActorBody::stopAllAnim()
{
	if (m_Entity)
	{
		m_Entity->StopAnim();
		m_CurAnim[0] = -1;
		m_CurAnim[1] = -1;
		m_CurSeqID[0] = -1;
		m_CurSeqID[1] = -1;
	}
}

void ActorBody::playEffect(const Rainbow::FixedString& fxName, bool reset_play, int motion_class)
{
	if (m_Entity)
	{
		m_Entity->PlayMotion(fxName, reset_play, motion_class);
	}
}

void ActorBody::playMotion(const char* name, int motion_class, bool force_play, float loopPlayTime)
{
	if (name == NULL || m_Entity == NULL) return;
	if (m_Entity)
	{
		m_Entity->PlayMotion(name, false, motion_class, loopPlayTime);
	}
}
void ActorBody::playMotion(const char* name, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class)
{
	if (name == NULL || m_Entity == NULL) return;
	if(!m_Entity->HasMotionIsPlaying(name))
	m_Entity->PlayMotion(name, loopPlayTime, OffsetPosition, rote, scale, isLoop, motion_class);
}
bool ActorBody::hasPlayingMotion(const char* name)
{
	if (name == NULL || m_Entity == NULL) return false;
	return m_Entity->HasMotionIsPlaying(name);
}

void ActorBody::stopMotion(const char* name)
{
	if (name != NULL) {
		if (m_Entity) m_Entity->StopMotion(name);
	}
}

void ActorBody::stopMotion(int motion_class)
{
	if (m_Entity) m_Entity->StopMotion(motion_class);
}

void ActorBody::playCustomMotion(char* zipPath, const char* entPath, bool reset_play /*= true*/, int motion_class /*= 0*/)
{
	if (!m_Entity)
		return;

	std::map<std::string, const char*>::iterator iter = m_motionNames.find(zipPath);
	if (iter == m_motionNames.end())
	{
		bool loadResult = false;
		SharePtr<EntityData> model = PkgUtils::ZipFileResLoad<EntityData>(zipPath, entPath);
		if (model) {
			m_Entity->Load(model);
			loadResult = true;
		}
		if (loadResult)
		{
			int index = m_Entity->GetMotionCount() - 1;
			if (index >= 0)
			{
				const char* motion_name = m_Entity->GetMotionNameByIndex(index, motion_class);
				if (stricmp(motion_name, "") != 0)
				{
					m_motionNames.insert(std::make_pair(zipPath, motion_name));
				}
			}
		}
	}
	else
	{
		m_Entity->PlayMotion(iter->second, reset_play, motion_class);
	}
}
//??????Ч??С
void ActorBody::setMotionScale(const char* name, float fScale)
{
	if (m_Entity)
		m_Entity->SetMotionScale(name, fScale);
}

float ActorBody::getMotionScale(const char* name)
{
	if (m_Entity)
		return m_Entity->GetMotionScale(name).x;

	return 1;
}

//?????Ч??
int ActorBody::getMotionCount()
{
	if (m_Entity)
		return m_Entity->GetMotionCount();

	return 0;
}

//?????Ч??
const char* ActorBody::getMotionNameByIndex(int index, int class_type)
{
	if (m_Entity)
		return m_Entity->GetMotionNameByIndex(index, class_type);

	return NULL;
}

const char* ActorBody::getCurMotionName()
{
	if (m_Entity)
	{
		if (m_Entity->IsPlaying())
			return m_Entity->GetCurMotionName();
	}

	return "";
}


#ifdef DEDICATED_SERVER
static int ani_timeout[MAX_SEQ];
static int ani_timeout_inited = 0;
#endif

bool ActorBody::hasAnimPlaying(int anim)
{
#ifdef DEDICATED_SERVER
	if (ani_timeout_inited == 0)
	{
		ani_timeout[SEQ_SHARK_BITE_2] = 2500;
		ani_timeout_inited = 1;
	}
	int seq = 0;
	if (anim < MAX_SEQ)
		seq = seqType2ID(anim);  // 玩家的动作可能判断有问题
	else
		seq = actionIDConversion(anim);  // 玩家的动作可能判断有问题
	auto itr = m_ActivePlaySeqList.find(seq);
	if (itr != m_ActivePlaySeqList.end())
	{
		uint64_t now = Timer::getSystemTick();
		int timeout = 800;  // 默认800毫秒
		if (anim > 0 && anim < MAX_SEQ)
		{
			if (ani_timeout[anim] != 0)
				timeout = ani_timeout[anim];
		}
		bool playing = now < itr->second + timeout;
		if (!playing)
			m_ActivePlaySeqList.erase(itr);

		return playing;
}
	return false;
#else
	if (m_Entity)
	{
		if (!(anim >= 0 && anim < MAX_SEQ))
			return false;

		int seq = seqType2ID(anim);
		return m_Entity->HasAnimPlaying(seq);
	}

	return false;
#endif
}

bool ActorBody::hasAnimIdPlaying(int seqId)
{
	if (!m_Entity)
	{
		return false;
	}

	if (seqId != 0)
	{
		int seq = SeqID2Type(seqId, m_IsPlayerModel);
		if (seq < 0 || seq >= MAX_SEQ)
		{
			return false;
		}
	}

	return m_Entity->HasAnimPlaying(seqId);
}
bool ActorBody::hasSeqPlaying(int seq)
{
	if (!m_Entity)
	{
		return false;
	}
	return m_Entity->HasAnimPlaying(seq);
}

int ActorBody::seqType2ID(int seqtype)
{
	assert(seqtype >= 0 && seqtype < MAX_SEQ);
	//LOG_INFO("seqType2ID AAAAA %d , %d , %d ", seqtype, m_iActSeqId, SeqType2ID(seqtype));
	return (seqtype == SEQ_PLAY_ACT) ? m_iActSeqId : SeqType2ID(seqtype, isPlayer() || m_IsPlayerModel);
}

int ActorBody::actionIDConversion(int actid)
{
	if (m_vCustomAnimMap.find(actid) != m_vCustomAnimMap.end() && m_vCustomAnimMap[actid] != 0)
	{
		return m_vCustomAnimMap[actid];
	}
	return actid;
}

int ActorBody::actionIDConversionReverse(int actid)
{
	for (auto keypair : m_vCustomAnimMap)
	{
		if (keypair.second == actid)
			return keypair.first;
	}

	return actid;
}

void ActorBody::addCustomAnimMap(int key, std::string motionID)
{
	int cusMotionID = 0;
	int mLen = motionID.length();
	if (mLen <= 0)
		return;

	//自定义动作id都大于10位  小于10位的是官方动作id
	if (motionID != "0" && mLen > 0 && mLen < 10)
		cusMotionID = atoi(motionID.c_str());
	else
		cusMotionID = CustomMotionMgr::GetInstancePtr()->findCusMotionID(motionID);

	if (0 == cusMotionID)
		return;

	for (auto iter = m_vCustomAnimMap.begin(); iter != m_vCustomAnimMap.end();)
	{
		if (iter->second == cusMotionID && iter->first != key)
			iter = m_vCustomAnimMap.erase(iter);
		else
			++iter;
	}

	if (m_vCustomAnimMap[key] == cusMotionID)
		return;

	m_vNeedLoadCusMotionID.push_back(cusMotionID);
	m_vCustomAnimMap[key] = cusMotionID;
	m_iCusAnimCount++;
}

bool ActorBody::insertMotionToModel(int motionID)
{
	if (!m_Entity) return false;
	if (m_Entity->GetMainModel() == nullptr) return false;

	if (m_Entity->GetMainModel()->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::Model* model = nullptr;

		if (CustomMotionMgr::GetInstancePtr())
			model = CustomMotionMgr::GetInstancePtr()->getCusMotionAct();

		if (NULL == model)
			return false;

		Rainbow::ModelData* modelData = nullptr;
			if (model->IsKindOf<Rainbow::ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
			if (legacymodel != nullptr && legacymodel->GetModelData())
			{
				modelData = legacymodel->GetModelData().Get();
			}
		}

		if (NULL == modelData)
			return false;

		const SharePtr<Rainbow::AnimationData>& anim = modelData->getAnimationSeq(motionID);
		if (!anim)
			return false;

		//m_Model->getModelData()->addAnimation(anim, true);
		if (m_OwnerActor)
		{
			long long objid = m_OwnerActor->getObjId();
			anim->setObjId(objid);
		}

		Rainbow::ModelData* mainModelData = nullptr;
		if (m_Entity->GetMainModel()->IsKindOf<Rainbow::ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(m_Entity->GetMainModel());
			if (legacymodel != nullptr && legacymodel->GetModelData())
			{
				mainModelData = legacymodel->GetModelData().Get();
			}
		}
		if (mainModelData != nullptr)
		{
			mainModelData->AddAnimationByDiff(anim, mainModelData);
		}

		m_bIsNeedRemoveCusMotion = true;
	}
	else if (m_Entity->GetMainModel()->IsKindOf<Rainbow::ModelNew>())
	{
		int sourceMotionId = actionIDConversionReverse(motionID);

		//地图内需要切回默认动作
// 		if (sourceMotionId == motionID)
// 			return false;

		if (m_Entity->GetMainModel()->IsKindOf<Rainbow::ModelNew>())
		{
			loadAnimToModel(sourceMotionId, motionID);
			m_bIsNeedRemoveCusMotion = true;
		}
	}

	return true;
}

bool ActorBody::loadCusAnim()
{
	if (!m_Entity) return false;

	Rainbow::Model* model = m_Entity->GetMainModel();
	if (!model)
		return false;
	
	std::vector<int>::iterator citer = m_vNeedLoadCusMotionID.begin();
	while (citer != m_vNeedLoadCusMotionID.end())
	{
		bool result = insertMotionToModel(*citer);
		if (!result)
		{
			citer++;
		}
		else
		{
			citer = m_vNeedLoadCusMotionID.erase(citer);
		}
	}

	if (m_vNeedLoadCusMotionID.size() > 0)
	{
		m_bIsNeedLoadCusAnim = true;
	}
	else
	{
		m_bIsNeedLoadCusAnim = false;
		m_vNeedLoadCusMotionID.clear();
	}

	if (model->IsKindOf<Rainbow::ModelNew>())
	{
		Rainbow::ModelNew* modelnew = static_cast<Rainbow::ModelNew*>(model);
		if (modelnew != nullptr)
		{
			modelnew->SetCustomAnimMap(&m_vCustomAnimMap);
		}
	}

	if (m_Entity) {
		m_Entity->ResetUpdate(false);
	}

	return !m_bIsNeedLoadCusAnim;
}

bool ActorBody::loadAnimToModel(int stateID, int motionID)
{
	if (!m_Entity) return false;
	if (m_Entity->GetMainModel() == nullptr) return false;

	if (m_Entity->GetMainModel()->IsKindOf<Rainbow::ModelNew>())
	{
		Rainbow::PPtr<Rainbow::ModelNew> modelnew(static_cast<Rainbow::ModelNew*>(m_Entity->GetMainModel()));

		if (modelnew != nullptr)
		{
			// base anim replace
			{
				SharePtr<SkeletonAnimationClip> destbaseAnim;
				char path[256] = { 0 };

				sprintf(path, "%s/Animation/%d.skAnim", m_ResourcePath.c_str(), motionID);
				destbaseAnim = GetAssetManager().LoadAsset<Rainbow::SkeletonAnimationClip>(path);

				if (destbaseAnim.IsValid())
				{
					{
						SharePtr<BaseAnimationClip> destStateAnim = destbaseAnim.CastTo<BaseAnimationClip>();
						if (modelnew && destStateAnim)
						{
							modelnew->SetStateClip(stateID, destStateAnim, false);
						}
					}

					// expand anim replace
					{
						SharePtr<SkeletonAnimationClip> destAnim;
						char path[256] = { 0 };

						sprintf(path, "%s/Expand/Animation/%d.skAnim", m_ResourcePath.c_str(), motionID);
						destAnim = GetAssetManager().LoadAsset<Rainbow::SkeletonAnimationClip>(path);

						if (!destAnim.IsValid())
						{
							char path2[256] = { 0 };
							sprintf(path2, "entity/Animation/empty.skAnim");
							destAnim = GetAssetManager().LoadAsset<Rainbow::SkeletonAnimationClip>(path2);
						}

						if (modelnew != nullptr && destAnim.IsValid())
						{
							SharePtr<BaseAnimationClip> destBaseAnim = destAnim.CastTo<BaseAnimationClip>();
							modelnew->SetStateClip(stateID, destBaseAnim, true);
						}
					}

					return true;
				}


				sprintf(path, "entity/Animation/role_common/%d.skAnim", motionID);
				destbaseAnim = GetAssetManager().LoadAsset<Rainbow::SkeletonAnimationClip>(path);
				if (destbaseAnim.IsValid())
				{
					{
						SharePtr<BaseAnimationClip> destStateAnim = destbaseAnim.CastTo<BaseAnimationClip>();
						if (modelnew && destStateAnim)
							modelnew->SetStateClip(stateID, destStateAnim, false);
					}

					// expand anim replace
					{
						SharePtr<SkeletonAnimationClip> destAnim;
						char path[256] = { 0 };
						sprintf(path, "entity/Animation/empty.skAnim");
						destAnim = GetAssetManager().LoadAsset<Rainbow::SkeletonAnimationClip>(path);

						if (modelnew != nullptr && destAnim.IsValid())
						{
							SharePtr<BaseAnimationClip> destBaseAnim = destAnim.CastTo<BaseAnimationClip>();
							if (modelnew && destBaseAnim)
								modelnew->SetStateClip(stateID, destBaseAnim, true);
						}
					}

					return true;
				}
			}
		}
	}

	return false;
}

void ActorBody::addIdleAnimal(int animal, int prop)
{
	m_vIdleAnimals[animal] = prop;
}

//2021-09-14 codeby:chenwei 设置副动作状态
void ActorBody::setSideAct(bool isSideAct)
{
	m_bSideAct = isSideAct;
}

//2021-09-14 codeby:chenwei 获取副动作状态
bool ActorBody::isSideAct()
{
	return m_bSideAct;
}

//20210929 codeby:chenwei 新增播放装扮互动特效
void ActorBody::playSkinActMotion(const int act, int motion_class, bool force_play)
{
	stopMotion(motion_class);

	if (!act || !m_Entity) return;

	const PlayActDef* def = GetDefManagerProxy()->getPlayActDef(act);

	if (NULL == def) return;
	if (def->Effect == "") return;

	//如果自己的皮肤和配置播放特效的皮肤匹配，才播放特有特效
	if (getSkinID() == def->EffectSkinID)
	{
		m_Entity->PlayMotion(def->Effect.c_str(), false, motion_class);
	}
}


//20211020 codeby:chenwei 是否播放装扮互动中
bool ActorBody::isPlayingSkinAct()
{
	if (getActID() > 0)
	{
		const PlayActDef* def = GetDefManagerProxy()->getPlayActDef(getActID());
		if (def)
		{
			if (def->ID == 25 || def->ID == 26) //变身或者高兴2动作 需要重置m_bInviteActStart参数
			{
				if (m_UIComponent)
				{
					m_UIComponent->m_bInviteActStart = true;
				}
				return true;
			}
			if (def->SkinID > 0 && def->SkinID2 > 0)
			{
				if (m_UIComponent)
				{
					m_UIComponent->m_bInviteActStart = true;
				}
				return true;
			}
		}
	}

	return false;
}


void ActorBody::playSkinActCsvEffect(int seqId)
{
	if (m_PlayerIndex > 0)
	{
		const SkinActDef* def = GetDefManagerProxy()->getSkinActDef(seqId, getSkinID());
		const SkinActDef* lastDef = GetDefManagerProxy()->getSkinActDef(m_NowPlaySeqID, getSkinID());
		if (lastDef)
		{
			if (m_OwnerPlayer)
			{
				m_OwnerActor->stopMotion(lastDef->StopEffect.c_str());
			}
			else
			{
				stopMotion(lastDef->SeqEffect.c_str());
			}
		}
		if (def)
		{
			if (m_OwnerPlayer)
			{
				m_OwnerActor->playMotion(def->SeqEffect.c_str(), 1);
				if (def->StopEffect != "")
				{
					m_OwnerActor->stopMotion(def->StopEffect.c_str());
				}
			}
			else
			{
				playMotion(def->SeqEffect.c_str());
				if (def->StopEffect != "")
				{
					stopMotion(def->StopEffect.c_str());
				}
			}
		}
	}
}

void ActorBody::playActorActionEffect(int seqId)
{
	if ((m_PlayerIndex <= 0 && !m_IsInUI) || seqId == 100100 || seqId == 100101)
	{
		return;
	}
	char effectName[256] = "";
	char soundName[256] = "";
	int avtID = 0;
	int cycleStatus = 0;
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetActionDataByActionId", "i>ssii", seqId, &effectName, &soundName, &avtID, &cycleStatus);
	stopActorActionEffect(m_NowPlaySeqID);
	if (!IsNullOrEmpty(effectName))
	{
		if (m_OwnerPlayer)
		{
			m_OwnerActor->playMotion(effectName, 1);
		}
		else
		{
			playMotion(effectName);
		}
	}
	if (avtID > 0)
	{
		hideAvatarPartModel(AVATAR_PART_TYPE::HAND_ORNAMENT, true);
		addAvatarPartModel(avtID, AVATAR_PART_TYPE::HAND_ORNAMENT, true, 101, true);
	}

	if (g_pPlayerCtrl && !IsNullOrEmpty(soundName) && soundName != "")
	{
		auto soundComp = g_pPlayerCtrl->getSoundComponent();
		if (soundComp != nullptr)
		{

			soundComp->playSoundByTrigger(soundName, 1.0, 1.0, cycleStatus > 0, false);
		}
	}
}

void ActorBody::stopActorActionEffect(int seqId)
{
	if ((m_PlayerIndex <= 0 && !m_IsInUI) || seqId == 100100 || seqId == 100101)
	{
		return;
	}
	if (seqId > -1)
	{
		char effectLastName[256] = "";
		char soundLastName[256] = "";
		int avtID = 0;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetActionDataByActionId", "i>ssi", seqId, &effectLastName, &soundLastName, &avtID);
		if (!IsNullOrEmpty(effectLastName))
		{
			if (m_OwnerPlayer)
			{
				m_OwnerActor->stopMotion(effectLastName);
			}
			else
			{
				stopMotion(effectLastName);
			}
		}
		if (avtID > 0)
		{
			hideAvatarPartModel(AVATAR_PART_TYPE::HAND_ORNAMENT, true);
			//还原记录的avtid
			if (GetAvatarPartModelShow(AVATAR_PART_TYPE::HAND_ORNAMENT))
			{
				addAvatarPartModel(getAvatarListModelId(AVATAR_PART_TYPE::HAND_ORNAMENT), AVATAR_PART_TYPE::HAND_ORNAMENT,true);
			}
		}
		if (g_pPlayerCtrl && !IsNullOrEmpty(soundLastName) && soundLastName != "")
		{
			auto soundComp = g_pPlayerCtrl->getSoundComponent();
			if (soundComp != nullptr)
			{
				soundComp->stopSoundByTrigger(soundLastName, true);
			}
		}
	}
}

void ActorBody::changeActorActionBySkin(int skinid)
{
#ifdef IWORLD_UNIVERSE_BUILD
	return;
#endif
	//华阳皮肤默认复活动作为100859
	int useActionId = 0;
	char defaultActionId[64] = "";
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	int uin = player ? player->getUin() : 0;
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetActionDataBySkinId", "ii>is", uin, skinid, &useActionId, &defaultActionId);
	if (useActionId > 0)
	{
		addCustomAnimMap(useActionId, defaultActionId);
		loadCusAnim();
	}
}

bool ActorBody::PlayActInHand(int actid, int playmode)
{
#ifndef IWORLD_SERVER_BUILD
	if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_EquipComponent->m_WeaponModel);
		if (mesh && mesh->getEntity())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(actid);
			if (def)
			{
				actid = def->ActID;
			}
			//mesh->LoadAllCustomAnim(actid, playmode);
			if (mesh->getEntity())
			{
				mesh->getEntity()->PlayAnim(actid, playmode);
			}
			return true;
		}
	}
	return false;
#else
	return true;
#endif
}

void ActorBody::checkSeqMustPlayAnim()
{
	if (m_suSeqMustPlayDesc)
	{
		if (m_suSeqMustPlayDesc->_curIdx >= 0 && m_suSeqMustPlayDesc->_pAnimPlayTrack &&
			m_suSeqMustPlayDesc->_animIdList.size())
		{
			int state = m_suSeqMustPlayDesc->_animIdList[m_suSeqMustPlayDesc->_curIdx];
			if ((m_suSeqMustPlayDesc->_pAnimPlayTrack->m_nSequence == seqType2ID(state) &&
				m_suSeqMustPlayDesc->_pAnimPlayTrack->getPlayCount() >= m_suSeqMustPlayDesc->_animPlayTimesList[m_suSeqMustPlayDesc->_curIdx]))
			{
				if (SEQ_BASKETBALL_GRAB_BEFORE == state)
				{
					m_Entity->PlayMotion("item_lanqiuxuli", false);
				}

				m_suSeqMustPlayDesc->_pAnimPlayTrack->m_State = AnimPlayTrack::STATE_NULL;
				m_suSeqMustPlayDesc->_curIdx++;
				if (m_suSeqMustPlayDesc->_curIdx >= (int)m_suSeqMustPlayDesc->_animIdList.size())
				{
					resetSeqAnimDesc();
					return;
				}
				int newState = m_suSeqMustPlayDesc->_animIdList[m_suSeqMustPlayDesc->_curIdx];

				m_CurAnim[0] = newState;
				m_CurAnim[1] = -1;
				m_CurSeqID[0] = seqType2ID(newState);
				m_CurSeqID[1] = -1;
				m_suSeqMustPlayDesc->_pAnimPlayTrack = m_Entity->PlayerAnimReturnTrack(seqType2ID(newState));
			}
		}
		else
		{
			resetSeqAnimDesc();
		}
	}
}

void ActorBody::syncUpperLowerBodyAnimation(int currentLayer, int seqId)
{
	// if (!m_Entity) return;
	//
	// Rainbow::Model* model = m_Entity->GetMainModel();
	// if (!model || !model->IsKindOf<Rainbow::ModelNew>()) return;
	//
	// Rainbow::ModelNew* modelNew = static_cast<Rainbow::ModelNew*>(model);
	// if (!modelNew) return;
	//
	// ModelAnimationPlayer* animPlayer = modelNew->GetAnimationPlayer();
	// if (!animPlayer) return;
	//
	// // 获取当前层的动画状态信息
	// Rainbow::Animator* animator = animPlayer->getanim;
	//
	// if (!animator) return;
	//
	// // 获取当前层的normalizedTime
	// float currentNormalizedTime = 0.0f;
	// Rainbow::AnimatorStateInfo currentStateInfo;
	// if (animator->GetAnimatorStateInfo(currentLayer, Rainbow::StateInfoIndex::kCurrentState, currentStateInfo))
	// {
	// 	currentNormalizedTime = currentStateInfo.normalizedTime;
	// }
	//
	// // 同步另一层的动画时间
	// int otherLayer = (currentLayer + 1) % 2;
	// if (m_CurSeqID[otherLayer] >= 0)
	// {
	// 	// 获取另一层的动画名称
	// 	core::string& otherAnimName = Rainbow::GetAnimStateName(m_CurSeqID[otherLayer]);
	// 	
	// 	// 使用CrossFade同步时间，设置normalizedTimeOffset参数
	// 	animator->CrossFade(otherAnimName, 0.1f, otherLayer, currentNormalizedTime, 0.1f, false);
	// 	
	// 	LOG_INFO("ActorBody::syncUpperLowerBodyAnimation - Layer %d synced to layer %d with normalizedTime: %f", 
	// 		currentLayer, otherLayer, currentNormalizedTime);
	// }
}

void ActorBody::SyncAnimStateTime(int seqid, int layer, int targetlayer)
{
	if (!m_Entity) return;

	Rainbow::Model* model = m_Entity->GetMainModel();
	if (!model || !model->IsKindOf<Rainbow::ModelNew>()) return;

	Rainbow::ModelNew* modelNew = static_cast<Rainbow::ModelNew*>(model);
	if (!modelNew) return;

	ModelAnimationPlayer* animPlayer = modelNew->GetAnimationPlayer();
	if (!animPlayer) return;

	// 调用ModelAnimationPlayer的SyncAnimStateTime接口
	animPlayer->SyncAnimStateTime(seqid, layer, targetlayer);
}
