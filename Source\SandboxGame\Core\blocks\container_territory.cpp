#include "container_territory.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "ActorVehicleAssemble.h"
#include "ClientActorHelper.h"
#include "ChunkSave_generated.h"
#include "IPlayerControl.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "GameCamera.h"
#include "ActorVillager.h"
#include "chunk.h"
#include "PlayerControl.h"
#include "Components/BoxCollider.h"
#include "Components/MeshRenderer.h"
#include "Core/GameScene.h"
#include "BlockScene.h"
#include "Misc/GOCreation.h"
#include "world_types.h"
#include "TerritoryManager.h"
#include <container_socdoor.h>
//#include "MovableObject.h"
/**********************************************************************************************
��    ����TerritoryContainer
��    �ܣ����
********************************************************************************************* */

// 在文件开头定义静态常量 - 使用完整尺寸
//BlockSize 、AABB的大小 填奇数
const Rainbow::Vector3f TerritoryContainer::DEFAULT_TERRITORY_BOX_SIZE(31, 31, 31);
// UI通知延时常量（tick）20tick = 1秒，延时0.5秒 = 10tick
const int TerritoryContainer::UI_NOTIFICATION_DELAY_TICKS = 10;

TerritoryContainer::TerritoryContainer() : WorldStorageBox()
{
	m_render = true;
	m_haveAward = false;
	m_OwnerUin = 1;
	m_showBoundingBox = true;
	m_lastErosionBlockCount = 0;
	m_uiNotificationDelayTicks = 0;
}
TerritoryContainer::TerritoryContainer(const WCoord& blockpos) :WorldStorageBox(blockpos)
{
	m_render = true;
	m_haveAward = false;
	m_OwnerUin = 1;
	m_showBoundingBox = true;
	m_lastErosionBlockCount = 0;
	m_uiNotificationDelayTicks = 0;
}

TerritoryContainer::~TerritoryContainer()
{
}

bool TerritoryContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerTerritory*>(srcdata);
	//loadContainerCommon(src->basedata());
	WorldStorageBox::load(src->basedata());

	m_flagPoint.resize(src->flagPoint()->size());
	for (size_t i = 0; i < m_flagPoint.size(); i++)
	{
		m_flagPoint[i] = Coord3ToWCoord(src->flagPoint()->Get(i));
	}

	m_villagersID.resize(src->villagers()->size());
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		if (src->villagers()->Get(i) != NULL) 
		{
			m_villagersID[i] = StrToWorldId(src->villagers()->Get(i)->c_str());
		}
		
	}
	m_haveAward = src->haveAward();
	m_render = false;
	m_showBoundingBox = true;
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> TerritoryContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	//auto basedata = saveContainerCommon(builder);
	auto basedata = WorldStorageBox::saveContainerStorage(builder);

	std::vector<FBSave::Coord3>flagPoint;
	for (size_t i = 0; i < m_flagPoint.size(); i++)
	{
		flagPoint.push_back(WCoordToCoord3(m_flagPoint[i]));
	}
	std::vector<string> villageID;
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		villageID.push_back(WorldIdToStr(m_villagersID[i]));
	}
	//flatbuffers::Offset<flatbuffers::Vector<const FBSave::Coord3 *>> flagPoint = 0,
	auto vpoint = builder.CreateVectorOfStructs(flagPoint);
	auto vvillage = builder.CreateVectorOfStrings(villageID);
	auto actor = FBSave::CreateContainerTerritory(builder, basedata, vpoint, vvillage,m_haveAward);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerTerritory, actor.Union());
}

void TerritoryContainer::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateDisplay();

	GetWorldManagerPtr()->getWorldInfoManager()->setVillagerWorship(pworld, m_OwnerUin, m_haveAward);
	UpdateEffect(pworld);
	
	// 只更新边界盒位置，使用已有尺寸的半值
	m_boundingBox.SetFromCenterAndHalfExtents(
		BlockCenterCoord(m_BlockPos).toVector3(),
		Rainbow::Vector3f(DEFAULT_TERRITORY_BOX_SIZE.x * BLOCK_SIZE * 0.5f,
						  DEFAULT_TERRITORY_BOX_SIZE.y * BLOCK_SIZE * 0.5f,
						  DEFAULT_TERRITORY_BOX_SIZE.z * BLOCK_SIZE * 0.5f)
	);

	// 修改：注册到领地管理器，传入 m_BlockPos 和 this
	TerritoryManager::GetInstance()->RegisterTerritory(m_BlockPos, this);
	TerritoryManager::GetInstance()->RegisterWithWorld(pworld);//函数内部已经处理成只调用一次
	
	// 新增：通知附近方块领地已创建
	NotifyNearbyBlocksOnCreate();
}
void TerritoryContainer::leaveWorld()
{
	// 在离开世界前通知附近方块
	NotifyNearbyBlocksOnDestroy();
	
	// 从领地管理器中注销
	TerritoryManager::GetInstance()->UnregisterTerritory(m_BlockPos);
	
	if (m_World)
	{
		if (!m_World->isRemoteMode())
		{
			//ֹͣЧ
			m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos());
			m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos());
		}
		else
		{
			//�ͻ���ͨͼ����Ҫ����һ��
			if (g_pPlayerCtrl && m_OwnerUin == g_pPlayerCtrl->getUin())
			{
				m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos(), false);
				m_World->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos(), false);
			}
		}
	}
	
	WorldContainer::leaveWorld();
}

void TerritoryContainer::OnTriggerEnter(const Rainbow::EventContent* touch)
{
	if (m_World->isRemoteMode()) return;
	if (!touch || !(touch->userData)) return;
	auto dst = static_cast<Rainbow::Collider*>(touch->userData);
	auto dstNode = static_cast<MNSandbox::SandboxNode*>(dst->GetUserData());
	if (!dstNode)
	{
		return;
	}
	if (!dstNode->IsKindOf<ClientPlayer>()) 
		return;
	int buffId = 205;
	auto player = dstNode->ToCast<ClientPlayer>();
	
	if (!player->getLivingAttrib()->hasBuff(buffId))
		player->getLivingAttrib()->addBuff(buffId, 1);
	//auto src = this->m_pGameObject->GetComponent<Collider>();
	//MINIW::GetPhysXManagerPtr()->OnEnterTouchRecard(src, dst, Rainbow::Vector3f::zero, Rainbow::Vector3f::zero, MINIW::PhysXManager::TOUCH_TRIGGER);
}


void TerritoryContainer::OnTriggerExit(const  Rainbow::EventContent* touch)
{
	if (m_World->isRemoteMode()) return;
	if (!touch || !(touch->userData)) return;
	auto dst = static_cast<Rainbow::Collider*>(touch->userData);
	auto dstNode = static_cast<MNSandbox::SandboxNode*>(dst->GetUserData());
	if (!dstNode)
	{
		return;
	}
	if (!dstNode->IsKindOf<ClientPlayer>())
		return;
	int buffId = 205;
	auto player = dstNode->ToCast<ClientPlayer>();

	if (player->getLivingAttrib()->hasBuff(buffId))
		player->getLivingAttrib()->removeBuff(buffId, 1);
	//auto src = this->m_pGameObject->GetComponent<Collider>();
	//MINIW::GetPhysXManagerPtr()->OnExitTouchRecard(src, dst, MINIW::PhysXManager::TOUCH_TRIGGER);
}


bool TerritoryContainer::IsModfiy(int userID)
{
	if (m_OwnerUin == userID)
	{
		return true;
	}
	return false;

}

void TerritoryContainer::updateTick()
{
	//auto test = Get24HourMaintenanceInfo();
	if (m_World->isRemoteMode()) return;

	// 只有当管理的腐蚀方块数量发生变化时才通知UI更新
	size_t currentCount = GetManagedErosionBlockCount();
	if (m_AttachToUI && currentCount != m_lastErosionBlockCount)
	{
		// 增加延时计数器
		m_uiNotificationDelayTicks++;
		
		// 达到延时tick数才真正通知UI更新
		if (m_uiNotificationDelayTicks >= UI_NOTIFICATION_DELAY_TICKS)
		{
			m_lastErosionBlockCount = currentCount;
			notifyChange2Openers(-1, true);
			m_uiNotificationDelayTicks = 0; // 重置延时计数器
		}
	}
	else if (m_AttachToUI && currentCount == m_lastErosionBlockCount)
	{
		// 如果数量没有变化，重置延时计数器
		m_uiNotificationDelayTicks = 0;
	}
}

void TerritoryContainer::updateDisplay(float dtime)
{

	auto physScene = m_World->m_PhysScene;
	if (nullptr != physScene)
	{
		Rainbow::RaycastHit hitInfo;
		//int mask = 0xFFFFFFFF;
		unsigned int mask = 0xffffffff;
		Rainbow::Vector3f dir_ = Vector3f::yAxis; //dir.GetNormalizedSafe();
		float dist = 1800.0f;
		auto pos = g_pPlayerCtrl->getPosition().toVector3()+ Vector3f(0,-400,0);
		//bool b = physScene->RayCast(pos, dir_, dist, hitInfo, mask, false);
		Rainbow::RaycastHits info =  physScene->RayCastAll(pos, dir_, dist, mask, false);
		
		for (auto& item :info)
		{
			auto clollider = item.collider;
			int dd = 0;
		}
		
	}

	
	// 如果启用了边界盒显示，绘制它
	if (m_showBoundingBox) {
		m_boundingBox.DrawWireframe(m_World, Rainbow::ColourValue(0.0f, 1.0f, 0.5f, 1.0f)); // 绿色
	}
	
}

std::vector<WCoord> TerritoryContainer::GetFlagPoint()
{
	return m_flagPoint;
}

bool TerritoryContainer::HasFlagPoint(const WCoord& point)
{
	for (size_t i = 0; i < m_flagPoint.size(); i++) 
	{
		if (point == m_flagPoint[i]) 
		{
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::AddFlagPoint(const std::vector<WCoord>& points)
{
	for (size_t i = 0; i < points.size(); i++)
	{
		if (!HasFlagPoint(points[i])) 
		{
			m_flagPoint.push_back(points[i]);
		}
	}
	return true;
}

bool TerritoryContainer::AddFlagPoint(const WCoord& point)
{
	if (!HasFlagPoint(point))
	{
		m_flagPoint.push_back(point);
	}
	return true;
}

bool TerritoryContainer::RemoveFlagPoint(const WCoord& point)
{
	for(size_t i = 0; i < m_flagPoint.size(); i++)
	{
		if (point == m_flagPoint[i]) 
		{
			m_flagPoint.erase(m_flagPoint.begin()+i);
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::ClearFlagPoint()
{
	m_flagPoint.clear();
	return true;
}

std::vector<WORLD_ID> TerritoryContainer::GetVillagersID()
{
	std::vector<WORLD_ID> ids;
	for (size_t i = 0; i < m_villagersID.size(); i++) 
	{
		ids.push_back(m_villagersID[i]);
	}
	return ids;
}

bool TerritoryContainer::HasVillagersID(const WORLD_ID& villagerid)
{
	for (size_t i = 0; i < m_villagersID.size(); i++) 
	{
		if (villagerid == m_villagersID[i]) 
		{
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::AddVillagerID(const std::vector<WORLD_ID>& villagerid)
{
	for(size_t i = 0;i < villagerid.size(); i++)
	{
		m_villagersID.push_back(villagerid[i]);
	}
	return true;
}

bool TerritoryContainer::AddVillagerID(const WORLD_ID& villagerid)
{
	m_villagersID.push_back(villagerid);
	return true;
}

bool TerritoryContainer::RemoveVillagerID(const WORLD_ID& villagerid)
{
	for (size_t i = 0; i < m_villagersID.size(); i++)
	{
		if (villagerid == m_villagersID[i])
		{
			m_villagersID.erase(m_villagersID.begin() + i);
			return true;
		}
	}
	return false;
}

bool TerritoryContainer::ClearVillagersID()
{
	m_villagersID.clear();
	return true;
}

float TerritoryContainer::getAttrib(int i)
{
	if (i == 0) //当前的维护的数量
		return GetManagedErosionBlockCount();
}

void TerritoryContainer::onAttachUI()
{
	m_AttachToUI = true;
	// 重置延时计数器，等待UI完全打开
	m_uiNotificationDelayTicks = 0;

	//for (int i = 0; i < GRID_FULL; i++)
	//{
	//	// GameEventQue::GetInstance().postBackpackChange(FURNACE_START_INDEX + i);
	//	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
	//		SetData_Number("grid_index", FURNACE_START_INDEX + i);
	//	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
	//		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
	//	}
	//}
	// GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void TerritoryContainer::onDetachUI()
{
	m_AttachToUI = false;
	m_lastErosionBlockCount = -1;
}

void TerritoryContainer::UpdateEffect(World* pworld)
{
	
	
	int blockdata = pworld->getBlockData(m_BlockPos);
	if ((8 & blockdata) || (4 & blockdata))
	{
		return;
	}
	int dir = (blockdata & 3);
	float yaw = 0;
	if (dir == 0)
	{
		yaw = -90;
	}
	else if (dir == 1)
	{
		yaw = 90;
	}
	else if (dir == 2)
	{
		yaw = 180;
	}
	else if (dir == 3)
	{
		yaw = 0;
	}
	int blockId = 0;

		BlockMaterial*metl = getBlockMtl();
		if (metl)
		{
			blockId = metl->getBlockResID();
		}
		

	//Ұ��ͼ�� ��Ҫ�����˿��Կ�
	if (blockId == 150001 && m_OwnerUin == 0)
	{
		int maxage = Rainbow::MAX_INT;
		bool issendclient = false;
		if (!pworld->isRemoteMode())
		{
			//������Ҫ���͵��ͻ� ���ͻ�����Ҫ���͵�����
			issendclient = true;
		}
		if (GetHaveAward())
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos(), issendclient);
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_02.prefab", getEffectPos(), maxage, yaw, 0, issendclient, 50);
		}
		else
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos(), issendclient);
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_01.prefab", getEffectPos(), maxage, yaw, 0, issendclient, 50);
		}
		return;
	}
	else if (GetWorldManagerPtr() && GetWorldManagerPtr()->getWorldInfoManager()->getVillagePoint(m_OwnerUin) != m_BlockPos)
	{
		return;
	}
	//��ͨͼ�� ֻ��Ҫ�Լ���
	if (g_pPlayerCtrl && m_OwnerUin == g_pPlayerCtrl->getUin())
	{
		int maxage = Rainbow::MAX_INT;
		if (GetHaveAward())
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_01.prefab", getEffectPos());
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_02.prefab", getEffectPos(), maxage, yaw, 0, false, 50);
		}
		else
		{
			pworld->getEffectMgr()->stopParticleEffect("prefab/particles/wl_jitan_02.prefab", getEffectPos());
			pworld->getEffectMgr()->playParticleEffectCommonAsync(2, "prefab/particles/wl_jitan_01.prefab", getEffectPos(), maxage, yaw, 0, false, 50);
		}
	}
	return;
}

WCoord TerritoryContainer::getEffectPos()
{
	return BlockBottomCenter(m_BlockPos);
}

std::string TerritoryContainer::WorldIdToStr(WORLD_ID id)
{
	return std::to_string(id);
}

WORLD_ID TerritoryContainer::StrToWorldId(std::string str)
{
	return std::stoll(str);
}

void TerritoryContainer::DrawLine(World* pworld, const WCoord& BlockPos,const WCoord& distBlockPos)
{
	Rainbow::Vector3f p1(0, 0, 0);
	Rainbow::Vector3f p2 = (BlockCenterCoord(distBlockPos) - BlockCenterCoord(BlockPos)).toVector3();
	Rainbow::Vector3f p0, p3;
	p0 = p1 * 2.0f - p2;
	Rainbow::Vector3f dir0;
	Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
	p3 = p2 * 2.0f - p1;
	MINIW::CatmullRomCurve cc;
	cc.setControlPoints(p0, p1, p2, p3);
	cc.setNormals(camlookdir, camlookdir);
	pworld->getRender()->getCurveScreenRender()->addCurve(2, cc, BlockCenterCoord(BlockPos), BlockCenterCoord(distBlockPos), 6.0f, 3.0f);
}

void TerritoryContainer::SetHaveAward(bool award)
{
	m_haveAward = award;

	if (m_World == NULL) return;

	//����chunk����
	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
	}
}

void TerritoryContainer::InitTrigger()
{
	if (!m_World)
	{
		return;
	}
}

// 新增领地边界盒相关方法实现
bool TerritoryContainer::IsPointInTerritoryBounds(const Rainbow::Vector3f& point) const
{
	return m_boundingBox.IsPointInside(point);
}

void TerritoryContainer::SetTerritoryBounds(const Rainbow::Vector3f& center, const Rainbow::Vector3f& halfExtents)
{
	m_boundingBox.SetFromCenterAndHalfExtents(center, halfExtents);
	
	// 更新碰撞器大小以匹配
	if (m_GameObject)
	{
		auto tf = m_GameObject->GetComponent<Rainbow::Transform>();
		tf->SetWorldPosition(center);
		tf->SetLocalScale(Rainbow::Vector3f(halfExtents.x * 2, halfExtents.y * 2, halfExtents.z * 2));
	}
}

TerritoryBoundingBox TerritoryContainer::GetTerritoryBounds() const
{
	return m_boundingBox;
}

void TerritoryContainer::ShowBoundingBox(bool show)
{
	m_showBoundingBox = show;
}

void TerritoryContainer::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (!pworld || !pworld->getContainerMgr()) return;
	auto container = pworld->getContainerMgr()->getContainer(blockpos);
	container->m_OwnerUin = player->getUin();
}

bool TerritoryContainer::DeductMaterial(const std::string& materialStr)
{
	if (!m_World || materialStr.empty()) {
		return false;
	}

	// 解析字符串格式 "itemid|count"
	size_t pipePos = materialStr.find('|');
	if (pipePos == std::string::npos) {
		return false; // 格式错误
	}

	try {
		int itemid = std::stoi(materialStr.substr(0, pipePos));
		int count = std::stoi(materialStr.substr(pipePos + 1));
		
		if (itemid <= 0 || count <= 0) {
			return false;
		}

		// 使用安全的扣除方法
		return SafeRemoveItemByCount(itemid, count);
	}
	catch (const std::exception&) {
		// 字符串转换失败
		return false;
	}
}

// 安全的材料扣除方法
bool TerritoryContainer::SafeRemoveItemByCount(int itemid, int count)
{
	// 第一步：检查总数量是否足够
	int totalAvailable = 0;
	int maxgrids = getGridCount();
	
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			totalAvailable += grid->getNum();
		}
	}
	
	if (totalAvailable < count)
	{
		return false; // 数量不足，不执行任何扣除
	}
	
	// 第二步：执行扣除（此时保证能完全扣除）
	int remainingToRemove = count;
	for (int i = 0; i < maxgrids && remainingToRemove > 0; i++)
	{
		BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			int removeFromThisGrid = std::min(remainingToRemove, grid->getNum());
			grid->addNum(-removeFromThisGrid);
			remainingToRemove -= removeFromThisGrid;
			
			// 通知格子变化
			afterChangeGrid(i + STORAGE_START_INDEX);
		}
	}
	
	return true; // 扣除成功
}

void TerritoryContainer::NotifyNearbyBlocksOnDestroy()
{
	if (!m_World) return;
	
	// 获取领地边界框
	TerritoryBoundingBox bounds = GetTerritoryBounds();
	Rainbow::Vector3f min = bounds.GetMin();
	Rainbow::Vector3f max = bounds.GetMax();
	
	// 转换为方块坐标并遍历
	int minX = static_cast<int>(std::floor(min.x / 100.0f));
	int minY = static_cast<int>(std::floor(min.y / 100.0f));
	int minZ = static_cast<int>(std::floor(min.z / 100.0f));
	int maxX = static_cast<int>(std::ceil(max.x / 100.0f));
	int maxY = static_cast<int>(std::ceil(max.y / 100.0f));
	int maxZ = static_cast<int>(std::ceil(max.z / 100.0f));
	
	for (int x = minX; x <= maxX; x++)
	{
		for (int y = minY; y <= maxY; y++)
		{
			for (int z = minZ; z <= maxZ; z++)
			{
				WCoord blockPos(x, y, z);
				
				// 跳过领地方块本身
				if (blockPos == m_BlockPos) continue;
				
				// 检查该位置是否真的在领地范围内
				Rainbow::Vector3f worldPos = blockPos.toVector3() * 100.0f;
				if (!bounds.IsPointInside(worldPos)) continue;
				
				// 获取该位置的容器并通知
				WorldContainer* container = m_World->getContainerMgr()->getContainer(blockPos);
				if (container)
				{
					ErosionContainer* erosionContainer = dynamic_cast<ErosionContainer*>(container);
					if (erosionContainer)
					{
						// 只有当这个领地确实是该方块当前绑定的领地时才通知销毁
						if (erosionContainer->m_TerritoryContainer == this)
						{
							erosionContainer->OnTerritoryDestroyed();
						}
					}
				}
			}
		}
	}
}

void TerritoryContainer::NotifyNearbyBlocksOnCreate()
{
	if (!m_World) return;
	
	// 获取领地边界框
	TerritoryBoundingBox bounds = GetTerritoryBounds();
	Rainbow::Vector3f min = bounds.GetMin();
	Rainbow::Vector3f max = bounds.GetMax();
	
	// 转换为方块坐标并遍历
	int minX = static_cast<int>(std::floor(min.x / 100.0f));
	int minY = static_cast<int>(std::floor(min.y / 100.0f));
	int minZ = static_cast<int>(std::floor(min.z / 100.0f));
	int maxX = static_cast<int>(std::ceil(max.x / 100.0f));
	int maxY = static_cast<int>(std::ceil(max.y / 100.0f));
	int maxZ = static_cast<int>(std::ceil(max.z / 100.0f));
	
	for (int x = minX; x <= maxX; x++)
	{
		for (int y = minY; y <= maxY; y++)
		{
			for (int z = minZ; z <= maxZ; z++)
			{
				WCoord blockPos(x, y, z);
				
				// 跳过领地方块本身
				if (blockPos == m_BlockPos) continue;
				
				// 检查该位置是否真的在领地范围内
				Rainbow::Vector3f worldPos = blockPos.toVector3() * 100.0f;
				if (!bounds.IsPointInside(worldPos)) continue;
				
				// 获取该位置的容器并通知
				WorldContainer* container = m_World->getContainerMgr()->getContainer(blockPos);
				if (container)
				{
					ErosionContainer* erosionContainer = dynamic_cast<ErosionContainer*>(container);
					if (erosionContainer)
					{
						erosionContainer->OnTerritoryCreated(this);
					}
				}
			}
		}
	}
}

int TerritoryContainer::GetAvailableMaterialCount(const std::string& materialStr)
{
	if (!m_World || materialStr.empty()) {
		return 0;
	}

	// 解析字符串格式 "itemid|count"
	size_t pipePos = materialStr.find('|');
	if (pipePos == std::string::npos) {
		return 0; // 格式错误
	}

	try {
		int itemid = std::stoi(materialStr.substr(0, pipePos));
		int requiredCount = std::stoi(materialStr.substr(pipePos + 1));
		
		if (itemid <= 0 || requiredCount <= 0) {
			return 0;
		}

		// 计算总可用数量
		int totalAvailable = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++)
		{
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				totalAvailable += grid->getNum();
			}
		}
		
		// 返回可以执行多少次完整的维护操作
		return totalAvailable / requiredCount;
	}
	catch (const std::exception&) {
		// 字符串转换失败
		return 0;
	}
}

bool TerritoryContainer::DeductMaterialBatch(const std::string& materialStr, int cycles)
{
	if (!m_World || materialStr.empty() || cycles <= 0) {
		return false;
	}

	// 解析字符串格式 "itemid|count"
	size_t pipePos = materialStr.find('|');
	if (pipePos == std::string::npos) {
		return false; // 格式错误
	}

	try {
		int itemid = std::stoi(materialStr.substr(0, pipePos));
		int countPerCycle = std::stoi(materialStr.substr(pipePos + 1));
		
		if (itemid <= 0 || countPerCycle <= 0) {
			return false;
		}

		int totalRequired = countPerCycle * cycles;
		
		// 使用现有的安全扣除方法
		return SafeRemoveItemByCount(itemid, totalRequired);
	}
	catch (const std::exception&) {
		// 字符串转换失败
		return false;
	}
}

// 高性能版本实现已内联到头文件中

void TerritoryContainer::AddManagedErosionBlock(ErosionContainer* erosionBlock)
{
	if (!erosionBlock) return;
	
	// 检查是否已经在列表中
	auto it = std::find(m_managedErosionBlocks.begin(), m_managedErosionBlocks.end(), erosionBlock);
	if (it == m_managedErosionBlocks.end())
	{
		m_managedErosionBlocks.push_back(erosionBlock);
		
		// 立即通知UI更新
		if (m_World && !m_World->isRemoteMode())
		{
			m_lastErosionBlockCount = m_managedErosionBlocks.size();
			notifyChange2Openers(-1, true);
		}
	}
}

void TerritoryContainer::RemoveManagedErosionBlock(ErosionContainer* erosionBlock)
{
	if (!erosionBlock) return;
	
	// 从列表中移除
	auto it = std::find(m_managedErosionBlocks.begin(), m_managedErosionBlocks.end(), erosionBlock);
	if (it != m_managedErosionBlocks.end())
	{
		m_managedErosionBlocks.erase(it);
		
		// 立即通知UI更新
		if (m_World && !m_World->isRemoteMode())
		{
			m_lastErosionBlockCount = m_managedErosionBlocks.size();
			notifyChange2Openers(-1, true);
		}
	}
}

std::map<int, int> TerritoryContainer::Calculate24HourMaintenanceNeeds() const
{
	std::map<int, int> totalNeeds;
	
	if (!m_World) return totalNeeds;
	
	// 24小时的维护次数计算
	// 每分钟检查一次维护，24小时 = 24 * 60 = 1440分钟
	//const int HOURS_24_IN_MINUTES = 24 * 60;
	const int MAINTENANCE_CHECKS_PER_24H = 24;// HOURS_24_IN_MINUTES;
	
	// 遍历所有管理的腐蚀方块
	for (ErosionContainer* erosionBlock : m_managedErosionBlocks)
	{
		if (!erosionBlock) continue;
		
		// 获取方块的维护材料需求
		ItemDef* itemdata = GetDefManagerProxy()->getItemDef(erosionBlock->getBlockID());
		if (!itemdata || itemdata->MaintenanceProps.empty()) continue;
		
		// 解析维护材料字符串 "itemid|count"
		string maintenanceProps = itemdata->MaintenanceProps.c_str();
		size_t pipePos = maintenanceProps.find('|');
		if (pipePos == std::string::npos) continue;
		
		try {
			int itemid = std::stoi(maintenanceProps.substr(0, pipePos));
			int countPerMaintenance = std::stoi(maintenanceProps.substr(pipePos + 1));
			
			if (itemid <= 0 || countPerMaintenance <= 0) continue;
			
			// 计算24小时内该方块需要的总材料
			int totalForThisBlock = countPerMaintenance * MAINTENANCE_CHECKS_PER_24H;
			
			// 累加到总需求中
			totalNeeds[itemid] += totalForThisBlock;
		}
		catch (const std::exception&) {
			// 解析失败，跳过这个方块
			continue;
		}
	}
	
	return totalNeeds;
}

bool TerritoryContainer::CanMaintain24Hours() 
{
	if (!m_World) return false;
	
	// 获取24小时维护需求
	std::map<int, int> needs = Calculate24HourMaintenanceNeeds();
	
	// 检查每种材料是否足够
	for (const auto& need : needs)
	{
		int itemid = need.first;
		int requiredCount = need.second;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++)
		{
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				availableCount += grid->getNum();
			}
		}
		
		// 如果任何一种材料不足，返回false
		if (availableCount < requiredCount)
		{
			return false;
		}
	}
	
	return true;
}

std::vector<TerritoryContainer::MaintenanceInfo> TerritoryContainer::Get24HourMaintenanceInfo() 
{
	std::vector<MaintenanceInfo> infoList;
	
	if (!m_World) return infoList;
	
	// 获取24小时维护需求
	std::map<int, int> needs = Calculate24HourMaintenanceNeeds();
	
	// 为每种材料创建详细信息
	for (const auto& need : needs)
	{
		int itemid = need.first;
		int requiredCount = need.second;
		
		// 计算当前拥有的该材料数量
		int availableCount = 0;
		int maxgrids = getGridCount();
		
		for (int i = 0; i < maxgrids; i++)
		{
			BackPackGrid* grid = index2Grid(i + STORAGE_START_INDEX);
			if (grid && grid->getItemID() == itemid)
			{
				availableCount += grid->getNum();
			}
		}
		
		// 创建维护信息
		MaintenanceInfo info;
		info.itemid = itemid;
		info.required = requiredCount;
		info.available = availableCount;
		info.sufficient = (availableCount >= requiredCount);
		
		infoList.push_back(info);
	}
	
	return infoList;
}
