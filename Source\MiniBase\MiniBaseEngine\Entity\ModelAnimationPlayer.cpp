#include "ModelAnimationPlayer.h"
#include "Components/Animation.h"
#include "Components/SkeletonAnimation.h"
#include "Common/LegacyClassTypeIds.h"
#include "Animation/Animator/AnimatorInfo.h"
#include "Animation/Animator/AnimatorEvent.h"
#include "Animation/Animator/AnimatorLayerPoseContext.h"
#include <Entity/LegacySequenceMap.h>

namespace Rainbow
{
	std::map<int, FixedString>  gs_AnimStateIdMap;

	std::map<int, core::string>  gs_AnimStateIdStrMap;

	core::string& GetAnimStateName(int id)
	{
		auto iter = gs_AnimStateIdStrMap.find(id);
		if (iter != gs_AnimStateIdStrMap.end())
		{
			return (iter->second);
		}

		gs_AnimStateIdStrMap[id] = Format("%d", id);
		return gs_AnimStateIdStrMap[id];
	}

	FixedString& GetAnimStateNameFixed(int id)
	{
		auto iter = gs_AnimStateIdMap.find(id);
		if (iter != gs_AnimStateIdMap.end())
		{
			return (iter->second);
		}

		gs_AnimStateIdMap[id] = Format("%d", id).c_str();
		return gs_AnimStateIdMap[id];
	}

	int GetAnimStateId(const char* name)
	{
		return atoi(name);
	}

	static bool IsAnimPlayHalfBody(int seqId)
	{
		bool halfBody = false;
		SequenceMap& sm = GetSequenceMap();
		SequenceMap::SeqDesc* smsd = sm.findSequenceDesc(seqId);

		if (smsd)
		{
			halfBody = smsd->halfbody > 0;
		}

		return halfBody;
	}

	static bool HasAnimSeqDesc(int seqId)
	{
		if (seqId >= MinDynamicLoadSeqID)
		{
			SequenceMap& sm = GetSequenceMap();
			SequenceMap::SeqDesc* smsd = sm.findSequenceDesc(seqId);

			if (smsd)
			{
				return true;
			}
		}

		return false;
	}

	static int GetLogicLayer(int layer, int totalLayerCount)
	{
		if (totalLayerCount > RolePlayerLayerCount)
		{
			return (int)((layer + 1) / 2);
		}

		return layer;
	}

	static std::map<int, AnimPlayMode>  gs_AnimPlayModeMap;

	static AnimPlayMode GetAnimPlayMode(int seqId, int eInputLoopMode)
	{
		AnimPlayMode eLoopMode = ANIM_MODE_ONCE;

		if (seqId >= StartMotionSeqID && seqId < StartMotionSeqID + TotalMotionSeqCount)
		{
			eLoopMode = ANIM_MODE_LOOP;
			return eLoopMode;
		}

		if (eInputLoopMode >= 0)
		{
			eLoopMode = (AnimPlayMode)eInputLoopMode;
			return eLoopMode;
		}

		// 加一层缓存，避免查找开销
		auto iter = gs_AnimPlayModeMap.find(seqId);
		if (iter != gs_AnimPlayModeMap.end())
		{
			return (iter->second);
		}

		SequenceMap& sm = GetSequenceMap();
		SequenceMap::SeqDesc* smsd = sm.findSequenceDesc(seqId);

		if (eLoopMode == ANIE_MODE_EDIT)
			eLoopMode = ANIM_MODE_ONCE_STOP;

		if (smsd)
		{
			eLoopMode = (AnimPlayMode)smsd->loopmode;
			gs_AnimPlayModeMap[seqId] = eLoopMode;
		}

		return eLoopMode;
	}
	//----------------------------------------------------

	IMPLEMENT_CLASS(ModelAnimationPlayer)

		ModelAnimationPlayer::ModelAnimationPlayer(MemLabelId label)
		:Component(label)
		, m_CurSeqId(0)
		, m_isPlayer(false),
		m_CurMotionIndex(0)
	{
		m_Animator = nullptr;
		m_LayerCount = 0;
		m_Skeletion = nullptr;
		m_SkeletionAnim = nullptr;
		m_PlayerAnimationState = nullptr;
		m_SkinAnimContainer = nullptr;

		m_RotateBoneID = Skeleton::s_InvalidParentIndex;
		m_BoneRotate = Quaternionf::identity;
		m_BoneScale = 1.0f;

		m_vCustomAnimMap = nullptr;
		m_TickType = kTickFlag;
	}

	ModelAnimationPlayer::~ModelAnimationPlayer()
	{
		m_Animator = nullptr;
		m_Skeletion = nullptr;
		m_SkeletionAnim = nullptr;
		// fixed memory leak m_PlayerAnimationState = nullptr;
		m_SkinAnimContainer = nullptr;
		m_vCustomAnimMap = nullptr;

		m_SkeletonAnimVec.clear();

		if (m_PlayerAnimationState != nullptr)
		{
			ENG_DELETE_LABEL(m_PlayerAnimationState, kMemAnimation);
		}

		m_AnimtionLayerMap.clear();
	}

	void ModelAnimationPlayer::OnAnimatorStateEvent(const Rainbow::EventContent* evt)
	{
		if (!evt || evt->userData == NULL)
			return;

		const Rainbow::AnimatorStateEvent& stateEvent = *static_cast<const Rainbow::AnimatorStateEvent*>(evt->userData);
		auto stateInfo = stateEvent.stateInfo;
		switch (stateEvent.stateMachineMessage)
		{
			{
		case StateMachineMessage::kOnStateEnter:
#if DEBUG_MODE 
			LogStringMsg("[Animator] StateStatusEvent: OnStateEnter \
										CurrentStateInfo : State Name:%s ,StatePath %s,StateFullName %s"
				, stateInfo.name.c_str(), stateInfo.path.c_str(), stateInfo.fullPath.c_str());
#endif
			break;
			}

		case StateMachineMessage::kOnStateExit:
		{
#if DEBUG_MODE 
			LogStringMsg("[Animator] StateStatusEvent: OnStateExit \
										CurrentStateInfo : State Name:%s ,StatePath %s,StateFullName %s"
				, stateInfo.name.c_str(), stateInfo.path.c_str(), stateInfo.fullPath.c_str());
#endif
			if (!m_isPlayer)
			{
				AnimPlayMsg apmsg;
				apmsg.msgtype = APMSG_SEQ_END;
				apmsg.seqid = GetAnimStateId(stateInfo.name.c_str());
				apmsg.playmode = GetAnimPlayMode(apmsg.seqid, -1);
				apmsg.name = GetAnimStateNameFixed(apmsg.seqid);
				this->PushPlayMsg(apmsg);
			}
			break;
		}

		case StateMachineMessage::kOnStateUpdate:

			break;

		default:
			break;
		}
	}


	void ModelAnimationPlayer::OnRemoveFromGameObject()
	{
		m_GameObject->RemoveEvent<ModelAnimationPlayer>(Evt_AnimatorStateEvent, &ModelAnimationPlayer::OnAnimatorStateEvent, this);
	}

	bool ModelAnimationPlayer::CanRotateHead()
	{
		return (GetIsPlayer() && m_Animator && m_Skeletion);   //  && m_LayerCount > RolePlayerLayerCount
	}

	void ModelAnimationPlayer::OnAddToScene(GameScene* scene)
	{
		Super::OnAddToScene(scene);

		if (GetIsPlayer() && m_Animator && m_Skeletion)   //  && m_LayerCount > RolePlayerLayerCount
		{
			m_Skeletion->RegisterAfterAnimationProcessEvent<ModelAnimationPlayer>(&ModelAnimationPlayer::OnAnimatorPostEvent, this);
			if (m_PlayerAnimationState == nullptr)
			{
				m_PlayerAnimationState = ENG_NEW_LABEL(PlayerAnimationLayerState, kMemAnimation)(this, m_Animator);
			}
		}
	}

	void ModelAnimationPlayer::OnRemoveFromScene(GameScene* scene)
	{
		Super::OnRemoveFromScene(scene);

		if (GetIsPlayer() && m_Animator && m_Skeletion)  //  && m_LayerCount > RolePlayerLayerCount
		{
			m_Skeletion->UnRegisterAfterAnimationProcessEvent<ModelAnimationPlayer>(&ModelAnimationPlayer::OnAnimatorPostEvent, this);
		}
	}

	void ModelAnimationPlayer::Tick(float dt)
	{
		IModelAnimationPlayer::ResetMsgBuffertCount();

		if (m_PlayerAnimationState != nullptr)
		{
			m_PlayerAnimationState->UpdatePlayerAnimatorState(dt);
		}
	}

	bool IsSyncLayer(Animator* animator, int layer)
	{
		if (animator && animator->m_Controller != nullptr && animator->m_Controller->GetLayerByIndex(layer) != nullptr)
		{
			return animator->m_Controller->GetLayerByIndex(layer)->GetSync();
		}

		return false;
	}

	int ModelAnimationPlayer::actionIDConversion(int actid)
	{
		if (m_vCustomAnimMap != nullptr)
		{
			for (auto keypair : (*m_vCustomAnimMap))
			{
				if (keypair.second == actid)
					return keypair.first;
			}
		}

		return actid;
	}

	int ModelAnimationPlayer::GetNextMotionSeqId()
	{
		m_CurMotionIndex = (m_CurMotionIndex + 1) % TotalMotionSeqCount;
		return StartMotionSeqID + m_CurMotionIndex;
	}

	int ModelAnimationPlayer::actionIDConversionReverse(int actid)
	{
		if (m_vCustomAnimMap != nullptr)
		{
			if (m_vCustomAnimMap->find(actid) != m_vCustomAnimMap->end())
			{
				return (*m_vCustomAnimMap)[actid];
			}
		}

		return actid;
	}

	bool ModelAnimationPlayer::HasAnim(const core::string& name)
	{
		if (m_Animator)
		{
			int layerCount = m_LayerCount;
			for (int i = 0; i < layerCount; i++)
			{
				if (m_Animator->HasState(i, name))
				{
					return true;
				}
			}
		}

		if (!m_SkeletionAnim) return false;
		AnimationState* state = m_SkeletionAnim->GetState(name.c_str());
		if (state) return true;
		return false;
	}

	int ModelAnimationPlayer::GetAnimLayer(const core::string& name, int seqId)
	{
		if (m_Animator)
		{
			if (seqId != -1)
			{
				int layerId = GetAnimLayer(seqId);
				if (layerId != -1)
				{
					return layerId;
				}
			}

			int layerCount = m_LayerCount;
			for (int i = layerCount - 1; i >= 0; i--)
			{
				if (!IsSyncLayer(m_Animator, i) && m_Animator->HasState(i, name))
				{
					if (seqId != -1)
					{
						m_AnimtionLayerMap[seqId] = i;
					}
					return i;
				}
			}
		}

		return -1;
	}

	int ModelAnimationPlayer::GetAnimLayer(int seqId)
	{
		auto iter = m_AnimtionLayerMap.find(seqId);
		if (iter != m_AnimtionLayerMap.end())
		{
			return (iter->second);
		}

		return -1;
	}

	bool ModelAnimationPlayer::PlayAnim(const core::string& name)
	{
		if (m_Animator)
		{
			int layer = GetAnimLayer(name);

			if (layer != -1)
			{
				if (m_isPlayer)
				{
					if (m_PlayerAnimationState == nullptr)
					{
						m_PlayerAnimationState = ENG_NEW_LABEL(PlayerAnimationLayerState, kMemAnimation)(this, m_Animator);
					}

					if (m_PlayerAnimationState != nullptr && m_PlayerAnimationState->PlayAnim(layer, name))
					{
						m_CurSeqId = 0;
						return true;
					}
				}
				else
				{
					m_Animator->Play(name, layer, 0);
					AnimatorStateInfo stateInfo = m_Animator->GetCurrentAnimatorStateInfo(layer);

					if (stateInfo.name == name)
					{
						return true;
					}
				}
			}

#if DEBUG_MODE 
			LogStringMsg("[Animator] PlayAnim() false , name : %s! ", name.c_str());
#endif
			return false;
		}

		if (m_SkeletionAnim)
		{
			AnimationState* state = m_SkeletionAnim->GetState(name.c_str());
			if (state)
			{
				m_CurSeqId = 0;
				m_SkeletionAnim->Play(name.c_str(), Animation::PlayMode::kStopSameLayer);
				return true;
			}
		}

		return false;
	}

	void ModelAnimationPlayer::StopAnim(const core::string& name)
	{
		bool sendEndEvt = true;
		int seqId = GetAnimStateId(name.c_str());
		AnimPlayMode playMode = ANIM_MODE_ONCE;

		if (m_Animator)
		{
			int layer = GetAnimLayer(name, seqId);

			if (layer != -1)
			{
				//LogStringMsg("ModelAnimationPlayer::StopAnim %s %d", name.c_str(), layer);
				if (m_isPlayer && m_LayerCount > PlayerLayerCount)
				{
					if (m_PlayerAnimationState != nullptr)
					{
						sendEndEvt = m_PlayerAnimationState->StopAnim(layer, name);
						playMode = m_PlayerAnimationState->GetAnimClipPlayMode(seqId);
					}
				}
				else
				{
					AnimatorStateInfo stateInfo = m_Animator->GetCurrentAnimatorStateInfo(layer);
					if (stateInfo.name == name)
					{
						if (m_Animator->HasState(layer, "Exit"))
						{
							m_Animator->Play("Exit", layer, 0);
						}
					}
				}
			}
		}
		else if (m_SkeletionAnim)
		{
			m_SkeletionAnim->Stop(name.c_str());
		}

		if (sendEndEvt)
		{
			{
				AnimPlayMsg apmsg;
				apmsg.msgtype = APMSG_SEQ_END;
				apmsg.seqid = actionIDConversionReverse(seqId);
				apmsg.playmode = m_isPlayer ? playMode : GetAnimPlayMode(apmsg.seqid, -1);
				apmsg.name = GetAnimStateNameFixed(apmsg.seqid);
				this->PushPlayMsg(apmsg);
			}
		}

		m_CurSeqId = 0;
	}

	bool ModelAnimationPlayer::SetAnimSpeed(int orginseq, float speed /*= 1.0f*/)
	{
		int seq = actionIDConversion(orginseq);

		core::string& seqName = GetAnimStateName(seq);
		if (m_Animator)
		{

			int layer = GetAnimLayer(seqName, seq);

			if (layer != -1)
			{
				if (m_isPlayer && m_LayerCount > PlayerLayerCount)
				{
					// todo
					return false;
				}
				else
				{
					m_Animator->SetSpeed(layer, seqName, speed);
					return true;
				}
			}

#if DEBUG_MODE 
			LogStringMsg("[Animator] PlayAnim() false , name : %s! ", seqName.c_str());
#endif
			return false;
		}

		if (m_SkeletionAnim)
		{
			AnimationState* state = m_SkeletionAnim->GetState(seqName.c_str());
			if (state)
			{
				state->SetSpeed(speed);
				return true;
			}
		}

		return false;
	}

	bool ModelAnimationPlayer::PlayAnim(int orginseq, float weight /*= 1.0f*/, float speed /*= 1.0f*/, int inputloopmode /*= -1*/, int layer /*= -1*/, float crossfade/* = -1.0f*/)
	{
		int seq = actionIDConversion(orginseq);

		core::string& seqName = GetAnimStateName(seq);
		if (m_Animator)
		{
			//LogStringMsg("ModelAnimationPlayer::playAnim %d %d", seq);

			int layer = GetAnimLayer(seqName, seq);

			if (layer != -1)
			{
				if (m_isPlayer && m_LayerCount > PlayerLayerCount)
				{
					if (m_PlayerAnimationState == nullptr)
					{
						m_PlayerAnimationState = ENG_NEW_LABEL(PlayerAnimationLayerState, kMemAnimation)(this, m_Animator);
					}

					if (m_PlayerAnimationState != nullptr && m_PlayerAnimationState->PlayAnim(layer, seqName, speed, inputloopmode != -1 ? inputloopmode : GetAnimPlayMode(orginseq, inputloopmode)))
					{
						m_CurSeqId = seq;
						return true;
					}
				}
				else
				{
					inputloopmode = inputloopmode != -1 ? inputloopmode : GetAnimPlayMode(orginseq, inputloopmode);
					if (inputloopmode != -1)
					{
						bool bLoop = inputloopmode == ANIM_MODE_LOOP;
						m_Animator->SetStateLoop(layer, seqName, bLoop);
					}

					m_Animator->SetSpeed(layer, seqName, speed);
					
					if(crossfade > 0)
						m_Animator->CrossFade(seqName, crossfade, layer, 0);
					else
						m_Animator->Play(seqName, layer, 0);

					AnimatorStateInfo stateInfo = m_Animator->GetCurrentAnimatorStateInfo(layer);

					if (stateInfo.name == seqName)
					{
						m_CurSeqId = seq;
						return true;
					}
				}
			}

#if DEBUG_MODE 
			LogStringMsg("[Animator] PlayAnim() false , name : %s! ", seqName.c_str());
#endif
			return false;
		}

		if (m_SkeletionAnim)
		{
			AnimationState* state = m_SkeletionAnim->GetState(seqName.c_str());
			if (state)
			{
				if (inputloopmode == -1 || inputloopmode == 0)
					state->SetWrapMode(WrapMode::kWrapModeRepeat);
				else
					state->SetWrapMode(WrapMode::kWrapModeClamp);

				state->SetSpeed(speed);
				state->SetWeight(weight);

				if (layer >= 0)
					state->SetLayer(layer);

				m_SkeletionAnim->Play(seqName.c_str(), Animation::PlayMode::kStopSameLayer);
				m_CurSeqId = seq;
				return true;
			}
		}

		return false;
	}

	void ModelAnimationPlayer::Stop()
	{
		if (m_SkeletionAnim)
		{
			m_SkeletionAnim->Stop();
		}
		m_CurSeqId = 0;
	}
	void ModelAnimationPlayer::SetTimeScale(float speedScale)
	{
		if (m_Animator)
		{
			m_Animator->GetAnimationController()->SetSpeed(speedScale);
		}

		if (m_SkeletionAnim)
		{
			m_SkeletionAnim->SetSpeed(speedScale);
		}
	}

	bool ModelAnimationPlayer::HasAnimPlaying(int seq)
	{
		seq = actionIDConversion(seq);

		//LogStringMsg("[Animator] HasAnimPlaying %d", seq);
		if (m_Animator)
		{
			if (m_isPlayer)
			{
				if (m_PlayerAnimationState != nullptr)
				{
					return m_PlayerAnimationState->HasAnimPlaying(seq);
				}
			}
			else
			{
				core::string& seqName = GetAnimStateName(seq);
				int layerCount = m_LayerCount;
				for (int i = 0; i < layerCount; i++)
				{
					AnimatorStateInfo stateInfo = m_Animator->GetCurrentAnimatorStateInfo(i);
					if (stateInfo.name == seqName)
					{
						return true;
					}

					bool hasNext = m_Animator->GetAnimatorStateInfo(i, Rainbow::StateInfoIndex::kNextState, stateInfo);
					if (hasNext)
					{
						if (stateInfo.name == seqName)
						{
							return true;
						}
					}
				}
			}

			return false;
		}

		if (m_SkeletionAnim)
		{
			return m_SkeletionAnim->IsPlaying(GetAnimStateNameFixed(seq));
		}
		return false;
	}

	void ModelAnimationPlayer::GetCurPlayingSeqList(std::vector<int>& playingList)
	{
		if (m_Animator)
		{
			if (m_isPlayer)
			{
				if (m_PlayerAnimationState != nullptr)
				{
					m_PlayerAnimationState->GetCurPlayingSeqList(playingList);
					return;
				}
			}
			else
			{
				playingList.push_back(m_CurSeqId);
				return;
			}
		}

		if (m_SkeletionAnim)
		{
			playingList.push_back(m_CurSeqId);
			return;
		}
	}

	void ModelAnimationPlayer::SetEffectDisEnable(bool enable)
	{
		//todo...
	}

	int  ModelAnimationPlayer::GetAnimPriority(int seq)
	{
		return -1;
	}

	void ModelAnimationPlayer::SetAnimPriority(int seq, int priority)
	{
		assert(false);
	}


	float ModelAnimationPlayer::GetAnimWeight(int seq)
	{
		return 0.0f;
	}

	void  ModelAnimationPlayer::SetAnimWeight(int seq, float weight)
	{
		assert(false);
	}

	bool ModelAnimationPlayer::HasAnim(int seq)
	{
		if (GetAnimLayer(seq) != -1)
		{
			return true;
		}

		if (m_isPlayer && HasAnimSeqDesc(seq))
			return true;

		return this->HasAnimState(seq);
	}

	bool ModelAnimationPlayer::HasAnimState(int seq)
	{
		const core::string& seqName = GetAnimStateName(seq);
		return this->HasAnim(seqName);
	}

	void ModelAnimationPlayer::Stop(int seq)
	{
		seq = actionIDConversion(seq);

		core::string& seqName = GetAnimStateName(seq);
		//assert(m_CurSeqId == seq);
		StopAnim(seqName);
	}

	void ModelAnimationPlayer::AddAnimationClips(SharePtr<SkinAnimContainer> animContainer)
	{
		if (!m_SkeletionAnim)
			return;

		SkinAnimContainer::AnimationClipVector& clips = animContainer->m_AnimationClips;
		if (clips.size() > 0)
		{
			assert(m_SkinAnimContainer.Get() == nullptr);
			m_SkinAnimContainer = animContainer;

			m_SkeletionAnim->SetPlayAutomatically(false);
			for (int i = 0; i < clips.size(); i++)
			{
				SharePtr<SkeletonAnimationClip> anim = clips[i];

				anim->ClearRange();
				m_SkeletionAnim->AddClip(anim.CastTo<BaseAnimationClip>());

				AnimationState* state = m_SkeletionAnim->GetState(anim.CastTo<BaseAnimationClip>().Get());

				//WrapMode rapemode = anim->GetWrapMode();
				//if (rapemode == kWrapModeClamp)
				{
					AnimationEvent animEvent;
					animEvent.functionName = ON_ANIM_END;
					animEvent.stringParameter = anim->GetName();
					animEvent.time = anim->GetStopTime();
					anim->AddRuntimeEvent(animEvent);
				}

				state->SetEnabled(false);
			}
		}
	}

	void ModelAnimationPlayer::AddAnimationReplaceClips(SharePtr<BaseAnimationClip>& originAnim, SharePtr<BaseAnimationClip>& destAnim)
	{
		AnimationClipOverrideVector::iterator clipOverrideIt = std::find_if(m_SkeletonAnimVec.begin(), m_SkeletonAnimVec.end(), FindOriginalClip(originAnim));
		if (clipOverrideIt != m_SkeletonAnimVec.end())
		{
			clipOverrideIt->m_OverrideClip = destAnim;
		}
		else
		{
			AnimationClipOverride clipOverride;
			clipOverride.m_OriginalClip = originAnim;
			clipOverride.m_OverrideClip = destAnim;
			m_SkeletonAnimVec.push_back(clipOverride);
		}
	}

	void ModelAnimationPlayer::ReInitiallizeAnimator()
	{
		if (m_Animator
			&& m_Animator->GetAnimationController() != nullptr
			&& m_Animator->GetAnimationController()->GetControllerData()
			&& m_SkeletonAnimVec.size() > 0)
		{
			m_Animator->GetAnimationController()->Initialize(&m_SkeletonAnimVec);
			m_SkeletonAnimVec.clear();
		}
	}

	SharePtr<Motion> ModelAnimationPlayer::GetStateClip(int seq)
	{
		if (m_Animator && m_PlayerAnimationState != nullptr)
		{
			core::string& seqName = GetAnimStateName(seq);
			int layer = GetAnimLayer(seqName, seq);

			if (layer != -1)
			{
				return m_Animator->GetStateClip(layer, seqName);
			}
		}

		return nullptr;
	}

	bool ModelAnimationPlayer::SetStateClip(int seq, SharePtr<BaseAnimationClip>& destAnim, bool extend)
	{
		if (m_Animator && m_PlayerAnimationState != nullptr)
		{
			core::string& seqName = GetAnimStateName(seq);
			int layer = GetAnimLayer(seqName, seq);

			if (extend)
			{
				layer = m_PlayerAnimationState->GetExtendLayerIndex(layer);
			}

			if (layer != -1)
			{
				return m_Animator->SetStateClip(layer, seqName, destAnim.CastTo<Motion>());
			}
		}

		return false;
	}


	void ModelAnimationPlayer::OnAnimtionEvent(const EventContent* evt)
	{
		if (m_isPlayer)
			return;

		const Rainbow::AnimationEvent* event = static_cast<const Rainbow::AnimationEvent*>(evt->userData);
		if (event && strcmp(event->functionName.c_str(), ON_ANIM_END) == 0)
		{
			AnimPlayMsg apmsg;
			apmsg.msgtype = APMSG_SEQ_END;
			apmsg.playmode = ANIM_MODE_ONCE;  // ?a???��??��?��?1y
			apmsg.seqid = actionIDConversionReverse(GetAnimStateId(event->stringParameter.c_str()));
			this->PushPlayMsg(apmsg);
		}
	}

	void ModelAnimationPlayer::OnAnimatorPostEvent(const Rainbow::EventContent* evt)
	{
		if (!m_isPlayer || !evt || evt->userData == NULL)
			return;

		SkinnedSkeleton* skSkeleton = m_Skeletion;
		if (!skSkeleton || !skSkeleton->GetSkeleton())
		{
			return;
		}

		const SInt32 boneIndex = m_RotateBoneID;
		if (boneIndex != Skeleton::s_InvalidParentIndex)
		{
			BoneNode* node = skSkeleton->GetSkeletonNode(boneIndex);

			if (node != nullptr)
			{
				ITransform* pIBone = node->GetParent();
				if (pIBone == nullptr) return;
				if (pIBone->IsTransform()) return;

				Quaternionf parentrot = static_cast<BoneNode*>(pIBone)->GetBoneRotationFromRoot();

				Quaternionf invp = parentrot.Inverse();
				Quaternionf newrot = m_BoneRotate * parentrot;
				newrot = invp * newrot;
				Quaternionf ro = node->GetLocalRotation();
				ro = newrot * ro;
				node->SetLocalRotation(ro);

				if (m_BoneScale != 1.0f)
				{
					node->SetLocalScale(node->GetLocalScale() * m_BoneScale);
				}

				node->SetDirty();
			}
		}
	}

	bool ModelAnimationPlayer::HasInitiallized()
	{
		if (m_SkeletionAnim)
		{
			return true;
		}

		if (m_Animator
			&& m_Animator->GetAnimationController() != nullptr
			&& m_Animator->GetAnimationController()->GetControllerData())
		{
			return true;
		}

		return false;
	}

	void ModelAnimationPlayer::SetIsPlayer(bool value)
	{
		m_isPlayer = (value || m_Animator && m_LayerCount > PlayerLayerCount);

		if (!m_isPlayer)
		{
			m_GameObject->AddEvent<ModelAnimationPlayer>(Evt_AnimatorStateEvent, &ModelAnimationPlayer::OnAnimatorStateEvent, this);
		}
		else
		{
			if (m_PlayerAnimationState == nullptr)
			{
				m_PlayerAnimationState = ENG_NEW_LABEL(PlayerAnimationLayerState, kMemAnimation)(this, m_Animator);
			}
		}
	}

	void ModelAnimationPlayer::SetBoneRotate(int boneid, const Quaternionf* rot, float scale)
	{
		m_RotateBoneID = boneid;
		if (rot) m_BoneRotate = *rot;
		m_BoneScale = scale;
	}


	bool ModelAnimationPlayer::RegistAnimKeyFrameEvent(int seqId, float time, const std::string& eventName)
	{
		if (m_PlayerAnimationState != nullptr)
		{
			return m_PlayerAnimationState->RegistAnimKeyFrameEvent(seqId, time, eventName);
		}
		return false;
	}
	bool ModelAnimationPlayer::UnRegistAnimKeyFrameEvent(int seqId, const std::string& eventName)
	{
		if (m_PlayerAnimationState != nullptr)
		{
			return m_PlayerAnimationState->UnRegistAnimKeyFrameEvent(seqId, eventName);
		}
		return false;
	}

	void ModelAnimationPlayer::ClearAllAnimKeyFrameEvent()
	{
		if (m_PlayerAnimationState != nullptr)
		{
			return m_PlayerAnimationState->ClearAllAnimKeyFrameEvent();
		}
	}

	float ModelAnimationPlayer::GetAnimClipLenth(int seqId)
	{
		if (m_Animator)
		{
			const core::string& seqName = GetAnimStateName(seqId);
			int layer = GetAnimLayer(seqName, seqId);
			if (layer != -1)
			{
				return m_Animator->GetStateClipLength(layer, seqName);
			}
		}
		return -1.;
	}

	bool ModelAnimationPlayer::HasAnimPlayEnd(int seqId)
	{
		if (m_PlayerAnimationState != nullptr)
		{
			seqId = actionIDConversion(seqId);
			const dynamic_array<AnimPlayClipInfo>& list = m_PlayerAnimationState->GetPlayAnimationList();
			for (auto itr = list.begin(); itr != list.end(); itr++)
			{
				if ((*itr).animId == seqId)
				{
					return (*itr).playEnd;
				}
			}
		}

		return false;
	}

	bool ModelAnimationPlayer::SetAnimatorTickInterval(int nInterval, int nMod)
	{
		if (m_Animator)
		{
			if (nInterval > 0
				&& nMod >= 0 && nMod < nInterval)
			{
				m_Animator->SetSkipFrameRate(nInterval);
				m_Animator->SetSkipFrameRateRemaining(nMod);
			}
		}

		return false;
	}

	void ModelAnimationPlayer::SetAnimatorSetCullingMode(int cullmode)
	{
		if (m_Animator)
		{
			m_Animator->SetCullingMode((Rainbow::Animator::CullingMode)cullmode);
		}
	}

	void ModelAnimationPlayer::SyncAnimStateTime(int seqid, int layer, int targetlayer)
	{
		if (!m_Animator) return;

		// 获取目标层的动画状态信息
		float targetNormalizedTime = 0.0f;
		Rainbow::AnimatorStateInfo targetStateInfo;
		if (m_Animator->GetAnimatorStateInfo(targetlayer, Rainbow::kCurrentState, targetStateInfo))
		{
			targetNormalizedTime = targetStateInfo.normalizedTime;
		}

		// 获取动画名称
		core::string& animName = GetAnimStateName(seqid);
		
		// 使用CrossFade同步时间，设置normalizedTimeOffset参数
		m_Animator->CrossFade(animName, 0.1f, layer, targetNormalizedTime, 0.1f, false);
		
		LOG_INFO("ModelAnimationPlayer::SyncAnimStateTime - Layer %d synced to layer %d with normalizedTime: %f, seqid: %d", 
			layer, targetlayer, targetNormalizedTime, seqid);
	}

	PlayerAnimationLayerState::PlayerAnimationLayerState(ModelAnimationPlayer* modelAnimation, Animator* animator)
		: m_modelAnimation(modelAnimation), m_Animator(animator)
	{
		if (animator != nullptr)
		{
			m_LayerCount = animator->GetLayerCount();
		}
	}

	PlayerAnimationLayerState::~PlayerAnimationLayerState()
	{
		m_modelAnimation = nullptr;
		m_Animator = nullptr;
		m_LayerCount = 0;
		m_PlayAnimationList.clear();
		m_AnimationEventList.clear();
		m_AnimtionClipMap.clear();
	}

	void PlayerAnimationLayerState::UpdatePlayerAnimatorState(float dt)
	{
		if (m_Animator)
		{
			if (m_LayerCount == 0)
			{
				m_LayerCount = m_Animator->GetLayerCount();
			}

			int layerCount = m_LayerCount;

			if (layerCount > PlayerLayerCount)
			{
				// update animator layer weight
				for (int i = 0; i < layerCount; i++)
				{
					if (i >= 1 && !IsSyncLayer(m_Animator, i))
					{
						float weight = CaculateAnimLayerWeight(GetLogicLayer(i, m_LayerCount));

						if (HasLayerAnimClipInfo(i))
						{
							SetAnimatorLayerWeight(i, weight);
							// LogStringMsg("[Animator] SetLayerWeight() layer %d ,weight %f! ", i, weight);
						}
						else
						{
							SetAnimatorLayerWeight(i, 0.f);
						}
					}
				}

				// update anim play cilp infos
				for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end();)
				{
					AnimPlayClipInfo& clipInfo = (*itr);

					float lastFrameTime = clipInfo.animPlayTime;
					clipInfo.animPlayTime += dt * clipInfo.speed;

					// check custom event
					if (clipInfo.playState != AnimPlayTrack::PlayState::STATE_OUT)
					{
						for (auto itr2 = m_AnimationEventList.begin(); itr2 != m_AnimationEventList.end(); itr2++)
						{
							AnimClipEventInfo& eventInfo = (*itr2);
							if (eventInfo.animId == clipInfo.animId
								&& eventInfo.animTime >= lastFrameTime && eventInfo.animTime <= clipInfo.animPlayTime)
							{
								AnimPlayMsg apmsg;
								apmsg.msgtype = APMSG_SEQ_KEYFRAME;
								apmsg.playmode = clipInfo.playmode;
								apmsg.seqid = m_modelAnimation->actionIDConversionReverse(clipInfo.animId);
								apmsg.name = eventInfo.eventName;
								m_modelAnimation->PushPlayMsg(apmsg);
							}
						}
					}

					if (clipInfo.playState == AnimPlayTrack::PlayState::STATE_IN)
					{
						if (clipInfo.animPlayTime > DefaultCrossFadeDur)
						{
							clipInfo.playState = AnimPlayTrack::PlayState::STATE_ACTIVE;
							//LogStringMsg("[Animator] SwitchState STATE_ACTIVE %d", clipInfo.animId);
						}
					}
					else if (clipInfo.playState == AnimPlayTrack::PlayState::STATE_ACTIVE)
					{
						if (clipInfo.animPlayTime > clipInfo.animLengh)
						{
							if (clipInfo.playmode == AnimPlayMode::ANIM_MODE_ONCE)
							{
								clipInfo.playState = AnimPlayTrack::PlayState::STATE_OUT;
								clipInfo.animPlayTime = 0.f;
								//LogStringMsg("[Animator] SwitchState STATE_OUT %d", clipInfo.animId);
							}

							if (!clipInfo.playEnd)
							{
								AnimPlayMsg apmsg;
								apmsg.msgtype = APMSG_SEQ_END;
								apmsg.playmode = clipInfo.playmode;
								apmsg.seqid = m_modelAnimation->actionIDConversionReverse(clipInfo.animId);
								apmsg.name = GetAnimStateNameFixed(clipInfo.animId);
								m_modelAnimation->PushPlayMsg(apmsg);

								if (apmsg.playmode != AnimPlayMode::ANIM_MODE_LOOP)
								{
									clipInfo.playEnd = true;
								}
								else
								{
									clipInfo.animPlayTime = 0.f;
								}
							}
						}
					}
					else if (clipInfo.playState == AnimPlayTrack::PlayState::STATE_OUT)
					{
						if (clipInfo.animPlayTime > DefaultCrossFadeDur)
						{
							//LogStringMsg("[Animator] clipInfo.animPlayTime > m_DefaultCrossFadeDur m_PlayAnimationList erase %d", clipInfo.animId);
							int layer = clipInfo.layer;
							int animId = clipInfo.animId;
							//LogStringMsg("[Animator] SwitchState STATE_NULL %d", clipInfo.animId);
							itr = m_PlayAnimationList.erase(itr);

							if (!HasLayerAnimClipInfo(layer))
							{
								if (layer >= 1)
								{
									if (m_Animator->HasState(layer, "Exit"))
									{
										m_Animator->Play("Exit", layer, 0);
									}
								}
								else
								{
									if (m_Animator->HasState(layer, "100100"))
									{
										m_Animator->Play("100100", layer, 0);
									}
								}

								//LogStringMsg("[Animator] SetLayerWeight 0  %d", animId);
								if (layer >= 1 && !IsSyncLayer(m_Animator, layer))
								{
									SetAnimatorLayerWeight(layer, 0.f);
								}
							}

							continue;
						}
					}

					itr++;
				}
			}
		}
	}

	void PlayerAnimationLayerState::SetAnimatorLayerWeight(int layer, float weight)
	{
		if (m_Animator)
		{
			m_Animator->SetLayerWeight(layer, weight);

			int extendLayer = GetExtendLayerIndex(layer);
			if (extendLayer != -1)
			{
				m_Animator->SetLayerWeight(extendLayer, weight);
			}
		}
	}

	int PlayerAnimationLayerState::GetExtendLayerIndex(int layer)
	{
		if (m_Animator)
		{
			dynamic_array<int> SyncLayerIndexList;
			m_Animator->GetSyncLayerIndex(layer, SyncLayerIndexList);

			if (SyncLayerIndexList.size() > 0)
			{
				return SyncLayerIndexList[0];
			}
		}

		return -1;
	}

	void PlayerAnimationLayerState::AddPlayClipInfo(const AnimPlayClipInfo& animmsg)
	{
		AddLayerPlayClipInfo(animmsg);
		m_PlayAnimationList.push_back(animmsg);
	}


	bool PlayerAnimationLayerState::AddLayerPlayClipInfo(const AnimPlayClipInfo& animmsg)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); )
		{
			bool bErase = false;
			AnimPlayClipInfo& clipInfo = (*itr);
			if (animmsg.logicLayer == clipInfo.logicLayer)
			{
				if (animmsg.animId == clipInfo.animId)
				{
					// LogStringMsg("[Animator] SwitchLayerPlayClipInfo m_PlayAnimationList erase %d", clipInfo.animId);
					itr = m_PlayAnimationList.erase(itr);
					bErase = true;
				}
				else
				{
					// LogStringMsg("[Animator] SwitchLayerPlayClipInfo AnimPlayTrack::PlayState::STATE_OUT %d", clipInfo.animId);
					clipInfo.animPlayTime = 0.f;
					clipInfo.playState = AnimPlayTrack::PlayState::STATE_OUT;
				}
			}

			if (!bErase)
			{
				itr++;
			}
		}

		int layer = animmsg.layer;
		if (layer >= 1 && !IsSyncLayer(m_Animator, layer))
		{
			float weight = CaculateAnimLayerWeight(animmsg.logicLayer);
			SetAnimatorLayerWeight(layer, weight);
		}

		return false;
	}


	bool PlayerAnimationLayerState::HasLayerAnimClipInfo(int layer)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			AnimPlayClipInfo clipInfo = (*itr);

			if (layer == clipInfo.layer)
			{
				return true;
			}
		}

		return false;
	}

	bool PlayerAnimationLayerState::HasLayerAnimPlaying(int layer)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			AnimPlayClipInfo clipInfo = (*itr);

			if (layer == clipInfo.layer
				&& clipInfo.playState != AnimPlayTrack::PlayState::STATE_OUT)
			{
				return true;
			}
		}

		return false;
	}

	AnimPlayMode PlayerAnimationLayerState::GetAnimClipPlayMode(int animId)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			AnimPlayClipInfo clipInfo = (*itr);

			if (animId == clipInfo.animId)
			{
				return clipInfo.playmode;
			}
		}

		return ANIM_MODE_ONCE;
	}

	float PlayerAnimationLayerState::CaculateAnimLayerWeight(int layer)
	{
		if (layer == 0)
		{
			return 1.f;
		}

		float weight = 0.f;
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			AnimPlayClipInfo clipInfo = (*itr);
			if (layer == clipInfo.logicLayer)
			{
				if (clipInfo.playState == AnimPlayTrack::PlayState::STATE_IN)
				{
					weight += clipInfo.animPlayTime / DefaultCrossFadeDur;
				}
				else if (clipInfo.playState == AnimPlayTrack::PlayState::STATE_OUT)
				{
					weight += (1.f - clipInfo.animPlayTime / DefaultCrossFadeDur);
					// LogStringMsg("[Animator] STATE_OUT CaculateAnimLayerWeight %f", weight);
				}
				else
				{
					weight = 1.0f;
					break;
				}
			}
		}
		weight = math::clamp(weight, 0.f, 1.f);
		return weight;
	}

	bool PlayerAnimationLayerState::PlayAnim(int layer, const core::string& name, float speed, int inputloopmode)
	{
		if (!m_Animator)
		{
			return false;
		}

		int seqId = GetAnimStateId(name.c_str());

		bool loopChanged = false;
		bool speedChanged = false;
		auto iter = m_AnimtionClipMap.find(seqId);
		if (iter != m_AnimtionClipMap.end())
		{
			if (inputloopmode != -1)
			{
				bool bLoop = inputloopmode == ANIM_MODE_LOOP;
				if (iter->second.loop != bLoop)
				{
					loopChanged = true;
				}
			}

			if (iter->second.speed != speed)
			{
				speedChanged = true;
			}
		}
		else
		{
			loopChanged = true;
			speedChanged = true;
		}

		// set state loopmode
		if (inputloopmode != -1 && loopChanged)
		{
			bool bLoop = inputloopmode == ANIM_MODE_LOOP;
			m_Animator->SetStateLoop(layer, name, bLoop);

			int extendLayer = GetExtendLayerIndex(layer);
			if (extendLayer != -1)
			{
				m_Animator->SetStateLoop(extendLayer, name, bLoop);
			}
		}

		// set state speed
		if (speedChanged)
		{
			m_Animator->SetSpeed(layer, name, speed);

			int extendLayer = GetExtendLayerIndex(layer);
			if (extendLayer != -1)
			{
				m_Animator->SetSpeed(extendLayer, name, speed);
			}
		}

		if (loopChanged || speedChanged)
		{
			AnimClipInfo cipInfo;
			cipInfo.loop = inputloopmode == ANIM_MODE_LOOP;
			cipInfo.speed = speed;
			m_AnimtionClipMap[seqId] = cipInfo;
		}

		bool bCrossFade = true;
		if (HasLayerAnimClipInfo(layer))
		{
			m_Animator->CrossFade(name, 0.25f, layer, 0.f, DefaultCrossFadeDur);
			bCrossFade = true;
		}
		else
		{
			m_Animator->Play(name, layer, 0);
			bCrossFade = false;
			//
		}


		AnimatorStateInfo stateInfo;
		m_Animator->GetAnimatorStateInfo(layer, !bCrossFade ? Rainbow::StateInfoIndex::kCurrentState : Rainbow::StateInfoIndex::kNextState, stateInfo);
		if (stateInfo.name == name)
		{
			if (m_LayerCount > PlayerLayerCount)
			{
				AnimPlayClipInfo apmsg;
				apmsg.animId = seqId;
				apmsg.layer = layer;
				apmsg.logicLayer = GetLogicLayer(layer, m_LayerCount);
				apmsg.playmode = GetAnimPlayMode(m_modelAnimation->actionIDConversionReverse(apmsg.animId), inputloopmode);
				apmsg.speed = speed;
				//如果有待触发事件，必须从0开始
				apmsg.animPlayTime = (bCrossFade|| HasAnimKeyFrameEvent(seqId)) ? 0.f : DefaultCrossFadeDur;
				apmsg.animLengh = stateInfo.length;
				apmsg.playState = AnimPlayTrack::PlayState::STATE_IN;
				AddPlayClipInfo(apmsg);
			}

			return true;
		}

		return false;
	}

	bool PlayerAnimationLayerState::StopAnim(int layer, const core::string& name)
	{
		bool result = false;
		int animId = GetAnimStateId(name.c_str());
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			AnimPlayClipInfo& clipInfo = (*itr);

			if (layer == clipInfo.layer
				&& animId == clipInfo.animId)
			{
				if (clipInfo.playState != AnimPlayTrack::PlayState::STATE_OUT)
				{
					result = clipInfo.playState == AnimPlayTrack::PlayState::STATE_ACTIVE;

					clipInfo.animPlayTime = 0.f;
					clipInfo.playState = AnimPlayTrack::PlayState::STATE_OUT;

					break;
				}
			}
		}

		return result;
	}

	SharePtr<Motion> PlayerAnimationLayerState::GetStateClip(int layerIndex, const core::string& stateName) const
	{
		if (m_Animator)
			return m_Animator->GetStateClip(layerIndex, stateName);

		return nullptr;
	}

	bool PlayerAnimationLayerState::HasAnimActiving(int seq)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			if ((*itr).animId == seq &&
				((*itr).playState == AnimPlayTrack::STATE_ACTIVE))
			{
				return true;
			}
		}

		return false;
	}

	bool PlayerAnimationLayerState::HasAnimPlaying(int seq)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			if ((*itr).animId == seq &&
				((*itr).playState == AnimPlayTrack::STATE_ACTIVE || (*itr).playState == AnimPlayTrack::STATE_IN))
			{
				return true;
			}
		}

		return false;
	}

	void PlayerAnimationLayerState::GetCurPlayingSeqList(std::vector<int>& playingList)
	{
		for (auto itr = m_PlayAnimationList.begin(); itr != m_PlayAnimationList.end(); itr++)
		{
			if (((*itr).playState == AnimPlayTrack::STATE_ACTIVE || (*itr).playState == AnimPlayTrack::STATE_IN))
			{
				playingList.push_back(m_modelAnimation->actionIDConversionReverse((*itr).animId));
			}
		}
	}

	bool PlayerAnimationLayerState::RegistAnimKeyFrameEvent(int seqId, float time, const std::string& eventName)
	{
		const char* strEventName = eventName.c_str();
		for (auto itr = m_AnimationEventList.begin(); itr != m_AnimationEventList.end(); itr++)
		{
			AnimClipEventInfo& clipInfo = (*itr);
			if (seqId == clipInfo.animId && clipInfo.eventName == strEventName)
			{
				return false;
			}
		}

		if (time >= 0 && time <= m_modelAnimation->GetAnimClipLenth(seqId))
		{
			AnimClipEventInfo apmsg;
			apmsg.animId = seqId;
			apmsg.animTime = time;
			apmsg.eventName = FixedString(strEventName);
			m_AnimationEventList.push_back(apmsg);
			return true;
		}

		return false;
	}

	bool PlayerAnimationLayerState::UnRegistAnimKeyFrameEvent(int seqId, const std::string& eventName)
	{
		const char* strEventName = eventName.c_str();
		for (auto itr = m_AnimationEventList.begin(); itr != m_AnimationEventList.end(); itr++)
		{
			AnimClipEventInfo& clipInfo = (*itr);
			if (seqId == clipInfo.animId && clipInfo.eventName == strEventName)
			{
				itr = m_AnimationEventList.erase(itr);
				return true;
			}
		}

		return false;
	}

	void PlayerAnimationLayerState::ClearAllAnimKeyFrameEvent()
	{
		m_AnimationEventList.clear();
	}

	bool PlayerAnimationLayerState::HasAnimKeyFrameEvent(int seqId)
	{
		for (auto itr = m_AnimationEventList.begin(); itr != m_AnimationEventList.end(); itr++)
		{
			if (itr && itr->animId == seqId)
			{
				return true;
			}
		}
		return false;
	}
}