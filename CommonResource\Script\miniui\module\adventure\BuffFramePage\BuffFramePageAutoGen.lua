--文件来源：assets/adventure/BuffFrame/BuffFramePage.xml
local BuffFramePageAutoGen = Class("BuffFramePageAutoGen",ClassList["MiniUICommonNodeClass"])

local BuffFramePageCtrl,BuffFramePageModel,BuffFramePageView = GetInst("MiniUIManager"):GetMVC("BuffFramePageAutoGen")
local BUFF_MAX_NUM = 20
--初始化
function BuffFramePageAutoGen:init(param)
	if not self.firstInit then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("BuffFramePageAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
		self.ctrl:InitPage()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end

	local function _Update()
		self.ctrl:InitPage()
	end
	self.ctrl.m_timer = GetInst("MiniUIScheduler"):regPrivate(self.root,_Update,0.25,-1,nil,false)
	self.firstInit = 0
end
function BuffFramePageAutoGen:_Unreg()
	
	GetInst("MiniUIScheduler"):unreg(self.ctrl.m_timer)
	self.ctrl.m_timer = nil
end
--显示
function BuffFramePageAutoGen:onShow()
	self.ctrl:Refresh()
end

--隐藏
function BuffFramePageAutoGen:onHide()
	self.ctrl:Reset()
	self:_Unreg()
end

--移除
function BuffFramePageAutoGen:onRemove()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)
	self:_Unreg()
end

--Ctrl:注册UI事件
function BuffFramePageCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.MainList, UIEventType_ClickItem, function(obj, context)
 		if self.MainListClickItem then
			self:MainListClickItem(obj, context)
		end
	end)
	GetInst("MiniUIComponents"):setCallback(self.view.widgets.MainList, "GList.itemRenderer", function(comp, idx, obj)
		self:OnUpdate()
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.CloseBTN, UIEventType_Click, function(obj, context)
		GetInst("MiniUIManager"):CloseUI("BuffFramePageAutoGen")
	end)
end

--View:获取需要操作的节点
function BuffFramePageView:GetHandleNodes()
	self.widgets={}
	--list
	self.widgets.MainList = self.root:getChildByPath("gp_Main.list_BuffList")
	--Button
	self.widgets.CloseBTN = self.root:getChildByPath("gp_Main.btn_CloseBTN")
	--gp
	self.widgets.GP_Main = self.root:getChildByPath("gp_Main")

	--title
	self.widgets.title = self.root:getChildByPath("gp_Main.lbl_StateTitle")
end

function BuffFramePageCtrl:MainList()
	return self.view.widgets.MainList
end

function BuffFramePageCtrl:GP_Main()
	return self.view.widgets.GP_Main
end

function BuffFramePageCtrl:InitPage()
	local ride = CurMainPlayer:getRidingHorse();
	local t_buff = {}
	local num = 0;
	 local attrib;
	if MainPlayerAttrib and MainPlayerAttrib:getBuffNum() > 0 then
		num = MainPlayerAttrib:getBuffNum();
		attrib = MainPlayerAttrib;

		for i=1, num do
			local info = attrib:getBuffInfo(i-1);
			--装备的buff不显示
			if info and info.def and info.def.BuffType == 1 and not info.def.Hide then
				table.insert(t_buff, {info=info, ownerType="player"});
			end
		end
	end

	if ride and ride:getLivingAttrib() and ride:getLivingAttrib():getBuffNum()>0 then
		num = ride:getLivingAttrib():getBuffNum();
		attrib = ride:getLivingAttrib();

		for i=1, num do
			local info = attrib:getBuffInfo(i-1);
			--装备的buff不显示
			if info and info.def and info.def.BuffType == 1 and not info.def.Hide then
				table.insert(t_buff, {info=info, ownerType="ride"});
			end
		end
	end
	for i=0,self:MainList():getNumItems()-1 do
		self:MainList():getChildAt(i):setVisible(false)
	end
	
	for i=1,#t_buff do
		if i > BUFF_MAX_NUM then
			return
		end
		if i > self:MainList():getNumItems() then
			self:MainList():addItemFromPool("ui://wypxe6zzb6hk6z")
		end
		
		local item = self:MainList():getChildAt(i-1)
		item:setVisible(true)


		local info = t_buff[i].info;
		local text = info.def.Desc;
		--BUff Title
		item:getChildByPath("lbl_BuffName"):setText(info.def.Name)

		--Time Text
		local time = math.ceil( info.ticks*0.05 );
		local timetext = math.floor(time/60)..":"..math.mod(time, 60);
		if time >= 9999 then
			timetext = GetS(680);
		end
		item:getChildByPath("lbl_BuffTime"):setText(timetext)

		--Time Color
		if info.def.Type == 0 then			--有益
			item:getChildByPath("lbl_BuffTime"):setColor({r = 1,g = 194, b = 16})
		elseif info.def.Type == 1 then			--不利
			item:getChildByPath("lbl_BuffTime"):setColor({r = 255,g = 137, b = 32})
		end

		--Icon
		if not self.tBuffBtnIconSetInfo[i] or self.tBuffBtnIconSetInfo[i] ~= info.def.ID then
			self.tBuffBtnIconSetInfo[i] = info.def.ID
			local def = info.def
			local gloaderComponent = item:getChildByPath("ld_BuffIcon")
			if def and gloaderComponent then
				local strIconId = def.Status.strIconID
				if strIconId ~= "" then
					GetInst("UIEditorPicSelectorMgr"):setCellIconById(strIconId, gloaderComponent);
				else
					local iconId = def.Status.IconID
					if iconId < 10000 then
					-- IconBank.csv 中的图标
						iconId = iconId + DEVUI_ICONRES_TYPE_ICONBANK * 1000000
					end
					GetInst("UIEditorPicSelectorMgr"):setCellIconById(iconId,gloaderComponent);
				end
			end
		end

		if info.def.IconName ~= '' or SingleEditorFrame_Switch_New then
			if SingleEditorFrame_Switch_New then
				if info.def and info.def.Status and info.def.Status.EffInfo then
					if t_buff[i].ownerType == "player" then  --下面的描述都是跟“角色”强绑定的
						for j = 1, MAX_BUFF_ATTRIBS do
							if info.def.Status.EffInfo[j-1].CopyID > 0 then
								local descStr = GetInst("ModsLibDataManager"):GetStatusEffectDescStr(info.def.Status.EffInfo[j-1])
								if descStr ~= "" then
									if text == info.def.Desc then
										if text == "" then
											text = descStr
										else
											text = text  .. "\n" .. descStr
										end
									else
										text = text  .. "\n" .. descStr
									end
								end
							end
						end
					end
					item:getChildByPath("rbl_BuffDesc"):setText(GenerateUBBStr(text))
				end
			else
				item:getChildByPath("ld_BuffIcon"):setIcon("ui/bufficons/"..info.def.IconName..".png")
			end

		end
		
	end
end