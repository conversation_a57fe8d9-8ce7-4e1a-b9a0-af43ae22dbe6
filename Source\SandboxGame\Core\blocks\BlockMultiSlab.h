#ifndef __BLOCK_MULTI_CEILING_H__
#define __BLOCK_MULTI_CEILING_H__

#include "BlockMaterial.h"
#include "BlockArchitecturalBase.h"

class BlockMultiSlab : public ModelBlockMaterial, public BlockArchitecturalBase
{
    DECLARE_BLOCKMATERIAL(BlockMultiSlab)
public:
    BlockMultiSlab();
    virtual ~BlockMultiSlab() = default;
    virtual void init(int resid) override;
	virtual int getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
    virtual bool onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount) override;
    virtual bool onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player) override;
    virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage) override;
    virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho) override;
    virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f);
    virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
    virtual void getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas);
    virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
    virtual void onBlockAdded(World *pworld, const WCoord &blockpos) override;
    virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
    virtual bool canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;
    virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
    virtual int getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player) override;
    virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
    virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
    virtual int getBlockHP(World* pworld, const WCoord& blockpos);
    virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;
    virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
    virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_Architecture; }
    virtual bool hasSolidTopSurface(int blockdata) { return true; };
private:
    virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
    bool isCoreBlock(int blockdata);
    bool isBottomSlab(int blockdata);
    virtual void initGeomName() override;

private:
    int thick = 18; // 玩家高度是180,不要超过20
    dynamic_array<UInt16> m_physDataIdxs;
    // 扩展方块位置定义 (天花板是水平扩展的)
    const static int SlabExtendPos[4][3][3];
};

#endif//__BLOCK_MULTI_CEILING_H__ 