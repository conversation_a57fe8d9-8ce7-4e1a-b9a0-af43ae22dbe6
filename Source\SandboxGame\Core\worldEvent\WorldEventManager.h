#pragma once
#include <unordered_map>
#include <memory>
#include <mutex>
#include <functional>
#include "WorldEvent.h"
#include "json/jsonxx.h"
#include "PlayManagerInterface.h"
#include "WorldEventCsv.h"
#include "SandboxGame.h"
#include "WorldEventMgrProxy.h"

// WorldEventManager
class EXPORT_SANDBOXGAME WorldEventManager;
class WorldEventManager : public IPluginBase, WorldEventMgrProxy {
public:

    WorldEventManager(PluginManager* p);
    virtual ~WorldEventManager();

    virtual bool Awake();
    virtual bool Init();
    virtual bool Execute(float dtime);
    virtual bool FixedTick();
    virtual bool Shut();

    virtual void onChunkLoaded(int chunkX, int chunkZ)  ;
    virtual void onChunkUnloaded(int chunkX, int chunkZ)  ;
    virtual void onDestroyContainer(int x, int y, int z)  ;


    void InitializeLua(const std::string& scriptPath);

    // Core update loop
    void Update(float deltaTime);

    // Event management
    void StartEvent(WorldEventDef* eventdef);
    void StopEvent(int eventid);
    bool IsEventActive(int  eventid) const;

    // 外部触发世界事件的接口
    bool TriggerAirDropEvent(int eventid, int pos_x, int pos_y, int pos_z);

    // 获取活动事件列表
    std::vector<WorldEvent*> GetActiveEvents();

private:
    WorldEventManager() = default;

    // Server time tracking
    float m_serverTime{0.0f};
    float m_loadcfgTime{0.0f};

    // Event management
    std::unordered_map<int, std::shared_ptr<WorldEvent>> m_activeEvents;
    std::mutex m_eventMutex;

    // Configuration
    std::vector<WorldEventDef> m_eventdef_list;


    // Helper methods
    void LoadConfig();
    void ReloadConfig();
    void CheckEventTriggers();
    std::shared_ptr<WorldEvent> CreateWorldEvent(WorldEventDef* eventdef);

    WorldEventDef* getEventDef(int eventid);
};
