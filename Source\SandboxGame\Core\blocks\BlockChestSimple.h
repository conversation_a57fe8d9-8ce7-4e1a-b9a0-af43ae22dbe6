
#ifndef __BLOCK_CHESTSIMPLE_H__
#define __BLOCK_CHESTSIMPLE_H__

#include "BlockMaterial.h"
#include "BlockMultiBase.h"

class BlockSimpleChest : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleChest)
public:

	virtual void init(int resid) override;
	//tolua_begin
	//virtual const char *getGeomName();

	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	//tolua_end
private:
	virtual void initGeomName() override;
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf);
	void reportData();
}; //tolua_exports

class BlockLockedChest : public BlockSimpleChest //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockLockedChest)
public:

	//virtual void init(int resid) override;

	//tolua_begin
	//virtual const char *getGeomName();

	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0)) override;
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	virtual float getDestroyHardness(int blockdata, IClientPlayer *player) override;
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_GRASS;
	//}
	//tolua_end
private:
	virtual void initGeomName() override;
	virtual void initDrawType() override;
}; //tolua_exports

class ChestPasswordMaterial: public BlockLockedChest //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(ChestPasswordMaterial)	
public:

	virtual void init(int resid) override;
	//tolua_begin
	ChestPasswordMaterial();
	virtual ~ChestPasswordMaterial();
	//virtual const char *getGeomName();
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0)) override;
	//tolua_end
private:
	virtual void initGeomName() override;
}; //tolua_exports


class ChestMultiChest : public BlockSimpleChest, public BlockMultiBase //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(ChestMultiChest)
public:

	virtual void init(int resid) override;

	//tolua_begin
	//virtual const char *getGeomName();
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	//virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player) override;
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false);
	//virtual int getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_GRASS;
	//}
	//tolua_end
private:
}; //tolua_exports
#endif