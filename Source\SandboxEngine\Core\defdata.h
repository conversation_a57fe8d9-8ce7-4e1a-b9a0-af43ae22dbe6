#ifndef __DEFDATA_H__
#define __DEFDATA_H__

#include <map>
#include <string>
#include "ActorTypes.h"
#include "world_types.h"
#include "BiomeTypes.h"
#include "Utilities/FixedString.h"
#include "OgreScriptLuaVM.h"
#include "json/jsonxx.h"
#include "tolua++.h"
#include "SandboxGame/Mods/ModConfig.h"

class GameMod;

namespace  MNSandbox {
	class  Object;
}

const std::string NoKey = "";
//tolua_begin
#define MAX_BLOCK_NAME  64
#define MAX_BLOCK_DESC  256
#define MAX_BIOME_MOBS  8
#define MAX_BIOME_TREE  2
#define MAX_BIOME_GRASS 16
#define MAX_BIOME_CORAL 8
#define MAX_BIOME_SEAPLANT 4
#define MAX_BIOME_FLOWER 40
#define MAX_BIOME_JARS 3
#define MAX_MONSTER_DROPGROUP 2
#define MAX_MONSTER_DROPITEM 3
#define MAX_ACHIEVEMENT_NAME 64
#define MAX_ACHIEVEMENT_DESC 256
#define MAX_ACHIEVEMENT_SCRIPT 256
#define MAX_BUFF_ATTRIBS 10
#define MAX_FOOD_BUFF 3
#define MAX_CLEAR_BUFF 5
#define MAX_TOOL_TYPE 12
#define MAX_RULE_NAME 64
#define MAX_HORSE_SKILL 4
#define MAX_JAGGED_FERN 1

#define MAX_CLEAR_BUFF 5
#define MAX_BIOME_PLANTS 8
#define MAX_BIOME_BUILDS 8
#define MAX_BIOME_PROPS  8

#define MAX_BIOME_MONSTERS 130 // 扩充monsterbiomedef.csv时需要对应扩充宏大小，宏大小需要比表格长度稍大
#define MAX_BIOME_TREE_GEN 10 

#define MAX_ACTIONER_PART 8
#define MAX_ACTIONER_LINE 64

#define MAX_PART_KEYS	21
#define MAX_PET_SKILL 10 //宠物最多特性 
#define MAX_MONSTER_NUM 1024 // 最大怪物id

#define MAX_BIOME_NEWCORAL 8
#define MAX_BIOME_WATER_FOG 5 // 水下雾效配置长度

#define MAX_ATTACK_CONFIG 10 // 最大连击配置数

#define MAX_BIOME_LEAVES_COLOR 3	//树叶随机颜色长度
#define MIN_BIOME_LEAVES_COLOR 1

// 存在每个插件文件中的插件信息（便于将来玩家之间插件互相使用，原来插件描述信息是存放在"pack_manifest.json"中的公共信息）
struct ModDesc
{
    Rainbow::FixedString version;	// 插件版本
    Rainbow::FixedString uuid;		// 插件所在的GameMod的UUID
    Rainbow::FixedString filename;	// 插件的文件名
    Rainbow::FixedString author;		// 插件作者
	void copy(const ModDesc& tmpDesc)
	{	
		uuid = tmpDesc.uuid;
		author = tmpDesc.author;
		version = tmpDesc.version;
		filename = tmpDesc.filename;
	}
};

struct BiomeDef
{
	int ID;
	Rainbow::FixedString Name;
	float MinHeight;
	float MaxHeight;
	float Heat;
	float Humid;

	int FillBlock;
	int FillDepth;
	int TopBlock;
	unsigned int WaterColor;
	unsigned int GrassColor;
	unsigned int WaterFogColor[MAX_BIOME_WATER_FOG];
	int WaterFogDepth[MAX_BIOME_WATER_FOG];
	bool EnableRain;
	bool EnableSnow;
	float ClearModulus;
	float RainModulus;
	float ThunderModulus;

	int ChunkTrees;
	int ChunkGrass[MAX_BIOME_GRASS];		//草(ChunkGrass): 如:ChunkGrass[] = {224, 243, 238};
	int ChunkGrassNum[MAX_BIOME_GRASS];
	int ChunkFlowers[MAX_BIOME_FLOWER];		//花(ChunkFlowers)
	int ChunkFlowerNum[MAX_BIOME_FLOWER];
	int ChunkTreex[MAX_BIOME_TREE_GEN];
	int ChunkTreeNum[MAX_BIOME_TREE_GEN];

	int ChunkCorals[MAX_BIOME_CORAL];		//珊瑚(ChunkCorals)
	int ChunkCoralNum[MAX_BIOME_CORAL];

	int ChunkNewCorals[MAX_BIOME_NEWCORAL];		//新珊瑚(ChunkNewCorals)
	int ChunkNewCoralNum[MAX_BIOME_NEWCORAL];

	int ChunkNewCoralsCluster;				//成群活体新珊瑚
	int ChunkNewCoralsDeathCluster;			//成群白化新珊瑚
	int ChunkPumpkin;						//南瓜
	int ChunkWatermelon;					//西瓜
	int ChunkRice;							//水稻
	int ChunkDeadBush;						//枯萎灌木
	int ChunkReeds;							//甘蔗
	int ChunkCactus;						//仙人掌
	int ChunkMushroom;						//蘑菇
	int ChunkRedPoisonousMushroom;			//蘑菇
	int ChunkWaterlily;						//荷叶
	int ChunkLotusFlower;					//荷花
	int ChunkWaterweed;						//水藻
	int ChunkDuckweed;						//浮萍
	int ChunkFloatingFlower;				//漂浮的花瓣
	int ChunkFloatingplank;					//漂浮的木板
	int ChunkCrystal;
	int ChunkNest;
	int ChunkPile;
	int ChunkShoreflower;                   //彼岸花
	int ChunkPineapple;                     //彼岸菠萝
    int ChunkRainbowGrass;					//星球-荧光彩虹草
    int ChunkSeaPlants[MAX_BIOME_SEAPLANT];	//海洋植物(ChunkSeaPlants):
	int ChunkSeaPlantNum[MAX_BIOME_SEAPLANT];
	int ChunkJars[MAX_BIOME_JARS];
	int ChunkJarNum[MAX_BIOME_JARS];
    int ChunkThicket;						//灌木丛
    int ChunkStarStation;					//星站
    int ChunkCotton;						//棉花树
	int ChunkJaggedFern[MAX_JAGGED_FERN];	//刺球
	int ChunkJaggedFernNum[MAX_JAGGED_FERN];
	int ChunkGiantScallops;					// 巨型扇贝
	int ChunkWitheredThicket;
	int ChunkSnowLotus;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	int parentId;
	int copyId;
	int geneRate;
	int skyboxType;
	int	biomeMonster[MAX_BIOME_MONSTERS]; //该地形下对应的生物,暂定为48
	unsigned short biomeMonsterNum[MAX_BIOME_MONSTERS]; //该地形下对应的生物数量

	int mapId;
	int BiomeGroupID;
	int Temperature[2];
	float CausticsPower;

	unsigned short WaterStorageCollectOnceTime;
	unsigned short WaterStorageCollectOnceVolume;

	BiomeDef(): ID(0), MinHeight(0), MaxHeight(0), Heat(0), Humid(0), FillBlock(0), FillDepth(0), TopBlock(0),
	            WaterColor(0),
	            GrassColor(0),
	            EnableRain(false),
	            EnableSnow(false),
	            ChunkTrees(0),
	            ChunkPumpkin(0),
	            ChunkWatermelon(0),
	            ChunkDeadBush(0),
	            ChunkReeds(0),
	            ChunkCactus(0),
	            ChunkMushroom(0), ChunkRedPoisonousMushroom(0),
	            ChunkWaterlily(0), ChunkDuckweed(0), ChunkWaterweed(0), ChunkLotusFlower(0),
				ChunkRice(0),
				ChunkFloatingFlower(0),ChunkFloatingplank(0),
	            ChunkCrystal(0),
	            ChunkNest(0),
	            ChunkPile(0),
				ChunkShoreflower(0),
				ChunkPineapple(0),
	            ChunkRainbowGrass(0),
				ChunkThicket(0),
				ChunkStarStation(0),
	            gamemod(NULL),
	            parentId(0), copyId(0),
	            geneRate(0),
	            mapId(0),
				ClearModulus(0.0f),
				RainModulus(0.0f),
				ThunderModulus(0.0f),
				ChunkCotton(0), skyboxType(0),
				ChunkWitheredThicket(0),
				ChunkSnowLotus(0),
				WaterStorageCollectOnceTime(0),
				WaterStorageCollectOnceVolume(0)
	{
	}
	void copy(const BiomeDef* tmpdef)
	{
		if (tmpdef == NULL) return;

		BiomeDef* def = this;
		def->ID = tmpdef->ID;
		def->Name = tmpdef->Name;
		def->MinHeight = tmpdef->MinHeight;
		def->MaxHeight = tmpdef->MaxHeight;
		def->Heat = tmpdef->Heat;
		def->Humid = tmpdef->Humid;

		def->FillBlock = tmpdef->FillBlock;
		def->FillDepth = tmpdef->FillDepth;
		def->TopBlock = tmpdef->TopBlock;
		def->WaterColor = tmpdef->WaterColor;
		def->GrassColor = tmpdef->GrassColor;
		std::memcpy(def->WaterFogColor, tmpdef->WaterFogColor, MAX_BIOME_WATER_FOG * sizeof(unsigned int));
		std::memcpy(def->WaterFogDepth, tmpdef->WaterFogDepth, MAX_BIOME_WATER_FOG * sizeof(int));
		def->EnableRain = tmpdef->EnableRain;
		def->EnableSnow = tmpdef->EnableSnow;
		def->ClearModulus = tmpdef->ClearModulus;
		def->RainModulus = tmpdef->RainModulus;
		def->ThunderModulus = tmpdef->ThunderModulus;

		def->ChunkTrees = tmpdef->ChunkTrees;
		std::memcpy(def->ChunkGrass, tmpdef->ChunkGrass, MAX_BIOME_GRASS * sizeof(int));
		std::memcpy(def->ChunkGrassNum, tmpdef->ChunkGrassNum, MAX_BIOME_GRASS * sizeof(int));
		std::memcpy(def->ChunkFlowers, tmpdef->ChunkFlowers, MAX_BIOME_FLOWER * sizeof(int));
		std::memcpy(def->ChunkFlowerNum, tmpdef->ChunkFlowerNum, MAX_BIOME_FLOWER * sizeof(int));
		std::memcpy(def->ChunkTreex, tmpdef->ChunkTreex, MAX_BIOME_TREE_GEN * sizeof(int));
		std::memcpy(def->ChunkTreeNum, tmpdef->ChunkTreeNum, MAX_BIOME_TREE_GEN * sizeof(int));

		std::memcpy(def->ChunkCorals, tmpdef->ChunkCorals, MAX_BIOME_CORAL * sizeof(int));
		std::memcpy(def->ChunkCoralNum, tmpdef->ChunkCoralNum, MAX_BIOME_CORAL * sizeof(int));

		std::memcpy(def->ChunkNewCorals, tmpdef->ChunkNewCorals, MAX_BIOME_NEWCORAL * sizeof(int));
		std::memcpy(def->ChunkNewCoralNum, tmpdef->ChunkNewCoralNum, MAX_BIOME_NEWCORAL * sizeof(int));

		def->ChunkNewCoralsCluster = tmpdef->ChunkNewCoralsCluster;
		def->ChunkNewCoralsDeathCluster = tmpdef->ChunkNewCoralsDeathCluster;
		def->ChunkPumpkin = tmpdef->ChunkPumpkin;
		def->ChunkWatermelon = tmpdef->ChunkWatermelon;
		def->ChunkDeadBush = tmpdef->ChunkDeadBush;
		def->ChunkReeds = tmpdef->ChunkReeds;
		def->ChunkCactus = tmpdef->ChunkCactus;
		def->ChunkMushroom = tmpdef->ChunkMushroom;
		def->ChunkWaterlily = tmpdef->ChunkWaterlily;
		def->ChunkDuckweed = tmpdef->ChunkDuckweed;
		def->ChunkCrystal = tmpdef->ChunkCrystal;
		def->ChunkNest = tmpdef->ChunkNest;
		def->ChunkPile = tmpdef->ChunkPile;
		def->ChunkShoreflower = tmpdef->ChunkShoreflower;
		def->ChunkPineapple = tmpdef->ChunkPineapple;
		def->ChunkRainbowGrass = tmpdef->ChunkRainbowGrass;
		std::memcpy(def->ChunkSeaPlants, tmpdef->ChunkSeaPlants, MAX_BIOME_SEAPLANT * sizeof(int));
		std::memcpy(def->ChunkSeaPlantNum, tmpdef->ChunkSeaPlantNum, MAX_BIOME_SEAPLANT * sizeof(int));
		std::memcpy(def->ChunkJars, tmpdef->ChunkJars, MAX_BIOME_JARS * sizeof(int));
		std::memcpy(def->ChunkJarNum, tmpdef->ChunkJarNum, MAX_BIOME_JARS * sizeof(int));
		def->ChunkThicket = tmpdef->ChunkThicket;
		def->ChunkStarStation = tmpdef->ChunkStarStation;
		def->ChunkCotton = tmpdef->ChunkCotton;
		std::memcpy(def->ChunkJaggedFern, tmpdef->ChunkJaggedFern, MAX_JAGGED_FERN * sizeof(int));
		std::memcpy(def->ChunkJaggedFernNum, tmpdef->ChunkJaggedFernNum, MAX_JAGGED_FERN * sizeof(int));
		def->ChunkGiantScallops = tmpdef->ChunkGiantScallops;
		def->ChunkWitheredThicket = tmpdef->ChunkWitheredThicket;
		def->ChunkSnowLotus = tmpdef->ChunkSnowLotus;
		def->gamemod = tmpdef->gamemod;
		def->ModDescInfo.copy(tmpdef->ModDescInfo);
		def->parentId = tmpdef->parentId;
		def->copyId = tmpdef->copyId;
		def->geneRate = tmpdef->geneRate;
		def->skyboxType = tmpdef->skyboxType;
		std::memcpy(def->biomeMonster, tmpdef->biomeMonster, MAX_BIOME_MONSTERS * sizeof(int));
		std::memcpy(def->biomeMonsterNum, tmpdef->biomeMonsterNum, MAX_BIOME_MONSTERS * sizeof(unsigned short));

		def->mapId = tmpdef->mapId;
		def->BiomeGroupID = tmpdef->BiomeGroupID;
		std::memcpy(def->Temperature, tmpdef->Temperature, 2 * sizeof(int));
		def->WaterStorageCollectOnceTime = tmpdef->WaterStorageCollectOnceTime;
		def->WaterStorageCollectOnceVolume = tmpdef->WaterStorageCollectOnceVolume;
	}
};

struct PlanetBiomeGeneDef
{

	int bottom_base_height_y ;
	int bottom_base_max_height_y;
	int airland_min_height;
	int airland_base_height_y ;
	int airland_noise_data;
	int bottom_noise_data; 
	int airland_bottom_noise_data;
	int totem_chunk_offset;
	int totem_chunk_trynum;
	int totem_oxygen_fruit_prob;
	int commicshop_chunk_offset;
	int commicshop_chunk_trynum;
	int commicshop_min_height;

	PlanetBiomeGeneDef(): bottom_base_height_y(30), bottom_base_max_height_y(45), airland_min_height(6),
	                      airland_base_height_y(57), airland_noise_data(10), bottom_noise_data(30),
	                      airland_bottom_noise_data(32), totem_chunk_offset(8), totem_chunk_trynum(3),
	                      totem_oxygen_fruit_prob(0), commicshop_chunk_offset(5), commicshop_chunk_trynum(2),
	                      commicshop_min_height(47)
	{
	}
};


enum BuildReplaceType
{
	genMonster = 0, //怪物
	genContainer = 1, //箱子
	genHandleByScript = 2, //生成不处理, 发触发器事件, 由业务处理
	genZombile = 3, //特殊的,专门给刷僵尸用的
	genItem = 4, //生成物品
	replaceBlock = 5, // 替换群落顶层方块
	genRandomNpc = 6, // 随机npc
	genRadiationSource = 7, // 辐射源
	genVehicle = 8, // 车辆
};

struct BuildReplaceDef
{
	int BlockId;
	BuildReplaceType ReplaceType;
	int TotalWeight;
	// id权重
	std::map<int, int> ReplaceIds;
};


struct BiomePlantTryDef
{
	int Trees;
	int Grass[MAX_BIOME_GRASS];
	int GrassNum[MAX_BIOME_GRASS];
	int Corals[8];
	int CoralNum[8];
	int NewCorals[8];
	int NewCoralNum[8];
	int SeaPlants[4];
	int SeaPlantNum[4];
	int Flowers[MAX_BIOME_FLOWER];
	int FlowerNum[MAX_BIOME_FLOWER];
	int Pumpkin;
	int Watermelon;
	int DeadBush;
	int Reeds;
	int Rice;
	int Cactus;
	int Pineapple;
	int Shoreflower;
	int Mushroom;
	int RedPoisonousMushroom;
	int BigMushroom;
	int Crystal;
	int Jars[4];
	int JarNum[4];
	int Cotton;
	int giantScallops;
	int NewCoralCluster;
	int NewCoralDeathCluster;
	int JaggedFern[1];
	int JaggedFernNum[1];
	int WitheredThicket;

	int Waterlily;
	int Duckweed;
	int Waterweed;
	int LotusFlower;
	int FloatingFlower;
	int Floatingplank;
};

struct OreDef
{
	int Index;
	int ID;   //id + (mapid << 16)
	int MinHeight;
	int MaxHeight;
	int MinFalloff;
	int MaxFalloff;
	int GenMethod;
	int TryGenCount; //一个区块尝试产生次数 Lode
	int MaxVeinBlocks; //一个矿脉最大多少块 MaxNum
	int ReplaceBlock;
	int emGenbiome[MAX_BIOME_TYPE];	//无效为BIOME_INVALID

	MNSandbox::Object* gamemod;
	int copyId;	

	OreDef(): Index(0), ID(0), MinHeight(0), MaxHeight(0), MinFalloff(0), MaxFalloff(0), GenMethod(0), TryGenCount(0), MaxVeinBlocks(0),
	          ReplaceBlock(0),
	          gamemod(0),
	          copyId(0)
	{
		for(int i=0; i<MAX_BIOME_TYPE; i++)
		{
			emGenbiome[i] = BIOME_INVALID;
		}
	}
};

struct DropItemDef
{
	int item;	// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
	int odds;

	DropItemDef()
		: item(0),odds(0)
	{

	}
};

struct SkinningToolDef
{
	int ToolID;
	float Efficiency;
};

struct ItemDef;
typedef unsigned int _uint32;

#define MAX_TOOLMINE_DROP   2
#define MAX_GROWTH_TIME   7
struct ModBlockDef
{
	std::vector<std::string> m_UsedKeys;
	std::unordered_map<std::string, unsigned char> m_mKeyMap;
	std::vector<std::string> m_unCheckKeys;
	void copy(ModBlockDef* data)
	{
		m_UsedKeys.clear();
// 		m_UsedKeys.assign(data->m_UsedKeys.begin(), data->m_UsedKeys.end());
		m_UsedKeys = data->m_UsedKeys;
		m_mKeyMap.clear();
		m_mKeyMap = data->m_mKeyMap;
		m_unCheckKeys.clear();
		m_unCheckKeys = data->m_unCheckKeys;
	}
};
struct BlockDef
{
	unsigned int ID;
	int EditType;
	int PlaceDir;
	int ClickCollide;
	int MoveCollide;
	int PhyCollide;
	int BlockFlow;
	int PushFlag;
	int GravityEffect;
	int Replaceable;
	int AntiExplode;
	int MaxHP;  // 血量 最大值255
	float Hardness;
	float Slipperiness;
    int BurnSpeed; //燃烧速度,  也会影响烧到相邻块的概率
    int CatchFire; //被相邻块点燃的概率
	int PowerState;
	int CoverNeighbor;
	int LightAtten; //光照穿过衰减, 15表示不透明
	int LightSrc; //此block作为光源的强度
	int UseNeighborLight; //使用旁边方块的光照
	int Height; //
	int TickPeriod;
	int SampleMode; //采样方式，暂时先用在手持模型的采样模式 -- rivershen 20170308
    DropItemDef ToolMineDrops[MAX_TOOLMINE_DROP];	// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
    DropItemDef HandMineDrops;						// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
	int PreciseDrop;
	int DropExp;
	int DropExpOdds;
	int MineTool;
	int ToolLevel;
    unsigned int MiniColor; //小地图颜色
	Rainbow::ColorRGBA32 RandomColors[MAX_BIOME_LEAVES_COLOR];
	int BlockSize[3];
    float Score;				//极限模式得分
	int TextureGroup;
	int UserData[1];
	int CorrodeSpeed; //毒液侵蚀速度
	bool Breakable;	// 是否被投射物击碎
    int TriggerType;	//触发器素材库分类
    int TerrainEditor;  //地形编辑素材库分类
	Rainbow::FixedString Name;
	Rainbow::FixedString EnglishName;

	Rainbow::FixedString Type;
	Rainbow::FixedString Texture1;
	Rainbow::FixedString Texture2;
	Rainbow::FixedString WalkSound;
	Rainbow::FixedString DigSound;
	Rainbow::FixedString PlaceSound;
	Rainbow::FixedString EffectName;
	int ModelType;	// 方块预制使用
	int BluePrintFlag;
	int ConvertID;
	char CustommodelAlpha;
	char CustommodelLight;
	bool MultiGridsBlockType;

	MNSandbox::Object* gamemod;

	int CopyID;
	bool IsTemplate;
	int ModelIndex;
	short SrcID;  //现在使用的模型对应的道具id

	//多语言
	Rainbow::FixedString MultiLangName;
	Rainbow::FixedString MultiLangDesc;

	short TempSrc;// 温度强弱 -15~15
	short TempAtten;// 温度衰减 0~15
	char GrowthTempRange;//植物生长温度区间
	int EnablePlaceSnow;//是否可以放置雪
	short GrowthTimeNum;
	short GrowthTime[MAX_GROWTH_TIME];
	short CropsSign;
	float Tenacity; //韧度值

	ModBlockDef* m_pModBlockDef;
	char ModContainerType;
	char ProtectZoneDestroy;

	unsigned short LOD;
	bool SupportCastShadow;
	BlockDef()
		: ID(0), EditType(0), PlaceDir(0), ClickCollide(0), MoveCollide(0), PhyCollide(0), BlockFlow(0), PushFlag(0),
		  GravityEffect(0),
		  Replaceable(0),
		  AntiExplode(0),
		  Hardness(0), Slipperiness(0), BurnSpeed(0), CatchFire(0),
		  PowerState(0), CoverNeighbor(0), LightAtten(0), LightSrc(0), UseNeighborLight(0), Height(0), TickPeriod(0),
		  SampleMode(0), PreciseDrop(0), DropExp(0), DropExpOdds(0),
		  MineTool(0), ToolLevel(0), MiniColor(0), Score(0), TextureGroup(0),  Breakable(false),
		  TriggerType(0),
		  TerrainEditor(0), EnglishName(""), Type(""), Texture1(""), Texture2(""),
		  BluePrintFlag(0),
		  ConvertID(0),
		  CustommodelAlpha(0),
		  CustommodelLight(0),
		  gamemod(NULL),
		  CopyID(0), GrowthTempRange(0),  TempSrc(0), TempAtten(0),
		  IsTemplate(false), ModelIndex(0), SrcID(-1), WalkSound(""), DigSound(""), PlaceSound(""), EffectName(""),EnablePlaceSnow(0),
		  CropsSign(0),GrowthTimeNum(0), Tenacity(0), ModelType(4), m_pModBlockDef(NULL),
		LOD(0), SupportCastShadow(true), ModContainerType(0), ProtectZoneDestroy(0)
	{
		BlockSize[0] = 1;
		BlockSize[1] = 1;
		BlockSize[2] = 1;
	}


	~BlockDef()
	{
		if (m_pModBlockDef) { delete(m_pModBlockDef); m_pModBlockDef = NULL; }
		if (MINIW::ScriptVM::game())
			tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "BlockDef");
	}

	void copy(const BlockDef* templateBlockDef) {
		//BlockDef* def = new BlockDef();
		//if (id > m_MaxBlockID) m_MaxBlockID = id;
		if (!templateBlockDef)
		{
			return;
		}
		BlockDef* def = this;
		def->CopyID = templateBlockDef->CopyID;
		def->gamemod = templateBlockDef->gamemod;

		def->ID = templateBlockDef->ID;
		def->EditType = templateBlockDef->EditType;
		def->ModelIndex = templateBlockDef->ModelIndex;
		def->PlaceDir = templateBlockDef->PlaceDir;
		def->Replaceable = templateBlockDef->Replaceable;
		def->ClickCollide = templateBlockDef->ClickCollide;
		def->MoveCollide = templateBlockDef->MoveCollide;
		def->BlockFlow = templateBlockDef->BlockFlow;
		def->PushFlag = templateBlockDef->PushFlag;
		def->GravityEffect = templateBlockDef->GravityEffect;
		def->AntiExplode = templateBlockDef->AntiExplode;
		def->Hardness = templateBlockDef->Hardness;
		def->Slipperiness = templateBlockDef->Slipperiness;
		def->BurnSpeed = templateBlockDef->BurnSpeed;
		def->CatchFire = templateBlockDef->CatchFire;
		def->PowerState = templateBlockDef->PowerState;
		def->CoverNeighbor = templateBlockDef->CoverNeighbor;
		def->LightAtten = templateBlockDef->LightAtten;
		def->LightSrc = templateBlockDef->LightSrc;
		def->UseNeighborLight = templateBlockDef->UseNeighborLight;
		def->Height = templateBlockDef->Height;
		def->IsTemplate = templateBlockDef->IsTemplate;
		def->ToolLevel = templateBlockDef->ToolLevel;
		def->Breakable = templateBlockDef->Breakable;
		def->SampleMode = templateBlockDef->SampleMode;
		def->TickPeriod = templateBlockDef->TickPeriod;
		for (int d = 0; d < MAX_TOOLMINE_DROP; d++)
		{
			def->ToolMineDrops[d].item = templateBlockDef->ToolMineDrops[d].item;
			def->ToolMineDrops[d].odds = templateBlockDef->ToolMineDrops[d].odds;
		}

		def->HandMineDrops.item = templateBlockDef->HandMineDrops.item;
		def->HandMineDrops.odds = templateBlockDef->HandMineDrops.odds;

		def->PreciseDrop = templateBlockDef->PreciseDrop;
		def->MineTool = templateBlockDef->MineTool;

		def->DropExp = templateBlockDef->DropExp;
		def->DropExpOdds = templateBlockDef->DropExpOdds;

		def->MiniColor = templateBlockDef->MiniColor;

		def->Score = templateBlockDef->Score;
		def->TextureGroup = templateBlockDef->TextureGroup;
		def->UserData[0] = templateBlockDef->UserData[0];
		def->Breakable = templateBlockDef->Breakable;
		def->TriggerType = templateBlockDef->TriggerType;
		def->TerrainEditor = templateBlockDef->TerrainEditor;

		def->Name = templateBlockDef->Name;
		def->EnglishName = templateBlockDef->EnglishName;
		//MyStringCpy(def->Type, sizeof(def->Type), templateBlockDef->Type);
		//MyStringCpy(def->Texture1.c_str(), sizeof(def->Texture1.c_str()), templateBlockDef->Texture1.c_str());
		//MyStringCpy(def->Texture2, sizeof(def->Texture2), templateBlockDef->Texture2);
		def->Type = templateBlockDef->Type;
		def->Texture1 = templateBlockDef->Texture1;
		def->Texture2 = templateBlockDef->Texture2;
		def->WalkSound = templateBlockDef->WalkSound;
		def->DigSound = templateBlockDef->DigSound;
		def->PlaceSound = templateBlockDef->PlaceSound;
		def->EffectName = templateBlockDef->EffectName;
		def->BluePrintFlag = templateBlockDef->BluePrintFlag;
		def->ConvertID = templateBlockDef->ConvertID;
		def->CustommodelAlpha = templateBlockDef->CustommodelAlpha;
		def->CustommodelLight = templateBlockDef->CustommodelLight;
		def->gamemod = templateBlockDef->gamemod;
		def->CopyID = templateBlockDef->CopyID;
		def->IsTemplate = templateBlockDef->IsTemplate;
		def->ModelIndex = templateBlockDef->ModelIndex;
		def->SrcID = templateBlockDef->SrcID;
		def->MultiLangName = templateBlockDef->MultiLangName;
		def->MultiLangDesc = templateBlockDef->MultiLangDesc;
		def->GrowthTempRange = templateBlockDef->GrowthTempRange;
		def->TempAtten = templateBlockDef->TempAtten;
		def->TempSrc = templateBlockDef->TempSrc;
		def->EnablePlaceSnow = templateBlockDef->EnablePlaceSnow;
		def->CropsSign = templateBlockDef->CropsSign;
		def->GrowthTimeNum = templateBlockDef->GrowthTimeNum;
		def->ModelType = templateBlockDef->ModelType;
		for (int d = 0; d < MAX_GROWTH_TIME; d++)
		{
			def->GrowthTime[d] = templateBlockDef->GrowthTime[d];
		}

		if (templateBlockDef->m_pModBlockDef)
		{
			if (!m_pModBlockDef)
			{
				m_pModBlockDef = new ModBlockDef;
			}
			m_pModBlockDef->copy(templateBlockDef->m_pModBlockDef);
		}
		else
		{
			if (m_pModBlockDef)
			{
				delete(m_pModBlockDef);
				m_pModBlockDef = NULL;
			}
		}

		def->LOD = templateBlockDef->LOD;
		def->SupportCastShadow = templateBlockDef->SupportCastShadow;
		def->ModContainerType = templateBlockDef->ModContainerType;
	}
	void clearAllDataExKey()
	{
		if (m_pModBlockDef)
		{
			m_pModBlockDef->m_UsedKeys.clear();
			m_pModBlockDef->m_mKeyMap.clear();
			m_pModBlockDef->m_unCheckKeys.clear();
		}
	}
	char addOneDataExKey(const std::string& key)
	{
		if (!m_pModBlockDef)
		{
			m_pModBlockDef = new ModBlockDef;
		}

		auto iter = m_pModBlockDef->m_mKeyMap.find(key);
		if (iter != m_pModBlockDef->m_mKeyMap.end())
		{
			for (auto iteru = m_pModBlockDef->m_unCheckKeys.begin(); iteru != m_pModBlockDef->m_unCheckKeys.end(); iteru++)
			{
				if (key.compare((*iteru)) == 0)
				{
					m_pModBlockDef->m_unCheckKeys.erase(iteru);
					break;
				}
			}
			return iter->second;
		}
		assert(m_pModBlockDef->m_UsedKeys.size() < 256);
		m_pModBlockDef->m_mKeyMap[key] = (char)m_pModBlockDef->m_UsedKeys.size();
		m_pModBlockDef->m_UsedKeys.push_back(key);
		return m_pModBlockDef->m_mKeyMap[key];
	}

	const std::string& getDataExKeyWithIdx(char idx) const
	{
		if (m_pModBlockDef)
		{
			if (idx < m_pModBlockDef->m_UsedKeys.size())
			{
				return m_pModBlockDef->m_UsedKeys[idx];
			}
		}
		return NoKey;
	}

	char getDataExKeyIdx(const std::string& key) const
	{
		if (m_pModBlockDef)
		{
			auto iter = m_pModBlockDef->m_mKeyMap.find(key);
			if (iter != m_pModBlockDef->m_mKeyMap.end())
			{
				return iter->second;
			}
		}
		return -1;
	}
	//由于地形加载load会比mod脚本加载早。先以此解决功能问题
	char getDataExKeyIdxForLoad(const std::string& key) const
	{
		int ret = getDataExKeyIdx(key);
		if (ret == -1)
		{
			ret = const_cast<BlockDef*>(this)->addOneDataExKey(key);
			m_pModBlockDef->m_unCheckKeys.push_back(key);
		}
		return ret;
	}
	const std::string& getDataExKeyWithIdxForSave(char idx) const
	{
		if (m_pModBlockDef)
		{
			if (idx < m_pModBlockDef->m_UsedKeys.size())
			{
				std::string retKey = m_pModBlockDef->m_UsedKeys[idx];
				for (auto item : m_pModBlockDef->m_unCheckKeys)
				{
					if (retKey.compare(item) == 0)
					{
						return NoKey;
					}
				}
				return m_pModBlockDef->m_UsedKeys[idx];
			}
		}
		return NoKey;
	}
};

struct BlockEffectDef 
{
	Rainbow::FixedString m_BlockTypeName;
	bool SupportLOD;
	bool SupportCastShadow;
	BlockEffectDef()
		: m_BlockTypeName("")
		, SupportLOD(true)
		, SupportCastShadow(true)
	{}
};

enum
{
	ITEM_NONE = 0,
	ITEM_BLOCK,
	ITEM_CRAFT,
	ITEM_TOOL
};

enum
{
	ITEM_USE_NORMAL = 0,  //放置方块或者不能用
	ITEM_USE_CLICKBLOCK = 1,  //点击方块调用脚本
	ITEM_USE_CLICKLIQUID = 2,  //点击液体调用脚本
	ITEM_USE_CLICKBUTTON = 3,  //点击使用按钮
	ITEM_USE_BOW = 4,     //按下使用按钮使用弓
	ITEM_USE_PRESSFOOD = 5,     //按下使用按钮吃食物
	ITEM_USE_ADVANCEDDIG = 6,  //按下使用按钮挖方块
	ITEM_USE_ACCOUNTREPO = 7, //使用帐号仓库的物品
	ITEM_USE_GUN = 8,     //点射式的枪
	ITEM_USE_CHARGETHROW = 9,     //蓄力扔出
	ITEM_USE_HOOK = 10,     //使用钩子
	ITEM_USE_PACKING_FCM_ITEM = 11, //使用微缩组合道具
	//ITEM_USE_AREATOOL = 11,	//使用区域工具
	///ITEM_USE_PRESSRPG18		= 9,	//按下使用RPG18
	//ITEM_USE_PRESSARBATOR = 5,	//按下使用按钮使连弩
	//ITEM_USE_PRESSBOMB = 6,	//按下使用炸弹
	//ITEM_USE_PRESSSLINGSHOT = 7,	//按下使用弹弓
	ITEM_USE_MUSIC_ITEM = 12, //使用音乐道具
	ITEM_USE_MUSICPU_ITEM = 13, //乐谱
	ITEM_USE_SHIELD_ITEM = 14, // 使用盾牌
	ITEM_USE_CHARGETHROW2 = 15, // 技能编辑器的蓄力扔出
	ITEM_USE_BUILDBLOCKREPAIR = 16,//建筑方块的 维修 升级
	ITEM_USE_WATER = 17,    //按下使用按钮喝水
};

// 道具分类
enum
{
	ITEM_TYPE_NULL = 0,
	ITEM_TYPE_BLOCK = 1,
	ITEM_TYPE_MISC = 2,
	ITEM_TYPE_TOOL = 3,
    ITEM_TYPE_GROUP = 4,	// 没用
    ITEM_TYPE_MONSTER = 5,	// 没用
	ITEM_TYPE_ACCOUNT = 6,
	ITEM_TYPE_BOW = 7,
	ITEM_TYPE_PROJECTILE = 8,
	ITEM_TYPE_EQUIP = 9,
	ITEM_TYPE_GUN = 10,
	ITEM_TYPE_FOOD = 11,
    ITEM_TYPE_TOOL_PROJECTILE = 12,	// 工具+投射物
    ITEM_TYPE_TOOL_FOOD = 13,		// 工具+食物
    ITEM_TYPE_BLOCK_FOOD = 14,		// 方块+食物
    ITEM_TYPE_PACK = 15,		// 包裹（礼包）
    ITEM_TYPE_VEHICLE = 16,		// 物理机械工具
    ITEM_TYPE_PASSPORT = 17,		// 地图通行证
	ITEM_TYPE_MUSIC = 18,

	ITEM_TYPE_BUILDING_BLOCK = 29, //建筑方块
	ITEM_TYPE_MOD = 10000,		//mod里的特殊道具（例如：渔币）
};

//道具模型分类
enum
{
    ICON_GEN_MESH,			// 图标生成的MESH
    OMOD_GEN_MESH,			// 模型文件生成的MESH
    BLOCK_GEN_MESH,			// 方块生成的MESH
    CUSTOM_GEN_MESH = 4,	// 微雕模型生成的MESH
    VEHICLE_GEN_MESH = 5,	// 物理机械生成的MESH
    FULLY_CUSTOM_GEN_MESH = 6,		// 完全自定义模型生成的mesh
    IMPORT_MODEL_GEN_MESH = 7,		//导入模型生成的mesh
	UGC_MODEL_GEN_MESH = 8,		//ugcModel生成Mesh
	PREFAB_GEN_MESH = 9,  //PREFAB文件生成的MESH
};

enum
{
    CUSTOM_HEAD,			//自定义生物模型：头
    CUSTOM_CHEST,			//自定义生物模型：胸部
    CUSTOM_BELLY,			//自定义生物模型：腹部
    CUSTOM_L_UPPERARM,		//自定义生物模型：左上臂
    CUSTOM_L_FOREARM,		//自定义生物模型：左前臂
    CUSTOM_L_HAND,			//自定义生物模型：左手
    CUSTOM_R_UPPERARM,		//自定义生物模型：右上臂
    CUSTOM_R_FOREARM,		//自定义生物模型：右前臂
    CUSTOM_R_HAND,			//自定义生物模型：右手
    CUSTOM_L_THIGH,			//自定义生物模型：左大腿
    CUSTOM_L_SHANK,			//自定义生物模型：左小腿
    CUSTOM_L_FOOT,			//自定义生物模型：左脚
    CUSTOM_R_THIGH,			//自定义生物模型：右大腿
    CUSTOM_R_SHANK,			//自定义生物模型：右小腿
    CUSTOM_R_FOOT,			//自定义生物模型：右脚
};

//对话配置分类
enum NpcPlotDefUseType
{
	NONE_NPCPLPTUSETYPE = 0,	
    TEMPLATE_NPCPLPTUSETYPE = 1,	//作为模板
    DIRECT_NPCPLPTUSETYPE = 2,		//功能直接生效
};

struct CustomItemData
{
	int itemid;
	Rainbow::FixedString modelfilename;
	int			folderindex;	//文件夹索引
	Rainbow::FixedString  classname;  //文件夹名（废弃）
	int type;//fcm保存的类型
	//std::map<int, std::string> avatarfilenames;
	short involvedid;		//关联id,例如生物对应的生物蛋id

	CustomItemData() : itemid(0), modelfilename(""), folderindex(0), classname("default"), type(0), involvedid(0)
	{

	}
};

//效果
//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct EffectInfo
{
	int CopyID;//效果配置ID，用来读取效果表
	double Value[MAX_BUFF_ATTRIBS];//每条效果最多有5个UI控件
	char endSet;  /****************************************memcpy 分界线********************************************/

	jsonxx::Object customScriptEffect;

	EffectInfo() :CopyID(0), endSet(0) {
		for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
			Value[i] = -1000000.0;
		}
		customScriptEffect.reset();
	}
	EffectInfo(const EffectInfo& def)
	{
		Memcpy(this, &def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
		customScriptEffect.reset();
	}

	void Clear() {
		CopyID = 0;
		memset(Value, 0, sizeof(Value));
		customScriptEffect.reset();
	}
};

// 用来把EffectInfo 的 customScriptEffect打包发送给Lua
struct CustomScriptEffectArr
{
	std::vector<jsonxx::Object*> arrCustomScriptEffect;
	int Size()
	{
		return (int)arrCustomScriptEffect.size();
	}
	jsonxx::Object* Get(int index)
	{
		return arrCustomScriptEffect[index];
	}
};

//装备部件
//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
#define MAX_EQUIPMENTPART_COUNT 3	//一个装备最多3个部件
struct EquipmentPartDef {
	int nAnchorID;
    int RelevantID;		//相关联的道具id
    int MeshType;		//枚举'CUSTOM_GEN_MESH', 区分是否是微缩模型
    int resClass;		//枚举'RES_MODEL_CLASS', 'EQUIP_MODEL_CLASS'
	int x;
	int y;
	int z;
	int yaw;
	int pitch;
	int roll;
	float scaleX;
	float scaleY;
	float scaleZ;

	char endSet;  /****************************************memcpy 分界线********************************************/
	std::string Model;	//微缩模型标识

	EquipmentPartDef() : nAnchorID(101), RelevantID(0), MeshType(OMOD_GEN_MESH), resClass(0), Model(""), x(0), y(0),
	                     z(0), yaw(0), pitch(0), roll(0), scaleX(1), scaleY(1), scaleZ(1), endSet(0)
	{
	}

	EquipmentPartDef(const EquipmentPartDef& def)
	{
		Memcpy(this, &def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
		Model = def.Model;
	}
};

//装备
//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
#define MAX_EQUIP_EFFECT_COUNT 10	//一个装备最多挂几条效果
struct ItemEquipDef {
    int nSlotType;	//装备类型(8,9,10,11,16), 对应穿戴位置'EQUIP_SLOT_TYPE'
    int nPartCount;	//拥有部件数量

	char endSet;  /****************************************memcpy 分界线********************************************/
    EquipmentPartDef partDef[MAX_EQUIPMENTPART_COUNT];	//部件列表
    EffectInfo EffInfo[MAX_EQUIP_EFFECT_COUNT];			//效果列表

	EntryControl entryControl; //枪械、装备词条控制

	ItemEquipDef() {
		nSlotType = 8;
		nPartCount = 3;
		endSet = 0;
	}

	ItemEquipDef(const ItemEquipDef& def)
	{
		copy(&def);
	}

	void copy(const ItemEquipDef* def)
	{
		if (!def)
			return;

		Memcpy(this, def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
		for (size_t i = 0; i < MAX_EQUIP_EFFECT_COUNT; i++)
		{
			EffInfo[i] = def->EffInfo[i];
		}
		for (size_t i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
		{
			partDef[i] = def->partDef[i];
		}
	}
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct ItemDef
{
	/****************************************memcpy 初始化begin, 只能添加基本类型********************************************/
	unsigned int ID;
	int Quality;
	int Chip;
	int ResType;
	int OriginEditType;
	int EditType;
	bool IsTemplate;
	int DropType;
	int MeshType;
	int InvolvedID;
	int UnlockType;
	int UnlockFlag;
	int ReplaceID;
	int ChipPrice;
	int ChipNum;
	int CondUnlcokType;
	int FilterType;
	int Type;				// 道具大类
	int CreateType;
	int SortId;
	int IconEffect;
	int TextureID;
	int UseTarget;
	float WieldScale;
	float ThirdPersonScale;
	float DropScale;
	int WieldPeriod; //挥舞周期
	int StackMax; //最大堆叠数目
	int Usable; //可以右键使用
	int Range; //操作范围
	int ItemGroup;
	int EnchantTag;
	int StuffType;
	int EnchantAfterID;
	int CanExtract;
	int CoolDown;
	float Score;
	int CostFoodLevel;
	float HandEffectScale;
	int PriorityBag;
	int ShowType;		//外显类道具分类1-皮肤，2-坐骑，3-avatar，4-皮肤碎片，5-坐骑碎片，非外显类道具空着不填
	int ShowId;			//外显类道具关联的ID 皮肤/坐骑/avatarID，非外显类道具空着不填
	//2024-09-10 code by:dyy 用来在特殊地形替换技能
	unsigned int SkillSpecialBiome; //会切换技能的特殊地形（到这个地形之后，AddSkills不生效，转而使用AddBiomeSkill）
	Rainbow::FixedString  AddBiomeSkill; //会切换技能的特殊地形（到这个地形之后，AddSkills不生效，转而使用AddBiomeSkill）
    //------------------------------------- 以上为CSV里的字段
    //20210924 codeby：fym item表新增字段ShowQuality需求
	int ShowQuality;    //用于区分商业化道具的品质 0-普通，1-稀有，2-珍贵，3-传说 
	//2023/04/13 codeby:fym item表新增道具品质
	int QualityLevel;  // 商业化道具品质背景图 品质等级（1=浅灰色,2=绿色，3=蓝色，4=紫色，5=橙色，6=红色，7=白金）
	int EXSortId; //用于高级创造排序【高级创造背包/高级资源列表/资源库，同分类下调整排序用，数字小的在前面，数字相同id小的在前面】

	//mod 专用
	bool IsDefTool;
	bool IsDefFood;
	bool IsDefProjectile;
	bool IsDefGun;
	bool IsDefBow;
	bool IsDefEquip;
	bool IsDefCustomGun;
	int CopyID;
	int iPackID;//包裹ID 用来关联包裹配置 PackGiftDef 的
	int TriggerType;	//触发器素材库分类
	int iOrignID;
	int ScriptMasking; // 触发器屏蔽创建方块或道具
	char HandEffect[MAX_BLOCK_NAME];
	char endSet;  /****************************************memcpy 分界线********************************************/
	Rainbow::FixedString Name;
	Rainbow::FixedString GainWay;
	Rainbow::FixedString Desc;
	Rainbow::FixedString Icon;
	Rainbow::FixedString UseScript;
	Rainbow::FixedString UseScript2;	//开垦完调用
	Rainbow::FixedString EnglishName;
	Rainbow::FixedString EmitScript;
	Rainbow::FixedString GetWay; // 获取方式
	Rainbow::FixedString UnlockWay; // 解锁途径(获取stringid，根据id索引stringdef表）
	Rainbow::FixedString Model;
	Rainbow::FixedString Texture;
	//多语言
	Rainbow::FixedString MultiLangName;
	Rainbow::FixedString MultiLangDesc;
	Rainbow::FixedString para;	//soc新增的杂项参数
	Rainbow::FixedString MaintenanceProps; //建筑腐蚀维护参数
	std::map<_uint32, Rainbow::FixedString> ForeignId2Key;			// 外部分配的ID到key的对应关系
	std::vector<int> ClassificationType; //背包 子类型
	std::vector<Rainbow::FixedString> Addskills;
	Rainbow::FixedString TypeDesc;
	Rainbow::FixedString Version;
	unsigned int Crc;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	std::vector<int> SkillID;
	Rainbow::FixedString ModelComp;//模型组件str
	ItemDef()
	{
		EnglishName = Rainbow::FixedString("");

		IsDefTool = false;
		IsDefProjectile = false;
		IsDefGun = false;
		IsDefFood = false;
		IsDefBow = false;
		IsDefEquip = false;
		IsDefCustomGun = false;
		CopyID = 0;
		gamemod = nullptr;
		endSet = 0;
	}

	~ItemDef()
	{
		if(MINIW::ScriptVM::game())
			tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "ItemDef");
	}

	int getSkinIDNum()
	{
		size_t size = SkillID.size();
		return static_cast<int>(size);
	}

	int getSkinID(int index)
	{
		return SkillID[index];
	}
	void pushSkillID(int id)
	{
		SkillID.push_back(id);
	}
	void clearSkill()
	{
		SkillID.clear();
	}
	int getClassificationTypeNum()
	{
		size_t size = ClassificationType.size();
		return static_cast<int>(size);
	}

	int getClassificationType(int index)
	{
		return ClassificationType[index];
	}

	void setForeignId2Key(_uint32 key,const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str.c_str();
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, Rainbow::FixedString>(key, str.c_str()));
		}
	}

	ItemDef(const ItemDef& def)
	{
		copy(&def);
	}

	void copy(const ItemDef* itemDef)
	{
		if (!itemDef)
			return;

		Memcpy(this, itemDef, (char*)&(this->endSet) - (char*)this);

		const ItemDef& def = *itemDef;
		endSet = 0;
		Name = def.Name;
		GainWay = def.GainWay;
		Desc = def.Desc;
		Icon = def.Icon;
		UseScript = def.UseScript;
		UseScript2 = def.UseScript2;	//开垦完调用
		EnglishName = def.EnglishName;
		EmitScript = def.EmitScript;
		GetWay = def.GetWay; // 获取方式
		UnlockWay = def.UnlockWay; // 解锁途径(获取stringid，根据id索引stringdef表）
		Model = def.Model; 
		Texture = def.Texture;
		MultiLangName = def.MultiLangName;
		MultiLangDesc = def.MultiLangDesc;
		TypeDesc = def.TypeDesc;
		Version = def.Version;
		//ForeignId2Key
		/*std::for_each(def.ForeignId2Key.begin(), def.ForeignId2Key.end(), [this](const std::map<_uint32, Rainbow::FixedString>::value_type& val) {
			ForeignId2Key.insert(val);
		});*/
		ForeignId2Key = def.ForeignId2Key;
		//保存整型的vector直接复制.
		ClassificationType = def.ClassificationType;
		SkillID = def.SkillID;
		//Addskills
		Addskills.resize(def.Addskills.size());
		for (int i = 0; i < def.Addskills.size(); i++)
		{
			Addskills[i] = def.Addskills[i];
		}
		para = def.para;
		Crc = def.Crc;
		gamemod = def.gamemod;
		ModDescInfo = def.ModDescInfo;
		ModelComp = def.ModelComp;

		SkillSpecialBiome = def.SkillSpecialBiome;
		AddBiomeSkill = def.AddBiomeSkill;
	}
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct ToolDef
{
	unsigned int ID;
	int Type;
	int Level;
	int AvatarPartID;	//表现关联的装扮ID
	float SkillCD;
	float Efficiency;
	short AttackType;
	float Attacks[10];	// 攻击力（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	float Armors[10];	// 防御力（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	short MoveSpeed;
	short SwimSpeed; //游泳速度
	int Duration;
	int CollectDuration;
	int AtkDuration;
	int RepairId[6];		// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
	int RepairAmount[6];
	float RepairDecresePercent; // 修炼后上限减少百分比
	float Score;
	int Enchant;
	int Tunestone;			// 新版符文石附魔
	int ConsumeID;			// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
	int ConsumeCount;
	float AccumulatorTime;
	int AccumulatorType;
	int HandDigSeq;
	int HandStartSeq;
	int HandLoopSeq;
	int HandAtkSeq;
	int ToolStartSeq;
	int ToolLoopSeq;
	int ToolAtkSeq;
	int BodyStandSeq;
	int BodyDigSeq;
	int BodyLoopSeq;
	int BodyAtkSeq;

	int PlayerIdleSeq;         // 待机
	int PlayerWalkSeq;         // 移动
	int PlayerRunSeq;          // 跑动
	int PlayerSneakSeq;        // 潜行待机
	int PlayerSneakWalkSeq;    // 潜行移动
	int PlayerJumpSeq;         // 跳跃

	int DownbodyIdleSeq;       // 控制下半身待机
	int DownbodyWalkSeq;       // 控制下半身移动
	int DownbodyRunSeq;        // 控制下半身跑动

	//char WieldSound[MAX_BLOCK_NAME];
	int SwitchModel;
	bool IsPlayerCustom;
	int	 EnchantEffectScale;
	float SpeedAdd;
	bool CanThrow;
	int SubType;
	int SubLevel;
    int Farmingspeed;		//开垦速度
    int AccumulatorExpend;		//蓄力每秒消耗
    int punchBuffId;		//自带近战buffer id
	int punchBuffV;		//自带近战buffer level
	short ViewLight;        //视野亮度增加效果
	short ResPressureLevel;	// 耐压阈值（控制水压环境中装备是否会被损坏）
	short PressDuration;// 压力消耗耐久（配合耐压阈值使用，控制水压环境中装备耐久的消耗速度）
	float TemperatureDefense;
	float AtkSpeed;			// 攻击速度倍率 
	int ComboTimer;			// 连段计时器
	int TouReduce;			// 削刃值 
	int Toughness;			// 韧性值
	float TouRecoverDelay;	// 韧性恢复延迟时间
	int TouRecoverSpeed;	// 韧性恢复速度（每秒）
	int RepelRes;			// 击退抗性
	int MarkScore;			// 装备评分（用于装备替换）
	bool IsGroup;

	char endSet;  /****************************************memcpy 分界线********************************************/
	Rainbow::FixedString Name;
	EffectInfo EffInfo[MAX_BUFF_ATTRIBS]; //效果配置
	Rainbow::FixedString AtkComboSeq[MAX_ATTACK_CONFIG]; //普攻连段动作列表
	Rainbow::FixedString SwimAtkSeq; //游泳攻击表
	Rainbow::FixedString RideAtkSeq; //骑乘攻击表
	Rainbow::FixedString BodyAtkEffect;
	Rainbow::FixedString AtkSound;
	Rainbow::FixedString FireSound;
	Rainbow::FixedString UseSound;

	ToolDef()
	{
		memset((void*)this, 0, (char*)&(this->endSet) - (char*)this);
		//TemperatureDefense = 0.0f;
	}

	ToolDef(const ToolDef& def)
	{
		TemperatureDefense = 0.0f;
		copy(&def);
	}

	void copy(const ToolDef* toolDef)
	{
		if (!toolDef)
			return;
		Memcpy(this, toolDef, (char*)&(this->endSet) - (char*)this);
		const ToolDef& def = *toolDef;
		endSet = 0;
		Name = def.Name;
		SwimAtkSeq = def.SwimAtkSeq;
		RideAtkSeq = def.RideAtkSeq;
		BodyAtkEffect = def.BodyAtkEffect;
		AtkSound = def.AtkSound;
		FireSound = def.FireSound;
		UseSound = def.UseSound;
		for (size_t i = 0; i < MAX_BUFF_ATTRIBS; i++)
		{
			EffInfo[i] = def.EffInfo[i];
		}
		for (size_t i = 0; i < MAX_ATTACK_CONFIG; i++)
		{
			AtkComboSeq[i] = def.AtkComboSeq[i];
		}
	}

	//是否是鱼竿
	bool isFishingRod() const
	{
		return Type == 29;
	}

	//FISHMOD_TODO:钓鱼mod鱼竿
	bool IsModFishRod() const {
		return Type == 31;
	}

	EQUIP_SLOT_TYPE getSlotType() const
	{
		//int slot = MAX_EQUIP_SLOTS;
		if (Type == 8) return EQUIP_HEAD;
		else if (Type == 9) return EQUIP_BREAST;
		else if (Type == 10) return EQUIP_LEGGING;
		else if (Type == 11) return EQUIP_SHOE;
		else if (Type == 16) return EQUIP_PIFENG;
		else if (Type == 6 || Type == 30) return EQUIP_WEAPON;
		else if (Type == 35) return EQUIP_HEAD_LINING;
		else if (Type == 36) return EQUIP_BREAST_LINING;
		else if (Type == 37) return EQUIP_LEGGING_LINING;
		else if (Type == 38) return EQUIP_SHOE_LINING;
		else if (Type == 39) return EQUIP_GROUP;
		return EQUIP_NONE;
	}
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct ProjectileDef
{
	unsigned int ID;
	int LocomotionType;
	float Gravity;
	float InitSpeed;
	float SpeedDecay;
	bool Pickable;
	int TriggerCondition;
	float TriggerDelay;
	float Bounds;
	int AttackType;
	/*
	* 伤害类型（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	* AttackType 0 ：不可为0（近战）2（爆炸）3（物理）可为1（远程）4-9（元素）
	* AttackType 1 ：< 4为物理爆炸，4-9为不同元素爆炸
	* AttackType 2 ：新爆炸，ExplodeRange用于半径，AttackValue用于伤害
	*/
	int DamageType;
	float AttackValue;
	int BuffId;
	int BuffLevel;
	int ModelRelevantID;
	int BeAmmunition;
	bool Break;	// 是否击碎方块
	int TriggerType;	//触发器素材库分类
	int TouReduce;		// 削刃值
	int Puncture;		//击碎值，用来和block的韧度值比较，看是否能击碎和穿透该block
	int penetrateLimit; //穿透生物数量上限
	int ExplodeRangeType; //爆炸范围类型，用于控制爆炸的范围
	float ExplodeRange; //爆炸范围，主要用于伤害范围控制、破坏计算、击飞计算
	bool BombBlock; //新爆炸 是否炸毁方块
	float ModelScale; //模型缩放

	char endSet;  /****************************************memcpy 分界线********************************************/
	Rainbow::FixedString Name;
	Rainbow::FixedString TriggerEffect;
	Rainbow::FixedString TriggerSound;
	Rainbow::FixedString Model;
	Rainbow::FixedString TailEffect;
	
	ProjectileDef(const ProjectileDef& def)
	{
		copy(&def);
	}

	void copy(const ProjectileDef* def)
	{
		if (!def)
			return;

		Memcpy(this, def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
		Name = def->Name;
		TriggerEffect = def->TriggerEffect;
		TriggerSound = def->TriggerSound;
		Model = def->Model;
		TailEffect = def->TailEffect;
		ModelScale = def->ModelScale;
	}

	ProjectileDef(): ID(0), LocomotionType(0), Gravity(0), InitSpeed(0), SpeedDecay(0), Pickable(false),
	                 TriggerCondition(0),
	                 TriggerDelay(0),
	                 Bounds(0),
	                 AttackType(0),
	                 AttackValue(0), BuffId(0),
	                 BuffLevel(0),
	                 ModelRelevantID(0),
	                 BeAmmunition(0),
	                 Break(false),
	                 TriggerType(0),
					 TouReduce(0),
		             Puncture(0),
					 penetrateLimit(0),
				     ModelScale(1.0f)
	{
	}
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct GunDef
{
	unsigned int ID;
	char Name[MAX_BLOCK_NAME];
	short Attack;
	short FireInterval;
	short Range;
	short Magazines;
	short InitSpread;
	float SpreadSpeed;
	float RecoilSpeed;
	float SpreadRecoverySpeed;
	float RecoilRecoverySpeed;
	short MaxSpread;
	float MaxRecoil;
	int Aim;
	int Crosshair;
	float ReloadTime;
	float ManualTime;
	float ManualDelayTime;
	float Weight;
	int NeedBullet;
	int ContinuousFire;
    int BulletID;			// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
    int CostItemID;         // 耗费的东西和子弹的投射物分开
	int IdleAnimFps;
	int ShootAnimFps;
	int ReloadAnimFps;
	int DrawAnimFps;
	int AimAnimFps;
	int AimShootAnimFps;
	int ManualAnimFps;
	int IdleAnimTps;
	int ShootAnimTps;
	int ReloadAnimTps;
	//int PullingAnimTps;
	char ShootEffect[MAX_BLOCK_NAME];
	char ShootSound[MAX_BLOCK_NAME];
	char ReloadSound[MAX_BLOCK_NAME];
	char EmptyShootSound[MAX_BLOCK_NAME];
	char ManualSound[MAX_BLOCK_NAME];
	float SpeedAdd;
	int GunType;
	int AutoAim; //自动瞄准(0关闭自瞄 1允许自瞄并默认开启)

	float RecoilSpeedFirst;
	float MaxRecoilFirst;
	float RecoilRecoverySpeedFirst;

	float RecoilSpeedThird;
	float MaxRecoilThird;
	float RecoilRecoverySpeedThird;


	char endSet;  /****************************************memcpy 分界线********************************************/
	std::string BulletFileName;

	GunDef()
	{
		CostItemID = 0;
	}

	GunDef(const GunDef& def)
	{
		copy(&def);
	}

	void copy(const GunDef* def)
	{
		if (!def)
			return;

		Memcpy(this, def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
		BulletFileName = def->BulletFileName;
	}

	int GetCostItemId() const
	{
		if (CostItemID == 0)
		{
			return BulletID;
		}
		else
		{
			return CostItemID;
		}
	}
};

struct CraftingDef
{
	int ID;
	std::vector<int> Type;
	std::vector<int> EditType;
	int TechId;
	int ResultID;
	int ResultCount;
	int UseExp;
	int MoneyCount;
	int MoneyID;
	int GridX;
	int GridY;
	bool IsGroup;
	//bool IsFollowMe;   这个值的对应功能用CraftingItemID代替了  code-by:DemonYan
	int MaterialID[9];
	int MaterialCount[9];
	int ContainerID[9];
	int MaterialNum;
	float Score;

	bool IsTemplate;
	int CopyID;
	Rainbow::FixedString EnglishName;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	std::map<_uint32, Rainbow::FixedString> ForeignId2Key;			// 外部分配的ID到key的对应关系
	int HelperCheck;
	int CookingTick;
	int CraftingItemID;
	int DisplayOrder;
	int UnlockLevel; //解锁等级

	std::vector<int>  SubType;
	std::vector<int>  SubTypeOrder;
	CraftingDef()
	{
		ID = 0;
		MaterialNum = 0;
		UnlockLevel = 0;
		memset(MaterialID, 0, sizeof(MaterialID));
		memset(MaterialCount, 0, sizeof(MaterialCount));
		memset(ContainerID, 0, sizeof(ContainerID));
		ForeignId2Key.clear();
	}
	//用于查找匹配配方是否一致
	bool operator== (const CraftingDef & other)
	{
		if (other.ID > 0)
		{
			return other.ID == this->ID;
		}
		else if (other.ResultID > 0)
		{
			return other.ResultID == this->ResultID; 
		}
		else
		{
			//用于匹配配方是否一致 主要用于石锅生成
			//std::map<int, int> thisMaterial;
			//std::map<int, int> otherMaterial;
			//for (int i = 0; i < 9; i++)
			//{
			//	if (this->MaterialID[i] > 0 && other.MaterialID[i] > 0)
			//	{
			//		thisMaterial[this->MaterialID[i]] = this->MaterialCount[i];
			//		otherMaterial[other.MaterialID[i]] = other.MaterialCount[i];
			//	}
			//	else if(this->MaterialID[i] == 0 && other.MaterialID[i] == 0)
			//	{
			//		break;
			//	}
			//	else
			//	{
			//		//两者长度不一致就是配方不一致
			//		return false;
			//	}
			//}
			////用于匹配组的配方
			//if (this->IsGroup)
			//{
			//	for (auto item = otherMaterial.begin(); item != otherMaterial.end(); ++item)
			//	{
			//		ItemDef* def = GetDefManagerProxy()->getItemDef(item->first);
			//		if (!def)
			//		{
			//			return false;
			//		}
			//		auto find = thisMaterial.find(def->ItemGroup);
			//		if (find == thisMaterial.end())
			//		{
			//			return false;
			//		}
			//		if (find->second != item->second)
			//		{
			//			return false;
			//		}
			//	}
			//}
			//else
			//{
			//	//用于匹配非组配方
			//	for (auto item = thisMaterial.begin(); item != thisMaterial.end();++item)
			//	{
			//		auto find = otherMaterial.find(item->first);
			//		if (find == otherMaterial.end())
			//		{
			//			return false;
			//		}
			//		if (find->second != item->second)
			//		{
			//			return false;
			//		}
			//	}
			//}
		}
		return false;
	}

	int getTypeSize()//返回值不可改为size_t lua获取之后会报错
	{
		return (int)Type.size(); 
	}

	int getTypeValue(int index)
	{
		if (index >= 0 && index < getTypeSize())
		{
			return Type[index];
		}
		return NULL;
	}

	void pushbackTypeValue(int val)
	{
		Type.push_back(val);
	}

	void setTypeValue(int val)
	{
		Type.clear();
		Type.push_back(val);
	}

	int getSubTypeSize()//返回值不可改为size_t lua获取之后会报错
	{
		return (int)SubType.size();
	}

	int getSubTypeValue(int index)
	{
		if (index >= 0 && index < getSubTypeSize())
		{
			return SubType[index];
		}
		return NULL;
	}

	void pushbackSubTypeValue(int val)
	{
		SubType.push_back(val);
	}

	void setSubTypeValue(int val)
	{
		SubType.clear();
		SubType.push_back(val);
	}
	
	bool containNum(int type)const
	{
		for (auto it = Type.begin(); it != Type.end(); ++it)
		{
			if ((*it) == type)
			{
				return true;
			}
		}
		return false;
	}
	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str.c_str();
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, Rainbow::FixedString>(key, str.c_str()));
		}
	}

	int getEditTypeNum() const
	{
		return (int)EditType.size();
	}
	int getEditTypeByIndex(int index) const
	{
		if (index >= 0 && index < getEditTypeNum())
		{
			return EditType[index];
		}
		return 0;
	}
};

struct ItemPosDataDef//手持物品的信息（位置、欧拉角）
{
	WCoord Pos;
	WCoord Rot;
};
struct ItemInHandDef//角色皮肤，手持物品的信息（位置、欧拉角）
{
	int itemId;
	ItemPosDataDef RightHandItemPosData; //右手
	ItemPosDataDef LeftHandItemPosData;  //左手
	ItemPosDataDef FPSRightItemPosData;  //右手
	ItemPosDataDef FPSLeftItemPosData;   //左手
	Rainbow::Vector3f FPSHandPos; //FPS视角手部位置
};

enum
{
	MONSTER_NORMAL_MODEL,	//
    MONSTER_ROLE_MODEL, //角色模型
    MONSTER_SKIN_MODEL, //皮肤模型
	MONSTER_HORSE_MODEL = 70, //坐骑模型 //20210728 codeby：chenwei 新增坐骑模型
	MONSTER_CUSTOM_MODEL = 40000, //自定义模型
	MONSTER_FULLY_CUSTOM_MODEL = 50000, //完全自定义模型
	MONSTER_IMPORT_MODEL = 60000, //导入模型
};
//tolua_end

struct ItemDropNewVal
{
	int v1 = 0;
	int v2 = 0;
};

struct SkinningDropItem
{
	int id = 0;
	unsigned short num = 0;
	unsigned short odds = 0;
	bool toolEfficiency = false;
};

//tolua_begin
//！！！！！不允许有std::string这样可能浅拷贝的类和结构对象存在
struct MonsterDefAllowMemCopyData
{
	unsigned int ID;
	int BabyID;
	int ParentID;
	int TextureID;
	int ModelType;

	int Type;
	int EditType;
	bool IsTemplate;
	int Nature;
	int TickPeriod;

	int Life;
	float LifeIncrease;
	float MinRandomScale;
	short Armors[10];			// 防御力（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	short ArmorRange;
	short ArmorMagic;
	float ArmorIncrease;
	short AttackType;
	short Attacks[10];			// 攻击力（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	float AttackIncrease;
	short AttackAnimTicks;
	int Height;
	int Width;
	int Thickness;
	int HitHeight;
	int HitWidth;
	int HitThickness;
	int ViewDistance;
	float AttackDistance;
	int Speed;
	int SpawnMaxLight;
	int SpawnSunLight;
	int SpawnMinHeight;
	int SpawnMaxHeight;
	int SpawnMinTime;
	int SpawnMaxTime;
	int SpawnMinTemperature;
	int SpawnMaxTemperature;
	int NumLimit;
	int PackNum;
	int PickItemOdds;
	int EquipGroup[8];
	int EquipOdds;
	int DropGroup[MAX_MONSTER_DROPGROUP];
	int DropGroupOdds[MAX_MONSTER_DROPGROUP];
	int DropItem[MAX_MONSTER_DROPITEM];			// 该字段开放玩家自定义（如果>=USER_MOD_NEWID_BASE，使用的时候需要动态转换）
	int DropItemOdds[MAX_MONSTER_DROPITEM];
	int DropItemNum[MAX_MONSTER_DROPITEM];

	std::vector<ItemDropNewVal> DropItemsNew;
	std::vector<ItemDropNewVal> DropItemsOddsNew;

	int BurnDropItem;
	int BurnDropItemOdds;
	int DropExp;
	int DropExpOdds;
	int LevelExp;	//击杀怪物获得等级经验
	int FeedItem;
	int FeedOdds;
	int Mass;
	int ActiveAtk;
	bool CanTame;
	bool CanBreed;
	bool CanRide;
	bool CanTalk;
	bool AvoidWater;
	bool IsPlayerCustom;
	int PathHide;
	bool SunHurt;
	bool HasAvatar;
	bool CanPassClosedWoodenDoors;
	float TurningSpeed;//转身速度
	int Toughness;//韧性
	int ToughnessRecover;//韧性恢复
	int TouReduce; // 削韧值
	int RepelRes;//击退抗性
	unsigned short SpawnWeight[MAX_BIOME_TYPE];

	float ModelScale;
	float KillScore;
	float BreedScore;
	float TameScore;
	float SkinningTime;
	float CorpseTime;
	int   BuffId;
	int BornBuff;
	bool NameDisPlay;
	bool DescDisplay;
	int BagNum;
	int Food;
	int FoodReduce;
	int TriggerType;	//触发器素材库分类
	bool DevShopDisPlay;//开发者商店显示开关
	bool CanToEgg;	//变蛋枪击中时是否可变蛋

	int AIConfigType;//AI配置类型，使用旧的卡片式还是AI行为树


	int TeamID;
	int CopyID;

	int ScriptDisable; // 触发器/脚本禁止创建此类生物

	float PreScale; //预览大小
	int BreedType; //繁殖类型
	int Fodder[2]; //饲料数据（第一个数据表示饲料类型，第二个表示消耗饲料槽次数）
	float TemperatureDefense; //温度防御
	short AlwaysUpdate;
	std::vector<SkinningDropItem> SkinningDropItems; //剥皮掉落配置
	void copy(const MonsterDefAllowMemCopyData* pData)
	{
		Memcpy(this, pData, sizeof(MonsterDefAllowMemCopyData));
	}
};


struct TypeCollideAABB
{
	CollideAABB box;
	std::string part;
};


struct MonsterDef : public MonsterDefAllowMemCopyData
{
	//消耗
	struct InteractFuncDef
	{
		short InteractionType;
		int ID;
	};
	ModDesc ModDescInfo;
	//多语言
	Rainbow::FixedString MultiLangName;
	Rainbow::FixedString MultiLangDesc;
	Rainbow::FixedString OpenStoreConfig; //开发者商店配置
	Rainbow::FixedString Desc;
	Rainbow::FixedString Dialogue;

	Rainbow::FixedString TickScript;
	Rainbow::FixedString InteractionFunction;

	Rainbow::FixedString Name;
	Rainbow::FixedString EnglishName;
	Rainbow::FixedString Model;

	Rainbow::FixedString Texture;
	Rainbow::FixedString Icon;

	std::vector<TypeCollideAABB> CollideBoxs;

	std::string HurtSound;
	std::string DeathSound;
	std::string SaySound;
	std::string StepSound;
	std::string AttackSound;
	std::string AttackStopSound;
	//增加一组攻击和攻击冷却音效，应对多技能的怪
	std::string AttackSound2;
	std::string AttackStopSound2;
	//char Effect[MAX_BLOCK_NAME];
	std::string Effect;
	std::string AIConfig; //AI配置ID
	std::map<_uint32, Rainbow::FixedString> ForeignId2Key;			// 外部分配的ID到key的对应关系
	MNSandbox::Object* gamemod;

	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str.c_str();
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, Rainbow::FixedString>(key, str.c_str()));
		}
	}
	void copy(const MonsterDef* templateBlockDef)
	{
		MonsterDefAllowMemCopyData::copy(templateBlockDef);
		SkinningDropItems = templateBlockDef->SkinningDropItems;  // 复制剥皮掉落配置
		
		ModDescInfo.copy(templateBlockDef->ModDescInfo);
		MultiLangName = templateBlockDef->MultiLangName;
		MultiLangDesc = templateBlockDef->MultiLangDesc;
		OpenStoreConfig = templateBlockDef->OpenStoreConfig;
		Desc = templateBlockDef->Desc;
		Dialogue = templateBlockDef->Dialogue;

		TickScript = templateBlockDef->TickScript;
		InteractionFunction = templateBlockDef->InteractionFunction;

		Name = templateBlockDef->Name;
		EnglishName = templateBlockDef->EnglishName;
		Model = templateBlockDef->Model;

		Texture = templateBlockDef->Texture;
		Icon = templateBlockDef->Icon;

		HurtSound = templateBlockDef->HurtSound;
		DeathSound = templateBlockDef->DeathSound;
		SaySound = templateBlockDef->SaySound;
		StepSound = templateBlockDef->StepSound;
		AttackSound = templateBlockDef->AttackSound;
		AttackStopSound = templateBlockDef->AttackStopSound;
		//增加一组攻击和攻击冷却音效，应对多技能的怪
		AttackSound2 = templateBlockDef->AttackSound2;
		AttackStopSound2 = templateBlockDef->AttackStopSound2;
		//char Effect[MAX_BLOCK_NAME];
		Effect = templateBlockDef->Effect;
		AIConfig = templateBlockDef->AIConfig; //AI配置ID
		gamemod = templateBlockDef->gamemod;
		for(auto item : templateBlockDef->ForeignId2Key)			// 外部分配的ID到key的对应关系
		{
			ForeignId2Key[item.first] = item.second;
		}
	}
};

struct MonsterSpawnDef
{
	int type;
	std::string name;
	int refresh_interval;
	unsigned short density;
	unsigned short max_num;
};

struct AnswerDef
{
	std::string Text;
    std::string MultiLangText;		//多语言支持
    std::string ScriptName;			//脚本名字
	int FuncType;
	int Val;
};

struct DialogueDef
{
	int ID;
	std::string Text;
	int Action;
	std::string Sound;
	std::string Effect;
	std::vector<AnswerDef> Answers;
	std::string MultiLangText;		//多语言支持

	int getAnswerNum()
	{
		size_t size = Answers.size();
		return static_cast<int>(size);
	}

	AnswerDef *getAnswerDef(int index)
	{
		if (index >= 0 && index < getAnswerNum())
			return &Answers[index];

		return NULL;
	}

	void pushAnswerDef(std::string Text, std::string MultiLangText, std::string ScriptName, int FuncType, int Val)
	{
		AnswerDef def;
		def.Text = Text;
		def.MultiLangText = MultiLangText;
		def.ScriptName = ScriptName;
		def.FuncType = FuncType;
		def.Val = Val;
		Answers.push_back(def);
	}
};

struct NpcPlotDef
{
	struct ConditionDef
	{
		int Type;
		std::vector<int> TaskIDs;
		int StartTime;
		int EndTime;
		int ItemID;
		int ItemNum;
		int GetTaskIDsNum()
		{
			size_t size = TaskIDs.size();
			return static_cast<int>(size);
		}

		int GetTaskIDByIndex(int index)
		{
			if (index >= 0 && index < GetTaskIDsNum())
				return TaskIDs[index];

			return 0;
		}
		void pushTaskID(int id)
		{
			TaskIDs.push_back(id);
		}

		void setTaskID(int ix, int id)
		{
			if (ix >= 0 && ix < GetTaskIDsNum())
			{
				TaskIDs[ix] = id;
			}
		}
	};
	int ID;
	std::string Name;
	std::string Icon;
	int InteractID;
	std::vector<ConditionDef> Conditions;
	std::vector<DialogueDef> Dialogues;
	std::vector<int> CreateTaskIDs;
	int EditType;
	bool IsTemplate;
	NpcPlotDefUseType UseType;
	int InteractType;
	int ExtraType;					// 同一个目标不同对话额外标识

	int CopyID;
	std::string EnglishName;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	std::map<_uint32, std::string> ForeignId2Key;			// 外部分配的ID到key的对应关系

	NpcPlotDef()
	{
		gamemod = nullptr;
		clear();
	}
	void clear()
	{
		Conditions.clear();
		Dialogues.clear();
		CreateTaskIDs.clear();
	}

	int getDialogueNum()
	{
		size_t size =  Dialogues.size();
		return static_cast<int>(size);
	}

	DialogueDef *getDialogueDef(int index)
	{
		if (index >= 0 && index < getDialogueNum())
			return &Dialogues[index];

		return NULL;
	}

	int getConditionNum()
	{
		size_t size = Conditions.size();
		return static_cast<int>(size);
	}

	ConditionDef *getConditionDef(int index)
	{
		if (index >= 0 && index < getConditionNum())
			return &Conditions[index];

		return NULL;
	}

	int getCreateTaskIDNum()
	{
		size_t size = CreateTaskIDs.size();
		return static_cast<int>(size);
	}

	int getCreateTaskID(int index)
	{
		if(index >= 0 && index < getCreateTaskIDNum())
			return CreateTaskIDs[index];

		return 0;
	}
	void setCreateTaskID(int ix, int id)
	{
		if (ix >= 0 && ix < getCreateTaskIDNum())
			CreateTaskIDs[ix] = id;
	}

	void pushConditions(int type, int startime, int endtime, int itemid, int itemnum)
	{
		ConditionDef def;
		def.TaskIDs.clear();
		def.Type = type;
		def.StartTime = startime;
		def.EndTime = endtime;
		def.ItemID = itemid;
		def.ItemNum = itemnum;
		Conditions.push_back(def);
	}
	void pushDialogues(int ID, std::string Text, int Action, std::string Sound, std::string Effect, std::string MultiLangText)
	{

		DialogueDef def;
		def.Answers.clear();

		def.ID = ID;
		def.Text = Text;
		def.Action = Action;
		def.Sound = Sound;
		def.Effect = Effect;
		def.MultiLangText = MultiLangText;
		Dialogues.push_back(def);
	}

	void pushCreateTaskID(int id)
	{
		CreateTaskIDs.push_back(id);
	}

	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str;
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, std::string>(key, str));
		}
	}

	
};

struct NpcTaskDef
{
	struct TaskContentDef
	{
        int Type;	//任务类型
        int ID;		//目标id
        int Num;	//目标数量
	};
	struct TaskRewardDef
	{
        int Type;	//奖励类型 0道具 1经验
        int ID;		//奖励id
        int Num;	//奖励数量
	};
	int ID;
	std::string Name;
	std::string MultiLangText;		//多语言任务名

	bool IsDeliver;
	bool ShowInNote;
	int InteractID;
	bool IsRepeat;
	bool UseInteract;
	std::vector<TaskContentDef> TaskContents;
	std::vector<TaskRewardDef> TaskRewards;

	std::vector<DialogueDef> Plots;
	std::vector<DialogueDef> UnCompleteds;
	std::vector<DialogueDef> Completeds;

	int CopyID;
	std::string EnglishName;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	std::map<_uint32, std::string> ForeignId2Key;			// 外部分配的ID到key的对应关系

	int getDialogueNum(int type)
	{
		size_t size = 0;
		if(type == 0)
			size = Plots.size();
		else if (type == 1)
			size = UnCompleteds.size();
		else if (type == 2)
			size = Completeds.size();

		return static_cast<int>(size);
	}

	DialogueDef *getDialogueDef(int type, int index)
	{
		if (index >= 0 && index < getDialogueNum(type))
		{
			if (type == 0)
				return &Plots[index];
			else if (type == 1)
				return &UnCompleteds[index];
			else if (type == 2)
				return &Completeds[index];
		}

		return NULL;
	}

	int getTaskContentDeNum()
	{
		size_t size = TaskContents.size();
		return static_cast<int>(size);
	}

	TaskContentDef *getTaskContentDef(int index)
	{
		if (index >= 0 && index < getTaskContentDeNum())
			return &TaskContents[index];

		return NULL;
	}

	int getTaskRewardDeNum()
	{
		size_t size = TaskRewards.size();
		return static_cast<int>(size);
	}

	TaskRewardDef *getTaskRewardDef(int index)
	{
		if (index >= 0 && index < getTaskRewardDeNum())
			return &TaskRewards[index];

		return NULL;
	}

	void pushTaskContent(int type, int id, int num)
	{
		TaskContentDef def;
		def.Type = type;
		def.ID = id;
		def.Num = num;
		TaskContents.push_back(def);
	}

	void pushTaskReward(int type, int id, int num)
	{
		TaskRewardDef def;
		def.Type = type;
		def.ID = id;
		def.Num = num;
		TaskRewards.push_back(def);
	}

	void pushPlot(int ID, std::string Text, int Action, std::string Sound, std::string Effect, std::string MultiLangText)
	{
		DialogueDef def;
		def.Answers.clear();
		def.ID = ID;
		def.Text = Text;
		def.Sound = Sound;
		def.Effect = Effect;
		def.MultiLangText = MultiLangText;

		Plots.push_back(def);
	}

	void pushUnCompleted(int ID, std::string Text, int Action, std::string Sound, std::string Effect, std::string MultiLangText)
	{
		DialogueDef def;
		def.Answers.clear();
		def.ID = ID;
		def.Text = Text;
		def.Sound = Sound;
		def.Effect = Effect;
		def.MultiLangText = MultiLangText;

		UnCompleteds.push_back(def);
	}


	void pushCompleted(int ID, std::string Text, int Action, std::string Sound, std::string Effect, std::string MultiLangText)
	{
		DialogueDef def;
		def.Answers.clear();
		def.ID = ID;
		def.Text = Text;
		def.Sound = Sound;
		def.Effect = Effect;
		def.MultiLangText = MultiLangText;

		Completeds.push_back(def);
	}

	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str;
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, std::string>(key, str));
		}
	}

};

struct HorseDef
{
	int ID;
	int RideHeight;
	float UIScale;
	float ShopUIScale;
	int MinHP;
	int MaxHP;
	int MinLandSpeed;
	int MaxLandSpeed;
	int MinFlySpeed;
	int MaxFlySpeed;
	int MinSwimSpeed;
	int MaxSwimSpeed;
	int MinJumpHeight;
	int MaxJumpHeight;
	int EggBlock;
	int EggGenTicks;
	int SaddleModel;
	int BornSaddle;
	int BornArmor;
	int ArmorSlotProb;
	int BornSkills[MAX_HORSE_SKILL];
	bool ChangeFPS;
	int HorseType; //20210728 codeby：chenwei 新增坐骑坐骑类型判定字段
	int RidingBindID;	//20211216 codeby：keguanqiang 新增坐骑骑乘绑点
	std::string PlayerCenterPosition;	//20221116 codeby：wangyu 个人中心界面位置调整

	void setToDefaultHorseDef()
	{
		RideHeight = 300;
		UIScale = 0.6f;
		MinHP = 50;
		MaxHP = 100;
		MinLandSpeed = 500;
		MaxLandSpeed = 1000;
		MinFlySpeed = 0;
		MaxFlySpeed = 0;
		MinSwimSpeed = 0;
		MaxSwimSpeed = 0;
		MinJumpHeight = 60;
		MaxJumpHeight = 100;
		EggBlock = 0;
		EggGenTicks = 0;
		BornSaddle = 0;
		BornArmor = 0;
		ArmorSlotProb = 0;
		BornSkills[0] = 0;
		BornSkills[1] = 1;
		BornSkills[2] = 2;
	}
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct FoodDef
{
	unsigned int ID;
	int UseTime;
	float AddFood;
	float AddFoodSat;
	int HealAmount;
	int HealAnimal;
	int BuffID[MAX_FOOD_BUFF];
	int BuffLevel[MAX_FOOD_BUFF];
	int BuffOdds[MAX_FOOD_BUFF];
	int Container;
	int UseMethod;
	int EffectRadius;
	int RandomBuff;
	int ClearBuff;
	int ChoosePriority;
	int BarbecueType;//0 表示不能放入篝火容器里， >0表示可以
	int HealThirst;//体力恢复值
	int HealAmountSpill;//增加可溢出血量
	int GetShield; //护盾
	int GetInsistence; //毅力
	int ClearBuffID[MAX_CLEAR_BUFF]; // 可清除对应的Buff
	int GetOxygen; //使用食物获取的氧气值
	int AddTemperature;
	int Type;
	int IngredientsType;

	char endSet;  /****************************************memcpy 分界线********************************************/

	FoodDef()
	{
	}

	FoodDef(const FoodDef& def)
	{
		copy(&def);
	}

	void copy(const FoodDef* def)
	{
		if (!def)
			return;
		Memcpy(this, def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
	}
};

struct StatusDef
{
	int Priority;
	int DeathClear;
	int AttackClear;
	int DamageClear;
	int IconID;//图标
	std::string strIconID;	// 字符串类型IconID  用于支持用户自定义图片icon
	int ParticleID;//特效
	int SoundID;//音效
	std::string strSoundID;	// 字符串类型SoundID  用于支持用户自定义声音
	int LimitTime;//持续模式 >0 表示有限的，否则表示无限
	EffectInfo EffInfo[MAX_BUFF_ATTRIBS];//最多5条效果
};

struct BuffDef
{
	int ID;
	Rainbow::FixedString Name;
	Rainbow::FixedString Desc;
	Rainbow::FixedString ScriptName;

	int AtkEffectTypeSID;
	int Level;
	int EffectTicks;
	int UpdatePeriod;
	char Type;
	char Nature;
	int NatureName;
	char SoundType;
	char BuffType;//1状态；2装备
	int BodyModel;
	bool NeedSwitchTPS;
	bool DisableAction;
	int PartScale;
	int TriggerType;	//触发器素材库分类
	int NumAttr;
	MODATTRIB_TYPE AttrTypes[MAX_BUFF_ATTRIBS];
	float AttrValues[MAX_BUFF_ATTRIBS];
	Rainbow::FixedString IconName;
	Rainbow::FixedString EffectName;
	Rainbow::FixedString SoundName;

	//改版后状态新增
	int CopyID;
	bool IsTemplate;
	Rainbow::FixedString EnglishName;
	ModDesc ModDescInfo;
	int ForeignId;
	std::map<_uint32, std::string> ForeignId2Key;			// 外部分配的ID到key的对应关系
	StatusDef Status;
	int SleepWeight;
	std::map<std::string, int> BiomeWeight;
	//多语言
	std::string MultiLangName;
	std::string MultiLangDesc;
	bool Hide; //在buff栏隐藏
	BuffDef(): ID(0), AtkEffectTypeSID(0), Level(0), EffectTicks(0), UpdatePeriod(0),
	           Type(0),
	           Nature(0),
	           SoundType(0),
	           BodyModel(0),
	           NeedSwitchTPS(false),
	           DisableAction(false), PartScale(0),
	           TriggerType(0),
	           NumAttr(0),
	           CopyID(0),
			   SleepWeight(0),
	           IsTemplate(false), Status()
	{
		BuffType = 1;
		ForeignId = 0;
		ForeignId2Key.clear();
	}
	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str;
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, std::string>(key, str));
		}
	}
};

struct EffectParamDef
{
	int UIType;
	int Default;
	int DescStringID;
};

//buff效果表
struct BuffEffectDef
{
	int ID;
	Rainbow::FixedString Name;
	Rainbow::FixedString Desc;
	Rainbow::FixedString IconName;
	char EditType;
	char Display;//0全部显示，1只在BUFF编辑显示，2只在装备编辑显示
	char TipsDisplay; //0=不隐藏，1=在bufftips中隐藏
	char TriggerType;
	int AttType;//作用的属性类型
	EffectParamDef EffectParam[MAX_BUFF_ATTRIBS];
};

//buff效果枚举表
struct BuffEffectEnumDef
{
	int ID;
	int Type;
	int AttType;//作用的属性类型
	Rainbow::FixedString EnumName;
};

//buff效果滑动条表
struct BuffEffectSliderDef
{
	int ID;
	int NumericalType;
	float Min;
	float Max;
	float Default;
	float Step;
	int UnitValue;
	int Prefix;

	BuffEffectSliderDef(): Prefix(0)
	{
		ID = 0;
		NumericalType = 1;
		Min = 0.0f;
		Max = 0.0f;
		Default = 0.0f;
		Step = 0.0f;
		UnitValue = 1;
	}
};

//图标库表
struct IconLibDef
{
	int ID;
	int Type;
	int Editing;
	Rainbow::FixedString IconName;
};

struct EnchantDef
{
	int ID;
	Rainbow::FixedString Name;

	int EnchantType;
	int EnchantLevel;
	float EnchantValue[2];
	int AttackType;
	int TargetType;
	int ConflictID;
	Rainbow::FixedString AttrDesc;
	int Weight;
	int ToolType[MAX_TOOL_TYPE];
	unsigned int Color; //附魔图标的颜色
	Rainbow::FixedString effect;
	Rainbow::FixedString effect2;
	Rainbow::FixedString IconName;
};

struct EnchantMentDef
{
	int StuffType;
	Rainbow::FixedString CurrencyType;
	int Cost;
	int MergeCost[5];
	int AttrWeight[5];
	int LevelWeight[5];
	int NpcAttrWeight[5];
	int NpcLevelWeight[5];
};

struct FurnaceDef
{
	int ID;
	int MaterialID;
	Rainbow::FixedString Name;
	int EditType;
	int Heat;
	int Result;		//1档温度产物
	int Exp;
	int ExpOdds;
	int ContainerID;
	float Score;

	bool IsTemplate;
	int CopyID;
	Rainbow::FixedString EnglishName;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	std::map<_uint32, std::string> ForeignId2Key;			// 外部分配的ID到key的对应关系

    int Type;		//类型: 0: 熔炉; 1:氧气炉, 2篝火
    int OxyValue;	//氧气值
    int BonfireHeat;//给篝火提供的燃值

    int Result2;	//2档温度产物
    int Result3;	//3档温度产物

    int ResultNum;		//1档产物数量
    int ResultNum2;		//2档产物数量
    int ResultNum3;		//3档产物数量

	int ProvideHeat;//燃料提供热量（每个tick提供的热量）
	int BurnTime;//原料熔炼需要的时间
	FurnaceDef()
	{
		ForeignId2Key.clear();
	}
	void setForeignId2Key(_uint32 key, const std::string& str)
	{
		if (ForeignId2Key.find(key) != ForeignId2Key.end())
		{
			ForeignId2Key[key] = str;
		}
		else
		{
			ForeignId2Key.insert(std::pair<_uint32, std::string>(key, str));
		}
	}
};

struct AchievementDef
{
	int ID;
	int FrontID[4];
	int IconID;
	int GridX;
	int GridY;
	Rainbow::FixedString Name;
	Rainbow::FixedString Desc;
	Rainbow::FixedString TrackDesc;
	int Group;
	bool IsGroup;
	int Type;
	int Goal;
	int GoalId;
	int GoalNum;
	int RewardType[2];
	int RewardID[2];
	int RewardNum[2];
	int Point;
	Rainbow::FixedString Script;
	int NextTrackID;//新增下一个追踪的字段 code_by:huangfubin
	Rainbow::FixedString GuidePicture; //新增引导图片 code_by Hyy
	int RewardDistributionType;//新增奖励领取类型 code_by Hyy
};

struct RoleDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	int Model;
	char Desc[MAX_BLOCK_DESC];
	int GeniusLv;
	char GeniusName[MAX_BLOCK_NAME];
	char GeniusDesc[MAX_BLOCK_DESC];
	char GeniusLvDesc[MAX_BLOCK_DESC];
	int GeniusType;
	float GeniusValue[3];
	int ULConsumeType;
	int ULConsumeID;
	int ULConsumeValue;
	float Ratio;
	int BuyTipsType;
	char BuyTips[MAX_BLOCK_DESC];

	int Height;
	int Width;
	int HitHeight;
	int HitWidth;
	int HitThickness;
	std::vector<TypeCollideAABB> CollideBoxs;
	std::vector<TypeCollideAABB> SneakCollideBoxs;  // 潜行时使用

	int CarryingHeight;
	int Arbody;
	int sID;
};

#define MAX_SKIN_BUYTYPE 3
#define MAX_SKIN_EFFECT 5
struct RoleSkinDef
{
	const char *getEffect(int effecttype) const
	{
		if (EffectType == effecttype) return Effect;
		else if (EffectType2 == effecttype) return Effect2;
		else if (EffectType3 == effecttype) return Effect3;
		else return NULL;
	}

	int ID;
	char Name[MAX_BLOCK_NAME];
	int Model;
	int TextureID;
	int Head;
	int EffectType; //1: 每走两格放置一个
	char Effect[MAX_BLOCK_NAME];
	int EffectType2; 
	char Effect2[MAX_BLOCK_NAME];
	int EffectType3;
	char Effect3[MAX_BLOCK_NAME];
	char ShowTimeEffect[MAX_BLOCK_NAME];

	int UseDays[MAX_SKIN_BUYTYPE];
	int MoneyType[MAX_SKIN_BUYTYPE];
	int ItemID[MAX_SKIN_BUYTYPE];
	int Price[MAX_SKIN_BUYTYPE];

	char Desc[1024];
	int BuyTipsType;
	char BuyTips[MAX_BLOCK_DESC];
	int Tag;
	int LimitBeginTime;
	int LimitEndTime;
	int OpenApiId;
	char Sound[MAX_BLOCK_DESC];
	int Height;
	int Width;
	int HitHeight;
	int HitWidth;
	int HitThickness;

	char EffectIcon1[MAX_BLOCK_NAME];
	char EffectIcon2[MAX_BLOCK_NAME];
	char EffectIcon3[MAX_BLOCK_NAME];
	char EffectIcon4[MAX_BLOCK_NAME];
	char EffectIcon5[MAX_BLOCK_NAME];
	char EffectIcon6[MAX_BLOCK_NAME];
	char EffectDesc1[MAX_BLOCK_DESC];
	char EffectDesc2[MAX_BLOCK_DESC];
	char EffectDesc3[MAX_BLOCK_DESC];
	char EffectDesc4[MAX_BLOCK_DESC];
	char EffectDesc5[MAX_BLOCK_DESC];
	char EffectDesc6[MAX_BLOCK_DESC];
	int FeatureType1; //1=变形，2=互动动作，3=变色，4=语音试听
	int FeatureType2;
	int FeatureType3;
	int FeatureType4;
	int FeatureType5;
	int FeatureType6;

	std::string BackgroundPic;
	std::string BackgroundAnim;
	float Backanim_x;
	std::string GifLogo;
	short ChangeType;
	short ChangeContact[MAX_SKIN_EFFECT];
	std::string ChangeLogo;
	float WindowScale;
	int Arbody;
	//20210923 codeby：wangyu 解析装扮互动字段
	int ActID;

	std::string PreSkin;
	std::string Animact;
	std::string Pitcure;
	int HeadFrames;
	int UnlockItem;
	std::string ChangeSkinID;//20220501 codeby: hanhuihua 多重换装
	int FrontID;//20220501 codeby: hanhuihua 多重换装前置解锁皮肤ID
	int ChangeOrder;//20220501 codeby: hanhuihua 多重换装换装顺序
	int ShowType;//20220501 codeby: hanhuihua 多重换装换装顺序

    std::string ShowSound;				//地图皮肤出场语音
    std::string IdleSound;				//地图皮肤休闲语音
    std::string DeathSound;				//地图皮肤死亡语音
    std::string SocialSound;			//地图皮肤社交语音

    int SoundScope;						//语音范围（半径）
    int Interval1;						//间隔时间1 --对应出场语音
    int Interval2;						//间隔时间2 --对应休闲语音
    int FirstTime1;						//初次判断时间1 --对应出场
    int Interval3;						//间隔时间3	--对应死亡语音
    int Interval4;						//间隔时间4 --对应社交语音
    int FirstTime2;						//初次判断时间2 --对应休闲语音
	int FirstTime3;						//初次判断时间3 --对应社交语音

	std::string ActOrder;				// 展示专属动作

	std::string LightCondition;         //光照条件
	std::string IllusionEffect;	        //幻化特效
	int IllusionPre;					//幻化准备时间
	int Specification;					//功能说明图

	int ThemeNum;						//主题序号
	std::string ThemeIcon;				//主题图标
	std::string SummonID;	//召唤ID

	int ShopEnterTimelineID;	// 入场动画ID
	std::string JumpScence;	//跳转场景

	std::string ChangeCondition;				//变身条件
	std::string ChangeEffect;			//变身特效
	float ChangeTime;					//变身时间
	int Geniusbuff;					// 天赋buff

	std::string ItemShowInfo;	//列表单元内模型缩放和y轴数据
	std::string AvtPartType;	//可搭配部件类型
	std::string ShopTabBkg;   // 商城专用侧边栏配图
	int SkillButton;   // 变形坐骑移动端是否显示技能按钮（0=显示，1=不显示）
	std::string NewPreSkin;//新换装ID
	int InteractiveSkin;//是否是与场景/其他皮肤可互动的皮肤
};

struct AvatarModelDef
{
	char AvatarID[MAX_BLOCK_NAME];	//avatar id
	char UIN[MAX_BLOCK_NAME];
	int ModelID;					
	char Name[MAX_BLOCK_NAME];
    char AstringID[MAX_BLOCK_DESC];	//描述
    int Part;						//部位Id	
    char Photo[MAX_BLOCK_DESC];		//二维图
    int Sex;
    int CostType;					//货币类型
    int CostNum;					//消耗货币数量�		
    int ItemID;						//消耗道具id
    int ExchangeRate;				//道具货币汇率
    char BuyTips[MAX_BLOCK_DESC];	//购买提示
    int CloseEnv;					//不开放环境
    int BeginTime;					//自动上线时间
    int EndTime;					//
    int ExperienceItemID;			//体验道具id
    int ExperienceTime;				//体验天数
    int Tag;						//标签提示
    int ps;							//备注
    bool ResIsOwn;					//本地是否包含
    char ResPath[MAX_BLOCK_DESC];	//资源路径
};

struct AvatarPartModelDef
{
    char UIN[MAX_BLOCK_NAME];		//用户UIN
    int IntactModelID;				//模型id
    char PartUIN[MAX_BLOCK_NAME];	//部件UIN
	//部件id 到期时间EndTime
    int HeadModelID;				//头部
    int HeadModeEndTime;
    int	BackModelID;				//背部
    int BackModelEndTime;
    int BrowModelID;				//表情
    int BrowModelEntTime;
    int ClothingModelID;			//上衣
    int ClothingModelEndTime;
    int FacingModelID;				//面饰
    int FacingModelEndTime;
    int HandModelID;				//手
    int HandModelEndTime;
    int PantsModelID;				//裤子
    int PantsModelEndTime;
    int ShoesModelID;				//鞋子
	int ShoesModelEndTime;
};

struct StorePropDef
{
	int ProductID;
	int ID;
	int Num;
	char CurrencyType[MAX_BLOCK_NAME];
	int Cost;
	char Desc[MAX_BLOCK_DESC];
	int Tag;
	int PurchaseLimitID;
	int BuyNum;
	int StringID;
};

struct MiniCoinDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	int IconId;
	int Num;
	float Cost;
	int BuyNum;
	char Desc[MAX_BLOCK_DESC];
	int Tag;
	int PurchaseLimitID;
	bool IsHide;
	int Order;
	int MiniCoinWithLimit;
	int Sort;
};

struct StoreHorseDef
{
	int ID;
	int BaseHorseID;
	int HeadID;
	int Level;
	int UnlockType;
	int UnlockItemID;
	int UnlockNum;
	int Ratio;
	char Sound[MAX_BLOCK_DESC];
	int BuyTipsType;
	char BuyTips[MAX_BLOCK_DESC];
	int Skill[MAX_HORSE_SKILL];
	int Show;
	int HorseTag;//20210802：增加新参数  codeby： wanyu
	int GoodsID;//20211115：增加新参数坐骑解锁道具  codeby： wanyu
	int GoodsNum;//20211115：增加新参数坐骑解锁道具数量  codeby： wanyu
	int Type;//20220330：增加新参数坐骑类型  codeby： wanyu
	int FinalUnlockItem;//20220407：增加新参数坐骑碎片最后解锁的坐骑  codeby： wanyu
	char giflogo[MAX_BLOCK_DESC];
	int Quality;//坐骑品质
	std::string BackgroundPic;//背景图
	std::string Position;//列表cell模型位置缩放角度
	std::string ShopTabBkg;   // 商城专用侧边栏配图
	int Move;//变形坐骑是否可以左右移动（0=不可以，1=可以）
};

struct HorseAbilityDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	char Icon[MAX_BLOCK_NAME];
	char Desc[MAX_BLOCK_DESC];
	int EffectID;
	float Effect[7];
};

struct NpcTradeDef
{
	int ID;
	int GroupID;
	int ItemID;
	int NpcID;
	int Price;
	int TradeType;
	int Weight;
	int NumFloor;
	int NumCeil;
	int PriceFloor;
	int PriceCeil;
	int LockNum;
	int PayItemID;
	int PayItemNumFloor;
	int PayItemNumCeil;
	int EnchantFlag;
	int EnchPriceFloor;
	int EnchPriceCeil;
	bool ADShow;
};

struct SignInDef
{
	int SignInType;
	int WhatDay;
	char RewardName[MAX_BLOCK_NAME];
	char RewardIcon[MAX_BLOCK_NAME];
	int RewardType;
	int RewardID;
	int RewardNum;
	char RewardTips[MAX_BLOCK_DESC];
	int RewardIntegral;
};

struct ExtremityScoreDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	int Type;
	int GoalID;
	int Num;
	float Score;
};

struct HeadIconDef
{
	int ID;
	int IconID;
	char Name[MAX_BLOCK_NAME];
	int Color;
	int SeriesID;
	char SeriesName[MAX_BLOCK_NAME];
	char Desc[MAX_BLOCK_DESC];
	int UnlockID;
	int UnlockNum;
};

#define MAX_CHESTDEF_ITEMS 10
struct ChestDef
{
	int ID; //blockid*100 + groupid
	int Key;
	int GroupOdds;
	int OddsMethod;
	int ItemID[MAX_CHESTDEF_ITEMS];
	int ItemNum[MAX_CHESTDEF_ITEMS];
	int ItemOdds[MAX_CHESTDEF_ITEMS];
    int ItemQuality[MAX_CHESTDEF_ITEMS];
};

struct ChestSpawnDef {
    int id;
    std::string name;
    std::string description;
    
    // 掉落配置
    int dropType;
    int scatterRange;
    
    // 生成配置
    int spawnType;
	std::string fixedSpawnRefreshTime;
    float fixedSpawnProbability;
    
    int randomSpawnCountMin;
    int randomSpawnCountMax;
};

#define PLANT_LVUP_REWARD 5
struct PlantDef
{
	int ID;
	int ModelID;
	int Exp;
	int ArchiveNum;
	int BuddyNum;
	int RewardID[PLANT_LVUP_REWARD];
	int RewardNum[PLANT_LVUP_REWARD];
};

struct FruitDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	char Icon[MAX_BLOCK_NAME];
	char Desc[MAX_BLOCK_DESC];
	float OpCost;
	float RipeCost1;
	float RipeCost2;
};

struct BookDef
{
	int ID;
	int ItemID;
	char ItemName[MAX_BLOCK_NAME];
	int SeriesID;
	int TypeID;
	char TypeName[MAX_BLOCK_NAME];
};

struct BookSeriesDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	int RewardType;
	int RewardID;
	int RewardNum;
};

#define MAX_RECYCLE_GET 2
struct RecycleDef
{
	int ID;
	int InvolvedID;
	int Type;
	int GetID[MAX_RECYCLE_GET];
	int GetNum[MAX_RECYCLE_GET];
};

#define MAX_RULE_OPTION 10
#define MAX_RULE_DEFOPT 4
struct GameRuleDef
{
	int ID;
	int OrderID;
	char Name[MAX_RULE_NAME];
	char Desc[MAX_BLOCK_DESC];
	int TypeID;
	char TypeName[MAX_BLOCK_NAME];
	int DefOption[MAX_RULE_DEFOPT];
	int OptionID[MAX_RULE_OPTION];
};

struct RuleOptionDef
{
	int ID;
	int Type;
	char DefaultDesc[MAX_BLOCK_DESC];
	char AfterDesc[MAX_BLOCK_DESC];
	float MinVal;  //如果只有一个值, 会使用MinVal
	float MaxVal;
};

struct FuncSwitchDef
{
	int ID;
	int Share;
	int AccSwitch;
	int AccEncode;
	int SmsPay;
	int SdkPay;
	int HomeChest;
	int FeedBack;
	int Reservation;
	int MobileBinding;
	int EmailBinding;
	int SecurityBinding;
	int QQWalletPay;
};

//struct HomeTraderDef
//{
//	int ItemID;
//	int Odds;
//	int MoneyType;
//	int Price;
//	int PriceAdd;
//	int MaxNum;
//	int Type;
//	int InvolvedID;
//};

#define MAX_REWARD_TASK 3
struct TaskDef
{
	int ID;
	char Icon[MAX_BLOCK_NAME];
	char Name[MAX_BLOCK_NAME];
	int TargetNum;
	int Reward[MAX_REWARD_TASK];
	int RewardNum[MAX_REWARD_TASK];
};

struct PlotDef
{
	int ID;
	int PlotID;
	char PlotDialog[MAX_BLOCK_DESC];
	char Icon[MAX_BLOCK_NAME];
	int Face;
	int Position;
	int SoundIdx;
};

struct HotkeyDef
{
	int ID;
	char Name[MAX_BLOCK_DESC];
	char FuncName[MAX_BLOCK_NAME];
	char TypeName[MAX_BLOCK_DESC];
	int Type;
	int DefaultCode;
};

struct KeyDef
{
	int Code;
	char Name[MAX_BLOCK_NAME];
};

struct PlantTreesDef
{
	int x;
	int y;
	int z;
};

struct ARGradeDef
{
	int ID;
	float Grade;
	int Percent;
	int Group;
	int Probability;
	int WordID;
};

struct ARStyleDef
{
	int ID;
	char Style[MAX_BLOCK_DESC];
	float Hue;
	float Saturability;
	float Lightness;
	float Sharpen;
	int Solid;
	int Tinting;
};

struct ItemSkillDef
{
	//消耗
	struct ItemSkillCostsDef
	{
		short CostType;
		int CostTarget;
		short CostVal;
	};
	//效果
	struct SkillFuncionsDef
	{
		struct ItemSkillFunctions_Block_Def
		{
			short ActionType;
			short DropOut;
			int BlockID;
            int curBlockID;
            int toBlockID;
		};
		struct ItemSkillFunctions_Attack_Def
		{
			short AttackType;
			short AttackVal;
		};
		struct ItemSkillFunctions_Buff_Def
		{
			int BuffID;
			float Duration;
			short Odds;
		};
		struct ItemSkillFunctions_Drift_Def //位移
		{
			float RepelPower;
			float BlowUpPower;
		};
		struct ItemSkillFunctions_Summon_Def //召唤
		{
			int MobID;
			int CallNum;
			int Duration;//秒
			short IsFollow; 
		};
		struct ItemSkillFunctions_Cure_Def //治疗
		{
			short TreatType;
			int TreatVal;
		};
		struct ItemSkillFunctions_Throw_Def //投射
		{
			int ProjectileID;
			float SpeedAdd;//速度加成
		};
		struct ItemSkillFunctions_UnlockCraft_Def //解锁配方
		{
			int itemID;
			short unlockType;//0=给全体解锁，1=给使用目标解锁
		};
		struct ItemSkillFunctions_Interact_Def //交互
		{
			int type;
			int id;
		};
		struct ItemSkillFunctions_PhysicsCatch_Def //物理抓取
		{
			int maxMass;
			int distance;
			int high;
		};
		struct ItemSkillFunctions_PhysicsThrow_Def //物理投掷
		{
			int maxMomentum;
		};
		struct ItemSkillFunctions_Self_Buff_Def // 14 对自己加buff
		{
			int BuffID;
			float Duration;
			short Odds;
		};
        struct ItemSkillFunctions_Clear_Buff_Nature_Def // 17 清除某一类的Buff
        {
            char buffNature;
        };
        struct ItemSkillFunctions_Monter_Buff_Def // 18 给生物加buff
        {
			int monterIDs[MAX_MONSTER_NUM];
            int buffID;
            short odds;
        };

		struct ItemSkillFunctions_CreateActor_Def // 19 创建actor类型
		{
			char actorName[64];
			int num;
		};

		struct ItemSkillFunctions_DefanceDamageReduce_Def // 20 防御减伤
		{
			float defanceRange;		// 防御范围角度
			float damageReduce;		// 减伤比例
			float knockbackResist;	// 击退抗性
			int gainBuff;			// 获得buff
		};

		struct ItemSkillFunctions_ProjectilePuncture_Def	// 21 穿刺技能（投掷物特有）
		{
			int punctureNum;		// 可穿刺数量（-1代表无限穿刺）
		};

		struct ItemSkillFunctions_ToughnessReduce_Def // 削韧技能
		{
			int touReduce;
		};

		union  SkillFuncionsUnionDef
		{
			ItemSkillFunctions_Block_Def  blockfun; 
			ItemSkillFunctions_Attack_Def attackfun;
			ItemSkillFunctions_Buff_Def   bufffun;
			ItemSkillFunctions_Drift_Def  driftfun;
			ItemSkillFunctions_Summon_Def summonfun;
			ItemSkillFunctions_Cure_Def	  curefun;
			ItemSkillFunctions_Throw_Def  throwfun;
			ItemSkillFunctions_UnlockCraft_Def unlockfun;
			ItemSkillFunctions_Interact_Def interactfun;
			ItemSkillFunctions_PhysicsCatch_Def physicsCatchfun;
			ItemSkillFunctions_PhysicsThrow_Def physicsThrowfun;
			ItemSkillFunctions_Self_Buff_Def selfbufffun;
            ItemSkillFunctions_Clear_Buff_Nature_Def clearBuffNatureFun;
            ItemSkillFunctions_Monter_Buff_Def monterBuffFun;
			ItemSkillFunctions_CreateActor_Def createActorFun;
			ItemSkillFunctions_DefanceDamageReduce_Def defanceDamgeReduceFun;
			ItemSkillFunctions_ProjectilePuncture_Def projPunctureFun;
			ItemSkillFunctions_ToughnessReduce_Def toughnessReduceFun;
		};
	   short oper_id;
	   SkillFuncionsUnionDef  func;
	};

	unsigned int ID;
	short Name;
	short Class;
	short Desc;
	short Icon;
	short Key;
	short SkillType;
	float ChargeMove;
	float ControlInterval;
	short ChargeType;
	float ChargeTime;
	float CDTime;
	float Distance;
	short TargetType;
	short TargetClass;
	int   TargetID;
	short RangeType;
	short RangeVal1;
	short RangeVal2;
	short RangeVal3;
	short TargetCamp;
	short TargetEffectArea;
	short TargetEffectValue;
	int   FpsStartAct;
	int   FpsChargeAct;
	int   FpsAttackAct;
	int   TpsStartAct;
	int   TpsChargeAct;
	int   TpsAttackAct;
	std::string StartSound;
	std::string ChargeSound;
	std::string AttackSound;
	std::string StartEffect;
	std::string ChargeEffect;
	std::string AttackEffect;
	std::string EffectTarget;
	std::vector<ItemSkillCostsDef>	 ItemSkillCosts;
	std::vector<SkillFuncionsDef>	SkillFuncions;
	float ChargeEffectSize;
	float AttackEffectSize;
	float EffectTargetSize;
	float ChargeEffectTime;
	float AttackEffectTime;
	float EffectTargetTime;
	std::string EffectTargetSound;
	bool ShowProjectile;

	int getSkillFunctionNum()
	{
		size_t size = SkillFuncions.size();
		return static_cast<int>(size);
	}

	SkillFuncionsDef *getSkillFunction(int index)
	{
		if (index < 0 || index >= (int)SkillFuncions.size())
		{
			return NULL;
		}
		return &SkillFuncions[index];
	}

	int getSkillCostNum()
	{
		size_t size = ItemSkillCosts.size();
		return static_cast<int>(size);
	}

	ItemSkillCostsDef *getSkillCost(int index)
	{
		if (index < 0 || index >= (int)ItemSkillCosts.size())
		{
			return NULL;
		}
		return &ItemSkillCosts[index];
	}
};

struct FileToLoad
{
	int ID;
	char strPathName[256];
	int	    uiLoad;
};

struct ModsToLoad
{
	int ID;
	char strPathName[256];
};

struct AntiCrack
{
	int Type;
	int ID;
};
struct PlayActDef
{
	int ID;
	std::string Name;
	int ActID;
	std::string Effect;
	std::string icon;
	std::string Face;
	char InGame;
	int SkinType;
	int SkinID;				// 对于装扮互动，是主动作皮肤ID
	//20210909 codeby：chenwei 新增装扮互动字段
    int SkinID2;			// 装扮互动，副动作皮肤ID
    int UnlockActSkinID;	// 解锁全皮肤动作皮肤ID（只要拥有该皮肤，所有皮肤均可播放该皮肤解锁动作）
	int SortWeight;
	int PosDirect;
	//20210927 codeyby:wangyu 装扮互动字段新增
	int EffectSkinID;
	int SkinID3;
	int SkinID4;
	int PosDirect2;
	int SkinOffset;
	float Distance2;
	float SkinScale;
	float Distance;
	std::string Desc;
	std::string Sound;
};

struct TechNode //tolua_export
{//tolua_export
	//tolua_begin
	int Id;
	int PreId;
	int ItemId;
	int Level;
	int CostItem;
	int CostValue;
	bool IsUnlocked;
	std::vector<TechNode*> Children;
	//tolua_end
};//tolua_exports 

struct TechTree //tolua_export
{//tolua_export
	//tolua_begin
	int WorkbenchLevel;
	int RootId;
	std::string Name;
	TechNode* RootNode;
	//tolua_end
};//tolua_exports 


struct RecordEffectDef
{
	int ID;
	std::string Path;	
};

//WARNING：!!!!!!!!!!!!!!!!!!!!!!!!新增字段时： 基础类型添加在endSet之前，非基础类型添加在endSet之后：!!!!!!!!!!!!!!!!!!!!!!!!
struct PhysicsActorDef
{
	int ActorID;
	int ActorType;
	int UseGravity;
	int Mass;
	float Drag;
	float AngularDrag;
	int FreezePositionX;
	int FreezePositionY;
	int FreezePositionZ;
	int FreezeRotationX;
	int FreezeRotationY;
	int FreezeRotationZ;
	int MaterialID;
	int ShapeID;
	int ShapeVal1;
	int ShapeVal2;
	int ShapeVal3;
	int Center;
	int EditType;
	float ModelScale;	//模型大小倍数
	int SecondBuild;
	char endSet;  /****************************************memcpy 分界线********************************************/

	PhysicsActorDef() {
		ActorID = 0;
		ActorType = 0;
		UseGravity = 0;
		Mass = 0;
		Drag = 0.f;
		AngularDrag = 0.f;
		FreezePositionX = 0;
		FreezePositionY = 0;
		FreezePositionZ = 0;
		FreezeRotationX = 0;
		FreezeRotationY = 0;
		FreezeRotationZ = 0;
		MaterialID = 0;
		ShapeID = 0;
		ShapeVal1 = 0;
		ShapeVal2 = 0;
		ShapeVal3 = 0;
		Center = 0;
		EditType = 0;
		ModelScale = 0.f;
		SecondBuild = 0;
		endSet = 0;
	}

	PhysicsActorDef(const PhysicsActorDef& def)
	{
		copy(&def);
	}

	void copy(const PhysicsActorDef* def)
	{
		if (!def)
			return;

		Memcpy(this, def, (char*)&(this->endSet) - (char*)this);
		endSet = 0;
	}
};

struct PhysicsMaterialDef
{
	int MaterialID;
	int CopyID;
	float DynamicFriction;
	float StaticFriction;
	float Bouncyness;
	float Hardness;
	int DragScale;
    int IsTemplate;		//是否作为模板供玩家选择
    int Selectable;		//是否作为预设材质供玩家选择
    int NameStringID;	//物理材质名称对应的stringdefID
	std::string Name;
	std::string Sound;
	MNSandbox::Object* gamemod;
	ModDesc ModDescInfo;
	PhysicsMaterialDef() : MaterialID(0), CopyID(0), DynamicFriction(0), StaticFriction(0), Bouncyness(0), Hardness(0),
	                       DragScale(0),
	                       IsTemplate(0), Selectable(0),
	                       NameStringID(0),
	                       gamemod(NULL)
	{
	}
};
//载具键位控制信息
struct PhysicsPartKeyDef
{
    int FuncID;			//所在的function id
    short KeyVal;			//键值
    short KeyType;		//按键类型:0长按，1点击
    std::string KeyIcon;//按键图标
	PhysicsPartKeyDef() : FuncID(0), KeyVal(0),
	                      KeyType(0), KeyIcon("")
	{
	}
};

struct PhysicsPartsDef
{
	//效果
	struct EffectFunctionsDef
	{
		struct EffectFunctionsDef_Wheel_Def //车轮
		{
			short FunctionID;
			short DriveSwitch;
			short DriveDirection;
			short SteeringSwitch;
			short SteeringDirection;
			int SteeringRange;
			float SteeringRate;
			short RecoverMode;
			//short LeftKey;
			//short RightKey;
			int BrakeTorque;
			short Icon;
			float MOI;
			float DampingRate;
			float MaxCompression;
			float MaxDroop;
			float SpringStrength;
			float SpringDamperRate;
		};
		struct EffectFunctionsDef_Engine_Def //引擎
		{
			short FunctionID;
			int MaxTorque;
			int MaxSpeed;
			short ForwardKey;
			short BackwardKey;
			short Icon;
			int MaxOmega;
			/*std::string StartupSound;
			std::string WorkSound;*/
		};
		struct EffectFunctionsDef_Seat_Def //座位
		{
			short FunctionID;
			short SeatType;
			short IsControl;
			short LeaveKey;
			short Icon;
			short AddHeight;
		};
		//struct EffectFunctionsDef_Connection_Def //连接
		//{
		//	short FunctionID;
		//	short ConnectionMode;
		//	short ObjectType;
		//	short IsConnected;
		//	int MaxConnection;
		//	short Icon;
		//};
		struct EffectFunctionsDef_Reset_Def //重置
		{
			short FunctionID;
			short ResetKey;
			short Icon;
		};
		struct EffectFunctionsDef_Cost_Def //消耗
		{
			short FunctionID;
			short CostType;
			int CostVal;
			short CostInterval;
			int MaxCost;
			short UIDisplay;
			short ButtonDisplay;
			short ModelDisplay;
			short SupplyWay;
			short SupplyItem;
			int SupplyVal;
		};
		struct EffectFunctionsDef_Overheat_Def	//过热
		{
			short FunctionID;
			int HeatPerUnit;
			int HeatDownTime;
			int HeatReduce;
			int MaxHeat;
			int MaxHeatReduce;
		};
		struct EffectFunctionsDef_Propeller_Def //推进器
		{
			short FunctionID;
			float Thruster;
			short Effect;
			short Sound;
			short SoundVolume;
			short KeyType;
			short Key;
			short KeyIcon;
			int ThrusterForce;
			int ThrusterType;
			int ThrusterStableType;
			int RigidForce;
		};
		struct EffectFunctionsDef_SThruster_Def
		{
			short FunctionID;
			float PowerChangeValue;
			int PowerChangeInterval;
			int CostInterval;
			float PowerMax;

			int Level1TopLimit;
			int Level1CostValue;

			int Level2TopLimit;
			int Level2CostValue;

			int Level3TopLimit;
			int Level3CostValue;

            float ThrusterForce2;   //无车轮的力
            float ThrusterForce1;  //有车轮的力

		};
		struct EffectFunctionsDef_Suspension_Def //悬挂
		{
			short FunctionID;
			float Stiffness1;
			float Damping1;
			float MaxForce1;
			float Stiffness2;
			float Damping2;
			float MaxForce2;
			float Stiffness3;
			float Damping3;
			float MaxForce3;
			float Stiffness4;
			float Damping4;
			float MaxForce4;
			float Stiffness5;
			float Damping5;
			float MaxForce5;
			float Stiffness6;
			float Damping6;
			float MaxForce6;
		};


		struct  EffectFunctionsDef_Boat_Propeller_Def //船只推进器
		{
			short FunctionID;
			float Thruster;
			//最大角度限制
			float maxUpAngle;
			//最小角度限制
			float maxDownAngle;

		};

		struct EffectFunctionsDef_Boat_Floatbucket_Def //船只浮桶
		{
			short FunctionID;
			float floatForce;

		};


		union  EffectFunctionsUnionDef
		{
			EffectFunctionsDef_Wheel_Def  wheelfun;
			EffectFunctionsDef_Engine_Def enginefun;
			EffectFunctionsDef_Seat_Def   seatfun;
			//EffectFunctionsDef_Connection_Def  connectionfun;
			EffectFunctionsDef_Reset_Def	 resetfun;
			EffectFunctionsDef_Cost_Def	 costfun;
			EffectFunctionsDef_Overheat_Def overheatfun;
			EffectFunctionsDef_Propeller_Def  propellerfun;
			EffectFunctionsDef_SThruster_Def sthrusterfun;
			EffectFunctionsDef_Suspension_Def suspensionfun;
			EffectFunctionsDef_Boat_Propeller_Def boatPropellerfun;
			EffectFunctionsDef_Boat_Floatbucket_Def boatBucketfun;
		};
		short func_id;
		EffectFunctionsUnionDef func;
	};

	int PartsID;
	short IsCore;
	int Life;
	short PartsType;
	short SubType;
	short IsCoreArmor;
	int AddPower;
	int UsePower;
	short UseType;
	short IsEnergy;
	short BlockNum;
	short PortTriggerType;   // 直连端口触发类型[0=改状态的，1=使用的]
	char CannotTrigger;
    int CostValue;		//用于需要消耗能量的设备，如引擎和喷射器消耗量
    short CostInterval;	//用于需要消耗能量的设备，如引擎和喷射器消耗间隔
	
	std::vector<PhysicsPartKeyDef> Keys;

	//std::string StartUpSound;
	//std::string WorkSound;

	//int FunctionEffect1;
	//int FunctionEffect2;
	//int FunctionEffect3;
	//int FunctionEffect4;
	//int FunctionEffect5;

	std::vector<EffectFunctionsDef>	EffectFunctions;

	int getEffectFunctionsNum()
	{
		size_t size = EffectFunctions.size();
		return static_cast<int>(size);
	}

	EffectFunctionsDef *getEffectFunctions(int index)
	{
		return &EffectFunctions[index];
	}
};

struct PhysicsPartsTypeInfoDef
{
    int iSubType;       //子类型
    int iStringId;      //对应string表的ID
    int iLimitNum;      //类型限制数量
    std::string sPicUrl;//图片路径
    int iMaxOutNum;		// 最大连出
    int iMaxInNum;		// 最大连入
	PhysicsPartsTypeInfoDef() : iSubType(0)
	                            , iStringId(0), iLimitNum(1), sPicUrl(""), iMaxOutNum(0), iMaxInNum(0)
	{
	}
};

struct PhysicsPartsTypeDef
{
	int iPartsType;
	std::map<int, PhysicsPartsTypeInfoDef> mPartsTypeInfo;
};

struct PhysicsPartsConnectDef
{
	int ID;
    int ControlType;			// 控制端
    int ControlSubType;			// 控制端子类
    int WorkType;				// 执行端
    int WorkSubType;			// 执行端子类
    int ControlRelationType;    //	控制类型关系
    char DefaultConnect;		//	默认连接
    char CanEdit;				//	开放编辑
};

struct ConditionStruct
{
	int TransferID;
    int TeamColor;						// 传送点队伍颜色
    int PassItemID;						// 传送点通行物品id
    int PassItemNum;					// 传送点通行物品数量
    int ForbidItemID;					// 传送点禁止通行物品id
    bool IsExpendable;					// 是否为消耗物
};

struct TransferDef
{
    int ID;								// 传送点id
    std::string TransferName;			// 传送点名字
    std::string TransferTip;			// 传送点传送提示
    int Status;							// 传送点状态
    int MapID;							// 地图id
	int x;
	int y;
	int z;	
	WORLD_ID vehicleID;
	int x_;
	int y_;
	int z_;									
	bool ShowName;						// 是否显示名字
	std::vector<ConditionStruct> Condition;

	ConditionStruct *getConditionStruct(int index)
	{
		return &Condition[index];
	}
	int getConditionSize()
	{
		size_t size = Condition.size();
		return static_cast<int>(size);
	}
	TransferDef(): ID(0), Status(0), MapID(0), x(0), y(0), z(0), z_(0), ShowName(false)
	{
		vehicleID = 0;
		x_ = 0;
		y_ = 0;
		y_ = 0;
	}
};

enum StarStationTransferStatus
{
    STARSTATION_INACTIVE = 0,				//星站未激活
    STARSTATION_ACTIVATED = 1,				//星站已激活，未付费
    STARSTATION_TRANSFERING = 2,            //星站传送过程中
    STARSTATION_TRANSFER_SUSPENDED = 3,     //星站传送暂停
    STARSTATION_TRANSFER_FAILED = 4,		//星站传送失败
    STARSTATION_TRANSFER_SUCCESS = 5,	    //星站传送成功
	STARSTATION_TRANSFERSTATUS_MAX = STARSTATION_TRANSFER_SUCCESS
};

struct UnfinishedStarStationTransferRecord
{
	int destStarStationID;
	int destMapID;
	int srcCabinPosX;
	int srcCabinPosY;
	int srcCabinPosZ;
	int status;
	UnfinishedStarStationTransferRecord():destStarStationID(0), destMapID(0),  srcCabinPosX(0), srcCabinPosY(0), srcCabinPosZ(0),status(0)
	{

	}
};

struct StarStationCabinDef
{
	int cabinPosX;
	int cabinPosY;
	int cabinPosZ;
	int cabinStatus;
	int bindPlayerUin;
	int cabinLevel;	// 增加一个等级数据，仅存储在存档中，在地图外使用，地图内，可以用之前的办法获取level
	StarStationCabinDef() : cabinPosX(0), cabinPosY(0), cabinPosZ(0), cabinStatus(0), bindPlayerUin(0), cabinLevel(0)
	{
	}
};

enum TransferStationType
{
	STAR_STATION = 0,
	MOD_TRANSFER_DOOR = 1,
};

struct StarStationTransferDef
{
    int starStationID;											// 星站ID									
    std::string starStationName;								// 星站名字
    bool isActive;												// 星站是否激活
    bool isSign;												// 控制台是否能在小地图生成标记点
    int mapID;													// 地图id(星球id)
    int statusEnd;												// 终点状态
    int consolePosX;
    int consolePosY;
    int consolePosZ;
    std::vector<StarStationCabinDef> vecCabinDef;				// 星站传送舱列表
	std::vector<UnfinishedStarStationTransferRecord> vecRecord;
	unsigned char stationType;									//传送站类型 0为原始的星站，1为mod的传送门
	int stationExtraData;

	int getCabinCount()
	{
		size_t size = vecCabinDef.size();
		return static_cast<int>(size);
	}

	StarStationCabinDef* getCabinDefByIndex(int index)
	{
		if(index < 0 || index >= (int)vecCabinDef.size())
		{
			return NULL ;
		}

		return &vecCabinDef[index];
	}

	bool addCabin(const StarStationCabinDef& cabinDef)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if(it->cabinPosX == cabinDef.cabinPosX && it->cabinPosY  == cabinDef.cabinPosY && it->cabinPosZ == cabinDef.cabinPosZ)
			{
				return false;
			}
		}

		vecCabinDef.push_back(cabinDef);
		return true;
	}

	bool addCabin(int cabinPosX, int cabinPosY, int cabinPosZ, short cabinLevel, int cabinStatus)
	{
		StarStationCabinDef cabinDef;
		cabinDef.cabinPosX = cabinPosX;
		cabinDef.cabinPosY = cabinPosY;
		cabinDef.cabinPosZ = cabinPosZ;
		cabinDef.cabinStatus = cabinStatus;
		cabinDef.cabinLevel = cabinLevel;
		return addCabin(cabinDef);
	}

	bool hasCabin(int cabinPosX, int cabinPosY, int cabinPosZ, StarStationCabinDef &cabinDef)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if(it->cabinPosX == cabinPosX && it->cabinPosY  == cabinPosY && it->cabinPosZ == cabinPosZ)
			{
				cabinDef = *it;
				return true;
			}
		}

		return false;
	}

	bool removeCabin(int cabinPosX, int cabinPosY, int cabinPosZ)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if(it->cabinPosX == cabinPosX && it->cabinPosY  == cabinPosY && it->cabinPosZ == cabinPosZ)
			{
				vecCabinDef.erase(it);
				return true;
			}
		}

		return false;
	}

	void clearCabins()
	{
		vecCabinDef.clear();
	}

	void updateAllCabinStatus(int status)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			updateCabinStatus(it->cabinPosX, it->cabinPosY, it->cabinPosZ, status);
		}
	}

	bool updateCabinStatus(int cabinPosX, int cabinPosY, int cabinPosZ, int status)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if(it->cabinPosX == cabinPosX && it->cabinPosY  == cabinPosY && it->cabinPosZ == cabinPosZ)
			{
				it->cabinStatus = status;
				return true;
			}
			
		}

		return false;
	}

	bool updateCabinBindPlayerUin(int cabinPosX, int cabinPosY, int cabinPosZ, int playerUin)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if(it->cabinPosX == cabinPosX && it->cabinPosY  == cabinPosY && it->cabinPosZ == cabinPosZ)
			{
				it->bindPlayerUin = playerUin;
				return true;
			}
		}

		return false;
	}

	bool updateCabinLevel(int cabinPosX, int cabinPosY, int cabinPosZ, int cabinLevel)
	{
		for (auto it = vecCabinDef.begin(); it != vecCabinDef.end(); ++it)
		{
			if (it->cabinPosX == cabinPosX && it->cabinPosY == cabinPosY && it->cabinPosZ == cabinPosZ)
			{
				it->cabinLevel = cabinLevel;
				return true;
			}
		}

		return false;
	}

	UnfinishedStarStationTransferRecord *getUnfinishedStarStationTransferRecord(int index)
	{
		if(index < 0 || index >= (int)vecRecord.size())
		{
			return NULL;
		}

		return &vecRecord[index];
	}

	int getUnfinishedStarStationTransferRecordCount()
	{
		size_t size = vecRecord.size();
		return static_cast<int>(size);
	}

	StarStationTransferDef(): starStationID(0), mapID(0), statusEnd(-1), isActive(false), consolePosX(0), consolePosY(0), consolePosZ(0)
		, stationType(0), stationExtraData(0)
	{
	}
};

struct NpcShopItemDef
{
    int iSkuID;              //商品ID
    int iItemID;             //出售的物品ID
    int iOnceBuyNum;         //单次购买的数量
    int iMaxCanBuyCount;     //最大可购买的次数
    int iRefreshDuration;    //商品恢复刷新时间间隔，当为0时表示不可恢复
    int iStarNum;            //购买该商品需要的星星的数量
	long long iCostItemInfo1;      //购买该商品需要的道具1的ID和数量（id*1000+num）
	long long iCostItemInfo2;      //购买该商品需要的道具2的ID和数量（id*1000+num）

	int iLeftCount;//配置信息中不用，只在获取当前的商店信息时用
    int iEndTime;//配置信息中不用，只在获取当前的商店信息时用
    int iShowAD; 			 //是否显示广告
};

struct NpcShopDef
{
    int iShopID;                                     //商店ID(npcid*100+idx[0-100])
    std::string sShopName;                           //商店名称
    std::string sShopDesc;                           //商店描述
    std::string EnglishName;
    std::string sInnerKey;							 //用来到地图里面找到NPC的
    std::vector<NpcShopItemDef> vShopItemList;  //商店商品列表

	NpcShopDef()
	{
		vShopItemList.clear();
	}
	int getSkuSize()
	{
		size_t size = vShopItemList.size();
		return static_cast<int>(size);
	}

	NpcShopItemDef* getNpcShopSkuDefByIdx(int skuidx)
	{
		if (skuidx < 0 || skuidx >= (int)vShopItemList.size()) { return NULL; }

		return &vShopItemList[skuidx];
	}

	NpcShopItemDef* getNpcShopSkuDef(int skuid)
	{
		for (int i = 0; i < (int)vShopItemList.size(); i++) {
			if (skuid == vShopItemList[i].iSkuID) {
				return &vShopItemList[i];
			}
		}

		return NULL;
	}

	void pushNpcShopItemDef(int iSkuID,             //商品ID
        int iItemID,             //出售的物品ID
        int iOnceBuyNum,         //单次购买的数量
        int iMaxCanBuyCount,     //最大可购买的次数
        int iRefreshDuration,   //商品恢复刷新时间间隔，当为0时表示不可恢复
        int iStarNum,            //购买该商品需要的星星的数量
		long long iCostItemInfo1,      //购买该商品需要的道具1的ID和数量（id*1000+num）
		long long iCostItemInfo2,		 //购买该商品需要的道具2的ID和数量（id*1000+num）
        int iShowAD)			//是否显示广告
	{
		NpcShopItemDef def;
		def.iSkuID = iSkuID;
		def.iItemID = iItemID;
		def.iOnceBuyNum = iOnceBuyNum;
		def.iMaxCanBuyCount = iMaxCanBuyCount;
		def.iRefreshDuration = iRefreshDuration;
		def.iStarNum = iStarNum;
		def.iCostItemInfo1 = iCostItemInfo1;
		def.iCostItemInfo2 = iCostItemInfo2;
		def.iShowAD = iShowAD;
		def.iLeftCount = 0;
		def.iEndTime = 0;

		vShopItemList.push_back(def);
	}
};

struct PackItemDef
{
    int iItemInfo;//物品的信息[1, 64] 乘以1000是为了扩展怕策划改 (id*1000+num)
    int iRatio;   //概率
};

struct PackGiftDef
{
    int iPackID;                           //包裹ID
    int iPackType;                         //包裹类型（0固定，1随机）
    int iMaxOpenNum;                       //随机包裹里最多可开启的次数（最大20个）
    int iRepeat;                           //0表示不可重复，其他表示可重复
    int iNeedCostItem;                     //0表示不消耗，其他表示消耗
	long long iCostItemInfo;               //打开包裹消耗物品的信息[1, 64] 乘以1000是为了扩展怕策划改 (id*1000+num)

    std::vector<PackItemDef> vPackItemList;//包裹里的物品

	int getPackItemListSize()
	{
		size_t size = vPackItemList.size();
		return static_cast<int>(size);
	}
	
	PackItemDef* getPackItemDefByIdx(int idx)
	{
		if (idx < 0 || idx >= (int)vPackItemList.size()) { return NULL; }

		return &vPackItemList[idx];
	}

	void clearPackItem()
	{
		vPackItemList.clear();
	}

	void pushPackItemDef( int iItemInfo,int iRatio)
	{
		PackItemDef def;
		def.iItemInfo = iItemInfo;
		def.iRatio = iRatio;
		vPackItemList.push_back(def);
	}
};

struct TriggerItemDef
{
	int ID;
	std::string Name;
	int Type;
	int ChildType;
	std::string ChildName;
	std::string Desc;
	std::string Remarks;
	std::string Param1;
	std::string Param2;
	std::string Param3;
	std::string Param4;
	std::string Param5;
	std::string Param6;
	std::string Param7;
	std::string Param8;
	std::string Param9;
	std::string Param10;
	std::string DefaultParam1;
	std::string DefaultParam2;
	std::string DefaultParam3;
	std::string DefaultParam4;
	std::string DefaultParam5;
	std::string DefaultParam6;
	std::string DefaultParam7;
	std::string DefaultParam8;
	std::string DefaultParam9;
	std::string DefaultParam10;
	std::string TriggerEventParams;
	int Display;
	std::string DisplayFilter;
	std::string Version;
};

struct TriggerEnumDef
{
	int ID;
	std::string EnumName;
	int Type;
	int Display;
};

struct TriggerFunctionDef
{
	int ID;
	std::string Name;
	int Type;
	std::string TypeName;
	std::string Desc;
	std::string ReturnType;
	std::string Param1;
	std::string Param2;
	std::string Param3;
	std::string Param4;
	std::string Param5;
	std::string Param6;
	std::string Param7;
	std::string Param8;
	std::string Param9;
	std::string Param10;
	std::string DefaultParam1;
	std::string DefaultParam2;
	std::string DefaultParam3;
	std::string DefaultParam4;
	std::string DefaultParam5;
	std::string DefaultParam6;
	std::string DefaultParam7;
	std::string DefaultParam8;
	std::string DefaultParam9;
	std::string DefaultParam10;
	int Display;
	std::string Version;
};

struct TriggerParamDef
{
	int ID;
	std::string Desc;
	std::string UseInput;
	std::string UseLibrary;
	std::string UseFunction;
	std::string UseVariable;
	std::string UseEnum;
	std::string UseObject;
	int UseTrigger;
	std::string DefaultValue;
	std::string ObjFilter;
};

//动作类型定义
enum
{
	TRIGGERACTTYPE_PLAYER = 1, // 人物动作
	TRIGGERACTTYPE_MOB = 2, // 怪物动作
	TRIGGERACTTYPE_NORMAL = 3, // 通用动作
};
struct TriggerActDef
{
	unsigned int ID;
	int TriggerType; // 动作类型
	int stringID;

	int LinkID; // 存在时，使用animact.csv的数据。成功加载后，变为负数
	int ActID;//第三人称动画
	std::string Effect;
	std::string Face;

	std::string Name;
	std::string Icon;
	int FpsActID; //第一人称动画
	TriggerActDef()
	{
		FpsActID = 0;
	}
};

struct ScriptAPIDef
{
	int ID;
	int Type;	
	std::string TypeName;
	std::string FunctionName;
	std::string FunctionDesc;
	std::string Desc1;
	std::string Desc2;
	std::string Desc3;
	std::string Desc4;
};

//特效
struct ParticleDef
{
	int ID;
	int AtkEffectTypeSID;
	int TriggerType;
	int Type;
	int TypesSpecialEffects;
	int PlayMode;
	std::string Name;
	std::string EffectName;
	std::string SoundName;
	std::string IconName;
	int Playtime;
};

//音效
struct SoundDef
{
	int ID;
	int Type;
	int AtkEffectTypeSID;
	int TriggerType;
	int Duration;
    Rainbow::FixedString Name;
    Rainbow::FixedString IconName;
    Rainbow::FixedString SoundPath;
};

//素材库类型
struct TriggerResourceType
{
	int ID;
	int Type;
	int Class;
	int NameId;
};

struct VehiclePartAction
{
	VehiclePartAction()
	{
		pos = WCoord(0, 0, 0);
		id = 0;
		memset(value, 0, sizeof(value));
		curLine = 0;
		state = 0;
		tickcount = 1;
		actionmode = 2;
		actionval = 0;
		remainval = 0;
	}
    WCoord pos;		//载具状态下为dim内的相对坐标，方块状态下为世界坐标
    int id;		//部件运动类型：转动，滑动
	int value[MAX_ACTIONER_LINE];	//部件动作数据(转轴方块：-360~+360；滑动方块：0未通电，1通电）
	int curLine;
	int state;
	int tickcount;
	//用于多序列器控制同一方块的一些状态保存......
	int actionmode;
	int actionval;
	/*
		v1:意义暂时未知
		v2:针对t铰链、液压臂等走绝对位置的方块，该字段保存的是剩余的tick值
	*/
	int remainval;
};
//星舞动角色动作
struct RoleActionDef
{ 
	int ID;         //编号
    std::string Name; //皮肤名称
    int StringID;        //动作名称
    int SkinID;           //皮肤ID
    int CharacterID;   //角色ID   
    int ActionID;    //动作ID
    int Toaction;    //对应动作ID
    int Tomodel;    //对应模型ID
    int Close;       //版本是否开放（0不开放，1开放）
    int Type;        //动作类型   
};

struct BPSettingDef
{
	int ID;
	std::string Name;
	std::vector<short> Mission;
	short MaxIntegral[3];
	std::vector<short> GeneralReward;
	std::vector<short> BattlePassReward[3];
	std::string OpenTime;
	std::string CloseTime;

	std::string Background1;
	std::string Background2;
	std::string Background3;
	std::string Modle;
	//std::string Uilocation;
	std::string Sound;
	short Rule;
	short Propaganda;
};

struct BPMissionDef
{
	short ID;
	int Name;
	int Mission;
	int Jump;
	int Icon;
	short Integral;
	short FuctionDec;
	std::string Remarks2;
	short Times;
};

struct BPDrawDef
{
	short ID;
	std::string Name;
	short Type[3];
	std::string Drawui1;
	std::string Drawui2;
	std::string Drawui3;
	short Rule;
	std::string Sound;
};

struct BPRewardDef
{
	short ID;
	short Item;
	short Count;
};

enum SFACTIVITY_TYPE
{
	SFACTIVITY_NULL = 0,

    SFACTIVITY_KILLMOB,				//击杀生物
    SFACTIVITY_TAMEMOB,				//驯服生物
    SFACTIVITY_BREEDMOB,			//繁殖生物
    SFACTIVITY_PLANT,				//作物种植
    SFACTIVITY_WILDPRO,				//野人职业授予
    SFACTIVITY_WILDSAFE,			//野人拯救
    SFACTIVITY_DIGITEM,				//采集物品
    SFACTIVITY_ENCHANT,				//给装备附魔
    SFACTIVITY_EXPLORE,				//探险地区
    SFACTIVITY_SAFEDAY,				//存活天数
    SFACTIVITY_GAMETIME,			//游戏时间

    SFACTIVITY_MAX,					//MAX 活动最大数目
};

enum
{
    HOMELAND_FUNC_MINERAL = 1,				//建造方块信息
    HOMELAND_FUNC_BLUEPRINT = 2,			//蓝图
    HOMELAND_FUNC_CUTDOWN_FARM_TIME = 3,	//缩短农作物成长时间
    HOMELAND_FUNC_CUTDOWN_ANIMAL_TIME = 4,	//缩短动物成长时间
    HOMELAND_FUNC_FARM_GO_BAD = 5,			//农作物变质
    HOMELAND_FUNC_ADD_PET_EXP = 6,			//增加宠物经验
    HOMELAND_FUNC_ADD_PET_PROPERTY = 7,		//增加宠物属性
    HOMELAND_FUNC_UNLOCK_RECIPE = 8,		//解锁配方
    HOMELAND_FUNC_UNLOCK_STYLE = 9,			//解锁风格
    HOMELAND_FUNC_UNLOCK_WEATHER = 10,		//解锁天气
    HOMELAND_FUNC_UNLOCK_SKYBOX = 11,		//解锁天空盒
    HOMELAND_FUNC_UNLOCK_MUSIC = 12,		//解锁音乐
    HOMELAND_FUNC_BUY = 13,					//创造背包获取 - 购买
    HOMELAND_FUNC_UNLOCK = 14,				//创造背包获取 - 解锁
    HOMELAND_FUNC_JUMP = 15,				//创造背包获取 - 跳转
    HOMELAND_FUNC_GET_PET = 16,				//获得宠物
    HOMELAND_FUNC_COMPOUND_PET = 17,		//合成宠物
    HOMELAND_FUNC_GETITEM = 18,				//获得道具
    HOMELAND_FUNC_UNLIMIT_USE = 19,			//无限数量方块
};


//tolua_end

//tolua_begin
//宠物
struct PetInfoDef
{
    int PetID; //宠物ID（不是唯一标识）
    int Stage;//子类型	
	int MonsterID;
	std::string HeadIcon;
	int PetQuality; //品质
    int BaseStaminaMin; //最小初始体力
    int BaseStaminaMax; //最大初始体力
    int BaseSpeedMin; //最小初始速度
    int BaseSpeedMax; //最大初始速度
    int BaseLuckMin; //最小初始运气
	int BaseLuckMax; //最大初始运气
	int SpeedMax; //最大速度 // 表格 -- 2021/08/05 codeby wudeshen
	int StaminaMax; //最大体力
	int LuckMax; //最大运气
	int SkillPackID; //特性包ID
	std::string SpecialEffect;//身上特效文件名
	std::string FootPrint;//脚印特效文件名
	int HandBookScore; //图鉴解锁积分
	PetInfoDef() : Stage(0), MonsterID(0), HeadIcon(""), BaseStaminaMin(0), BaseStaminaMax(0),
		BaseSpeedMin(0), BaseSpeedMax(0), BaseLuckMin(0), BaseLuckMax(0), SkillPackID(0),SpecialEffect(""), FootPrint("")
	{
	}
};

struct PetDef
{
	int PetID;
	std::map<int, std::map<int, PetInfoDef>> mPetInfo;
};


//宠物技能
struct PetSkillsDef
{
	int SkillID;
	int SkillName;	
	int Desc;
	std::string Icon;
};

struct PetExploreDef
{
	int ExploreMapID;
	int MapName;
	int NeedHomeLv;
	int NeedMapID;
	int BaseTime;
	int SpecialSkill;
	int MaxPetNum;
	int SpecialEventID;
	int Milepost1_Distance;
	int Milepost2_Distance;
	int Milepost3_Distance;
	int Milepost4_Distance;
	int Milepost5_Distance;
	int Milepost1_AwardPackID;
	int Milepost2_AwardPackID;
	int Milepost3_AwardPackID;
	int Milepost4_AwardPackID;
	int Milepost5_AwardPackID;
	int Milepost1_PowerValue;
	int Milepost2_PowerValue;
	int Milepost3_PowerValue;
	int Milepost4_PowerValue;
	int Milepost5_PowerValue;
	std::string MapIcon;

	PetExploreDef()
	{}
};

struct PetEventDef
{
	int EventID;
	int Desc;
	int MissionFunction1_ID;
	int MissionFunction1_Val1;
	int MissionFunction1_Val2;
	int MissionFunction1_Val3;
	int MissionFunction1_Val4;
	std::string Icon;
	std::string Icon1;
};


//家园宠物特性包表
struct PetSkillPackDef
{
	int PackID; //id
	int SkillID[MAX_PET_SKILL];
	int SkillLv[MAX_PET_SKILL];
	int SkillProb[MAX_PET_SKILL];
};

//符文表code by:tanzhenyu
struct RuneDef
{
	int    ID;//
	char   Name[MAX_BLOCK_NAME];//条目名称
	int    Level;
	int    Kind;//条目所属类型 攻击1/防御2/效率4//可叠加
	int    EnchantType;
	char   TypeName[MAX_BLOCK_NAME];//
	float  Value[4];
	int          AttackType;
	int          TargetType;
	int          ConflictID;//保留
	char         AttrDesc[MAX_BLOCK_DESC];
	int          Weight;
	int          ToolType[MAX_TOOL_TYPE];
	unsigned int Color; //附魔图标的颜色
	char         effect[MAX_BLOCK_NAME];
	char         effect2[MAX_BLOCK_NAME];
	char         IconName[MAX_BLOCK_NAME];
	int          DisplayType;
    int activateItemID;
	int Category;//类型
};

#define MAX_LEAF_LAYER 5
#define MAX_LEAF_ODDS 4
#define MAX_LEAF_WIDTH 7

enum
{
	LEAF_EMPTY = 0,
	LEAF_WOOD = 1,
	LEAF_LEAF = 2,
	LEAF_ODDS_EXIST = 3,
	LEAF_LEAFORWOOD = 4,
};
//tolua_end

struct TreeDef //tolua_exports
{ //tolua_exports
	//tolua_begin
	int ID;
	char Name[MAX_BLOCK_NAME];
	char Model[MAX_BLOCK_NAME];
	int MinHeight;
	int MaxHeight;
	int LeafBlock;
	int WoodBlock;
	int MinLayers;
	int MaxLayers;
	//tolua_end
	int LayerBlockOdds[MAX_LEAF_LAYER][MAX_LEAF_ODDS+1]; //[layer][n_odds]
	char LeafBlocks[MAX_LEAF_LAYER][MAX_LEAF_WIDTH][MAX_LEAF_WIDTH]; //0: empty,  1: wood,   2: leaf,   3: possible existed leaf
}; //tolua_exports

//tolua_begin
struct VoxelPalette
{
	unsigned int BlockID[256];
};

struct StringDef
{
	int ID;
	std::string str;
};
struct CreateRoleAvatarDef
{
	std::string avatarID;
	int ID;
	int uin;
	int type;
	int version_min;
	int zoom_percentage;
};

struct OverseasGrayDef
{
	int ApiID;
	std::string Name;
	std::string MinVersion;
	std::string MaxVersion;
	int CusLanguger;	//定制化版本 语言 1开启 0不开启
};

// 端内wiki跳转配置
struct WikiConfigDef
{
	int id;					// 跳转入口场景id
	std::string desc;		// 跳转入口描述
	int isShow;				// 是否显示入口 1显示 0不显示
	int openWith;			// 打开方式 1浏览器打开 0端内
	std::string address;	// 跳转地址
};

#define MAX_CHARINIT_ITEMS 13
struct CharacterDef
{
	int ID;
	char Name[MAX_BLOCK_NAME];
	int ItemID[MAX_CHARINIT_ITEMS];
	int ItemNum[MAX_CHARINIT_ITEMS];
	int ItemEquip[MAX_CHARINIT_ITEMS];
	int EquipPart[MAX_CHARINIT_ITEMS];
};

struct MobSpawnerDef
{
	int ID;
	int MobResID;
	int Duration;
	int MinSpawnDelay;
	int MaxSpawnDelay;
	int SpawnCount;
	int MaxSameMob;
	int ForceSpawn;
	int DungeonOdds;
};

#define MAX_EGG_HORSES 5
struct HorseEggDef
{
	int ID;
	int BroodAge;
	int SpeedPrice;
	int Horse[MAX_EGG_HORSES];
	int HorseProb[MAX_EGG_HORSES];
};



//20211020 喷漆表格数据结构 codeby:keguanqiang
struct SprayPaintDef
{
	struct PaintInfoDef
	{
		std::string TexName;	//贴图名  &p前缀表示喷漆贴图
        int Left;		//喷漆位置左右偏移量
        int Up;			//喷漆位置上下偏移量

		PaintInfoDef() : TexName(""), Left(0), Up(0)
		{
		}
	};

	int ID;
	int ShowTime;
	std::vector<PaintInfoDef> PaintInfos;

	SprayPaintDef() : ID(0), ShowTime(0)
	{
	}
};

struct MinicodeMonsterDef
{
	int ID;
	int CopyID;
	int Height;
	bool HasAvatar;
	char Name[32];
	char Model[32];
};

struct MusicalDef {
	int Id;
	std::string Name;
	int         Type;
	std::string Resourcepath;
	std::string Ban;
	int Thirdpersonplayingaction1;
	int Thirdpersonplayingaction2;
	int Thirdpersonholdingaction1;
	int Thirdpersonholdingaction2;
	int Playtheactioninthefirstperson;
	int Firstpersonhandheldaction;
	int notmove;
	int score;
	int Midinumber;
	std::string Monophonic;
};
struct ScoreDef {
	int Id;
	std::string Name;
	std::string Location;
};

// midi乐器表
struct InstrumentDef {
    int ID;           // 乐器编码
    std::string Name; // 乐器名字
    std::string Icon; // 乐器名字
};

// 专属动作特效
struct SkinActDef {
	int ID;
	int SeqId;
	std::string SeqName;
	std::string SeqEffect;
	int ShowTime;
	int RestoreTime;
	int PosY;
	int Angle;
	int Location;
	std::string StopEffect;
};

//	皮肤召唤 codeby:changhongfeng
struct SummonDef {
	int SummonID;
	std::string Name;
	int Model;
	std::string Effect;
	int SummonAction;
	std::string SummonEffect;
	std::string SummonTip;
	int SummonBuff;
	std::string DetailLocation;
	std::string SummonSound;
};

//	定制装扮 codeby:wangyu
struct AvatarDef {
	int ID;
	std::string Name;
	std::string nameIcon;
	int relateSkin;
	int useSkinId;
};

/*
方块正常状态下，为NORMAL_MODE；连接上动作序列器后，若序列器未通电，切换为ACTIONER_MODE_INIT；序列器通电时，切换为ACTIONER_MODE_POS状态；
运行过程中，序列器断电时，若为循环状态，切换为ACTIONER_MODE_STOP，关节停止在当前位置；ACTIONER_MODE_STOP状态下，设置为非循环状态，则关节直接回位
到初始位置，状态切换为ACTIONER_MODE_INIT。
*/
enum
{
    NORMAL_MODE = 1,		//普通模式（其他模式下，关节方块的参数设置不受除动作序列器之外的操作影响）
    ACTIONER_MODE_INIT = 2,	//1. 连接上动作序列器时的初始状态；2. STOP模式下，切换为非循环状态，直接运行至初始状态
    ACTIONER_MODE_POS = 3,	//运行动作序列的时候
    ACTIONER_MODE_STOP = 4	//运行动作序列过程中，强制停止
};

struct ResourcePackDef {
	int Id;
	int Library;
	int Page;
	int FirstClassifyID;
	std::string FirstClassifyString;
	int SecondClassifyID;
	int SecondClassifySort;
	std::string SecondClassifyString;
	int Source;
	int SourceValue;
	int Format;
	int AddButton;
	int Click;
	int ShortcutBars;
	int Editable;
	int PreviewON;
	std::string ViewMenu;
	int CustomFolder;
	std::string Manage;
	int PreviewType;
	std::string PreviewInfo;
};

struct FishingDef
{
	int ID;				// 鱼的ID
	std::string name;	// 名称
	int rarity;		// 稀有度
	int baitTime;	// 咬饵时间
	int allBiomeWeight;	// 全生态基础权重
	std::unordered_map<int, int> biomeWeight;	// 生态类型权重, 在全生态权重的基础上
	int tempestAdd;	// 暴风雨权重加成
	float feint;	// 彩虹0假动作概率
	std::unordered_map<int, float> rodAddWeights; // 不同鱼竿加成系数
	int up;		// 是否举鱼
};

struct ColorMixDef
{
	int ID;				// 颜色瓶ID
	std::string name;	// 名称
	unsigned int Color; //颜色RGB
	std::unordered_map<int, int> MixColor; // 与其他颜色混合后id
};

struct BadDayDef
{
	int ID;
	int Weight;
	float Modified;
};

struct BiomeGroupDef
{
	int ID;					//地形组ID
	std::string Name;		// 名称
	int SunDayMin;	//晴天时长下限
	int SunDayMax;	//晴天时长上限
	int BadDayMin;	//恶劣天气时长下限
	int BadDayMax;	//恶劣天气时长上限
	std::vector<BadDayDef> bayDayDefs;
};

/*------------------------------------------------------------
//LL:20220212:游戏语言配置
------------------------------------------------------------*/
//tolua_begin
struct GameLanguage
{
    int         id;         //语言ID索引
    int         cid;        //PC下cid值(10进制)
    std::string code;       //语言码(ISO369小写)
	std::string name;       //语言名(多语言)
	int			optional;	//是否可选
	std::string font;       //字体名
    std::vector<std::string> flags;  //支持语言标识列表(以,间隔,全部小写)
};
//tolua_end

/*
 * 大区信息
 */
//tolua_begin
struct STGameZone
{
	int     ID;             //<idx>国家ID<安卓查询>
	std::string  Country;        //<idx>国家缩写(2~3位)[注意大小写][公共查询]
	int     CountryCode;    //<idx>国家数字码(大写)
	int     AreaCode;       //<idx>国家电话区号
	std::string  CountryName;    //国家名
	std::string  Zone;           //<map->list>大区缩写[注意大小写]
	std::string  ZoneName;       //大区名
	std::string  Location;       //时区名
	int     TimeZone;       //UTC世界时差 分钟
	std::string  Currency;       //货币符号
	std::string	Desc;			//说明
	std::string	AreaFlag;		//国旗
	int		StringID;		//国家名对应stringid
	int		VoiceZone;      //语音对应的区域
};
//tolua_end

//tolua_begin
enum TRANSLATE_TYPE     //保存文件类型
{
    TRANSLATE_TYPE_NONE = 0,

    TRANSLATE_TYPE_MOD = 1,			            //插件
    TRANSLATE_TYPE_CUSTOM_MODEL = 2,			//微缩模型
    TRANSLATE_TYPE_ACTOR_CUSTOM_MODEL = 3,		//生物微缩模型
    TRANSLATE_TYPE_FULLY_CUSTOM_MODEL = 4,		//完全自定义模型
    TRANSLATE_TYPE_PACKING_FCM = 5,			    //微缩组合模型
    TRANSLATE_TYPE_IMPORT_CUSTOM_MODEL = 6,		//外部导入模型
    TRANSLATE_TYPE_MESSAGE_BOARD = 7,			//留言板
    TRANSLATE_TYPE_ACTOR_VEHICLE_ASSEMBLE = 8,	//挂载器
    TRANSLATE_TYPE_BLUE_PRINT = 9,			    //蓝图
    TRANSLATE_TYPE_LETTER = 10,					//信纸
    TRANSLATE_TYPE_BOOK = 11,					//书本
    TRANSLATE_TYPE_WSB_INSTRUCTION_CHIP = 12,	//指令芯片
    TRANSLATE_TYPE_MACHINE_CAPSULE = 13,		//机械胶囊
    TRANSLATE_TYPE_TRIGGER = 14,				//触发器
    TRANSLATE_TYPE_UIEDITOR = 15,				//UI编辑器
	TRANSLATE_TYPE_GAME_INTRO = 16,				//游戏开局介绍
	TRANSLATE_TYPE_GAME_RULE = 17,				//玩法设置相关文本 
    TRANSLATE_TYPE_MAX             			//这里做最大值记录
};
//tolua_end

//tolua_begin
enum UPWDESC_TYPE
{
    UPWDESC_ADD = 0,
    UPWDESC_DEL = 1,
    UPWDESC_UPDATE = 2
};

//漂流瓶官方文本
struct DriftBottleOfficialText
{
	std::string title;
	std::string context;
	std::string author;
	int id;
	bool shouldAnonymity;
};

//随机信件官方文本
struct LettersOfficialText
{
	std::string title;
	std::string context;
	std::string author;
	int id;
	bool shouldAnonymity;
};


//机器人对话表
struct BotConversationsDef
{
	unsigned int ID;
	std::string Content;
	std::string ICON;
	unsigned short TriggerType;
	unsigned int Parameter;
	unsigned short DisplayType;
	unsigned int DisplayTimer;
	unsigned short NextCondition;
	unsigned int NextID;
	unsigned short GridX;
	unsigned short GridY;
};
//tolua_end
#define GUEST_UIN  1

//tolua_begin
struct DevUIResourceDef
{
    int ID;
    int Type;
    int SubType;
    int OpenState;	//0:关 1:开
    int Order;		//排序
    std::string Name;
    std::string PackName;
    std::string ResourceName;
    std::string Dir;
    std::string Url;
};
//tolua_end

//刷怪房配置
struct DungeonsDef
{
	int ID;
	int Mob1ResID;
	int Mob1Num;
	int Mob2ResID;
	int Mob2Num;
	int Mob3ResID;
	int Mob3Num;
};

//怪物雕像配置
struct MonsterStatueDef
{
	int ID;
	std::string Name;
	int AnimID;
	int AnimTick;
};

//玩家属性配置

enum class PlayerAttributeType
{
	Life = 1,
	Food,
	Thirst,
	Temperature,
	Oxygen,
	Comfort,
	Radiation,
	Strength,
};

struct PlayerAttribCsvDef
{
	int ID;
	std::string Name;
	float InitVal;
	float MaxVal;
	float MinVal;
	float ReviveVal;
	float Recovery;

	struct BuffConfig {
		int id;
		int level;
		float threshold;
	};

	struct ExternalAttrChange {
		int type;
		float timesec;
		float value;
	};

	struct InternalAttrChange {
		int type;
		float value;
	};
	std::map<int, ExternalAttrChange> ExternalIncreace;
	std::map<int, ExternalAttrChange> ExternalDecreace;
	std::map<int, InternalAttrChange> InternalDecreace;
	std::vector<BuffConfig> Debuffs;
	std::vector<BuffConfig> Buffs;
};
//tolua_begin
struct DieInfoCsvDef
{
	int ID;
	int LanguageId;		//多语言id
	int ifDying;		//是否进入濒死 1进入，0直死
	int probability;	//恢复概率 万分比
	int time;			//濒死时间
	int move;			//濒死是否移动 1可以 0不可
	std::string Icon;
};
//tolua_end
// 定义建筑蓝图数据结构
struct ArchitecturalBlueprintCsvDef
{
	int ID;
	std::string Name;
	//int Type;            // 建筑类型分类
	//int Level;           // 建筑等级
	//std::string PreviewImage; // 预览图路径
	std::string Icon;         // 图标路径
	std::string Description;  // 描述
	//int UnlockCondition; // 解锁条件ID

	// 材料消耗
	struct MaterialConsumption {
		int ProduceItemID;    // 制造道具ID
		int ConsumeItemID;    // 消耗道具ID
		int Count;            // 数量
	};

	// 四种材料类型
	MaterialConsumption Wood;    // 木材
	MaterialConsumption Stone;   // 石材
	MaterialConsumption Iron;    // 铁材
	MaterialConsumption Steel;   // 钢材

	// 获取所有需要的材料 (消耗道具ID -> 数量)
	std::map<int, int> getRequiredMaterials() const {
		std::map<int, int> result;
		if (Wood.ConsumeItemID > 0 && Wood.Count > 0) {
			result[Wood.ConsumeItemID] = Wood.Count;
		}
		if (Stone.ConsumeItemID > 0 && Stone.Count > 0) {
			result[Stone.ConsumeItemID] = Stone.Count;
		}
		if (Iron.ConsumeItemID > 0 && Iron.Count > 0) {
			result[Iron.ConsumeItemID] = Iron.Count;
		}
		if (Steel.ConsumeItemID > 0 && Steel.Count > 0) {
			result[Steel.ConsumeItemID] = Steel.Count;
		}
		return result;
	}
};

struct EquipGroupDef
{
	int ID;
	int GroupType;
	std::map<EQUIP_SLOT_TYPE, int> GroupEquipMap;//key为装备类型 value 为装备模型id
};

//tolua_begin
struct Cost {
	int CostItem;
	int CostValue;
};
//tolua_end

struct WorkbenchTechCsvDef//tolua_export
{//tolua_export
	//tolua_begin
	int ID;
	int PreId;
	int ItemId;
	Cost _Cost;
	int WorkbenchLevel;
	std::string Note;
	std::string ModelViewPos;
	std::string ModelViewScale;
	//tolua_end
};//tolua_exports 

struct OperateUIData
{
	int ID;
	char Type;
	char IconType;
	std::string IconName;
	int UIText;
};

#endif
