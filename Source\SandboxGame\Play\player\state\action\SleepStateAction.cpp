#include "SleepStateAction.h"
//#include "stringdef.h"
#include "BlockBed.h"
#include "GameNetManager.h"
#include "ActorBindVehicle.h"
#include "SoundComponent.h"
#include "RiddenComponent.h"
//#include "GameEvent.h"
#include "ActorVehicleAssemble.h"
#include "LuaInterfaceProxy.h"
#include "ActorBody.h"
#include "special_blockid.h"

SleepStateAction::SleepStateAction(ClientPlayer* pPlayer)
: ActionBase(pPlayer)
{
	m_RestInBed = false;
	m_IsSleeping = false;
	m_SleepTimer = 0;
	m_sleepTickInDay = 0;
	m_sleepRealHour = 0;
}

SleepStateAction::~SleepStateAction()
{

}

int SleepStateAction::sleep(const WCoord &blockpos, bool refreshRevivePoint)
{
	if (mpCtrl->getWorld() == NULL || isSleeping() || isRestInBed()) return 0;
	if (!mpCtrl->getWorld()->isRemoteMode())
	{
		if (mpCtrl->getSitting() || isSleeping() || mpCtrl->isDead())
		{
			return STRDEF_SLEEP_STATUS;
		}

		if (!mpCtrl->getWorld()->hasSky())
		{
			return STRDEF_SLEEP_NOTHERE;
		}

		WorldCanvas* container = dynamic_cast<WorldCanvas*>(BedLogicHandle::getCoreContainer(mpCtrl->getWorld(), blockpos));
		if (container && container->getStage() == 2)  //破败的帐篷不能睡觉
		{
			return STRDEF_SLEEP_STATUS;
		}

		bool ignoreDayTime = false;
		if (g_WorldMgr && g_WorldMgr->getSpecialType() == HOME_GARDEN_WORLD)
			ignoreDayTime = true;

		if (!ignoreDayTime && mpCtrl->getWorld()->isDaytime())
		{
			//允许白天睡觉
			//return STRDEF_SLEEP_DAYTIME;
		}

		WCoord curblock = CoordDivBlock(mpCtrl->getPosition());
		if (Rainbow::Abs(curblock.x - blockpos.x) > 3 || Rainbow::Abs(curblock.y - blockpos.y) > 2 || Rainbow::Abs(curblock.z - blockpos.z) > 3)
		{
			return STRDEF_SLEEP_TOOFAR;
		}

	}

	auto RidComp = mpCtrl->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		mpCtrl->mountActor(NULL);
	}

	//getLocoMotion()->setBound(20, 20);
	//mpCtrl->updateBound(20, 20);
	mpCtrl->getLocoMotion()->m_yOffset = 0;
	//getLocoMotion()->m_yOffset = 20;

	//int offY = refreshRevivePoint ? 50 : 10;//床刷新复活点,睡袋不刷新//睡袋高度要低
	int offY = 10;
	if (mpCtrl->getWorld()->blockExists(blockpos))
	{
		int blockdata = mpCtrl->getWorld()->getBlockData(blockpos);
		int dir = blockdata & 3;
		int blockid = mpCtrl->getWorld()->getBlockID(blockpos);
		static float OffXArray[4] = { 40.0f ,-40.0f, 50.0f, -50.0f };
		static float OffZArray[4] = { -50.0f, 50.0f, 40.0f, -40.0f };
		WCoord sleepPos = BedLogicHandle::getSleepPosition(mpCtrl->getWorld(), blockpos);
		//static int PosOffset[4][2] = { {90,50}, {10,50}, {50,90}, {50,10} };
		//getLocoMotion()->m_Position = blockpos*BLOCK_SIZE + WCoord(PosOffset[dir][0], 90, PosOffset[dir][1]);
		//getLocoMotion()->m_Position = blockpos*BLOCK_SIZE + WCoord(50, 50, 50);
		if (blockid == 1223) {
			sleepPos.x += OffXArray[dir];
			sleepPos.z += OffZArray[dir];
		}
		sleepPos.y += offY;
		mpCtrl->getLocoMotion()->setPosition(sleepPos.x, sleepPos.y, sleepPos.z);
		static float YawArray[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
		mpCtrl->getLocoMotion()->m_RotateYaw = YawArray[dir];
		mpCtrl->getLocoMotion()->m_RotationPitch = -15.0f;
		if (mpCtrl->isNewMoveSyncSwitchOn())
		{
			mpCtrl->setMoveControlYaw(YawArray[dir]);
			mpCtrl->setMoveControlPitch(-15.0f);
		}
	}
	else
	{
		//getLocoMotion()->m_Position = blockpos*BLOCK_SIZE + WCoord(50, 50, 50);
		WCoord playerpos = blockpos * BLOCK_SIZE + WCoord(50, offY, 50);
		mpCtrl->getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
		mpCtrl->getLocoMotion()->m_RotateYaw = 0.0f;
		mpCtrl->getLocoMotion()->m_PrevRotatePitch = 15.0f;
		if (mpCtrl->isNewMoveSyncSwitchOn())
		{
			mpCtrl->setMoveControlYaw(0.0f);
		}
	}
	if (!mpCtrl->hasUIControl())
	{
		mpCtrl->syncPos2Client(mpCtrl->isMoveControlActive());
	}

	//ȥ���˴�˯��״̬����
	//ֻ����������˯���İ�ť����setSleeping(true)
	//setSleeping(true);
	setRestInBed(true);
	//�����ɴ�buff
	onRestInBedBuff();
	m_SleepTimer = 0;
	mpCtrl->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);

	if (mpCtrl->hasUIControl())
	{
		//ge GetGameEventQue().postRidingChange(1);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("ridetype", 1);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
	}

	if (!mpCtrl->getWorld()->isRemoteMode())
	{
		//˯��ȡ�������
		//playSound("misc.sleep", 1.0f, 1.0f);	 
		/*if(refreshRevivePoint)
		setRevivePoint(&blockpos, false);*/

		PB_PlayerSleepHC playerSleepHC;
		playerSleepHC.set_uin(mpCtrl->getUin());
		playerSleepHC.set_flags(0);

		PB_Vector3* pos = playerSleepHC.mutable_pos();
		pos->set_x(blockpos.x);
		pos->set_y(blockpos.y);
		pos->set_z(blockpos.z);

		GameNetManager::getInstance()->sendBroadCast(PB_PLAYER_SLEEP_HC, playerSleepHC);
	}
	else
	{
		//˯��ȡ�������
	   /*if(refreshRevivePoint)
		setRevivePoint(&blockpos, false);*/
	}

	//睡觉模型变化
	if (mpCtrl->getBody())
	{
		mpCtrl->getBody()->show(!BedLogicHandle::IsSleepNeedHide(mpCtrl->getWorld(), blockpos), true);
	}

	return 0;
}

int SleepStateAction::sleepInVehicleBed(const WCoord &blockpos, ActorVehicleAssemble* assemble)
{
	if (mpCtrl->getWorld() == NULL || assemble == NULL) return 0;
	if (!mpCtrl->getWorld()->isRemoteMode())
	{
		if (mpCtrl->getSitting() || isSleeping() || mpCtrl->isDead())
		{
			return STRDEF_SLEEP_STATUS;
		}

		if (!mpCtrl->getWorld()->hasSky())
		{
			return STRDEF_SLEEP_NOTHERE;
		}

		WorldCanvas* container = dynamic_cast<WorldCanvas*>(BedLogicHandle::getCoreContainer(mpCtrl->getWorld(), blockpos));
		if (container && container->getStage() == 2)  //破败的帐篷不能睡觉
		{
			return STRDEF_SLEEP_STATUS;
		}


		bool ignoreDayTime = false;
		bool isHomeland = (g_WorldMgr && g_WorldMgr->getSpecialType() == HOME_GARDEN_WORLD);
		if (isHomeland)
			ignoreDayTime = true;

		if (!ignoreDayTime && mpCtrl->getWorld()->isDaytime())
		{
			//允许白天睡觉
			//return STRDEF_SLEEP_DAYTIME;
		}

		WCoord pos;
		pos = assemble->convertWcoord(Rainbow::Vector3f((float)blockpos.x, (float)blockpos.y, (float)blockpos.z));
		pos = CoordDivBlock(mpCtrl->getPosition());

		WCoord curblock = CoordDivBlock(mpCtrl->getPosition());
		if (Rainbow::Abs(curblock.x - pos.x) > 3 || Rainbow::Abs(curblock.y - pos.y) > 2 || Rainbow::Abs(curblock.z - pos.z) > 3)
		{
			return STRDEF_SLEEP_TOOFAR;
		}

		//�Ƿ�Ҫ�ж���Χ�й��ﲻ��˯��?
		//...
	}
	auto RidComp = mpCtrl->getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		mpCtrl->mountActor(NULL);
	}
	//mpCtrl->updateBound(20, 20);
	mpCtrl->getLocoMotion()->m_yOffset = 0;
	//setSleeping(true);
	setRestInBed(true);
	m_SleepTimer = 0;
	mpCtrl->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	//m_VehicleID = assemble->getObjId();
	//m_VehicleAttachPos = blockpos;//	Bind(assemble->getObjId(),blockpos, );	
	//getActorBindVehicle()->Bind(assemble->getObjId(), blockpos, mpCtrl->getWorld() && !mpCtrl->getWorld()->isRemoteMode());
	//auto comp = dynamic_cast<PlayerBindVehicle*>(mpCtrl->GetComponentByName("PlayerBindVehicle"));
	auto comp = mpCtrl->getActorBindVehicle();
	if (comp)
	{
		comp->Bind(assemble->getObjId(), blockpos, mpCtrl->getWorld() && !mpCtrl->getWorld()->isRemoteMode());
	}

	if (mpCtrl->hasUIControl())
	{
		//ge GetGameEventQue().postRidingChange(1);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("ridetype", 1);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
	}

	if (!mpCtrl->getWorld()->isRemoteMode())
	{
		//playSound("misc.sleep", 1.0f, 1.0f);
		auto soundComp = mpCtrl->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("misc.sleep", 1.0f, 1.0f);
		}

		PB_PlayerSleepHC playerSleepHC;
		playerSleepHC.set_flags(0);

		PB_Vector3* pos = playerSleepHC.mutable_pos();
		pos->set_x(blockpos.x);
		pos->set_y(blockpos.y);
		pos->set_z(blockpos.z);

		GameNetManager::getInstance()->sendToClient(mpCtrl->getUin(), PB_PLAYER_SLEEP_HC, playerSleepHC);
	}

	return 0;
}

void SleepStateAction::wakeUp(bool immediately, bool updateallflag, bool setrevive)
{
	if (mpCtrl->getWorld() == NULL || mpCtrl->getLocoMotion() == NULL)
	{
		return;
	}

	//getLocoMotion()->setBound(180, 60);
	//mpCtrl->updateBound(180, 60);
	mpCtrl->getLocoMotion()->m_yOffset = 0;

	WCoord blockpos = CoordDivBlock(mpCtrl->getPosition());
	WCoord newpos = blockpos;

	if (IsBedBlock(mpCtrl->getWorld()->getBlockID(blockpos)))
	{
		BedLogicHandle::setBedOccupied(mpCtrl->getWorld(), blockpos, false);
		bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(newpos, mpCtrl->getWorld(), blockpos, 0);

		if (!succeed)
		{
			newpos = TopCoord(blockpos);
		}

		mpCtrl->getLocoMotion()->gotoPosition(BlockCenterCoord(newpos), mpCtrl->getLocoMotion()->m_RotateYaw, mpCtrl->getLocoMotion()->m_RotationPitch);
	}

	if (mpCtrl->getWorld() && g_WorldMgr && !mpCtrl->getWorld()->isRemoteMode() && (m_sleepTickInDay > 0) && (g_WorldMgr->getDayNightTime() == TICKS_ONEDAY))
	{
		//�ۼ�˯�����ŵ�tick
		int nOffsetWorldTime = TICKS_ONEDAY - m_sleepTickInDay;
		mpCtrl->m_WorldTimes[mpCtrl->getWorld()->getCurMapID()] += nOffsetWorldTime;
		g_WorldMgr->setWorldTime(g_WorldMgr->getWorldTime() + nOffsetWorldTime);
		//刷新方块
		if (nOffsetWorldTime > 0)
		{
			mpCtrl->getWorld()->reshChunkBlock();
		}
	}

	m_sleepTickInDay = 0;
	setSleeping(false);
	setRestInBed(false);

	//getActorBindVehicle()->UnBind(mpCtrl->getWorld() && !mpCtrl->getWorld()->isRemoteMode());
	auto comp = mpCtrl->getActorBindVehicle();//by__Logo
	if (comp)
	{
		comp->UnBind(mpCtrl->getWorld() && !mpCtrl->getWorld()->isRemoteMode());
	}
	if (mpCtrl->hasUIControl())
	{
		//ge GetGameEventQue().postRidingChange(0);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("ridetype", 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
	}

	if (immediately)
	{
		m_SleepTimer = 0;
	}
	else
	{
		m_SleepTimer = 100;
	}

	if (setrevive)
	{
		//setRevivePoint(&blockpos, false);
	}

	if (!mpCtrl->getWorld()->isRemoteMode())
	{
		PB_PlayerSleepHC playerSleepHC;
		playerSleepHC.set_flags(immediately ? 2 : 1);
		playerSleepHC.set_uin(mpCtrl->getUin());

		PB_Vector3* pos = playerSleepHC.mutable_pos();
		pos->set_x(mpCtrl->getLocoMotion()->m_Position.x);
		pos->set_y(mpCtrl->getLocoMotion()->m_Position.y);
		pos->set_z(mpCtrl->getLocoMotion()->m_Position.z);

		GameNetManager::getInstance()->sendBroadCast(PB_PLAYER_SLEEP_HC, playerSleepHC);
		if (mpCtrl->getBody())
		{//����ʱҪ��ʾģ��
			mpCtrl->getBody()->show(true);
		}
	}

	//睡觉模型变化
	if (BedLogicHandle::IsSleepNeedHide(mpCtrl->getWorld(), blockpos))
	{
		mpCtrl->getBody()->show(true, true);
	}

	//浣溪皮肤睡觉特效stop
	if (mpCtrl->getBody()->getSkinID() == 269 || mpCtrl->getBody()->getSkinID() == 251)
	{
		const SkinActDef* def = GetDefManagerProxy()->getSkinActDef(100102, mpCtrl->getBody()->getSkinID());
		if (def && def->SeqEffect != "") {
			mpCtrl->getBody()->stopMotion(def->SeqEffect.c_str());
		}
	}

	//��Ҫ����ڴ��ϲ��е�BUFF
	offRestInBedBuff();
}

//�����ɴ��ϵ�BUFF
void SleepStateAction::onRestInBedBuff()
{
	if (!isSleeping() && isRestInBed())
	{
		if (mpCtrl->getWorld() && mpCtrl->getWorld()->isDaytime())
		{
			if (true/*LuaInterfaceProxy::getSingletonPtr()->shouldUseNewHpRule()*/) {

			}
			else {
				LivingAttrib* attrib = mpCtrl->getLivingAttrib();
				if (attrib)
				{
					attrib->addBuff(80, 1);
				}
			}



		}
	}
}

//����ɴ��ϵ�BUFF
void SleepStateAction::offRestInBedBuff()
{
	if (!isSleeping() && !isRestInBed())
	{
		LivingAttrib* attrib = mpCtrl->getLivingAttrib();
		if (attrib)
		{
			attrib->removeBuff(80, 1);
		}
	}
}

void SleepStateAction::playerTrySleep()
{
	if (!isSleeping() && isRestInBed())
	{
		setRestInBed(false);
		setSleeping(true);
		if (mpCtrl->getWorld() && !mpCtrl->getWorld()->isDaytime())
		{
			auto soundComp = mpCtrl->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound("misc.sleep", 1.0f, 1.0f);
			}
		}

		if (!mpCtrl->getWorld()->isRemoteMode())
		{
			PB_PlayerSleepHC playerSleepHC;
			playerSleepHC.set_flags(5);
			playerSleepHC.set_uin(mpCtrl->getUin());
			GameNetManager::getInstance()->sendBroadCast(PB_PLAYER_SLEEP_HC, playerSleepHC);
		}

		if (mpCtrl->getWorld() && g_WorldMgr && !mpCtrl->getWorld()->isRemoteMode())
		{
			m_sleepTickInDay = g_WorldMgr->getDayNightTime();
			m_sleepRealHour = g_WorldMgr->getHours();
		}
	}
}

void SleepStateAction::setSleeping(bool b)
{
	m_IsSleeping = b;
	mpCtrl->setFlagBit(ACTORFLAG_SLEEP, b);
}

bool SleepStateAction::isInBed()
{
	Block block;
	auto comp = mpCtrl->getActorBindVehicle();//by__Logo
	if (comp)
		block = comp->getVehicleAttachPosBlock();

	if (block.isEmpty() == false)
	{
		return IsBedBlock(block.getResID());
	}

	if (mpCtrl->getWorld() == NULL)
	{
		return false;
	}

	return IsBedBlock(mpCtrl->getWorld()->getBlockID(CoordDivBlock(mpCtrl->getPosition())));
}
