
#include "ClientActorLiving.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "OgreUtils.h"
#include "DefManagerProxy.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "ClientMob.h"
#include "ClientPlayer.h"
#include "ActorRocket.h"
#include "proto_common.h"
#include "MpActorManager.h"
#include "ClientActorManager.h"
#include "LuaInterfaceProxy.h"
#include "special_blockid.h"
#include "container_world.h"
#include "container_alientotem.h"
#include "BlockMaterialMgr.h"
#include "ActorHorse.h"
#include "ObserverEventManager.h"
#include "ActorVehicleAssemble.h"
#include "GameMode.h"
#include "PlayerControl.h"
#include <ctime>
#include "GameCamera.h"
#include "ServerInterpolTick.h"
#include "ActorFollow.h"
#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "BlockEnvEffectsComponent_Actor.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxEventObjectManager.h"
#include "PlayerAttrib.h"
#include "SandboxCoreDriver.h"
#include "SandboxListener.h"
#include "RiddenComponent.h"
#include "SoundComponent.h"
#include "AttackedComponent.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerLocoMotion.h"
#include "GameNetManager.h"
#include "ui_modelview.h"
#include "FishingComponent.h"
#include "LightningChainComponent.h"
#include "WeaponSkinMgr.h"
#include "UIActorBodyMgr.h"
#include "MpActorTrackerEntry.h"
#include "TemperatureComponent.h"
#include "RadiationComponent.h"
#include "SoundLogicComponent.h"
#include "SunHurtComponent.h"
#include "FindComponent.h"
#include "FireBurnComponent.h"
#include "ToAttackTargetComponent.h"
#include "ClientFlyComponent.h"
#include "ClientAquaticComponent.h"
#include "VacantVortexComponent.h"
#include "DieInDayComponent.h"
#include "ChargeJumpComponent.h"

#include "ActorBodySafeHandle.h"
#define NEUTRAL_ENEMY_ID 201
#define NEUTRAL_PASSIV_ID 202
#define VILLAGE_TEAM_ID 100

#include "Optick/optick.h"

using namespace MNSandbox;
using namespace Rainbow;
IMPLEMENT_SCENEOBJECTCLASS(ActorLiving)
ActorLiving::ActorLiving() : m_UIViewBody(NULL)
                             , m_CustomScale(1.0f), m_nAnimBodyId(-1)
							 , m_UIModelView(NULL), m_ModelViewIndex(0), m_pHPProgress(nullptr), m_attchUIFrom(FUIMODELSPRITE_ATTACH_FROM_NULL)
							 , m_pActorFollow(nullptr), m_isInDefanceState(false), m_pTeamComponent(nullptr)
{
	// 默认事件
	Event().CreateEventDispatcher("moveToPosition");

	// 挂载默认组件
	cacheBlockEnvEffectsComponent(CreateComponent<BlockEnvEffectsComponent_Actor>("BlockEnvEffectsComponent_Actor"));
	cacheToAttackTargetComponent(CreateComponent<ToAttackTargetComponent>("ToAttackTargetComponent"));
	cacheFindComponent(CreateComponent<FindComponent>("FindComponent"));
	CreateComponent<ServerInterpolTick>("ServerInterpolTick");
    m_pTeamComponent = CreateComponent<TeamComponent>("TeamComponent");
	CreateComponent<HPProgressComponent>("HPProgressComponent");
	m_pAttackingTargetComponent = CreateComponent<AttackingTargetComponentEx>("AttackingTargetComponent");
	cacheTemperatureComponent(CreateComponent<TemperatureComponent>("TemperatureComponent"));
	cacheRadiationComponent(CreateComponent<RadiationComponent>("RadiationComponent"));
	CreateComponent<SoundLogicComponent>("SoundLogicComponent");
	createEvent();
}

void ActorLiving::createEvent()
{
	typedef ListenerFunctionRef<bool&, ClientActor*, OneAttackData&> ListenerAttackedFrom;
	ListenerAttackedFrom* listenerAttackedFrom = SANDBOX_NEW(ListenerAttackedFrom, [this](bool& ret, ClientActor *attacker, OneAttackData &atkdata) -> void {
		ret = this->attackedFrom(atkdata, attacker);
	});
	Event2().Subscribe("attackedFrom", listenerAttackedFrom);

	//void setRidingActor(ClientActor*  actor)
	typedef ListenerFunctionRef<ClientActor*> ListenerSetRidingActor;
	ListenerSetRidingActor* listenerSetRidingActor = SANDBOX_NEW(ListenerSetRidingActor, [this](ClientActor* actor) -> void {
		this->setRidingActor(actor);
	});
	Event2().Subscribe("setRidingActor", listenerSetRidingActor);

	//void setFire(int buffid, int bufflv, int ticks = -1, long long fromObjid = 0)
	typedef ListenerFunctionRef<int, int, int, long long> ListenerSetFire;
	ListenerSetFire* listenerSetFire = SANDBOX_NEW(ListenerSetFire, [this](int buffid, int bufflv, int ticks, long long fromObjid) -> void {
		this->setFire(buffid,bufflv,ticks,fromObjid);
	});
	Event2().Subscribe("setFire", listenerSetFire);
}

ActorLiving::~ActorLiving()
{
	if (GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		//LOG_WARNING("~ ClientPlayer set #### : %p", m_UIViewBody);
		m_UIViewBody->setOwnerActorNull();
		ENG_DELETE(m_UIViewBody);
	}

	//if (m_UIViewBody)
	{
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	//	detachUIModelView(m_ModelViewIndex);
#endif
	}
	setAtkingTarget(NULL);	

	//这是个组件 析构在其他地方
	m_pActorFollow = nullptr;
	//ENG_DELETE(m_pServerInterpolTick);
	//ENG_DELETE(m_pActorFollow);
	//ENG_DELETE(m_pAttackingTargetCom);
	//ENG_DELETE(m_pHpProgressCom);
	//ENG_DELETE(m_pTeamCom);
}

void ActorLiving::enterWorld(World* pworld)
{
	ClientActor::enterWorld(pworld);

	LivingLocoMotion* livingLoc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (livingLoc)
		livingLoc->attachPhysActor();
}

void ActorLiving::leaveWorld(bool keep_inchunk)
{
	LivingLocoMotion* livingLoc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (livingLoc)
		livingLoc->detachPhysActor();

	setAtkingTarget(NULL);

	auto targetComponent = getToAttackTargetComponent();
	if (targetComponent)
	{
		targetComponent->setTarget(nullptr);
	}
	ClientActor::leaveWorld(keep_inchunk);
}

LightningChainComponent* ActorLiving::getLightningChainComponent()
{
	if (m_LightningChainComponent)
	{
		return m_LightningChainComponent;
	}

	m_LightningChainComponent = CreateComponent<LightningChainComponent>("LightningChainComponent");
	return m_LightningChainComponent;
}

FishingComponent* ActorLiving::getFishingComponent()
{
	if (!m_pFishingComponent)
	{
		return CreateComponent<FishingComponent>("FishingComponent");
	}
	else
	{
		return m_pFishingComponent;
	}
}

void ActorLiving::tick()
{
	OPTICK_EVENT();
	SANDBOXPROFILING_LOG(log, "ActorLiving::tick");
	//m_pServerInterpolTick->onTick();
	m_pHPProgress->initHurtTypePerTick();
	auto targetComponent = getToAttackTargetComponent();
	if (targetComponent)
	{
		targetComponent->checkClear();
	}
	ClientActor::tick();
	livingHPtick();

	if (m_pWorld == nullptr) 
	{
		return;
	}

	if (!m_pWorld->isRemoteMode())
	{
		attackTick();
	}

	//auto* pLightningCom = GetComponent<LightningChainComponent>();
	//if (pLightningCom)
	//{
	//	pLightningCom->OnTick();
	//}
	//auto* pFishingCom = GetComponent<FishingComponent>();
	//if (pFishingCom)
	//{
	//	pFishingCom->OnTick();
	//}

	if(m_pWorld->isRemoteMode()) return;

	if(!isDead())
	{
		bool subtract_oxygen = false;
		float oxygen_userate = getOxygenUseRate();
		if(m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR) oxygen_userate *= GetLuaInterfaceProxy().get_lua_const()->planet_oxygenuse_beilv;
		int OxygenUse = getOxygenUseInterval();
		if (OxygenUse == 0)
		{
			OxygenUse = 60;
		}
		//isWater 能判断是否在液体里面，在水下面的载具里面（没有真实碰到水）返回false
		if(oxygen_userate>0 && oxygen_userate < 2 && (m_LiveTicks%OxygenUse)==0 && getLocoMotion()->isInsideNoOxygenBlock() && isInWater())
		{
			if(getLivingAttrib()->getOxygen() > 0) 
				subtract_oxygen = true;
			getLivingAttrib()->addOxygen(-oxygen_userate);
		}

		if((m_LiveTicks%20)==0)
		{
			auto funcWrapper = getFuncWrapper();
			bool isRole = funcWrapper ? funcWrapper->isMinicodeRole() : false;
			if(!isRole && getLocoMotion()->isInsideOpaqueBlock())
			{
				WCoord pos;
				if(getLocoMotion()->getOneNoOpaqueBlock(&pos))
				{
					bool isUgcEdit = m_pWorld->GetWorldMgr()->isUGCEditMode();
					if (!isUgcEdit)
					{
						getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
					}
					 //getLocoMotion()->m_Position = pos;
					 
				}
				else
				{
					auto component = getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_WALL, 1.0f * GetLuaInterfaceProxy().get_lua_const()->zhixi_shanghai_beilv); // modify by null, 窒息伤害倍率
					}
				}
			}

			if(getLocoMotion()->isInsideNoOxygenBlock() && !isApplyOxyPack())
			{
				if(oxygen_userate>0 && !subtract_oxygen && getLivingAttrib()->getOxygen()==0)
				{
					ActorHorse *horse = dynamic_cast<ActorHorse *>(this);
					auto RidComp = getRiddenComponent();
					if (horse && horse->isUseSwimSpeed() && RidComp) {
						ActorLiving* riddenby = dynamic_cast<ActorLiving *>(RidComp->getRiddenByActor());
						if (riddenby) {
							return;
						}
					}
					float hurtrate = m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR ? GetLuaInterfaceProxy().get_lua_const()->planet_oxygenhurt_beilv : GetLuaInterfaceProxy().get_lua_const()->nishui_shanghai_beilv;
					
					auto component = getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_DROWN, 1.0f * hurtrate);
					}

					WCoord pos = getPosition();
					pos.x += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					pos.y += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					pos.z += GenRandomInt(BLOCK_SIZE) - GenRandomInt(BLOCK_SIZE);
					m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/1025.ent", pos, 40);
				}

				auto RidComp = getRiddenComponent();
				if(RidComp && RidComp->isRiding() && dynamic_cast<ActorLiving *>(RidComp->getRidingActor())!=NULL)
				{
					//--修改逻辑，可以在萌眼星球上车
					ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(RidComp->getRidingActor());
					ActorHorse *horse = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
					if ((!horse || !horse->canRriddenByNoOxygen()) && (!vehicle))
					{
						RidComp->mountActor(NULL);
					}
				}
			}

			if ( (!getLocoMotion()->isInsideNoOxygenBlock() || oxygen_userate == 2) && (m_LiveTicks % 60) == 0 )
			{
				getLivingAttrib()->addOxygen(1.0f);
			}
		}
	}

	//跟随状况检测
	//m_pActorFollow->onTick();
	//Super::Tick(0.05f); // 组件跟随所属一起tick
	
	//冰冻扛起暂时屏蔽
	//auto checkForzenBuff = [](ActorLiving* living)
	//{
	//	if (living && living->getLivingAttrib() && living->getLivingAttrib()->hasBuff(FORZEN_BUFF))
	//	{
	//		return true;
	//	}
	//	return false;
	//};
	//auto CarryComp = getCarryComponent();
	//if (CarryComp && !m_pWorld->isRemoteMode())
	//{
	//	auto* actor = CarryComp->getCarriedActor();
	//	if (actor && getLocoMotion() && actor->getLocoMotion())
	//	{
	//		getLocoMotion()->m_RotateYaw = getLocoMotion()->m_PrevRotateYaw = actor->getLocoMotion()->m_RotateYaw + 90;

	//		auto actorCarryComp = actor->getCarryComponent();
	//		if (actor && actorCarryComp && /*actor->m_CarryingActor != getObjId()*/!actorCarryComp->checkCarringActor(getObjId()))
	//		{
	//			CarryComp->setCarriedActor(NULL);
	//			WCoord pos = actor->getPosition();
	//			pos.y += 90;
	//			int range = BLOCK_SIZE * 3 / 2;
	//			pos.x += GenRandomInt(range) - GenRandomInt(range);
	//			pos.z += GenRandomInt(range) - GenRandomInt(range);
	//			setPosition(pos);
	//		}

	//		if (isDead() || needClear() || !checkForzenBuff(this))
	//		{
	//			if (actor && actorCarryComp &&/*actor->m_CarryingActor == getObjId()*/actorCarryComp->checkCarringActor(getObjId()))
	//			{
	//				ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
	//				if (player)
	//					player->carryActor(NULL, player->getPosition());
	//			}
	//		}
	//	}
	//	else if (CarryComp->isCarried())
	//	{
	//		CarryComp->setCarriedActor(NULL);
	//		if (actor)
	//		{
	//			WCoord pos = actor->getPosition();
	//			pos.y += 90;
	//			int range = BLOCK_SIZE * 3 / 2;
	//			pos.x += GenRandomInt(range) - GenRandomInt(range);
	//			pos.z += GenRandomInt(range) - GenRandomInt(range);
	//			setPosition(pos);
	//		}
	//	}
	//}
}

void ActorLiving::attackTick()
{
	if (getAttackingTargetComponent())
		getAttackingTargetComponent()->attackTick();
}

void ActorLiving::bindFishingComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pFishingComponent = dynamic_cast<FishingComponent*>(pComponent);
}

void ActorLiving::update(float dtime)
{
	ClientActor::update(dtime);

	if (m_pFishingComponent)
	{
		m_pFishingComponent->OnUpdate();
	}

//#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
//	if(m_UIViewBody && m_UIViewBody->getIsAttachModelView()) 
//		m_UIViewBody->update(dtime);
//#else
//	if(m_UIViewBody && m_UIViewBody->getAttachedModelView()) m_UIViewBody->update(dtime);
//#endif
	// 添加一个野指针判断

	if (m_UIViewBody)
	{
		if (!GetActorBodySafeHandle()->IsVaild(m_UIViewBody)) {
			//assert(false);
			m_UIViewBody = nullptr;
		}
	}

	if (m_UIViewBody)
	{
		bool needUpdate = false;
		if (getBody())
		{
			int num1 = getBody()->getThornBallModeInfo().size();
			int num2 = m_UIViewBody->getThornBallModeInfo().size();
			needUpdate = (num1 != num2);

			if (needUpdate)
			{
				m_UIViewBody->removeThornBallMesh(true);
				auto thronInfo = getBody()->getThornBallModeInfo();
				for (auto iter = thronInfo.begin(); iter != thronInfo.end(); iter++)
				{
					m_UIViewBody->createThornBallMeshModel((*iter).AnchorId, (*iter).Pos);
				}
			}
		}
	}
}

bool ActorLiving::canBePushed()
{
	return !needClear();
}

void ActorLiving::NotifyPlayAnim2Tracking(int anim, bool include_me /* = false */, int anim1/* =127 */, int isLoop/* =0 */)
{
	if(m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_ActorAnimHC actorAnimHC;
		actorAnimHC.set_actorid(getObjId());
		actorAnimHC.set_anim(anim);
		actorAnimHC.set_anim1(anim1);
		actorAnimHC.set_isloop(isLoop);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_ANIM_HC, actorAnimHC, this, include_me, UNRELIABLE_SEQUENCED);
	}
}

void ActorLiving::NotifyStopAnim2Tracking(int anim, bool include_me)
{
	if (!m_pWorld->isRemoteMode())
	{
		PB_ActorStopAnimHC actorStopAnimHC;
		actorStopAnimHC.set_actorid(getObjId());
		actorStopAnimHC.set_anim(anim);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_STOP_ANIM_HC, actorStopAnimHC, this, include_me, UNRELIABLE_SEQUENCED);
	}
}


bool ActorLiving::canAttack()
{
	return true;
}

bool ActorLiving::attackActor(ClientActor *target, int seq, int targetIndex) //916冒险 2021/08/18 codeby:wudeshen
{
	bool comboAttack = ComboAttackStateRunning();
	if (!comboAttack && !canAttack())
		return false;

	PlayerLocoMotion* motion = dynamic_cast<PlayerLocoMotion*>(getLocoMotion());
	if (motion)
	{
		motion->triggerEffectAttackMotion();
	}
	setAtkingTarget(target, targetIndex);

	if (getAttackingTargetComponent())
	{
		if (comboAttack)
			getAttackingTargetComponent()->doAttackAnim(ATTACK_PUNCH);
		else
			getAttackingTargetComponent()->setAttackAnim(ATTACK_PUNCH);
	}

	if (!comboAttack)
	{
		if (!isPlayer())
			stopAnim(seq);
		playAnim(seq);
	}

	getHPProgressComponent()->updateLastAttackTime();

	//ClientPlayer * player = dynamic_cast<ClientPlayer*>(this);  //近战武器加buff  code-by:yanfengying
	//ActorLiving * living = dynamic_cast<ActorLiving*>(target);
	//if (player && living)
	//{
	//	BackPackGrid* grid = player->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	//	int toolId = player->getCurToolID();
	//	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(toolId);
	//	if (toolId != 0 && tooldef != NULL && tooldef->punchBuffId && grid && grid->getDuration() > 0)
	//	{
	//		living->getLivingAttrib()->addBuff(tooldef->punchBuffId, tooldef->punchBuffV, -1, 0, getObjId());
	//	}
	//}
	return true;
}

void ActorLiving::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		pClientFlyComponent->moveToPosition(pos, yaw, pitch, interpol_ticks);
		return;
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		pClientAquaticComponent->moveToPosition(pos, yaw, pitch, interpol_ticks);
		return;
	}

	//m_pServerInterpolTick->moveToPosition(pos, yaw, pitch, interpol_ticks);
	MNSandbox::SandboxContext context(this);
	context.SetData_UserObject("pos", pos);
	context.SetData_Number("yaw", (double)yaw);
	context.SetData_Number("pitch", (double)pitch);
	context.SetData_Number("interpol_ticks", (double)interpol_ticks);
	Event().Emit("moveToPosition", context);
}

void ActorLiving::playAnim(int seq, bool include_me /* = false */, int seq1/* =127 */, int isLoop/* =-1 */)
{
	if(getBody())
	{
 		getBody()->playAnim(seq, isLoop);
		NotifyPlayAnim2Tracking(seq, include_me, seq1, isLoop);
	}
}


void ActorLiving::stopAnim(int seq, bool include_me)
{
	if (getBody())
	{
		getBody()->stopAnim(seq);
		NotifyStopAnim2Tracking(seq, include_me);
	}
}


bool ActorLiving::castShadow()
{
    OPTICK_EVENT();
	//return m_RidingActor == 0;
	auto pVacantVortexComponent = getVacantVortexComponent();
	if (pVacantVortexComponent)
	{
		return pVacantVortexComponent->castShadow();
	}
	else
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			return RidComp->checkRidingByActorObjId(0);
		}
		else
		{
			return true;
		}
	}
	return false;
}

WCoord ActorLiving::getEyePosition()
{
	float bodyScale = getScale();
	float scale = 1.0;

	LivingAttrib* attr = static_cast<LivingAttrib*>(getAttrib());
	if (attr)
		scale += attr->getModAttrib(MODATTR_ACTOR_SCALE);

	float realScale = scale * bodyScale;//* m_CustomScale; getEyeHeight时获取了已经放大过的m_BoundHeight，故不需要乘以m_CustomScale
	int realEyeHeight = (int)(realScale * getEyeHeight());
	return getLocoMotion()->getPosition() + WCoord(0, realEyeHeight, 0);
}

void ActorLiving::setScale(float s)
{
	if (!getBody())
		return;
	
	if (s <= 0.00001f) // 灏忎簬绛変簬0琛ㄧず鍒锋柊 EPSINON
	{
		s = getScale();
	}

	LivingAttrib* attr = dynamic_cast<LivingAttrib *>(getAttrib());
	if(attr)
	{
		float scale = 1.0f + attr->getModAttrib(MODATTR_ACTOR_SCALE);
		getBody()->setScale(s);
		getBody()->setRealScale(scale*s*m_CustomScale);

		if(m_pWorld && !m_pWorld->isRemoteMode())
		{
			PB_PlayerScaleHC playerScaleHC;
			playerScaleHC.set_uin(0);
			playerScaleHC.set_scale(m_CustomScale);
			playerScaleHC.set_objid(getObjId());

			//m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_ATTR_SCALE_HC, playerScaleHC, this, true);
			GetGameNetManagerPtr()->sendBroadCast(PB_PLAYER_ATTR_SCALE_HC, playerScaleHC);
		}
	}
}

float ActorLiving::getScale()
{
	if (getBody())
		return getBody()->getBodyScale();

	return 1.0f;
}

void ActorLiving::updateScale()
{
	setScale(0.0f);
}

void ActorLiving::setCustomScale(float s)
{
	// 限制最大缩放比例，太大渲染顶点索引会报错，太小会导致看不见
	if (s > 50.0f)
		s = 50.0f;
	else if (s < 0.1f)
		s = 0.1f;

	m_CustomScale = s;

	// 更新实际缩放
	updateScale();
}

LivingAttrib* ActorLiving::getLivingAttrib()
{
	return dynamic_cast<LivingAttrib*>(getAttrib());
}

int ActorLiving::GetAttchUIFrom()
{
	return m_attchUIFrom;
}

void ActorLiving::attachUIModelView(Rainbow::UILib::ModelView *modelview, int index, int attachFrom)
{
	if(m_UIViewBody == NULL)
	{
		m_UIViewBody = newActorBody();
		LOG_INFO("ActorLiving::attachUIModelView new:%p",m_UIViewBody);
		GET_SUB_SYSTEM(UIActorBodyMgr)->AddActorBodySpecialComp(m_UIViewBody);
	}

	if(m_UIViewBody){
		//chenyu add
		m_UIViewBody->setIsInUI(true);

		m_UIViewBody->setScale(getUIModelViewScale());
		getLivingAttrib()->applyEquips(m_UIViewBody);
		LOG_INFO("ActorLiving::attachUIModelView :%p,%p,%d",m_UIViewBody,modelview,index);
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
		if (modelview)
		{
			//modelview->SetManageMode(false);
			modelview->attachActorBody(m_UIViewBody, index);
		}
		m_UIModelView = modelview;
		m_attchUIFrom = attachFrom;

		if (m_attchUIFrom != FUIMODELSPRITE_ATTACH_FROM_NULL)
		{
			MINIW::ScriptVM::game()->callFunction("ActorLivingAttachUI", "i", m_attchUIFrom);
		}
#else
		m_UIViewBody->attachUIModelView(modelview, index);
#endif

		getLivingAttrib()->ApplyWeaponEnchantEffect(m_UIViewBody);
		m_UIViewBody->setNeedUpdateLookUp(false);
		m_UIViewBody->playAnim(SEQ_STAND);
	}
}

void ActorLiving::detachUIModelView(int index)
{
	LOG_INFO("ActorLiving::detachUIModelView:%p",m_UIViewBody);
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (m_UIModelView && m_UIViewBody)
	{
		m_UIModelView->detachActorBody(m_UIViewBody, index);
		//if (GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
		//{
		//	m_UIViewBody->setOwnerActorNull();
		//}
		//m_UIViewBody = nullptr;
	}
	else if (m_UIViewBody && m_attchUIFrom != FUIMODELSPRITE_ATTACH_FROM_NULL)
	{
		MINIW::ScriptVM::game()->callFunction("ActorLivingDetachUI", "i", (int)m_attchUIFrom);
		m_attchUIFrom = FUIMODELSPRITE_ATTACH_FROM_NULL;
	}
	else if(GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		m_UIViewBody->AttachScene(nullptr);
	}

#else
	if(m_UIViewBody) m_UIViewBody->detachUIModelView(NULL);
#endif
}

ActorBody *ActorLiving::getUIViewBody()
{
	return m_UIViewBody;
}

int ActorLiving::getOxygenUseInterval()
{
	return 60;
}

float ActorLiving::getOxygenUseRate()
{
	return 1.0f;
}

bool ActorLiving::attackedFrom(OneAttackData& atkdata, ClientActor* attacker)
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
	{
		return false;
	}

	if (getLivingAttrib()->hasBuff(SUDDEN_ILLNESS_BUFF)) {
		getLivingAttrib()->removeBuff(SUDDEN_ILLNESS_BUFF);
	}

	auto hppc = getHPProgressComponent();
	if (!hppc)
	{
		return false;
	}
	hppc->onAttackedFrom(atkdata, attacker);

	// 记录直接攻击者，盾牌防御需要用到
	if (!(atkdata.directAttacker))
	{
		atkdata.directAttacker = attacker;
	}
	if (atkdata.fromplayer)
	{
		attacker = dynamic_cast<ClientActor*>(atkdata.fromplayer);
	}


	if (atkdata.buffId == 0)
	{
		// 普通攻击，需要带上攻击生物身上的buffer
		ClientMob *mob = dynamic_cast<ClientMob*>(attacker);
		if (mob && mob->m_Def->BuffId != 0)
		{
			atkdata.buffId = mob->m_Def->BuffId / 1000;
			atkdata.buffLevel = mob->m_Def->BuffId % 1000;
		}
	}

	if(atkdata.atktype != ATTACK_EXPLODE) //爆炸伤害不分敌友
	{
		// 解耦，逻辑放到对应子类中 code_by:xiehaijiao
		//ActorLiving *atkliving = dynamic_cast<ActorLiving *>(attacker);
		//ClientMob * pmob = dynamic_cast<ClientMob*>(this);
		//if (!pmob)
		//{
		//	//判断当前规则是否为自由攻击
		//	bool allow = false;
		//	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
		//	{
		//		int opt = GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_ATTACKPLAYER);
		//		if (opt == 0)
		//		{
		//			allow = true;
		//		}
		//	}
		//	if (!allow)
		//	{
		//		//不是自由攻击的话，同队之间不能攻击
		//		if (atkliving != NULL && isSameTeam(atkliving))
		//		{
		//			return false;
		//		}
		//	}
		//}
		if (!checkGameRule(dynamic_cast<ActorLiving*>(attacker)))
		{
			return false;
		}
	}

	if ((atkdata.atktype < MAX_PHYSICS_ATTACK || atkdata.atktype == ATTACK_FLASH) && attacker)
	{
		setBeHurtTarget(attacker);
		setBeAtk(attacker);
	}
	else if (atkdata.triggerhit) // 触发器直接设置的伤害
	{
		setBeHurtTarget(attacker);
		setBeAtk(attacker);
	}

	if (isPlayer()&&!attacker)
	{
		setBeHurtTarget(nullptr);
		setBeAtk(nullptr);
	}
	hppc->setLastAttackedType(atkdata.atktype);//上次受击类型应该在掉血前记录
	hppc->updateLastAttackTime();

	if(!getAttrib()->attackedFrom(atkdata, attacker))
	{
		return false;
	}

	/*BeAttackedEvent e(this, &atkdata, attacker);
	MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("DispatchEvent",
		MNSandbox::SandboxContext(nullptr).SetData_Usertype("beAttackedEvent", &e));*/
	MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_DispatchEvent",
		MNSandbox::SandboxContext(nullptr).SetData_Usertype("target", this).SetData_Usertype("atkdata", &atkdata).SetData_Usertype("attacker", attacker));

	if(isDead())
		playDeathSound();
	else
		playHurtSound();

	

	if (attacker)
	{
		ActorLiving *actorLiving = dynamic_cast<ActorLiving *>(attacker);
		if (actorLiving == NULL)
		{
			ClientActor * shooter = attacker->getShootingActor();
			actorLiving = dynamic_cast<ActorLiving *>(shooter);
		}

		if (actorLiving) actorLiving->setHPProgressDirty();
	}

	return true;
}

void ActorLiving::playHurtSound()
{
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound("misc.hit", getSoundVolume(), getSoundPitch());
	}
	
}

void ActorLiving::playDeathSound()
{
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound("misc.hit", getSoundVolume(), getSoundPitch());
	}
}

/*
void ActorLiving::playStepSound()
{
	float volume = 0.5f;
	float pitch = 1.0f;

	WCoord blockpos = CoordDivBlock(getLocoMotion()->getPosition());
	int blockid = m_pWorld->getBlockID(blockpos);
	if(blockid > 0)
	{
		const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		if(blockdef && blockdef->MoveCollide==1)
		{
			playSound(blockdef->WalkSound, volume, pitch);
			return;
		}
	}

	blockid = m_pWorld->getBlockID(DownCoord(blockpos));
	if(blockid > 0)
	{
		const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		if(blockdef && blockdef->MoveCollide==1)
		{
			playSound(blockdef->WalkSound, volume, pitch);
		}
	}
}*/

void ActorLiving::playStepSound()
{
	float volume = 0.5f;
	float pitch = 1.0f;

	WCoord blockpos = CoordDivBlock(getLocoMotion()->getPosition()-WCoord(0,10,0));
	int blockid = m_pWorld->getBlockID(blockpos);
	if(blockid > 0)
	{
		const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
		if (blockdef)
		{
			auto sound = getSoundComponent();
            if (sound && blockdef->WalkSound.IsVaild()) {
                //std::string strwalkSound = walksound.c_str();		
                sound->playSound(blockdef->WalkSound, GSOUND_WALK);
            }
		}
		
	}
}

void ActorLiving::playClimbStepSound()
{
	float volume = 1.0f;
	float pitch = 1.0f;

	int blockid;
	WCoord blockpos = CoordDivBlock(getLocoMotion()->getPosition()+WCoord(0,getLocoMotion()->m_yOffset,0));

	for(int i=0; i<4; i++)
	{
		WCoord ng = NeighborCoord(blockpos, i);
		blockid = m_pWorld->getBlockID(ng);
		if(!IsAirBlockID(blockid))
		{
			const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
			//playSound(blockdef->WalkSound, GSOUND_WALK);
			auto sound = getSoundComponent();
			if (sound)
			{
				Rainbow::FixedString walksound = blockdef->WalkSound;
				if (walksound.IsVaild()) 
				{
					sound->playSound(blockdef->WalkSound, volume, pitch);
				}
			}
			break;
		}
	}
}
void  ActorLiving::playAttackSound()
{

}

float ActorLiving::getSoundVolume()
{
	return 0.8f;
}

float ActorLiving::getSoundPitch()
{
	bool ischild = false;
	if(ischild) return (GenRandomFloat()-GenRandomFloat())*0.2f + 1.5f;
	else return (GenRandomFloat()-GenRandomFloat())*0.2f + 1.0f;
}

void ActorLiving::onBuffChange(int chgtype, int buffid, int bufflv, int buffticks, int buffInstanceId) //chgtype=0:add, 1:remove, 2:clear all
{
	if(m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_ActorBuffChangeHC actorBuffChangeHC;
		actorBuffChangeHC.set_objid(getObjId());

		if (chgtype != 2)
		{
			PB_ActorBuff* buff = actorBuffChangeHC.add_buffs();
			buff->set_buffid(buffid);
			buff->set_bufflv(chgtype == 0 ? bufflv : 0);
			buff->set_ticks(buffticks);
			buff->set_buffinstanceid(buffInstanceId);
		} 

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_BUFF_CHANGE_HC, actorBuffChangeHC, this, true);
	}
}

bool ActorLiving::isBurning()
{
	LivingAttrib *attrib = static_cast<LivingAttrib *>(getAttrib());
	return attrib->hasBuffByNature(1);
}

void ActorLiving::setFire(int buffid, int bufflv, int ticks /* = -1 */, long long fromObjid /* = 0*/)
{
	LivingAttrib *attrib = static_cast<LivingAttrib *>(getAttrib());
	if(attrib == NULL) return;

	if(buffid <= 0)
	{
		attrib->removeBuffByNature(1);
	}
	else
	{
		attrib->addBuff(buffid, bufflv, ticks, 0, fromObjid);
	}
}
#pragma region Logo todo:delete

void ActorLiving::setAtkingTarget(ClientActor *target, int targetIndex)
{
	if (getAttackingTargetComponent())
		getAttackingTargetComponent()->setTarget(target, targetIndex);
}

int ActorLiving::getAtkingTimer()
{
	if (getAttackingTargetComponent())
	{
		return getAttackingTargetComponent()->getAtkingTimer();
	}
	return 0;
}
ClientActor *ActorLiving::getAtkingTarget()
{
	if (getAttackingTargetComponent())
	{
		return getAttackingTargetComponent()->getTarget();	
	}
	return nullptr;
}

int ActorLiving::getAttackAnimTicks(ATTACK_TYPE attacktype)
{
	if (getAttackingTargetComponent())
	{
		return getAttackingTargetComponent()->getAttackAnimTicks(attacktype);
	}
	return 0;
}
bool ActorLiving::isAttacking()
{
	if (getAttackingTargetComponent())
	{
		return getAttackingTargetComponent()->isAttacking();
	}
	return false;
}
#pragma endregion

bool ActorLiving::canActorBeSeen(ClientActor *actor)
{
	WCoord p1 = getEyePosition();
	WCoord p2 = actor->getEyePosition();

	return !m_pWorld->clip(p1, p2);
}

bool ActorLiving::isActorInLookDir(ClientActor *actor, float fov, bool ignore_y)
{
	Rainbow::Vector3f lookdir = getLocoMotion()->getLookDir();
	if(ignore_y) lookdir.y = 0.0f;
	lookdir = MINIW::Normalize(lookdir);

	Rainbow::Vector3f dir = (actor->getPosition() - getPosition()).toVector3();
	if(ignore_y) dir.y = 0.0f;
	float dirlen = dir.Length();
	if(dirlen < Rainbow::EPSILON) dirlen = Rainbow::EPSILON;
	dir /= dirlen;

	float dotp = DotProduct(lookdir, dir);
	float tmp = 1.0f-fov/dirlen;
	if(tmp < 0) tmp = 0;

	return dotp>tmp;
}

bool ActorLiving::isApplyOxyPack()
{
	if(getObjType() == OBJ_TYPE_ROLE)
	{
		//氧气背包
		/*
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
		if(player->getCurDorsumID() == ITEM_OXYGEN_PACK)
			return true;
		*/

		if(getLivingAttrib()->getEquipItemWithType(EQUIP_PIFENG) == ITEM_OXYGEN_PACK)
			return true;

		// 萌眼星新增氧气面罩抵消减益Buff code-by:lizb
		if (getLivingAttrib()->getEquipItemWithType(EQUIP_PIFENG) == ITEM_OXYGEN_MASK)
			return true;
	}

	return false;
}

void ActorLiving::doActualItemSkillAttack(ClientActor *target, ATTACK_TYPE type, int demage, int touReduce)
{
	OneAttackData atkdata;

	if(target==NULL || target->isDead()) return ;
	if(!target->canAttackWithItem()) return ;
	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((type >= ATTACK_PUNCH && type <= MAX_MAGIC_ATTACK) || type == PHYSICS_ATTACK))
	{
		const int index = AtkType2ArmorIndex(type);
		atkdata.atkTypeNew = (1 << index);
		// 爆炸
		if (type == ATTACK_EXPLODE)
		{
			atkdata.explodePoints[0] = (float)demage;
		}
		else
		{
			atkdata.atkPointsNew[index] = (float)demage;
		}
	}
	else
	{
		atkdata.atktype = type;
		atkdata.atkpoints = (float)demage;
	}
	atkdata.enchant_atk = 0;
	atkdata.buff_atk = 0;
	atkdata.touReduce = touReduce;
	LivingAttrib *attrib = getLivingAttrib();
	if(targettype < ATTACK_TARGET_OTHERS) atkdata.buff_atk += attrib->getModAttrib(MODATTR_ATTACK_PLAYER+targettype);

	auto RidComp = getRiddenComponent();
	auto functionWrapper = getFuncWrapper();
	atkdata.critical = functionWrapper && functionWrapper->getFallDistance() >0 && !getLocoMotion()->m_OnGround && !getLocoMotion()->isOnLadder() && !getLocoMotion()->m_InWater && !(RidComp && RidComp->isRiding());
	atkdata.fromplayer =  dynamic_cast<ClientPlayer *>(this);
	auto component = target->getAttackedComponent();
	if (component)
	{
		component->attackedFrom(atkdata, this);
	}
}



bool ActorLiving::isMineTool(int blockid, const ToolDef *tooldef, float maxhardness /* = Rainbow::MAX_FLOAT */)
{
	auto mtl = g_BlockMtlMgr.getMaterial(blockid);
	const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
	if (mtl && mtl->getBlockHardness() >= maxhardness)//(blockdef->Hardness >= maxhardness)
	{
		return false;
	}
	int needtool = blockdef->MineTool;
	if (needtool > 0)
	{
		//const ToolDef *needtooldef = GetDefManagerProxy()->getToolDef(needtool);
		if (tooldef && needtool == tooldef->Type && blockdef->ToolLevel <= tooldef->Level)
		{
			return true;
		}
		else
			return false;
	}

	return true;
}

int ActorLiving::getMineBlockTicks(int toolid, int blockid, int blockdata, BLOCK_MINE_TYPE *minetype_ret /* = NULL */)
{
	if (minetype_ret) *minetype_ret = BLOCK_MINE_NONE;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) return 0;

	BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (blockmtl == NULL) return 0;

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
	float hard = blockmtl->getDestroyHardness(blockdata, player);
	if (hard < 0) return Rainbow::MAX_INT;

	//int toolid = getCurToolID();
	if (toolid == 0) toolid = 11000;
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(toolid);
	float skinAdd = 0.f; //皮肤加成

	BLOCK_MINE_TYPE minetype = BLOCK_MINE_NOTOOL;
	if (isMineTool(blockid, tooldef))
	{
		if (getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_DIG_PRECISE) > 0) minetype = BLOCK_MINE_PRECISE;
		else minetype = BLOCK_MINE_TOOLFIT;

		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != NULL)
		{
			// 皮肤加成 -- 效率加成 
			const char* attrType = "WeaponSkin_System_Efficiency";
			skinAdd = weaponSkinMgr->GetSkinAdditionPercent(attrType, player, toolid);
		}
	}

	if (minetype_ret) *minetype_ret = minetype;
	if (hard == 0) return 0;

	float efficiency = 1.0f;
	if (tooldef) {
		int toolEfficiency = getLivingAttrib()->getEquipMineBlockEfficiency(tooldef->Efficiency);
		efficiency = (toolEfficiency / 100.0f + 1.0f) * (1.0f + getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_DIG_SPEED));
	}

	float basictime = hard * (1.5f / 5.0f);
	if (minetype == BLOCK_MINE_NOTOOL)
	{
		basictime *= 4.0f;
		efficiency = 1.0f;
	}

	float geniusV = 0;
	if (player)
		geniusV = player->getGeniusValue(GENIUS_DIGSPEED_INC);
	efficiency *= 1.0f + geniusV + skinAdd;

	float ticks = basictime*20.0f / efficiency;
	if (player) {
		float fAdd = player->getPlayerAttrib()->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_DIG_SPEED, 1.0f);
		ticks *= (1.0f - fAdd);

		if (ticks < 1.0f) {
			ticks = 1.0f;
		}
	}

	return int(ceil(ticks));
}


void ActorLiving::setRidingActor(ClientActor * p)
{
	/*if (p) m_RidingActor = p->getObjId();
	else m_RidingActor = 0;*/

	auto RidComp = sureRiddenComponent();
	if (RidComp)
	{
		RidComp->setRidingActor_Base(p);
	}
	
	setHPProgressDirty();
}

bool ActorLiving::playActForTriggerForActorbody(int act)
{
	ActorBody* body = getBody();
	auto def = GetDefManagerProxy()->getTriggerActDef(act);
	if (def)
	{

		if (body->getCurAnim(0) == SEQ_PLAY_ACT)
			body->setCurAnim(-1, 0);
		if (body->getEntity())
			body->getEntity()->StopMotion(30000);

		body->setActID(-1);
		body->setActTriggerID(act);
		auto tracker = getWorld()->getMpActorMgr()->getTrackerEntry(getObjId());
		if (tracker)
		{
			// 重置同步字段，否则可能有字段可能不会同步 20211127 codeby:liusijia
			tracker->resetAniSendState();
		}

		if (!body->isAvatarModel() && body->getInitTex() && body->getFaceMesh())
		{
			body->getFaceMesh()->SetTexture("g_DiffuseTex", body->getInitTex());
		}
		return true;
	}
	return false;
}

bool ActorLiving::playActForTrigger(int act)
{
	if (m_pWorld->isRemoteMode())
		return true;

	if (!getBody())
		return false;

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getRidingActor())
		return false;

	return playActForTriggerForActorbody(act);
}

//HPProgressComponent-----
ATTACK_TYPE ActorLiving::getLastAttackedType()
{
	return getHPProgressComponent()->getLastAttackedType();
}

int ActorLiving::getHurtType()
{
	return getHPProgressComponent()->getHurtType();
}

void ActorLiving::addHPEffect(float hp)
{
	getHPProgressComponent()->addHPEffect(hp);
}

void ActorLiving::addArmorEffect(float hp)
{
	getHPProgressComponent()->addHPEffect(hp);
}


void ActorLiving::setHPProgressDirty()
{
	getHPProgressComponent()->setHPProgressDirty();
}

void ActorLiving::setArmorProgressDirty()
{
	getHPProgressComponent()->setHPProgressDirty();
}

void ActorLiving::livingHPtick()
{
	m_pHPProgress->livingHPtick();
	//护甲是覆盖在生命上的
	armorTick();
}

void ActorLiving::mobHPTick()
{
	getHPProgressComponent()->mobHPTick();
}

void ActorLiving::armorTick()
{


}
void ActorLiving::bindHPProgress(HPProgressComponent* pComponent)
{
	m_pHPProgress = pComponent;
}

int ActorLiving::getTeamType(ActorLiving* target)
{
	//目标对象是自己
	if (target->getObjId() == getObjId())
	{
		return TEAM_TYPE_FRIENDLY;
	}

	auto teamComp = m_pTeamComponent;
	int teamId = teamComp->getTeam();
	int targetTeamId = target->getTeam();
	if (teamId == NEUTRAL_ENEMY_ID || teamId == NEUTRAL_PASSIV_ID)
	{
		if (targetTeamId == NEUTRAL_ENEMY_ID || targetTeamId == NEUTRAL_PASSIV_ID)
		{
			return TEAM_TYPE_FRIENDLY;
		}
		else
		{
			if (teamId == NEUTRAL_ENEMY_ID)
			{
				return TEAM_TYPE_ENEMY;
			}
			else
			{
				return TEAM_TYPE_NEUTRAL;
			}
		}
	}

	if (teamId >= 0 && teamId <= VILLAGE_TEAM_ID)
	{
		if (targetTeamId == NEUTRAL_PASSIV_ID || (targetTeamId > VILLAGE_TEAM_ID && targetTeamId != NEUTRAL_ENEMY_ID))
		{
			return TEAM_TYPE_NEUTRAL;
		}
		else if(targetTeamId > 0 && targetTeamId <= VILLAGE_TEAM_ID && targetTeamId == teamId)
		{
			return TEAM_TYPE_FRIENDLY;
		}

		return TEAM_TYPE_ENEMY;
	}

	if (teamId > VILLAGE_TEAM_ID && teamId < NEUTRAL_ENEMY_ID)
	{
		if (targetTeamId == NEUTRAL_ENEMY_ID)
		{
			return TEAM_TYPE_ENEMY;
		}
		else if (targetTeamId == teamId)
		{
			return TEAM_TYPE_FRIENDLY;
		}

		return TEAM_TYPE_NEUTRAL;
	}

	return TEAM_TYPE_ANY;
}

HPProgressComponent* ActorLiving::getHPProgressComponent()
{
	return m_pHPProgress;
}

void ActorLiving::setInDefanceState(bool isDefanceState)
{
	m_isInDefanceState = isDefanceState;

	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_ActorDefanceStateCH actorDefanceStateCH;
		actorDefanceStateCH.set_objid(getObjId());
		actorDefanceStateCH.set_defancestate(isDefanceState);
		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_DEFANCESTATE_CH, actorDefanceStateCH);
	}
}

void ActorLiving::SetForcedMove(float knockback, float knockup, ClientActor* actor)
{
	/*
	* 击退抵抗
	* 怪物：装备叠加、自身配置
	* 玩家：装备叠加
	*/
	LivingAttrib* attrib = getLivingAttrib();
	if (attrib)
	{
		attrib->handleRepelResist(knockback);
	}

	WCoord dp = actor->getLocoMotion()->getPosition() - getLocoMotion()->getPosition();

	dp.y = 0;
	if (dp.isZero())
	{
		dp.x = GenRandomInt(2) == 0 ? -1 : 1;
		dp.z = GenRandomInt(2) == 0 ? -1 : 1;
	}

	Rainbow::Vector3f vec = dp.toVector3();
	float len = vec.Length();
	float force = 40.0f * knockback;

	bool motionChange = false;
	Rainbow::Vector3f motion = Rainbow::Vector3f::zero;//actor->getLocoMotion()->m_Motion * 0.5f;
	if (knockback > std::numeric_limits<float>::epsilon())
	{
		motion.x -= vec.x / len * force;
		motion.z -= vec.z / len * force;
		motionChange = true;
	}
	//motion.y += force;
	// 防御状态下击飞无效 code-by:liya
	if (!IsInDefanceState() && knockup > std::numeric_limits<float>::epsilon())
	{
		motion.y += 40.0f * knockup;
		motionChange = true;
	}

	if(motionChange)
		setMotionChange(motion, true);
}

void ActorLiving::getsyncData(jsonxx::Object &data)
{
	ClientActor::getsyncData(data);
	LivingAttrib *livingattr = getLivingAttrib();
	if (!livingattr)
	{
		return;
	}
	if (!data.has<jsonxx::Array>("equips"))
	{
		data<<"equips"<<jsonxx::Array();
	}
	jsonxx::Array &obj_ = (jsonxx::Array &)data.get<jsonxx::Array>("equips");
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		BackPackGrid *itemgrid = livingattr->getEquipGrid((EQUIP_SLOT_TYPE)i);
		obj_.addObject();
		jsonxx::Object &obj_itemgrid = (jsonxx::Object &)obj_.get<jsonxx::Object>(i);
		obj_itemgrid << "index_" << i;
		if (itemgrid == NULL || itemgrid->isEmpty())
		{
			int item = 0;
			obj_itemgrid << "itemid" << item;
			continue;
		}
		itemgrid->saveWithIndex(obj_itemgrid);
	}
}

void ActorLiving::setsyncData(jsonxx::Object &data)
{
	ClientActor::setsyncData(data);
	LivingAttrib *livingattr = getLivingAttrib();
	if (!livingattr)
	{
		return;
	}
	if (data.has<jsonxx::Array>("equips"))
	{
		auto attr = getLivingAttrib();
		auto playerAttr = dynamic_cast<PlayerAttrib *>(attr);
		auto equips = data.get<jsonxx::Array>("equips");
		BackPackGrid itemgrid;
		for(size_t i=0; i<equips.size(); i++)
		{
			itemgrid.load(equips.get<jsonxx::Object>(i));
			if (equips.get<jsonxx::Object>(i).has<jsonxx::Number>("index_"))
			{
				int index_ = equips.get<jsonxx::Object>(i).get<jsonxx::Number>("index_");
				if (playerAttr)
				{
					if (playerAttr->getEquipGrid(EQUIP_SLOT_TYPE(index_)))
					{
						playerAttr->getEquipGrid(EQUIP_SLOT_TYPE(index_))->loadWithIndex(equips.get<jsonxx::Object>(i));
						static_cast<ClientPlayer *>(this)->applyEquips(EQUIP_SLOT_TYPE(index_));
					}
				}
				else
				{
					attr->equip(EQUIP_SLOT_TYPE(index_), &itemgrid);
				}
			}
		}
		if (equips.size())
		{
			if(g_pPlayerCtrl && g_pPlayerCtrl->isInSpectatorMode() 
				&& g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
			{
				g_pPlayerCtrl->switchCurrentItem();
			}
			if (playerAttr)
			playerAttr->ApplyWeaponEnchantEffect(getBody());
		}
	}
}

//HPProgressComponent-----end

#pragma region Logo todo:delete
//TeamComponent-----
void ActorLiving::setTeam(int id, bool ResetAttr)
{
	auto teamComp = m_pTeamComponent;
    //auto teamComp = GetComponent<TeamComponent>();
	if (teamComp) teamComp->setTeam(id, ResetAttr);

	if (g_WorldMgr && GameNetManager::getInstance()->isHost()){
		PB_SetTeamIDHC setTeamIDHC;
		setTeamIDHC.set_teamid(id);
		setTeamIDHC.set_objid(getObjId());

		if (isPlayer())
		{
			g_WorldMgr->signChangedToSync(getObjId(), BIS_TEAM_ID);
			GameNetManager::getInstance()->sendBroadCast(PB_SET_TEAM_HC, setTeamIDHC);
		}
		else
		{
			if (getWorld() && getWorld()->getMpActorMgr())
				getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_SET_TEAM_HC, setTeamIDHC, this);
		}
	}
}

int ActorLiving::getTeam()
{
	auto teamComp = m_pTeamComponent;
	if (teamComp) return teamComp->getTeam();
	else return 0;
}

bool ActorLiving::isSameTeam(ActorLiving *target)
{
	auto teamComp = m_pTeamComponent;
	if (teamComp) return teamComp->isSameTeam(target);
	else return false;
}

void ActorLiving::addGameScoreByRule(int ruleid, int num)
{
	auto teamComp = m_pTeamComponent;
	if (teamComp) teamComp->addGameScoreByRule(ruleid, num);
}
//TeamComponent-----end
#pragma endregion

void ActorLiving::setSunHurt(bool sunHurt) //目前只有野人祭司设置这个，但由于FlyMob不调SunHurtComponent.tick，设了也没用
{
	if (sunHurt) //延迟生成SunHurtComponent
	{
		auto compSunHurt = CreateComponent<SunHurtComponent>("SunHurtComponent");
		if (compSunHurt)
		{
			compSunHurt->SetCanPlayEffect(true);
		}
	}
	
	Event().Emit("sunhurt_set", SandboxContext().SetData_Bool(sunHurt));
}
bool ActorLiving::getSunHurt()
{
	return Event().Emit("sunhurt_get").IsExecSuccessed();
}

void ActorLiving::setDieInDay(bool dieInDay, int deathAnimId)
{
	if (dieInDay)
	{
		CreateComponent<DieInDayComponent>("DieInDayComponent");
	}
	SandboxContext context = SandboxContext();
	context.SetData_Bool(dieInDay);
	context.SetData_Number(deathAnimId);
	Event().Emit("dieinday_set", context);
}

bool ActorLiving::ComboAttackStateRunning()
{
	if (!dataAttackDef)
		return false;

	return ComboAttackCalculate;
}

int ActorLiving::GetTeamType(IActorLiving* target)
{
	return getTeamType(static_cast<ActorLiving*>(target));
}

ActorFollow* ActorLiving::getActorFollow()
{
	if (!m_pActorFollow)
	{
		m_pActorFollow = CreateComponent<ActorFollow>("Follow");
	}
	return m_pActorFollow;
}

void ActorLiving::DoRepairBlock(const WCoord& blockpos)
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;
	int blockid = m_pWorld->getBlockID(blockpos);
	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl) 
		return;

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	float health = lua_const->architectural_repaired_value;//todo 需要修复公式计算
	auto player = dynamic_cast<ClientPlayer*>(this);
	if (!player) 
		return;
	if (pmtl->DoOnBlockRepaired(m_pWorld, blockpos, player, health)&& !m_pWorld->isRemoteMode()) {
		// 观察者事件接口
		ObserverEvent_ActorBlock obevent((long long)getObjId(), pmtl->getBlockResID(), blockpos.x, blockpos.y, blockpos.z);
		GetObserverEventManager().OnTriggerEvent("Block.Repair", &obevent);
	}
}

bool ActorLiving::DoHurtBlock(const WCoord& blockpos, ATTACK_TYPE attacktype, float attackPoints, bool& isDestroy)
{
	if (m_pWorld == NULL)
		return false;
	int blockid = m_pWorld->getBlockID(blockpos);
	if (blockid == 0)
		return false;
	auto blockdef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockdef || blockdef->Hardness < 0)
		return false;

	// 获取方块材质信息
	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl)
		return false;
	
	if (pmtl && m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEOPERATED) > 0)
	{
		if (attackPoints == 0)
		{
			attackPoints = getPlayerAttrib()->getAttackPoint(attacktype, attacktype);
			if (attackPoints == 0)
			{
				const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(11000);
				if (tooldef) attackPoints = tooldef->Attacks[0];
			}
		}
		
		attackPoints = getPlayerAttrib()->getAttackPointWithStatus(attacktype, attackPoints);
		int blockdata = m_pWorld->getBlockData(blockpos);
		auto player = dynamic_cast<ClientPlayer*>(this);
		float hardness = math::min(1.0f, pmtl->getDestroyHardness(blockdata, player));
		float damageInc = getPlayerAttrib()->getModAttrib(attacktype == ATTACK_PUNCH ? MODATTR_ATTACK_PUNCH : MODATTR_ATTACK_RANGE)
				+ getPlayerAttrib()->getActorAttValueWithStatusByType(attacktype == ATTACK_PUNCH ? BUFFATTRT_MELEE_DAMAGE : BUFFATTRT_REMOTE_DAMAGE, true);
		float damage = attackPoints * (1 + damageInc) * (1 - hardness);

		if (pmtl->DoOnBlockDamaged(m_pWorld, blockpos, player, damage) &&m_pWorld && !m_pWorld->isRemoteMode()) 
		{
			auto hpvalue = pmtl->getBlockHP(m_pWorld, blockpos);
			if (hpvalue <= 0) isDestroy = true;

			// 观察者事件接口
			ObserverEvent_ActorBlock obevent((long long)getObjId(), pmtl->getBlockResID(), blockpos.x, blockpos.y, blockpos.z);
			GetObserverEventManager().OnTriggerEvent("Block.Attack", &obevent);
		}
	}

	return true;
}

