
#include "PlayerControl.h"
#include "backpack.h"
#include "DefManagerProxy.h"
//#include "mod/ModManager.h"
//#include "GameEvent.h"
#include "container_crafting.h"
#include "container_repair.h"
#include "container_enchant.h"
#include "Common/OgreShared.h"

#include "container_world.h"
#include "OgreScriptLuaVM.h"
#include "world.h"
#include "ClientItem.h"
#include "ClientActorManager.h"
#include "proto_common.h"
#include "GameNetManager.h"
//#include "ClientAccount.h"
#include "WorldManager.h"
#include "ObserverEventManager.h"
#include "WorldStringManagerProxy.h"
#include "CraftMgr.h"
#include "RuneDef.h"
#include "LuaInterfaceProxy.h"
#include <assert.h>
#include <algorithm>
#include <vector>
#include <time.h>
#include "ClientInfoProxy.h"
#include "SandboxCoreDriver.h"
#include "ICloudProxy.h"
#include "SandboxIdDef.h"
#include "special_blockid.h"
#include "OgreMD5.h"
//#include "ArchiveManager.h"

#ifdef IWORLD_SERVER_BUILD
// 返回是否保存存档
bool _needSavePack()
{
	static bool inited = false;
	static bool needSave = false;
	if (!inited)
	{
		MNSandbox::SandboxResult sandboxResult = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
		if (sandboxResult.IsExecSuccessed())
		{
			inited = true;
			needSave = sandboxResult.GetData_Bool();
		}
		else
		{
			return false;
		}
	}
	return needSave;
}
#define BACKPACK_PLOG if(_needSavePack()) PLOG(INFO)
#else
inline bool _needSavePack(){return false;}
#define BACKPACK_PLOG if(false) std::cout
#endif
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

BackPack::BackPack(ClientPlayer *player) : mPlayer(player)
{
	memset(m_Containers, 0, sizeof(m_Containers));

	m_Containers[BACKPACK_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 30, BACKPACK_START_INDEX, player);
	m_Containers[SHORTCUT_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 8, SHORTCUT_START_INDEX, player);
	m_Containers[SHORTCUTEX_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 8, SHORTCUTEX_START_INDEX, player);
	m_Containers[MINICRAFT_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(CraftingContainer, 7, MINICRAFT_START_INDEX);
	m_Containers[CRAFT_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(CraftingContainer, 10, CRAFT_START_INDEX);
	m_Containers[COMMON_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 5, COMMON_START_INDEX);
	m_Containers[MOUSE_PICKITEM_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 2, MOUSE_PICKITEM_INDEX, player);
	m_Containers[EQUIP_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,8, EQUIP_START_INDEX, player);
	m_Containers[PRODUCT_LIST_TWO_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,100, PRODUCT_LIST_TWO_INDEX);
	m_Containers[COMMON_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,300, COMMON_PRODUCT_LIST_INDEX);
	m_Containers[EQUIP_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,100, EQUIP_PRODUCT_LIST_INDEX);
	m_Containers[BUILD_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,400, BUILD_PRODUCT_LIST_INDEX);
	m_Containers[MACHINE_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,100, MACHINE_PRODUCT_LIST_INDEX);
	m_Containers[REPAIR_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(RepairContainer,3, REPAIR_START_INDEX);
	m_Containers[ENCHANT_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(EnchantContainer,2, ENCHANT_START_INDEX);
	m_Containers[RECIPE_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,100, RECIPE_PRODUCT_LIST_INDEX);
	m_Containers[COOKING_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,100, COOKING_PRODUCT_LIST_INDEX);
	m_Containers[RUNE_INLAY_CONTAINER_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,RUNE_INLAY_CONTAINER_SIZE, RUNE_INLAY_CONTAINER_INDEX, player);//需传player否则不会同步数据code by:tanzhenyu
	m_Containers[RUNE_MERGE_CONTAINER_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,RUNE_MERGE_CONTAINER_SIZE, RUNE_MERGE_CONTAINER_INDEX, player);//需传player否则不会同步数据code by:tanzhenyu
	m_Containers[RUNE_AUTH_CONTAINER_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer,RUNE_AUTH_CONTAINER_SIZE, RUNE_AUTH_CONTAINER_INDEX, player);
	m_Containers[PROPS_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 100, PROPS_PRODUCT_LIST_INDEX);
	m_Containers[MATERIAL_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 100, MATERIAL_PRODUCT_LIST_INDEX);
	m_Containers[DECORATE_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 100, DECORATE_PRODUCT_LIST_INDEX);
	m_Containers[SHORTCUT_START_INDEX_EDIT / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 24, SHORTCUT_START_INDEX_EDIT, player);
	m_Containers[SHORTCUT_SINGLE_INDEX_EDIT / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 1, SHORTCUT_SINGLE_INDEX_EDIT, player);
	m_Containers[EXT_BACKPACK_START_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 60, EXT_BACKPACK_START_INDEX, player);//扩展背包


	m_Containers[WITHHOLD_BACKPACK_START_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer, 38, WITHHOLD_BACKPACK_START_INDEX, player);
	//m_Containers[BOOKCABINET_START_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer(BOOKCABINET_CAPACITY, BOOKCABINET_START_INDEX);
	//m_Containers[INTERPRETER_START_INDEX / GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer(4, INTERPRETER_START_INDEX);
	//m_Containers[EDITBOOK_START_INDEX/GRID_INDEX_BASIS] = SANDBOX_NEW(PackContainer(26, EDITBOOK_START_INDEX);
	m_tmpShortcutMode = false;
}
			   
BackPack::~BackPack()
{
	mPlayer = NULL;
	SANDBOX_DELETE(m_Containers[BACKPACK_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[SHORTCUT_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[SHORTCUTEX_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[MINICRAFT_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[CRAFT_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[COMMON_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[MOUSE_PICKITEM_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[EQUIP_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[PRODUCT_LIST_TWO_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[COMMON_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[EQUIP_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[BUILD_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[MACHINE_PRODUCT_LIST_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[REPAIR_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[ENCHANT_START_INDEX/GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[RECIPE_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[COOKING_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[RUNE_INLAY_CONTAINER_INDEX / GRID_INDEX_BASIS]);//code by:tanzhenyu
	SANDBOX_DELETE(m_Containers[RUNE_MERGE_CONTAINER_INDEX / GRID_INDEX_BASIS]);//code by:tanzhenyu
	SANDBOX_DELETE(m_Containers[RUNE_AUTH_CONTAINER_INDEX / GRID_INDEX_BASIS]);//code by:tanzhenyu
	SANDBOX_DELETE(m_Containers[PROPS_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[MATERIAL_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS]);	
	SANDBOX_DELETE(m_Containers[DECORATE_PRODUCT_LIST_INDEX / GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[SHORTCUT_START_INDEX_EDIT / GRID_INDEX_BASIS]);
	SANDBOX_DELETE(m_Containers[SHORTCUT_SINGLE_INDEX_EDIT / GRID_INDEX_BASIS]);//code by:bryanwang	
	SANDBOX_DELETE(m_Containers[EXT_BACKPACK_START_INDEX / GRID_INDEX_BASIS]);//扩展背包

	SANDBOX_DELETE(m_Containers[WITHHOLD_BACKPACK_START_INDEX / GRID_INDEX_BASIS]);

	//SANDBOX_DELETE m_Containers[BOOKCABINET_START_INDEX / GRID_INDEX_BASIS];
	//SANDBOX_DELETE m_Containers[INTERPRETER_START_INDEX / GRID_INDEX_BASIS];
	//SANDBOX_DELETE m_Containers[EDITBOOK_START_INDEX/GRID_INDEX_BASIS];
}

BaseContainer *BackPack::getContainer(int index)
{
	int i = index/GRID_INDEX_BASIS;
	assert(i>=0 && i<MAX_PACKINDEX_TYPE);
    if (i>=0 && i<MAX_PACKINDEX_TYPE)
        return m_Containers[i];
    else
        return NULL;
}

PackContainer *BackPack::getPack(int index)
{
	int i = index/GRID_INDEX_BASIS;
	assert(i>=0 && i<=EQUIP_START_INDEX);
    if (i>=0 && i<=EQUIP_START_INDEX)
        return static_cast<PackContainer *>(m_Containers[i]);
    else
        return NULL;
}
//code by:tanzhenyu
int BackPack::addItem_byGameInitItem(GameInitItem* item)
{
	GridCopyData gridcopydata(item->itemid, item->num, item->durable, item->toughness, item->enchant_num, item->enchants, 0, 0, 0, item->userdata.c_str(),&(item->runedata));
	return addItem_byGridCopyData(gridcopydata, 1);
}
//code by:tanzhenyu
int BackPack::addItem_byGridCopyData(const GridCopyData& gridcopydata, int priorityType)
{
	int n = 0;
	PackContainer* packContainer = getPack(getShortcutStartIndex());
	if (!packContainer)  return n;
	if(1 == priorityType)
	{
		n = getPack(getShortcutStartIndex())->addItem_byGridCopyData(gridcopydata);
		if(n < gridcopydata.num)
		{
			GridCopyData tmp(gridcopydata);
			tmp.num = gridcopydata.num - n;
			n += getPack(BACKPACK_START_INDEX)->addItem_byGridCopyData(tmp);
		}
	}
	else if(2 == priorityType)
	{
		n = getPack(BACKPACK_START_INDEX)->addItem_byGridCopyData(gridcopydata);
		if(n < gridcopydata.num)
		{
			n += getPack(getShortcutStartIndex())->addItem_byGridCopyData(gridcopydata);
		}
	}

	//最后选择额外背包
	if (n < gridcopydata.num)
	{
		bool haveext = mPlayer && mPlayer->HaveExtBackPack();
		bool isExtBack = isExtBackPack(gridcopydata.resid);
		if (haveext && !isExtBack)
		{
			n += getPack(EXT_BACKPACK_START_INDEX)->addItem_byGridCopyData(gridcopydata);
		}
	}

	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d}", gridcopydata.resid, n);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_additem", tmp, true);
	}
	return n;
}

int BackPack::addItem_byGrid(int resid, int num, const BackPackGrid& grid, int priorityType)
{
	GridCopyData gridcopydata(&grid);
	gridcopydata.resid = resid;
	gridcopydata.num = num;
	return addItem_byGridCopyData(gridcopydata, priorityType);
}

//int BackPack::addItem(int resid, int num, int priorityType, int enchantnum, const int enchants[], void *userdata, const char *userdata_str)
//{
//	int n = 0;
//	PackContainer* packContainer = getPack(getShortcutStartIndex());
//	if (!packContainer)  return n;
//	if (num <= 0)
//		return 0;
//
//	
//	if(1 == priorityType)
//	{
//		n = packContainer->addItem(resid, num, -1, -1, enchantnum, enchants, userdata, userdata_str);
//		if(n < num)
//		{
//			n += getPack(BACKPACK_START_INDEX)->addItem(resid, num-n, -1, -1, enchantnum, enchants, userdata, userdata_str);
//		}
//	}
//	else if(2 == priorityType)
//	{
//		n = getPack(BACKPACK_START_INDEX)->addItem(resid, num, -1, -1, enchantnum, enchants, userdata, userdata_str);
//		if(n < num)
//		{
//			n += packContainer->addItem(resid, num, -1, -1, enchantnum, enchants, userdata, userdata_str);
//		}
//	}
//	
//	if (validServerBackPackLog())
//	{
//		BACKPACK_PLOG << "[bplog]additem uin=" << mPlayer->getUin() << " item=" << resid << "_" << n;
//	}
//	return n;
//}

int BackPack::addItem(int resid, int num, int priorityType)
{
	GridCopyData data;
	data.resid = resid;
	data.num = num;
	return addItem_byGridCopyData(data, priorityType);
	//return addItem(resid, num, priorityType, 0, NULL);
}

//int BackPack::addItemWithTunestone(int resid, int num, int priorityType, int tunestonenum, const int tunestones[], void* userdata /*= 0*/, const char* userdata_str /*= ""*/)
//{
//	int n = 0;
//	PackContainer* packContainer = getPack(getShortcutStartIndex());
//	if (!packContainer)  return n;
//	if (num <= 0)
//		return 0;
//
//
//	if (1 == priorityType)
//	{
//		n = packContainer->addItemWithTunestone(resid, num, -1, tunestonenum, tunestones, userdata, userdata_str);
//		if (n < num)
//		{
//			n += getPack(BACKPACK_START_INDEX)->addItemWithTunestone(resid, num - n, -1, tunestonenum, tunestones, userdata, userdata_str);
//		}
//	}
//	else if (2 == priorityType)
//	{
//		n = getPack(BACKPACK_START_INDEX)->addItemWithTunestone(resid, num, -1, tunestonenum, tunestones, userdata, userdata_str);
//		if (n < num)
//		{
//			n += packContainer->addItemWithTunestone(resid, num, -1, tunestonenum, tunestones, userdata, userdata_str);
//		}
//	}
//
//	if (validServerBackPackLog())
//		BACKPACK_PLOG << "[bplog]additem uin=" << mPlayer->getUin() << " item=" << resid << "_" << n;
//	return n;
//}

//code by:tanzhenyu
int BackPack::tryAddItem_byGrid(int resid, int num, BackPackGrid* data)
{
	GridCopyData gridcopydata(data);
	gridcopydata.resid = resid;
	gridcopydata.num   = num;
	return tryAddItem_byGridCopyData(gridcopydata);
}
//code by:tanzhenyu
int BackPack::tryAddItem_byGridCopyData(const GridCopyData& gridcopydata)
{
	int n = 0;
	{
		PackContainer* packContainer = getPack(getShortcutStartIndex());
		if (!packContainer)  return n;
		n = packContainer->addItem_byGridCopyData(gridcopydata);
		if(n < gridcopydata.num)
		{
			GridCopyData tmp(gridcopydata);
			tmp.num = gridcopydata.num - n;
			n += getPack(BACKPACK_START_INDEX)->addItem_byGridCopyData(tmp);
		}

		if(n != 0)
		{
			if(GetClientInfoProxy()->getMultiPlayer() == 0)
			{
				MINIW::ScriptVM::game()->callFunction("GetItemTips", "ii", gridcopydata.resid, n);
			}
			else
			{
				if(mPlayer != NULL)	mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETITEM, gridcopydata.resid, n);
			}
		}	
	}
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d}", gridcopydata.resid, n);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_additem", tmp, true);
	}
	return n;
}

std::string BackPack::getExtBackPackInfo()
{
	if (mPlayer && mPlayer->HaveExtBackPack())
	{
		int cont = mPlayer->GetExtBackPackGridCount();
		jsonxx::Array info;
		int realindex;
		for (int i = 0; i < cont; i++)
		{
			realindex = EXT_BACKPACK_START_INDEX + i;
			BackPackGrid* grid = index2Grid(realindex);
			if (grid && !grid->isEmpty())
			{
				jsonxx::Object gridinfo;
				grid->save(gridinfo);
				jsonxx::Object linfo;
				linfo << "index" << i;
				linfo << "gridinfo" << gridinfo;
				grid->setItem(0, 0);
				afterChangeGrid(realindex);
				info.import(linfo);
			}

		}
		if (!info.empty())
		{
			return info.json();
		}
	}

	return "";
}
void BackPack::loadExtBackPackInfo(const std::string& info)
{
	if (mPlayer)
	{
		jsonxx::Array jinfo;
		if (jinfo.parse(info))
		{
			int realindex;
			for (int i = 0; i < jinfo.size(); ++i)
			{
				const jsonxx::Object& linfo = jinfo.get<jsonxx::Object>(i);
				int index = linfo.get<jsonxx::Number>("index");
				const jsonxx::Object& gridinfo = linfo.get<jsonxx::Object>("gridinfo");
				realindex = EXT_BACKPACK_START_INDEX + index;
				BackPackGrid* grid = index2Grid(realindex);
				if (grid)
				{
					grid->load(gridinfo);
				}
				afterChangeGrid(realindex);
			}
		}
	}
}

//int BackPack::tryAddItem(int resid, int num, int durable, int toughness, int enchantnum, int enchants[], void *userdata, const char *userdata_str)
//{
//	int n = 0;
//	{
//		PackContainer* packContainer = getPack(getShortcutStartIndex());
//		if (!packContainer)  return n;
//		n = packContainer->addItem(resid, num, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//		if(n < num)
//		{
//			n += getPack(BACKPACK_START_INDEX)->addItem(resid, num-n, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//		}
//
//		if(n != 0)
//		{
//			if(GetClientInfoProxy()->getMultiPlayer() == 0)
//			{
//				MINIW::ScriptVM::game()->callFunction("GetItemTips", "ii", resid, n);
//			}
//			else
//			{
//				if(mPlayer != NULL)	mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETITEM, resid, n);
//			}
//		}	
//	}
//	if (validServerBackPackLog())
//	{
//		BACKPACK_PLOG << "[bplog]additem uin=" << mPlayer->getUin() << " item=" << resid << "_" << n;
//	}
//	return n;
//}

const int BAG_SLOTS = 30;

int BackPack::getEmptyBagIndex()
{
	auto pack = getPack(BACKPACK_START_INDEX);
	if (!pack) return -1;

	for(int i=0; i <BAG_SLOTS; ++i)
	{
		if (pack->m_Grids[i].isEmpty()) return BACKPACK_START_INDEX + i;
	}
	return -1;
}

int BackPack::getEmptyShortcutIndex()
{
	auto pack = getPack(getShortcutStartIndex());
	if (!pack) return -1;
	int SHORTCUT_SLOTS = pack->getGridCount();
	for(int i = 0; i <SHORTCUT_SLOTS; ++i)
	{
		if(pack->m_Grids[i].isEmpty()) return getShortcutStartIndex() + i;
	}
	return -1; 
}

int BackPack::takeItemFrom(int gindex, int num, bool delsrc)
{
	BackPackGrid *pgrid = index2Grid(gindex);
	assert(pgrid != NULL);
	if(pgrid==NULL || pgrid->isEmpty()) return 0;

	int resid = pgrid->getItemID();
	if(pgrid->getNum() < num) num = pgrid->getNum();

	auto pack = getPack(getShortcutStartIndex());
	if (!pack) return -1;
	int n = pack->addItem_byGridWithNum(pgrid, num);
	if(n < num)
	{
		n += getPack(BACKPACK_START_INDEX)->addItem_byGridWithNum(pgrid, num-n);
	}
	if (mPlayer != NULL)
	{
		mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, resid, n);
		mPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, resid, n);
	}
	if(mPlayer != NULL) mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, resid, n);

	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d}", resid, n);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_additem", tmp, true);
	}
	if (delsrc) 
	{
		SubtractItemFromContainerEx(getContainer(gindex), pgrid, num, gindex);
	}
	return n;
}

namespace AntiSetting{
	extern void recordGainItem(int uin, int item_id, int num, int old_id = 0, int old_count = 0);
}
void BackPack::setItem(int resid, int grid_index, int num, const char *sid_str/* = ""*/)
{
	PackContainer *container = (PackContainer*)getContainer(grid_index);
	if(container == NULL) return;
	if (num < 0 || !container->canPutItem(grid_index)) return;

	BackPackGrid *grid = container->index2Grid(grid_index);
	if (!grid)
		return;
	if (num == 0)
		resid = 0;
	int old_id = 0, old_num = 0;
	if (grid->getNum()){
		old_id = grid->getItemID();
		old_num = grid->getNum();
	}
	
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
	auto *def = GetDefManagerProxy()->getHomeItemDef(resid);
	if (def && def->Function1_ID == 2)  //家园蓝图纸
	{		
		std::string userdata_str("");
		
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_getGridUserdataStr",
			SandboxContext(nullptr)
			.SetData_String("Function1_val", def->Function1_val1));
		if (result.IsExecSuccessed())
		{
			userdata_str = result.GetData_String("userdata_str");
		}
		SetBackPackGridWithClear(*grid, ITEM_BLUEPRINT, num, -1, -1, (void *)resid, 1, 0, userdata_str.c_str(), sid_str);
	}
#else
	if (false)
	{
	}
#endif
	else if (resid == ITEM_SPRINKLER)
	{
		//花洒装水一次 可以浇灌20次要求写死  写入到userdata  code-by：liwentao
		SetBackPackGridWithClear(*grid, resid, num, -1, -1, (void *)GetLuaInterfaceProxy().get_lua_const()->number_of_sprinklers, 1, 0, "", sid_str);
	}
	else
	{
		//SetBackPackGridWithClear(*grid, resid, num, -1, (void *)resid, 1, 0, "", sid_str);
		SetBackPackGridWithClear(*grid, resid, num, -1, -1, 0, 1, 0, "", sid_str);
	}
	if (num && resid)
		AntiSetting::recordGainItem(mPlayer? mPlayer->getUin(): 0, resid, num, old_id, old_num);

	afterChangeGrid(grid_index);
	
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d,\"index\":%d}", resid, num, grid_index);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_additem", tmp, true);
	}
}

void BackPack::addItemToEquip(int resid, int grid_index, int num /*= 1*/)		//添加道具到装备栏
{
	if (grid_index < EQUIP_START_INDEX || grid_index >= FURNACE_START_INDEX) { return; }
	BackPackGrid * grid = index2Grid(grid_index);
	if (grid)
	{
		mPlayer->onDisApplyEquips(grid->getItemID());	//卸载装备栏位回调
	}
	setItem(resid, grid_index, num);
	mPlayer->onApplyEquips(resid, grid_index);			//装载装备回调
}
void BackPack::removeItemFromEquip(int grid_index, int num)		//销毁道具
{
	if (grid_index < EQUIP_START_INDEX || grid_index >= FURNACE_START_INDEX) { return; }
	BackPackGrid * grid = index2Grid(grid_index);
	if (grid)
	{
		mPlayer->onDisApplyEquips(grid->getItemID());	//卸载装备栏位回调
	}
	removeItem(grid_index, num);
}

void BackPack::setItemWithoutLimit(int resid, int grid_index, int num, const char *userdata_str, const char *sid_str/* = ""*/)
{
	PackContainer *container = (PackContainer*)getContainer(grid_index);
	if(container == NULL) return;
	if (num < 0 || !container->canPutItem(grid_index)) return;

	BackPackGrid *grid = container->index2Grid(grid_index);
	if (!grid)
		return;
	if (num == 0)
		resid = 0;
	int old_id = 0, old_num = 0;
	if (grid->getNum()){
		old_id = grid->getItemID();
		old_num = grid->getNum();
	}

	string newUserDataStr(userdata_str);
	if (resid == ITEM_BOOK && grid_index == 32012)  //编书台放书本的格子
	{
		jsonxx::Object txtObj;
		std::string reportContent = "";
		if (txtObj.parse(newUserDataStr))
		{
			if (txtObj.has<jsonxx::String>("title"))
			{
				std::string title = txtObj.get<jsonxx::String>("title");
				reportContent = title + ";";
			}

			if (txtObj.has<jsonxx::String>("authorname"))
			{
				std::string authorname = txtObj.get<jsonxx::String>("authorname");
				reportContent += authorname + ";";
			}

			if (txtObj.has<jsonxx::String>("context"))
			{
				std::string context = txtObj.get<jsonxx::String>("context");
				reportContent += context + ";";
			}

			if (txtObj.has<jsonxx::String>("multiLangName"))
			{
				std::string multiLangName = txtObj.get<jsonxx::String>("multiLangName");
				reportContent += multiLangName + ";";
			}

			if (txtObj.has<jsonxx::String>("multiLangEditor"))
			{
				std::string multiLangEditor = txtObj.get<jsonxx::String>("multiLangEditor");
				reportContent += multiLangEditor + ";";
			}

			if (txtObj.has<jsonxx::String>("multiLangDetails"))
			{
				std::string multiLangDetails = txtObj.get<jsonxx::String>("multiLangDetails");
				reportContent += multiLangDetails;
			}
			if (mPlayer)
			{
				char ckey[64] = { 0 };
#if defined(_WIN32)
				sprintf(ckey, "%d%I64d", mPlayer->getUin(), time(NULL));
#else
				sprintf(ckey, "%d%ld", mPlayer->getUin(), time(NULL));
#endif
				txtObj << "key" << ckey;
				newUserDataStr = txtObj.json();
				GetWorldStringManagerProxy()->insert(ckey, reportContent, SAVEFILETYPE::BOOK);
				//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
				//	.SetData_Number("type", 12)
				//	.SetData_String("content", reportContent)
				//	.SetData_String("key", ckey));
			}
		}
		
	}

	SetBackPackGrid(*grid, resid, num, -1, -1, 0, 1, 0, newUserDataStr.c_str());
	afterChangeGrid(grid_index);

	if (num && resid)
		AntiSetting::recordGainItem(mPlayer ? mPlayer->getUin() : 0, resid, num, old_id, old_num);
		
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d,\"index\":%d,\"old_item\":%d,\"old_num\":%d}", resid, num, grid_index, old_id, old_num);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_setitem", tmp, true);
	}
	if (resid > 0 && num > 0){
		AntiSetting::recordGainItem(mPlayer? mPlayer->getUin(): 0, resid, num, old_id, old_num);
	}
}

int BackPack::addStorageItem(int index, int num, int openContainerBase/* =STORAGE_START_INDEX */)
{
	auto container = getContainer(openContainerBase);
	BackPackGrid *pgrid = index2Grid(index);

	if(container == nullptr || pgrid == nullptr || pgrid->isEmpty()) return 0;

	//mobile版:触发器事件:箱子中有道具被放入
	int baseindex = container->getBaseIndex();
	if (STORAGE_START_INDEX == baseindex && mPlayer)
	{
		WorldContainer* storage = dynamic_cast<WorldContainer*>(container);

		if (storage){
			WCoord blockpos = storage->m_BlockPos;
			World* pworld = mPlayer->getWorld();
			int blockid = pworld->getBlockID(blockpos);
			ItemChangeForTrigger(true, blockpos, blockid, pgrid->getItemID(), num);
		}
	}

	int n = container->addItem_byGridWithNum(pgrid,	num );
	
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d,\"index\":%d}", pgrid->getItemID(), n, index);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_addstorage", tmp, true);
	}
	return n;
}

void BackPack::validateItems()
{
}

int BackPack::addItemWithPickUp_bySocGridCopyData(const GridCopyData& gridcopydata)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(gridcopydata.resid);
	if (!def) return 0;
	//配置1时，优先进入快捷栏 配置0就优先进入背包
	int PriorityBag = def->PriorityBag;
	PackContainer* first_Pack = getPack(PriorityBag == 1 ? SHORTCUT_START_INDEX : BACKPACK_START_INDEX);
	PackContainer* second_Pack = getPack(PriorityBag == 1 ? BACKPACK_START_INDEX : SHORTCUT_START_INDEX);

	int n = first_Pack->addItem_byGridCopyData(gridcopydata);
	if (n < gridcopydata.num)
	{
		GridCopyData tmp(gridcopydata);
		tmp.num = gridcopydata.num - n;
		n += second_Pack->addItem_byGridCopyData(tmp);
	}

	std::string name, desc;
	bool isVehicle = false;

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_parseUserdatastrForNameDesc",
		SandboxContext(nullptr)
		.SetData_String("userdata_str", gridcopydata.userdata_str)
		.SetData_String("name", name)
		.SetData_String("desc", desc));
	if (result.IsExecSuccessed())
	{
		isVehicle = result.GetData_Bool("isVehicle");
		name = result.GetData_String("name");
		desc = result.GetData_String("desc");
	}

	// 捡起村民生物蛋时提示对应的名字 code-by:lizb
	int itemId = gridcopydata.resid;
	if (itemId == 13439 || itemId == 13440 || itemId == 13441)
	{
		const char* userdata_str = gridcopydata.userdata_str.c_str();
		name = BackPack::getSpecialItemName(itemId, userdata_str);
	}

	if (GetClientInfoProxy()->getMultiPlayer() == 0)
	{
		if (isVehicle || !name.empty())
			MINIW::ScriptVM::game()->callFunction("GetItemTips", "iis", gridcopydata.resid, n, name.c_str());
		else
			MINIW::ScriptVM::game()->callFunction("GetItemTips", "ii", gridcopydata.resid, n);
	}
	else
	{
		if (mPlayer != NULL) mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETITEM, gridcopydata.resid, n, name.c_str());
	}

	return n;
}

int BackPack::addItemWithPickUp_bySocGridCopyData(int resid, int num)
{
	GridCopyData gridcopydata;
	gridcopydata.resid = resid;
	gridcopydata.num = num;

	return addItemWithPickUp_bySocGridCopyData(gridcopydata);
}

//code by:tanzhenyu
int BackPack::addItemWithPickUp_byGridCopyData(const GridCopyData& gridcopydata)
{
	bool needBackPackStart = false;
	//判断背包里面是否已经有了
	int sec = -1;
	int thr = -1;
	//获取拾取index
	int index = getPickUpIndex(gridcopydata, sec, thr);
	int n = getPack(index)->addItem_byGridCopyData(gridcopydata);
	
	if((sec>=0)&& (n < gridcopydata.num))
	{
		GridCopyData tmp(gridcopydata);
		tmp.num = gridcopydata.num - n;
		n += getPack(sec)->addItem_byGridCopyData(tmp);
	}

	if ((thr >= 0) && (n < gridcopydata.num))
	{
		GridCopyData tmp(gridcopydata);
		tmp.num = gridcopydata.num - n;
		n += getPack(thr)->addItem_byGridCopyData(tmp);
	}

	std::string name,desc;
	bool isVehicle = false;

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_parseUserdatastrForNameDesc",
		SandboxContext(nullptr)
		.SetData_String("userdata_str", gridcopydata.userdata_str)
		.SetData_String("name", name)
		.SetData_String("desc", desc));
	if (result.IsExecSuccessed())
	{
		isVehicle = result.GetData_Bool("isVehicle");
		name = result.GetData_String("name");
		desc = result.GetData_String("desc");
	}

	// 捡起村民生物蛋时提示对应的名字 code-by:lizb
	int itemId = gridcopydata.resid;
	if (itemId == 13439 || itemId == 13440 || itemId == 13441)
	{
		const char* userdata_str = gridcopydata.userdata_str.c_str();
		name = BackPack::getSpecialItemName(itemId, userdata_str);
	}

	if(GetClientInfoProxy()->getMultiPlayer() == 0)
	{
		if (isVehicle || !name.empty())
			MINIW::ScriptVM::game()->callFunction("GetItemTips", "iis", gridcopydata.resid, n, name.c_str());
		else
			MINIW::ScriptVM::game()->callFunction("GetItemTips", "ii", gridcopydata.resid, n);
	}
	else
	{
		if(mPlayer != NULL) mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETITEM, gridcopydata.resid, n, name.c_str());
	}	

	return n;
}

int BackPack::getPickUpIndex(const GridCopyData& gridcopydata, int& sec, int& thr)
{
	int itemCount = getPack(BACKPACK_START_INDEX)->getItemCount(gridcopydata.resid);
	const ItemDef* def = GetDefManagerProxy()->getItemDef(gridcopydata.resid);
	bool haveext = mPlayer && mPlayer->HaveExtBackPack();
	bool isExtBack = isExtBackPack(gridcopydata.resid);
	if (def && def->StackMax && ((itemCount % def->StackMax) > 0))
	{
		sec = getShortcutStartIndex();
		if (haveext&& !isExtBack)
		{
			thr = EXT_BACKPACK_START_INDEX;
		}
		else
		{
			thr = -1;
		}
		
		return BACKPACK_START_INDEX;
	}
	//判断是否有扩展背包 判断物品是否是扩展背包如果是则要过滤
	if (haveext&& !isExtBack)
	{
			itemCount = getPack(EXT_BACKPACK_START_INDEX)->getItemCount(gridcopydata.resid);
			if (def && def->StackMax && ((itemCount % def->StackMax) > 0))
			{
				sec = getShortcutStartIndex();
				thr = BACKPACK_START_INDEX;
				return EXT_BACKPACK_START_INDEX;
			}
	}
	sec = BACKPACK_START_INDEX;
	if (haveext && !isExtBack)
	{
		thr = EXT_BACKPACK_START_INDEX;
	}
	else
	{
		thr = -1;
	}
	return getShortcutStartIndex();
}

bool  BackPack::isExtBackPack(int resid)
{
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(resid);
	if (tooldef && tooldef->Type == 30)
	{
		return true;
	}
	return false;
}


//int BackPack::addItemWithPickUp(int resid, int num, int duration, int toughness, int enchantnum, const int enchats[], void *userdata, const char *userdata_str)
//{
//	int n = getPack(getShortcutStartIndex())->addItem(resid, num, duration, toughness, enchantnum, enchats, userdata, userdata_str);
//	if(n < num)
//	{
//		n += getPack(BACKPACK_START_INDEX)->addItem(resid, num-n, duration, toughness, enchantnum, enchats, userdata, userdata_str);
//	}
//	std::string name,desc;
//	bool isVehicle = false;
//
//	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_parseUserdatastrForNameDesc",
//		SandboxContext(nullptr)
//		.SetData_String("userdata_str", userdata_str)
//		.SetData_String("name", name)
//		.SetData_String("desc", desc));
//	if (result.IsExecSuccessed())
//	{
//		isVehicle = result.GetData_Bool("isVehicle");
//		name = result.GetData_String("name");
//		desc = result.GetData_String("desc");
//	}
//
//	if(GetClientInfoProxy()->getMultiPlayer() == 0)
//	{
//		if (isVehicle)
//			MINIW::ScriptVM::game()->callFunction("GetItemTips", "iis", resid, n, name.c_str());
//		else
//			MINIW::ScriptVM::game()->callFunction("GetItemTips", "ii", resid, n);
//	}
//	else
//	{
//		if(mPlayer != NULL) mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETITEM, resid, n, name.c_str());
//	}	
//
//	if (validServerBackPackLog())
//	{
//		BACKPACK_PLOG << "[bplog]addpickup uin=" << mPlayer->getUin() << " item=" << resid << "_" << n;
//	}
//
//	return n;
//}

bool BackPack::moveItem(int fromindex, int toindex, int num)
{
	if(num <= 0) return false;

	BackPackGrid *srcgrid = NULL;
	srcgrid	= index2Grid(fromindex);
	return moveItem(srcgrid, num, toindex, fromindex);
}

bool BackPack::moveItem(BackPackGrid * srcgrid, int &num, int toindex, int fromindex)
{
	if (srcgrid == NULL || srcgrid->isEmpty() || srcgrid->getNum() == 0)
	{
		return false;
	}
	int toolType = getGridToolType(fromindex);
	if (toolType == 31 && toindex != MOUSE_PICKITEM_INDEX && toindex >= 2000) //新鱼竿不可丢
	{
		if (mPlayer != NULL)
		{
			mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 81150);
		}
		return false;
	}

	if (num > srcgrid->getNum()) num = srcgrid->getNum();
	int srcitemid = srcgrid->getItemID();
	//处理扩展背包卸载时候报错原有数据
	//if (fromindex == EQUIP_START_INDEX + 4)//披风坑位
	//{
		int tooltype = getGridToolType(fromindex);
		if (tooltype == 30)//扩展背包类型
		{
			BackPackGrid* grid = index2Grid(fromindex);
			if (grid)
			{
				grid->setUserdataStr(getExtBackPackInfo().c_str());
			}
		}
	//}
	
	//词条特质
	mPlayer->doEquipModEntry(srcitemid, false);

	BackPackGrid *dstgrid = index2Grid(toindex);
	if(dstgrid == NULL) return false;
	if(!dstgrid->isEmpty() && dstgrid->getNum() > 0)
	{
		if(dstgrid->getItemID() == srcgrid->getItemID())
		{
			//染色方块，id相同但颜色不同时切换到swap逻辑 by：Jeff 2022/12/14
			if (IsDyeableBlock(srcgrid->getItemID()) && dstgrid->userdata_str != srcgrid->userdata_str)
			{
				swapItem(fromindex, toindex);
				return false;
			}
			int maxnum = dstgrid->def->StackMax - dstgrid->getNum();
			if (dstgrid->getIndex() < BUILDBLUEPRINT_START_INDEX || dstgrid->getIndex() > BUILDBLUEPRINT_START_INDEX + 1000)
			{
				if (maxnum == 0) return false;
				if (num > maxnum)  num = maxnum;
			}
		}
		else return false;

		dstgrid->addNum(num);
	}
	else
	{
		if (toolType >= 8 && toolType <= 12 || toolType >= 35 && toolType <= 38)
		{
			
		}
		dstgrid->setItem(*srcgrid, num);
	}

	if (mPlayer != NULL)
	{
		if (isBackpackIndex(toindex))
		{
			mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, dstgrid->getItemID(), num);
			mPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, dstgrid->getItemID(), 0, num);
		}
	}
	// 需要在afterChangeGrid之前
	SubtractItemFromContainerEx(getContainer(fromindex), srcgrid, num, fromindex);

	afterChangeGrid(toindex);
	
	//记录鼠标拾取的道具是从哪儿来的
	if (mPlayer && toindex == MOUSE_PICKITEM_INDEX)
		mPlayer->SetPickItemLastIndex(fromindex);

	// 从地图上的Container获取到道具的情况
	//下面家园的判断都走不进去，因为处理的是编辑运行模式
	if (mPlayer && g_WorldMgr && (g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isOriginAdventureMode()))
	{
		static bool isFromBackpack = true;
		if(toindex==MOUSE_PICKITEM_INDEX) {//鼠标拾取时设置isFromBackpack

			isFromBackpack = (fromindex >= BACKPACK_START_INDEX && fromindex < SHORTCUT_START_INDEX + 1000);
		}
		//mobile版:触发器事件:箱子中的道具被取出(手机是直接取出, pc是先取到鼠标上)
		if ( IsGridIndexType(fromindex, STORAGE_START_INDEX) && (IsGridIndexType(toindex, SHORTCUT_START_INDEX) || IsGridIndexType(toindex, BACKPACK_START_INDEX)))
		{
			isFromBackpack = false;
		}

		bool isToBackpack  = (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX + 1000);

		if (!isFromBackpack && isToBackpack) { //背包:新增道具事件(Item数量增加则为新增)
			mPlayer->addItemOnTrigger(dstgrid->getItemID(), num);
			isFromBackpack = true;
		}

		if (MOUSE_PICKITEM_INDEX == fromindex)
		{
			//触发器事件:PC版
			int lastIndex = mPlayer->GetPickItemLastIndex();
			if (fromindex == MOUSE_PICKITEM_INDEX && IsGridIndexType(toindex, STORAGE_START_INDEX))
			{
				//有道具被放入箱子
				if (lastIndex >= BACKPACK_START_INDEX && lastIndex < SHORTCUT_START_INDEX + 1000)
				{
					ItemChangeForTrigger(true, toindex, srcitemid, num);
				}
			}
			else if (fromindex == MOUSE_PICKITEM_INDEX && !IsGridIndexType(toindex, STORAGE_START_INDEX))
			{
				//有道具从箱子取出
				if (IsGridIndexType(lastIndex, STORAGE_START_INDEX))
					ItemChangeForTrigger(false, lastIndex, srcitemid, num);
			}
		}
		else
		{
			if ((IsGridIndexType(fromindex, SHORTCUT_START_INDEX) || IsGridIndexType(fromindex, BACKPACK_START_INDEX)) && IsGridIndexType(toindex, STORAGE_START_INDEX))
			{
				//有道具被放入箱子
				if (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX + 1000)
				{
					ItemChangeForTrigger(true, toindex, srcitemid, num);
				}
			}
			if ((IsGridIndexType(toindex, SHORTCUT_START_INDEX) || IsGridIndexType(toindex, BACKPACK_START_INDEX)) && IsGridIndexType(fromindex, STORAGE_START_INDEX))
			{
				//有道具从箱子取出
				if (IsGridIndexType(fromindex, STORAGE_START_INDEX))
					ItemChangeForTrigger(false, fromindex, srcitemid, num);
			}
		}

	}

	if (mPlayer && fromindex >= EQUIP_START_INDEX && fromindex < FURNACE_START_INDEX)
	{
		mPlayer->onDisApplyEquips(fromindex, toindex, srcitemid, num);	//卸载装备栏位回调
	}
	else if (mPlayer && toindex >= EQUIP_START_INDEX && toindex < FURNACE_START_INDEX)
	{
		mPlayer->onApplyEquips(srcitemid, toindex);			//装载装备回调
	}
	//处理扩展背包加载原有背包内道具
	//if (toindex == EQUIP_START_INDEX + 4) //披风坑位----没有固定坑位了 ----by charles xie
	//{
		tooltype = getGridToolType(toindex);
		if (tooltype == 30) //扩展背包
		{
			BackPackGrid* grid = index2Grid(toindex);
			if (grid)
			{
				loadExtBackPackInfo(grid->userdata_str);
			}
		}
	//}
	
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"findex\":%d,\"tindex\":%d,\"fitem\":%d,\"fnum\":%d,\"titem\":%d,\"tnum\":%d}", 
			fromindex, toindex, srcgrid->getItemID(), srcgrid->getNum(), dstgrid->getItemID(), dstgrid->getNum());
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_moveitem", tmp, true);
	}
	return true;
}

bool BackPack::shiftMoveItem(int fromindex, int totype)
{
	BackPackGrid *srcgrid = index2Grid(fromindex);
	if(srcgrid==NULL || srcgrid->getNum() == 0) return false;

	PackContainer *packer = getPack(totype);
	if(packer == NULL) return false;

	int toolType = getGridToolType(fromindex);
	if (toolType == 31) //新鱼竿不可丢
	{
		if (mPlayer != NULL)
		{
			mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 81150);
		}
		return false;
	}

	int itemid = srcgrid->getItemID();
	GridCopyData gridcopydata(srcgrid);
	gridcopydata.userdata = nullptr;
	int movednum = packer->addItem_byGridCopyData(srcgrid);
	if(movednum > 0)
	{
		if (mPlayer != NULL)
		{
			if (isBackpackIndex(totype))
			{
				mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, srcgrid->getItemID(), movednum);
				mPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, srcgrid->getItemID(), 0, movednum);
			}
		}
		

		if(srcgrid->addNum(-movednum) == 0)
		{
			srcgrid->clear();
		}

		//触发器事件:pc版:按住shift移动道具
		//有道具从箱子取出
		if (IsGridIndexType(fromindex, STORAGE_START_INDEX))
			ItemChangeForTrigger(false, fromindex, itemid, movednum);
		//有道具被放入箱子
		if (IsGridIndexType(totype, STORAGE_START_INDEX))
			ItemChangeForTrigger(true, totype, itemid, movednum);

		afterChangeGrid(fromindex);		
	}
	
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"findex\":%d,\"item\":%d,\"rnum\":%d,\"totype\":%d,\"moved_num\":%d}", 
			fromindex, itemid, srcgrid->getNum(), totype, movednum);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_shiftmoveitem", tmp, true);
	}
	return true;
}

void BackPack::mendItem(int index, int mendAmount)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid == nullptr || pgrid->isEmpty()) return;

	// 最大耐久需要降低
	int itemid = pgrid->getItemID();
	int duration = pgrid->getDuration();
	int maxDuration = pgrid->getMaxDuration();
	int new_maxDuration = 0;
	MINIW::ScriptVM::game()->callFunction("GetMaxDurationAfterRepair", "iii>i", itemid, duration, maxDuration, &new_maxDuration);

	pgrid->setMaxDuration(new_maxDuration);
	pgrid->addDuration(new_maxDuration, true);

	afterChangeGrid(index);
}

namespace AntiSetting {
	extern int ProtocolHostIn;  // 记录当前本机(主机)正在处理的协议ID
	extern int ProtocolSender;  // 记录当前本机(主机)正在处理的协议的发送者ID
}
void BackPack::removeItem(int index, int num)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid == NULL) return;
	if (num <= 0) return;

	//PC版:触发器事件:箱子中的道具被取出(扔掉鼠标拾取的道具, 且该道具是从箱子取出来的)
	if (index == MOUSE_PICKITEM_INDEX && mPlayer && IsGridIndexType(mPlayer->GetPickItemLastIndex(), STORAGE_START_INDEX))
		ItemChangeForTrigger(false, mPlayer->GetPickItemLastIndex(), pgrid->getItemID(), num);

	//mobile版:触发器事件:箱子中的道具被取出(手机是直接取出, pc是先取到鼠标上)
	if (GetClientInfoProxy()->isMobile() && IsGridIndexType(index, STORAGE_START_INDEX))
		ItemChangeForTrigger(false, index, pgrid->getItemID(), num);

	int item_id = pgrid->getItemID();
	//assert(pgrid->getNum() >= num);
	if(num > pgrid->getNum()) num = pgrid->getNum();
	
	if(pgrid->addNum(-num) == 0) pgrid->clear();

	afterChangeGrid(index);
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d,\"index\":%d,\"result_item\":%d,\"result_num\":%d,\"protocol\":%d}"
			, item_id, num, index, pgrid->getItemID(), pgrid->getNum(), AntiSetting::ProtocolHostIn);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_removeitem", tmp, true);
	}
}

int BackPack::removeItemInNormalPack(int itemid, int num)
{
	std::vector<int> checkPack = {getShortcutStartIndex(), BACKPACK_START_INDEX};

	int count = 0;
	if (num <= 0)
		return count;

	for (auto packIndex : checkPack)
	{
		int itemCount = getPack(packIndex)->getItemCount(itemid);
		if (itemCount > 0)
		{
			if (itemCount >= num)
			{
				getPack(packIndex)->removeItemByCount(itemid, num);
				count += num;
				num = 0;
				break;
			}
			else
			{
				getPack(packIndex)->removeItemByCount(itemid, itemCount);
				count += itemCount;
				num -= itemCount;
			}
		}
	}

	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d}", itemid, count);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_removeitem", tmp, true);
	}
	return count;
}
//code by:tanzhenyu
void BackPack::replaceItem_byGridCopyData(const GridCopyData& gridcopydata, int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid == nullptr) return;

	int old_item = pgrid->getItemID();
	int old_num = pgrid->getNum();
	SetBackPackGrid(*pgrid, gridcopydata.resid, gridcopydata.num, gridcopydata.duration, gridcopydata.toughness, gridcopydata.userdata, 1, 0, gridcopydata.userdata_str.c_str());
	pgrid->setEnchants(gridcopydata.enchantnum, gridcopydata.enchants);
	if(gridcopydata.runedata)
		pgrid->getRuneData().setdata(*gridcopydata.runedata);
	else
		pgrid->getRuneData().clear();
	afterChangeGrid(index);
	
	if (validServerBackPackLog() && _needSavePack())
	{
		char tmp[256];
		snprintf(tmp, sizeof(tmp),"{\"item\":%d,\"num\":%d,\"index\":%d,\"old_item\":%d,\"old_num\":%d}", pgrid->getItemID(), pgrid->getNum(), index, old_item, old_num);
		Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_setitem", tmp, true);
	}
	
	if (gridcopydata.resid > 0 && gridcopydata.num > 0){
		AntiSetting::recordGainItem(mPlayer? mPlayer->getUin(): 0, gridcopydata.resid, gridcopydata.num, old_item, old_num);
	}
}

void BackPack::replaceItemByNum(int index, int resid, int num)
{
	GridCopyData data;
	data.resid = resid;
	data.num = 1;
	replaceItem_byGridCopyData(data, index);
}

//void BackPack::replaceItem(int index, int resid, int num, int durable, int enchantNum/* = 0*/, int *enchantIds/* = nullptr*/, void *userdata/* = 0*/, const char *userdata_str/* = ""*/)
//{
//	BackPackGrid *pgrid = index2Grid(index);
//	if(pgrid == nullptr) return;
//
//	int old_item = pgrid->getItemID();
//	int old_num = pgrid->getNum();
//	SetBackPackGrid(*pgrid, resid, num, durable, -1, userdata, 1, 0, userdata_str);
//	pgrid->setEnchants(enchantNum, enchantIds);
//	pgrid->getRuneData().clear();
//	//pgrid->index = index;
//
//	afterChangeGrid(index);
//	
//	if (validServerBackPackLog())
//	{
//		BACKPACK_PLOG << "[bplog]replaceitem uin=" << mPlayer->getUin() << " index=" << index
//			<< " old=" << old_item << "_" << old_num
//			<< "result=" << pgrid->getItemID() << "_" << pgrid->getNum();
//	}
//	if (resid > 0 && num > 0){
//		AntiSetting::recordGainItem(mPlayer? mPlayer->getUin(): 0, resid, num, old_item, old_num);
//	}
//}

static  bool NeedCheckPackIndex(int index)
{
	int i = index/GRID_INDEX_BASIS;
	if(i == CRAFT_START_INDEX/GRID_INDEX_BASIS)
	{
		return false;
	}
	if (i == MINICRAFT_START_INDEX / GRID_INDEX_BASIS)
	{
		return false;
	}
	if(i <= FURNACE_START_INDEX/GRID_INDEX_BASIS) return true;
	if(i>=FUNNEL_START_INDEX/GRID_INDEX_BASIS && i<=HORSE_EQUIP_INDEX/GRID_INDEX_BASIS) return true;

	if(i == NPCTRADE_START_INDEX)
	{
		if(((index-NPCTRADE_START_INDEX)%2) != 0) return true; //npc商店的产出
	}

	return false;
}

void DoCheckPackGridValid(BackPackGrid *pgrid)
{  
	if(pgrid == NULL) return;
	int num = pgrid->getNum();
	if(num<0)
	{
		//memset(&pgrid, 0, sizeof(pgrid));//crash
		if(pgrid) pgrid->clear();
	}
	else if(pgrid &&  pgrid->def && num > pgrid->def->StackMax)
	{
		if (num > 200)  // 兼容旧的数据（有一个版本StackMax开放到99了）
			pgrid->setNum(200);
	}
}

BackPackGrid *BackPack::index2Grid(int index)
{
	//SANDBOXPROFILING_FUNC("index2Grid");
	auto ctn = getContainer(index);
	if (ctn == nullptr) return nullptr;
	BackPackGrid *pgrid = ctn->index2Grid(index);

	//家园道具不检查
	bool needCheck = true;
	if (mPlayer && mPlayer->getWorld() && mPlayer->getWorld()->getMapSpecialType() == HOME_GARDEN_WORLD)
		needCheck = false;

	if(pgrid && pgrid->def && NeedCheckPackIndex(index) && needCheck)
	{
		DoCheckPackGridValid(pgrid);
	}
	return pgrid;
}

void BackPack::afterChangeGrid(int gridindex)
{
	assert(mPlayer);
	BaseContainer *container = getContainer(gridindex);
	if(container) container->afterChangeGrid(gridindex);
}

//判断家园编辑模式
bool BackPack::isHomeLandGameMakerMode()
{
	if (!mPlayer)
	{
		return false;
	}

	return mPlayer->isHomeLandGameMakerMode();
}

//获取快捷栏索引
int BackPack::getShortcutStartIndex()
{
	if (!mPlayer)
	{
		return SHORTCUT_START_INDEX;
	}

	if (mPlayer->isHomeLandGameMakerMode())
	{
		return SHORTCUTEX_START_INDEX;
	}

	if (g_WorldMgr)
	{
		if (g_WorldMgr->isUGCEditMode())
		{
			return SHORTCUT_SINGLE_INDEX_EDIT;
		}
		else if (g_WorldMgr->isUGCEditBuildMode())
		{
			if (m_tmpShortcutMode)
			{
				return SHORTCUT_START_INDEX;
			}
			return SHORTCUT_START_INDEX_EDIT;
		}
		// 录像模式，如果是高级创造录的像也用高级创造的快捷栏下标
		else
		{
			auto worldDesc = GetClientInfoProxy()->getCurWorldDesc();
			if (worldDesc && worldDesc->worldtype == OWTYPE_RECORD && worldDesc->editorSceneSwitch == 1)
			{
				return SHORTCUT_START_INDEX_EDIT;
			}
		}
	}

	return SHORTCUT_START_INDEX;
}

void BackPack::swapItem(int fromgrid, int togrid)
{
	if(fromgrid == togrid) return;

	BackPackGrid *pfrom = index2Grid(fromgrid);
	if(pfrom == NULL) return;

	int toolType = getGridToolType(togrid);
	if (toolType == 31 && fromgrid >= 2000) //新鱼竿不可丢
	{
		if (mPlayer != NULL)
		{
			mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 81150);
		}
		return;
	}

	int srcitemid = pfrom->getItemID();
	//移除扩展背包加载原有背包内道具
	//if (fromgrid == EQUIP_START_INDEX + 4)//披风坑位
	//{
		int tooltype = getGridToolType(fromgrid);
		if (tooltype == 30)//扩展背包类型
		{
			BackPackGrid* grid = index2Grid(fromgrid);
			if (grid)
			{
				grid->setUserdataStr(getExtBackPackInfo().c_str());
			}
		}
	//}
	//togrid
	//if (togrid == EQUIP_START_INDEX + 4)//披风坑位
	//{
		tooltype = getGridToolType(togrid);
		if (tooltype == 30)//扩展背包类型
		{
			BackPackGrid* grid = index2Grid(togrid);
			if (grid)
			{
				grid->setUserdataStr(getExtBackPackInfo().c_str());
			}
		}
	//}



	BaseContainer *destcontainer = getContainer(togrid);
	if(destcontainer == NULL) return;

	if(!destcontainer->canPutItem(togrid) && pfrom->getNum()>0) return;

	BackPackGrid *pto = destcontainer->index2Grid(togrid);
	if(pto == NULL) return;
	//词条特质
	mPlayer->doEquipModEntry(srcitemid, false);

	BackPackGrid tmp;
	tmp.setItem(*pto);
	pto->setItem(*pfrom);
	pfrom->setItem(tmp);

	afterChangeGrid(fromgrid);
	afterChangeGrid(togrid);
	//处理扩展背包加载原有背包内道具
	
	//if (togrid == EQUIP_START_INDEX + 4) //披风坑位
	{
		tooltype = getGridToolType(togrid);
		if (tooltype == 30) //扩展背包
		{
			BackPackGrid* grid = index2Grid(togrid);
			if (grid)
			{
				loadExtBackPackInfo(grid->userdata_str);
			}
		}
	}
	//if (fromgrid == EQUIP_START_INDEX + 4) //披风坑位
	{
		tooltype = getGridToolType(fromgrid);
		if (tooltype == 30) //扩展背包
		{
			BackPackGrid* grid = index2Grid(fromgrid);
			if (grid)
			{
				loadExtBackPackInfo(grid->userdata_str);
			}
		}
	}


	if (mPlayer != NULL)
	{
		if (isBackpackIndex(togrid))
		{
			mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, pto->getItemID(), pto->getNum());
			mPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, pto->getItemID(), 0, pto->getNum());
		}
	}
	

	// fromgrid = srcindex = n-1+SHORTCUT_START_INDEX
	// 已完成转换,因此要用pfrom来判断是否为空
	/*if(togrid >= EQUIP_START_INDEX && togrid < FURNACE_START_INDEX && !pfrom->isEmpty())
	{
		if (mPlayer) mPlayer->onDisApplyEquips(fromgrid, togrid, srcitemid, 1);
	}*/

	if (mPlayer)
	{
		//卸载装备
		if (togrid >= EQUIP_START_INDEX && togrid < FURNACE_START_INDEX && !pfrom->isEmpty())
			mPlayer->onDisApplyEquips(fromgrid, togrid, pfrom->getItemID(), 1);

		if (fromgrid >= EQUIP_START_INDEX && fromgrid < FURNACE_START_INDEX && !pto->isEmpty())
			mPlayer->onDisApplyEquips(fromgrid, togrid, pto->getItemID(), 1);

		//穿戴装备
		if (togrid >= EQUIP_START_INDEX && togrid < FURNACE_START_INDEX && !pto->isEmpty())
			mPlayer->onApplyEquips(pto->getItemID(), togrid);
		else if (fromgrid >= EQUIP_START_INDEX && fromgrid < FURNACE_START_INDEX && !pfrom->isEmpty())
			mPlayer->onApplyEquips(pfrom->getItemID(), fromgrid);


		//触发器事件:PC版
		if (MOUSE_PICKITEM_INDEX == fromgrid)
		{
			int lastIndex = mPlayer->GetPickItemLastIndex();
			if (fromgrid == MOUSE_PICKITEM_INDEX && IsGridIndexType(togrid, STORAGE_START_INDEX))
			{
				//有道具被放入箱子
				if (lastIndex >= BACKPACK_START_INDEX && lastIndex < SHORTCUT_START_INDEX + 1000)
				{
					ItemChangeForTrigger(true, togrid, srcitemid, togrid);
					mPlayer->SetPickItemLastIndex(togrid);
				}
			}
			else if (fromgrid == MOUSE_PICKITEM_INDEX && !IsGridIndexType(togrid, STORAGE_START_INDEX))
			{
				//有道具从箱子取出
				if (IsGridIndexType(lastIndex, STORAGE_START_INDEX))
				{
					ItemChangeForTrigger(false, lastIndex, srcitemid, destcontainer->getGridNum());
					mPlayer->SetPickItemLastIndex(togrid);
				}
			}
		}
		else
		{
			if ((IsGridIndexType(fromgrid, SHORTCUT_START_INDEX) || IsGridIndexType(fromgrid, BACKPACK_START_INDEX)) && IsGridIndexType(togrid, STORAGE_START_INDEX))
			{
				//有道具被放入箱子
				if (togrid >= BACKPACK_START_INDEX && togrid < SHORTCUT_START_INDEX + 1000)
				{
					ItemChangeForTrigger(true, togrid, srcitemid, destcontainer->getGridNum());
				}
			}
			if ((IsGridIndexType(togrid, SHORTCUT_START_INDEX) || IsGridIndexType(togrid, BACKPACK_START_INDEX)) && IsGridIndexType(fromgrid, STORAGE_START_INDEX))
			{
				//有道具从箱子取出
				if (IsGridIndexType(fromgrid, STORAGE_START_INDEX))
					ItemChangeForTrigger(false, togrid, srcitemid, destcontainer->getGridNum());
			}
		}
	}
}

int BackPack::lootItem(int fromIndex, int num)
{
	int realNum = getGridNum(fromIndex);
	int itemId = getGridItem(fromIndex);
	if (realNum < num) num = realNum;

	if (itemId > 0 && num > 0)
	{
		if (mPlayer != nullptr)
		{
			int pickNum = mPlayer->gainItemsByIndex(fromIndex, num);
			if (pickNum > 0)
			{
				removeItem(fromIndex, pickNum);
			}
			return realNum - pickNum;
		}
	}
	return 0;
}

void BackPack::discardItem(int index, int num)
{
	if (mPlayer != nullptr)
	{
		mPlayer->throwBackpackItem(index, num);
	}
}

int BackPack::findItemInNormalPack(int itemid)
{
	int index;
	if((index=getPack(getShortcutStartIndex())->findItem(itemid)) >= 0) return index;
	if((index=getPack(BACKPACK_START_INDEX)->findItem(itemid)) >= 0) return index;

	return -1;
}

int BackPack::getItemCountInNormalPack(int itemid)
{
	int count = 0;

	count += getPack(getShortcutStartIndex())->getItemCount(itemid);
	count += getPack(BACKPACK_START_INDEX)->getItemCount(itemid);

	return count;
}

/**
 * @brief 用GridVisitor遍历背包
 * 
 * @param visitor 遍历者
 */
void BackPack::searchNormalPack(GridVisitor *visitor)
{
	if (!visitor)
		return;
	getPack(getShortcutStartIndex())->visitPack(visitor);
	getPack(BACKPACK_START_INDEX)->visitPack(visitor);
}

int BackPack::getSameGroupItemCountInNormalPack(int itemid)
{
	int count = 0;
	auto grids = getPack(getShortcutStartIndex())->m_Grids;
	for (size_t i = 0; i < grids.size(); i++)
	{
		BackPackGrid &grid = grids[i];
		auto itemDef = GetDefManagerProxy()->getItemDef(grid.getItemID());
		if (grid.getItemID() == itemid || (itemDef && itemDef->ItemGroup == itemid ))
		{
			count += grid.getNum();
		}
	}
	grids = getPack(BACKPACK_START_INDEX)->m_Grids;
	for (size_t i = 0; i < grids.size(); i++)
	{
		BackPackGrid &grid = grids[i];
		auto itemDef = GetDefManagerProxy()->getItemDef(grid.getItemID());
		if (grid.getItemID() == itemid || (itemDef && itemDef->ItemGroup == itemid))
		{
			count += grid.getNum();
		}
	}
	return count;
}

void BackPack::placeItem(int fromgrid, int togrid, int num)
{
	BackPackGrid *pfrom = index2Grid(fromgrid);
	assert(pfrom->getNum() >= num);

	BaseContainer *destcontainer = getContainer(togrid);
	if(destcontainer==NULL || !destcontainer->canPutItem(togrid) && pfrom->getNum()>0) return;

	BackPackGrid *pto = destcontainer->index2Grid(togrid);

	BackPackGrid tmp;
	tmp.setItem(*pfrom, num);
	pto->setItem(tmp, num);

	removeItem(fromgrid, num);

	afterChangeGrid(togrid);
	
	if (mPlayer == NULL) return;

	if (isBackpackIndex(togrid))
	{
		mPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, pto->getItemID(), pto->getNum());
		mPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, pto->getItemID(), 0, pto->getNum());
	}
}

int BackPack::getGridItem(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid) return pgrid->getItemID();
	else return 0;
}

int BackPack::getGridNum(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && !pgrid->isEmpty()) return pgrid->getNum();
	else return 0;
}

int BackPack::getGridDuration(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && !pgrid->isEmpty()) return pgrid->getDuration();
	else return 0;
}	

int BackPack::getGridMaxDuration(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	return pgrid ? pgrid->getMaxDuration() : -1;
}

int BackPack::getGridEnchantNum(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && !pgrid->isEmpty()) return pgrid->getNumEnchant();
	else return 0;
}

int BackPack::getGridEnchantId(int index, int idIndex)
{
	assert(idIndex<=getGridEnchantNum(index));
	BackPackGrid *pgrid = index2Grid(index);
	return pgrid ? pgrid->getIthEnchant(idIndex) : 0;
}

unsigned int BackPack::getGridEnchantColor(int index)
{
	unsigned int color = 0;
	int highestLevel = 0;
	//附魔和符文在同一个格子上不会共存,可以直接共用此接口   code by: tanzhenyu
	int runeNum = getRuneNum(index);
	if(runeNum > 0){
		for (int i = 0; i < runeNum; i++){
			const GridRuneItemData*  item = getRuneItem(index, i);
			if(item){
				int curLevel = item->getRuneId() % 100;
				if(highestLevel < curLevel){
					if (item->getRuneDef())//加个判空保护
					{
						color = item->getRuneDef()->Color;
						highestLevel = curLevel;
					}
					else//如果该符文词条被删除的话，则替换符文
					{
						GridRuneItemData runeData;
						runeData.rune_id = 0;
						runeData.rune_val1 = 0;
						runeData.rune_val0 = 0;
						runeData.item_id = 0;
						replaceRune(index, runeData,i);
					}
				}
			}
		}
		return color;
	}
	//没有符文  走附魔数据
	int num = getGridEnchantNum(index);
	for(int i=0; i<num; i++)
	{
		int id = getGridEnchantId(index, i);
		const EnchantDef *enchdef = GetDefManagerProxy()->getEnchantDef(id);
		if(enchdef)
		{
			int enlevel = id % 100;

			if(highestLevel == 0)
			{
				highestLevel = enlevel;
				color = enchdef->Color;
			}
			else if(enlevel > highestLevel)
			{
				highestLevel = enlevel;
				color = enchdef->Color;
			}
		}	
	}

	return color;
}

int BackPack::getGridToolType(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if (!pgrid) { return -1; }
	int itemid = pgrid->getItemID();
	if(itemid > 0)
	{
		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if(tooldef)
		{
			return tooldef->Type;
		}
	}
	
	return -1;
}

int BackPack::getGridUserdata(int index)
{
	BackPackGrid *pgrid = index2Grid(index);

	return pgrid ? ((pgrid->def && pgrid->def->ID == ITEM_BLUEPRINT) ? ((int)(size_t)pgrid->userdataEx) : ((int)(size_t)pgrid->userdata)) : -1;
}

const char* BackPack::getGridUserdataStr(int index)
{
	BackPackGrid *pgrid = index2Grid(index);

	return pgrid ? pgrid->userdata_str.c_str() : "";
}

const char* BackPack::getGridSidStr(int index)
{
	BackPackGrid *pgrid = index2Grid(index);

	return pgrid ? pgrid->sid_str.c_str() : "";
}

std::string BackPack::getModItemName(int index)
{
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		return pgrid->getModItemName();
	}
	return "";
}
std::string BackPack::getModItemDesc(int index)
{
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		return pgrid->getModItemDesc();
	}
	return "";
}
std::string BackPack::getModExtradata(int index)
{
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		return pgrid->getModExtradata();
	}
	return "";
}

std::string BackPack::getGridInfo(int index)
{
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		return pgrid->getJsonStr();
	}
	return "";
}
jsonxx::Object BackPack::getGridJsonxxInfo(int index)
{
	jsonxx::Object result;
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		 pgrid->save(result);
	}
	return result;
}

bool BackPack::setGridInfo(int index, const char* info)
{
	BackPackGrid* pgrid = index2Grid(index);
	if (pgrid)
	{
		pgrid->loadJsonStr(info);
		afterChangeGrid(index);
		return true;
	}
	return false;
}

bool BackPack::setGridJsonxxInfo(int index,  jsonxx::Object* info)
{
	if (info)
	{
		BackPackGrid* pgrid = index2Grid(index);
		if (pgrid)
		{
			pgrid->load(*info);
			afterChangeGrid(index);
			return true;
		}
	}
	return false;
}

int BackPack::getGridSortId(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	return pgrid ? pgrid->sortId : 0;
}

int BackPack::getGridCount(int baseIndex)
{
	auto container = getContainer(baseIndex);
	if (container)
	{
		return container->getGridCount();
	}
	return 0;
}

int BackPack::getShortcutGridCount()
{
	int index = getShortcutStartIndex();
	return getGridCount(index);
}

int BackPack::getGridEnough(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	return pgrid ? pgrid->enough : 0;
}

bool BackPack::canPutItem(int index)
{
	BaseContainer *container = getContainer(index);
	if(container == NULL) return false;
	else return container->canPutItem(index);
}

const char *BackPack::getGridItemName(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && pgrid->def)
	{
		return pgrid->def->Name.c_str();
	}
	else return "";
}

int BackPack::getGridMaxStack(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && pgrid->def)
	{
		return pgrid->getMaxStack();
	}

	return 1;
}

void BackPack::attachContainer(BaseContainer *container)
{
	int index = container->getBaseIndex()/GRID_INDEX_BASIS;

	m_Containers[index] = container;
	if(mPlayer && mPlayer->hasUIControl()) container->onAttachUI();
}

void BackPack::detachContainer(BaseContainer *container)
{
	int index = container->getBaseIndex()/GRID_INDEX_BASIS;

	if(mPlayer && mPlayer->hasUIControl()) container->onDetachUI();

	assert(m_Containers[index] == container);
	m_Containers[index] = NULL;
}

bool LessThan(const BackPackGrid &g1, const BackPackGrid &g2)
{
	int id1 = g1.getItemID();
	int id2 = g2.getItemID();

	if(id1 == 0) return false;
	if(id2 == 0) return true;

	return id1 < id2;
}

bool LessThan2sortId(const BackPackGrid &g1, const BackPackGrid &g2)
{
	if(g1.isEmpty()) return false;
	if(g2.isEmpty()) return true;

	if(g1.enough > g2.enough)
	{
		return true;
	}
	else if(g1.enough < g2.enough)
	{
		return false;
	}
	else
	{
		return g1.sortId < g2.sortId;
	}
}

void BackPack::clearPackByType(int index, const std::string &reason)
{
	PackContainer *container = (PackContainer*)getContainer(index);
	container->clear();
	
	char tmp[256];
	snprintf(tmp, sizeof(tmp),"{\"reason\":%s, \"index\":%d}", reason.c_str(), index);
	Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_clear", tmp, true);
}

void BackPack::clearPack(const std::string &reason)
{
	PackContainer *container = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	container->clear();

	container = (PackContainer*)getContainer(SHORTCUT_START_INDEX);
	container->clear();

	container = (PackContainer*)getContainer(SHORTCUTEX_START_INDEX);
	container->clear();

	container = (PackContainer*)getContainer(EQUIP_START_INDEX);
	container->clear();

	container = (PackContainer*)getContainer(SHORTCUT_START_INDEX_EDIT);
	container->clear();

	container = (PackContainer*)getContainer(SHORTCUT_SINGLE_INDEX_EDIT);
	container->clear();
	char tmp[256];
	snprintf(tmp, sizeof(tmp),"{\"reason\":%s}", reason.c_str());
	Rainbow::GetICloudProxyPtr()->packLog(mPlayer->getUin(), "pack_clear", tmp, true);
}

void BackPack::setCreateModeShortCut()
{
	PackContainer *container = (PackContainer*)getContainer(getShortcutStartIndex());

	for(size_t i=0; i<container->m_Grids.size(); i++)
	{
		if(container->m_Grids[i].def == NULL) continue;

		container->m_Grids[i].setNum(1);
		afterChangeGrid(container->m_Grids[i].getIndex());
	}
}

void BackPack::mergePack(int base_index, bool isChange)
{
	PackContainer *container = (PackContainer*)getContainer(base_index);
	if(container == NULL) return;

	for(size_t i=0; i<container->m_Grids.size(); i++)
	{
		if(container->m_Grids[i].def == NULL || container->m_Grids[i].def->StackMax <= container->m_Grids[i].getNum()) continue;
		BackPackGrid &grid1 = container->m_Grids[i];

		size_t j = i+1;
		for( ; j<container->m_Grids.size(); j++)
		{
			BackPackGrid &grid2 = container->m_Grids[j];
			if(grid2.def == NULL) continue;
			if(grid1.def->ID == grid2.def->ID)
			{
				//染色方块，id相同但颜色不同时不合并 by：Jeff 2023/02/06
				if (grid1.userdata_str != grid2.userdata_str && IsDyeableBlock(grid1.def->ID))
				{
					continue;
				}
				int num = grid1.def->StackMax - grid1.getNum();
				if(num >= grid2.getNum())
				{
					num = grid2.getNum();
					SetBackPackGrid(grid2, 0, 0);
					if(isChange)
					{
						afterChangeGrid(grid2.getIndex());
					}		
				}
				else
				{
					grid2.addNum(-num);
				}

				grid1.addNum(num);
				if(isChange)
				{
					afterChangeGrid(grid1.getIndex());
				}
				if(grid1.getNum() >= grid1.def->StackMax) break;
			}
		}
	}
}

bool BackPack::mergeItem(int fromIndex, int toIndex)
{
	if(fromIndex == toIndex) return false;

	BackPackGrid *fgrid = index2Grid(fromIndex);
	BackPackGrid *dgrid = index2Grid(toIndex);

	if(!fgrid || !dgrid) return false;

	if(fgrid->def && dgrid->def && fgrid->def->ID == dgrid->def->ID)
	{
		//染色方块，id相同但颜色不同时不合并 by：Jeff 2023/02/06
		if (IsDyeableBlock(fgrid->def->ID) && fgrid->userdata_str != dgrid->userdata_str)
		{
			return false;
		}
		int num = dgrid->getMaxStack() - dgrid->getNum();
		if(num == 0 )
		{
			return false;
		}
		else if(num >= fgrid->getNum())
		{
			dgrid->addNum(fgrid->getNum());
			fgrid->clear();
		}
		else
		{
			fgrid->addNum(-num);
			dgrid->addNum(num);
		}

		afterChangeGrid(fromIndex);	
		afterChangeGrid(toIndex);
		
		return true;
	}
	return false;
}

void BackPack::copyCurShotcutToOldEdit(int fromIndex, int toIndex)
{
	if (fromIndex >= SHORTCUT_START_INDEX_EDIT && fromIndex <= toIndex && toIndex < SHORTCUT_START_INDEX_EDIT + 1000)
	{
		if (toIndex - fromIndex > 7)
		{
			toIndex = fromIndex + 7;
		}
		int startIdx = SHORTCUT_START_INDEX;
		for (size_t i = fromIndex; i <= toIndex; i++)
		{
			BackPackGrid *fgrid = index2Grid(i);
			BackPackGrid *dgrid = index2Grid(startIdx);
			if (!fgrid || !dgrid) return;

			SetBackPackGrid(*dgrid, fgrid->getItemID(), fgrid->getNum(), -1, -1, 0, 1, 0, fgrid->userdata_str.c_str());
			afterChangeGrid(startIdx);
			startIdx++;
		}
	}
}

void BackPack::copyOldEditToCurShotcut(int fromIndex, int toIndex)
{
	if (fromIndex >= SHORTCUT_START_INDEX_EDIT && fromIndex <= toIndex && toIndex < SHORTCUT_START_INDEX_EDIT + 1000)
	{
		if (toIndex - fromIndex > 7)
		{
			toIndex = fromIndex + 7;
		}
		int startIdx = SHORTCUT_START_INDEX;
		for (size_t i = fromIndex; i <= toIndex; i++)
		{
			BackPackGrid *dgrid = index2Grid(i);
			BackPackGrid *fgrid = index2Grid(startIdx);
			if (!fgrid || !dgrid) return;

			SetBackPackGrid(*dgrid, fgrid->getItemID(), fgrid->getNum(), -1, -1, 0, 1, 0, fgrid->userdata_str.c_str());
			afterChangeGrid(i);
			startIdx++;
		}
	}
}

//将一个元素插入指定位置，如果该位置无item直接放置，如果有将此位置及后面的item往后挪动，shortcuts的size就删除该item
bool BackPack::insertItemToCurShotcutEdit(int itemId, int start, std::vector<int>& shortcuts)
{
	if (start >= shortcuts.size())
	{
		return true;
	}
	int shortcut = shortcuts[start];
	if (shortcut == 0)//空位
	{
		shortcuts[start] = -abs(itemId);//表示此位置改变了
		return true;
	}
	while (shortcut != 0 && start < shortcuts.size())
	{
		int oldItem = shortcuts[start];
		shortcuts[start] = -abs(itemId);//表示此位置改变了
		if (insertItemToCurShotcutEdit(oldItem, start + 1, shortcuts))
		{
			break;
		}
	}
	return true;
}

void BackPack::insertItemsToCurShotcutEdit(int fromIndex, int toIndex, std::vector<int> items)
{
	if (fromIndex >= SHORTCUT_START_INDEX_EDIT && fromIndex <= toIndex && toIndex < SHORTCUT_START_INDEX_EDIT + 1000)
	{
		if (toIndex - fromIndex != 7)
		{
			return;
		}
		int num = items.size();
		if (num > 8 || num <= 0)
		{
			return;
		}
		std::vector<int> shortcuts;
		//编辑模式数量都是1，不用管
		for (size_t i = fromIndex; i <= toIndex; i++)
		{
			BackPackGrid *fgrid = index2Grid(i);
			if (!fgrid) return;
			shortcuts.push_back(fgrid->getItemID());
		}

		int startIdx = 0;
		for (size_t i = 0; i < num; i++)
		{
			int itemId = items[i];
			insertItemToCurShotcutEdit(itemId, startIdx++, shortcuts);
		}

		startIdx = 0;
		for (size_t i = fromIndex; i <= toIndex; i++)
		{
			BackPackGrid *fgrid = index2Grid(i);
			if (!fgrid) return;

			if (shortcuts[startIdx] < 0)
			{
				SetBackPackGrid(*fgrid, -shortcuts[startIdx], 1);
				afterChangeGrid(i);
			}
			startIdx++;
		}
	}
}

static bool IsSamePackGrid(BackPackGrid &g1, BackPackGrid &g2)
{
	if(g1.getItemID()==0 && g2.getItemID()==0) return true;

	if(g1.getItemID()==g2.getItemID() && g1.getNum()==g2.getNum() && g1.getDuration()==g2.getDuration() && g1.getNumEnchant()==g2.getNumEnchant() && g1.userdata==g2.userdata && g1.enough==g2.enough && g1.sortId==g2.sortId && g1.userdata_str == g2.userdata_str)
	{
		for(int i=0; i<g1.getNumEnchant(); i++)
		{
			if(g1.getIthEnchant(i) != g2.getIthEnchant(i)) return false;
		}
		return true;
	}
	return false;
}
void BackPack::sortPack(int base_index, bool isChange)
{
	PackContainer *container = (PackContainer*)getContainer(base_index);
	if (container == nullptr) return;

	std::vector<BackPackGrid>gridbak(container->m_Grids.size());
	for(size_t i=0; i < container->m_Grids.size(); i++)
	{
		gridbak[i] = container->m_Grids[i];
	}

	if(isChange)
		mergePack(base_index, false);
	
	std::vector<int> index;

	for(size_t i=0; i < container->m_Grids.size(); i++)
	{
		index.push_back(container->m_Grids[i].getIndex());
	}
	
	if (base_index == PRODUCT_LIST_TWO_INDEX || base_index == COMMON_PRODUCT_LIST_INDEX || base_index == EQUIP_PRODUCT_LIST_INDEX
		|| base_index == BUILD_PRODUCT_LIST_INDEX || base_index == MACHINE_PRODUCT_LIST_INDEX || base_index == RECIPE_PRODUCT_LIST_INDEX
		|| base_index == PROPS_PRODUCT_LIST_INDEX || base_index == MATERIAL_PRODUCT_LIST_INDEX || base_index == DECORATE_PRODUCT_LIST_INDEX)
	{
		std::sort(container->m_Grids.begin(), container->m_Grids.end(), LessThan2sortId);
	}
	else
	{
		std::sort(container->m_Grids.begin(), container->m_Grids.end(), LessThan);
	}

	if(isChange)
	{
		bool remoteplayer = !mPlayer->hasUIControl();

		for(size_t i=0; i < container->m_Grids.size(); i++)
		{
			BackPackGrid &grid = container->m_Grids[i];
			if(remoteplayer && base_index==BACKPACK_START_INDEX)
			{
				if(!IsSamePackGrid(gridbak[i], grid))
				{
					afterChangeGrid(grid.getIndex());
				}
			}
		}
	}
}

void BackPack::sortStorageBox()
{
	WorldStorageBox *storageBox = (WorldStorageBox*)getContainer(STORAGE_START_INDEX);
	if (storageBox == nullptr) return;

	int totalgrids = storageBox->getGridCount();
	std::vector<BackPackGrid> storageBoxGrids(totalgrids);

	for(int i=0; i<totalgrids; i++)
	{
		BackPackGrid* grid = storageBox->index2Grid(STORAGE_START_INDEX+i);
		if(grid){
			storageBoxGrids[i] = *grid;
		}
	}

	for(size_t i=0; i<storageBoxGrids.size(); i++)
	{
		if(storageBoxGrids[i].def == NULL || storageBoxGrids[i].def->StackMax <= storageBoxGrids[i].getNum()) continue;
		BackPackGrid &grid1 = storageBoxGrids[i];

		size_t j = i+1;
		for( ; j<storageBoxGrids.size(); j++)
		{
			BackPackGrid &grid2 = storageBoxGrids[j];
			if(grid2.def == NULL) continue;
			if(grid1.def->ID == grid2.def->ID)
			{
				if (IsDyeableBlock(grid1.def->ID) && grid1.userdata_str != grid2.userdata_str)
				{
					continue;
				}

				int num = grid1.def->StackMax - grid1.getNum();
				if(num >= grid2.getNum())
				{
					num = grid2.getNum();
					SetBackPackGrid(grid2, 0, 0);		
				}
				else
				{
					grid2.addNum(-num);
				}

				grid1.addNum(num);	
				if(grid1.getNum() >= grid1.def->StackMax) break;
			}
		}
	}

	std::sort(storageBoxGrids.begin(), storageBoxGrids.end(), LessThan);

	for(int i=0; i<totalgrids; i++)
	{
		int index = STORAGE_START_INDEX + i;
		BackPackGrid* grid = storageBox->index2Grid(index);
		if(grid)
		{
			BackPackGrid &src = *grid;
			if(!IsSamePackGrid(storageBoxGrids[i], src))
			{
				src = storageBoxGrids[i];
				afterChangeGrid(index);
			}
		}
	}
}

//cratring表项同样原材料放一起统计
void GetNeedMaterialID(const CraftingDef *def, std::vector<int> &materialIds, std::vector<int> &needSum)
{
	for( int i = 0; i < (sizeof(def->MaterialID)/sizeof(def->MaterialID[0])); i++)						//遍历MaterialID
	{
		if(def->MaterialID[i] <= 0) continue;

		bool needPack = true;
		for(size_t j=0;j<materialIds.size(); j++)
		{
			if(def->MaterialID[i] == materialIds[j])
			{
				needSum[j] += def->MaterialCount[i];
				needPack = false;
				continue;
			}
		}
		if(needPack)
		{
			materialIds.push_back(def->MaterialID[i]);
			needSum.push_back(def->MaterialCount[i]);
		}
	}
}


void BackPack::setBackPackData(std::map<int, int> &data)
{
	PackContainer *backpackContainer = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	PackContainer *shortcutContainer = (PackContainer*)getContainer(getShortcutStartIndex());

	if(backpackContainer)
	{	
		for (size_t n = 0; n < backpackContainer->m_Grids.size(); n++)
		{
			BackPackGrid grid = backpackContainer->m_Grids[n];
			if (0 == grid.def) continue;

			data[grid.def->ID] += grid.getNum();
		}
	}

	if(shortcutContainer)
	{			
		for(size_t n=0; n<shortcutContainer->m_Grids.size(); n++)
		{
			BackPackGrid grid = shortcutContainer->m_Grids[n];
			if(0 == grid.def) continue;

			data[grid.def->ID] +=  grid.getNum();			
		}
	}

	//auto def = GetDefManagerProxy()->getCraftingDef(craftid);
	//if(!def) return;

	//PackContainer *backpackContainer = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	//PackContainer *shortcutContainer = (PackContainer*)getContainer(SHORTCUT_START_INDEX);

	//for( int i = 0; i < (def->GridX*def->GridY); i++)						//遍历MaterialID
	//{
	//	if(def->MaterialID[i] <= 0) continue;

	//	bool isExist = false;
	//	for(size_t j=0;j<datas.size(); j++)
	//	{
	//		if(datas[j].itemid == def->MaterialID[i])
	//		{
	//			isExist = true;
	//			break;
	//		}
	//	}

	//	if(!isExist)
	//	{
	//		int sum = 0;
	//		if(backpackContainer)
	//		{			
	//			for(size_t k=0; k<backpackContainer->m_Grids.size(); k++)
	//			{
	//				BackPackGrid grid = backpackContainer->m_Grids[k];
	//				if(0 == grid.def) continue;

	//				if(grid.def->ID == def->MaterialID[i])
	//				{
	//					sum += grid.getNum();				 
	//				}			
	//			}
	//		}

	//		if(shortcutContainer)
	//		{			
	//			for(size_t n=0; n<shortcutContainer->m_Grids.size(); n++)
	//			{
	//				BackPackGrid grid = shortcutContainer->m_Grids[n];
	//				if(0 == grid.def) continue;

	//				if(grid.def->ID == def->MaterialID[i])
	//				{
	//					sum += grid.getNum();				 
	//				}			
	//			}
	//		}

	//		if(sum > 0)
	//		{
	//			BackPackData data;
	//			data.itemid = def->MaterialID[i];
	//			data.num = sum;

	//			datas.push_back(data);
	//		}
	//	}
	//}
}

void BackPack::setBackPackDataForCraft(std::map<int, int>& data, std::string* strHex)
{
	PackContainer* backpackContainer = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)getContainer(getShortcutStartIndex());
	auto defMgr = GetDefManagerProxy();
	if (!defMgr)
		return;

	if (backpackContainer)
	{
		for (size_t n = 0; n < backpackContainer->m_Grids.size(); n++)
		{
			BackPackGrid grid = backpackContainer->m_Grids[n];
			if (0 == grid.def || !defMgr->isCraftingMaterial(grid.def->ID)) continue;

			data[grid.def->ID] += grid.getNum();
		}
	}

	if (shortcutContainer)
	{
		for (size_t n = 0; n < shortcutContainer->m_Grids.size(); n++)
		{
			BackPackGrid grid = shortcutContainer->m_Grids[n];
			if (0 == grid.def || !defMgr->isCraftingMaterial(grid.def->ID)) continue;

			data[grid.def->ID] += grid.getNum();
		}
	}

	if (strHex)
	{
		// 算一下 hex
		int nSize = data.size();
		if (nSize > 0)
		{
			std::vector<int> vecIds;
			vecIds.reserve(nSize);
			std::vector<int> vecNums;
			vecNums.reserve(nSize);
			for (auto ite = data.begin(); ite != data.end(); ++ite)
			{
				vecIds.push_back(ite->first);
				vecNums.push_back(ite->second);
			}

			char output[16] = { 0 };
			char hex[33] = { 0 };

			MINIW::Md5Context context;
			context.begin();
			context.append((const UInt8*)&vecIds[0], vecIds.size() * sizeof(int));
			context.append((const UInt8*)&vecNums[0], vecNums.size() * sizeof(int));
			context.end((UInt8*)output);
			MINIW::Md5ToHex((char*)hex, (char*)output);

			*strHex = hex;
		}
	}
}

bool BackPack::showRecipeProduct()
{
	return  CraftHelper::CheckRecipeUnlockNum(mPlayer);
}

bool SortFunc(const BackPackGrid &g1, const BackPackGrid &g2)
{
	if (g1.isEmpty()) return false;
	if (g2.isEmpty()) return true;

	if (g1.enough > g2.enough)
	{
		return true;
	}
	else if (g1.enough < g2.enough)
	{
		return false;
	}
	const CraftingDef *def1 = GetDefManagerProxy()->getCraftingDef(g1.sortId);
	if (!def1)
		return false;
	const CraftingDef *def2 = GetDefManagerProxy()->getCraftingDef(g2.sortId);
	if (!def2)
		return false;

	return def1->DisplayOrder < def2->DisplayOrder;
}

int BackPack::updateProductContainer(int base_index, int level, bool updateAll/* = false*/, int sortFunc/* = 0*/)
{
	PackContainer *productContainer = (PackContainer*)getContainer(base_index);
	if(productContainer == NULL) return 0;
	productContainer->initGrids(base_index);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_updateProductContainer", SandboxContext(NULL).
		SetData_Usertype("productContainer", productContainer).
		SetData_Usertype("backPack", this).
		SetData_Number("base_index", base_index).
		SetData_Number("level", level).
		SetData_Bool("updateAll", updateAll)
	);
	int cnt = 0;
	if (result.IsSuccessed())
	{
		cnt = (int)result.GetData_Number();
	}
	if (sortFunc == 1)
	{
		std::sort(productContainer->m_Grids.begin(), productContainer->m_Grids.end(), SortFunc);
	}
	else if (sortFunc != -1)
	{
		sortPack(base_index, false);
	}
	return cnt;
}
int BackPack::updateCookBookProductContainer(long long uin, int base_index, bool updateAll, int sortFunc)								//更新背包物品中可制作食谱的物品(材料不足也算)
{
	PackContainer* productContainer = (PackContainer*)getContainer(base_index);
	if (productContainer == NULL) return 0;
	productContainer->initGrids(base_index);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_updateCookBookProductContainer", SandboxContext(NULL).
		SetData_Usertype("productContainer", productContainer).
		SetData_Usertype("backPack", this).
		SetData_Number("uin", uin).
		SetData_Number("base_index", base_index).
		SetData_Bool("updateAll", updateAll)
	);
	int cnt = 0;
	if (result.IsSuccessed())
	{
		cnt = (int)result.GetData_Number();
	}
	if (sortFunc == 1)
	{
		std::sort(productContainer->m_Grids.begin(), productContainer->m_Grids.end(), SortFunc);
	}
	else if (sortFunc != -1)
	{
		sortPack(base_index, false);
	}
	return cnt;
}


bool BackPack::addCraftProduct(const CraftingDef *def, PackContainer *productContainer, int base_index, int level, bool updateAll/* = false*/, std::map<int, int>* backpackData/* = nullptr*/, std::string* strHex/* = nullptr*/)
{
	if(def == NULL) return false; 
	bool bResult = false;

	CraftMgr* craftMgr = GET_SUB_SYSTEM(CraftMgr);
	if (craftMgr)
	{
		bResult = craftMgr->CanAddToCookProduct(def, level, base_index);
	}
	if (COOKING_PRODUCT_LIST_INDEX == base_index)
	{		
		if (!bResult)
		{
			return false;
		}
	}
	else
	{
		if (updateAll && COMMON_PRODUCT_LIST_INDEX == base_index)
		{
			if (COMMON_PRODUCT_LIST_INDEX == base_index && bResult)
			{
				return false;
			}
		}
		{
			if (COMMON_PRODUCT_LIST_INDEX == base_index &&
				(!def->containNum(0) || bResult))
			{
				return false;
			}
		}
		if (!updateAll)
		{
			if (def->CraftingItemID != GetDefManagerProxy()->getCraftEmptyHandID() && (def->CraftingItemID == BLOCK_STONE_POT || def->CraftingItemID == BLOCK_IRON_POT))
			{
				return false;
			}
			if (PRODUCT_LIST_TWO_INDEX == base_index && def->CraftingItemID != GetDefManagerProxy()->getCraftEmptyHandID()) //空手由CraftingItemID判断  code-by:DemonYan
			{
				return false;
			}

			if (EQUIP_PRODUCT_LIST_INDEX == base_index && (!def->containNum(1)))
			{
				return false;
			}
			else if (PROPS_PRODUCT_LIST_INDEX == base_index && (!def->containNum(2)))
			{
				return false;
			}
			else if (MATERIAL_PRODUCT_LIST_INDEX == base_index && (!def->containNum(3)))
			{
				return false;
			}
			else if (BUILD_PRODUCT_LIST_INDEX == base_index && (!def->containNum(4)))
			{
				return false;
			}
			else if (DECORATE_PRODUCT_LIST_INDEX == base_index && (!def->containNum(5)))
			{
				return false;
			}

			else if (MACHINE_PRODUCT_LIST_INDEX == base_index && (!def->containNum(6)))
			{
				return false;
			}
		}
		/*if (!CraftMgr::getSingletonPtr()->CanAddToProduct(def, level, base_index))
		{
			return false;
		}*/
	}

	auto itemDef = GetDefManagerProxy()->getItemDef(def->ResultID);
	if (!itemDef)
		return false;
	if (itemDef && itemDef->CondUnlcokType > 0 && g_WorldMgr != NULL && !g_WorldMgr->isUnlockItem(itemDef->CondUnlcokType)) return false;
	if (itemDef && itemDef->CondUnlcokType == -1 && g_pPlayerCtrl != NULL && !g_pPlayerCtrl->isUnlockItem(itemDef->ID)) return false;

	bool canCrafting = false;
	int share = -1;
	int canCraftNum = CraftHelper::GetPlayerCanCraftNumAndAnyItem(mPlayer, def->ID, backpackData, strHex);
	if (canCraftNum > 0)	//材料足够
	{
		canCrafting = true;
		share = canCraftNum;
	}
	else  //材料不足
	{
		if (canCraftNum == 0 || RECIPE_PRODUCT_LIST_INDEX == base_index) //配方合成没有材料也显示
		{
			canCrafting = true;
		}
		else if (-1 == canCraftNum && (COMMON_PRODUCT_LIST_INDEX == base_index || EQUIP_PRODUCT_LIST_INDEX == base_index || BUILD_PRODUCT_LIST_INDEX == base_index || MACHINE_PRODUCT_LIST_INDEX == base_index)
			|| PROPS_PRODUCT_LIST_INDEX == base_index || MATERIAL_PRODUCT_LIST_INDEX == base_index || DECORATE_PRODUCT_LIST_INDEX == base_index
			)
		{
			canCrafting = true;	//创造锤相关的，没有材料也显示 code_by:huangfubin
		}
		share = 0;
	}

	if (canCrafting)
	{
		//char * anyItem = "hasAnyItem";
		for (size_t i = 0; i < productContainer->m_Grids.size(); i++)
		{
			BackPackGrid &grid = productContainer->m_Grids[i];
			if (NULL == grid.def)
			{
				int enough = 0;
				if (share > 0)
				{
					enough = 1;
				}
				/*if (hasAnyItem)
				{
					SetBackPackGrid(grid, def->ResultID, share*def->ResultCount, -1, 0, enough, def->ID, anyItem);
				}
				else
				{*/
				SetBackPackGrid(grid, def->ResultID, share*def->ResultCount, -1, -1, 0, enough, def->ID);
				//}
				break;
			}
		}
	}

	return canCrafting;
}

bool BackPack::addCookbookProduct(const CraftingDef* def, PackContainer* productContainer, int base_index, bool updateAll)
{
	if (def == NULL) return false;
	auto itemDef = GetDefManagerProxy()->getItemDef(def->ResultID);
	if (!itemDef)
		return false;
	if (itemDef && itemDef->CondUnlcokType > 0 && g_WorldMgr != NULL && !g_WorldMgr->isUnlockItem(itemDef->CondUnlcokType)) return false;
	if (itemDef && itemDef->CondUnlcokType == -1 && g_pPlayerCtrl != NULL && !g_pPlayerCtrl->isUnlockItem(itemDef->ID)) return false;
	bool canCrafting = false;
	int share = -1;
	int canCraftNum = CraftHelper::GetPlayCanCookBookNumAndAnyItem(mPlayer, def->ID);
	if (canCraftNum > 0)	//材料足够
	{
		canCrafting = true;
		share = canCraftNum;
	}
	else  //材料不足
	{
		if (canCraftNum == 0 || RECIPE_PRODUCT_LIST_INDEX == base_index) //配方合成没有材料也显示
		{
			canCrafting = true;
		}
		else if (-1 == canCraftNum && (COMMON_PRODUCT_LIST_INDEX == base_index || EQUIP_PRODUCT_LIST_INDEX == base_index || BUILD_PRODUCT_LIST_INDEX == base_index || MACHINE_PRODUCT_LIST_INDEX == base_index)
			|| PROPS_PRODUCT_LIST_INDEX == base_index || MATERIAL_PRODUCT_LIST_INDEX == base_index || DECORATE_PRODUCT_LIST_INDEX == base_index
			)
		{
			canCrafting = true;	//创造锤相关的，没有材料也显示 code_by:huangfubin
		}
		share = 0;
	}
	if (canCrafting)
	{
		//char * anyItem = "hasAnyItem";
		for (size_t i = 0; i < productContainer->m_Grids.size(); i++)
		{
			BackPackGrid& grid = productContainer->m_Grids[i];
			if (NULL == grid.def)
			{
				int enough = 0;
				if (share > 0)
				{
					enough = 1;
				}
				/*if (hasAnyItem)
				{
					SetBackPackGrid(grid, def->ResultID, share*def->ResultCount, -1, 0, enough, def->ID, anyItem);
				}
				else
				{*/
				SetBackPackGrid(grid, def->ResultID, share * def->ResultCount, -1, -1, 0, enough, def->ID);
				//}
				break;
			}
		}
	}

	return canCrafting;
}



int BackPack::updateCraftContainer(int resultID, int base_index, int enough, int makeNum)
{
	return CraftHelper::UpdateCraftContainer(mPlayer, resultID, base_index, enough, makeNum);
}

int  BackPack::updateCookBookContainer(int resultID, int base_index, int enough, int makeNum)
{
	return CraftHelper::UpdateCookBookContainer(mPlayer, resultID, base_index, enough, makeNum);
}

bool BackPack::doCrafting(int craftingid, int *remainNum /*=nullptr*/, int num /*=1*/)
{
	return CraftHelper::PlayerDoCraft(mPlayer, craftingid, num, remainNum);
}

bool BackPack::doPlayerPreDeductCraftMaterials(int craftingid, int num)
{
	return CraftHelper::PlayerPreDeductCraftMaterials(mPlayer, craftingid, num);
}

bool BackPack::doPlayerCraftFromWithhold(int craftingid, int* remainNum /*=nullptr*/, int num /*=1*/)
{
	return CraftHelper::PlayerCraftFromWithhold(mPlayer, craftingid, num, remainNum);
}

bool BackPack::doPlayerReturnPreDeductedMaterialsByCraft(int craftID, int num)
{
	return CraftHelper::PlayerReturnPreDeductedMaterialsByCraft(mPlayer, craftID, num);
}

void BackPack::doRepair(int durable)
{
	RepairContainer *container = (RepairContainer*)m_Containers[REPAIR_START_INDEX/GRID_INDEX_BASIS];
	container->doRepair(durable);
}

void BackPack::clearEnchant(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && pgrid->def)
	{
		int old_dur = pgrid->getDurationEnchant();
		pgrid->setEnchants(0, NULL);
		pgrid->changeDurationOnRuneOrEnchantChange(old_dur);
	}
}

bool BackPack::enchant(int gridindex, int enchantid)
{
	BackPackGrid *grid = index2Grid(gridindex);

	if(grid && !grid->isEmpty() && grid->addEnchant(enchantid))
	{
		afterChangeGrid(gridindex);
		return true;
	}
	return false;	
}

bool BackPack::addRune(int gridindex, const GridRuneItemData &one)
{
	BackPackGrid *grid = index2Grid(gridindex);

	if(grid && !grid->isEmpty() && grid->addRune(one))
	{
		afterChangeGrid(gridindex);
		return true;
	}
	return false;
}
//当前格子的物品 镶嵌的符文数量 code by:tanzhenyu
int BackPack::getRuneNum(int index)
{
	BackPackGrid *grid = index2Grid(index);
	if(grid){
		return grid->getRuneData().getRuneNum();
	}
	return 0;
}
//获取当前格子的物品指定下标的的符文数据 code by:tanzhenyu
const GridRuneItemData* BackPack::getRuneItem(int gridindex, int runeIndex)
{
	BackPackGrid *grid = index2Grid(gridindex);
	if(grid){	
		const GridRuneData& rdata  = grid->getRuneData();
		if(runeIndex < 0 || runeIndex >= rdata.getRuneNum())//越界
			return NULL;

		const GridRuneItemData& data = rdata.getItemByIndex(runeIndex);
		return &data;
	}
	return NULL;
}
//替换已镶嵌的符文code by:tanzhenyu
bool BackPack::replaceRune(int gridindex, const GridRuneItemData &one, int runeIndex)
{
	BackPackGrid *grid = index2Grid(gridindex);

	if(grid && grid->replaceRune(one, runeIndex))
	{
		afterChangeGrid(gridindex);
		return true;
	}
	return false;
}
//删除符文 code by:tanzhenyu
void BackPack::clearRune(int index)
{
	BackPackGrid *pgrid = index2Grid(index);
	if(pgrid && pgrid->def)
	{
		int old_dur = pgrid->getRuneData().getDuration();
		pgrid->getRuneData().clear();
		pgrid->changeDurationOnRuneOrEnchantChange(old_dur);
	}
}

bool BackPack::enoughGridForItem(int itemid, int num)
{
	if (itemid <= 0) return false;
	auto def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == nullptr) return false;
	PackContainer *backpackContainer = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	PackContainer *shortcutContainer = (PackContainer*)getContainer(getShortcutStartIndex());
	if (backpackContainer == nullptr || shortcutContainer == nullptr) return false;

	int maxStack = def->StackMax;

	// backpack grid cnt for item
	for(size_t j=0; j<backpackContainer->m_Grids.size(); j++)
	{
		BackPackGrid grid = backpackContainer->m_Grids[j];
		if(nullptr == grid.def || grid.def->ID == 0)
		{
			num -= maxStack;
		}
		else if(grid.def->ID == itemid)
		{
			num -= maxStack - grid.getNum();
		}
		if (num <= 0) return true;
	}
	
	for(size_t j=0; j<shortcutContainer->m_Grids.size(); j++)
	{
		BackPackGrid grid = shortcutContainer->m_Grids[j];
		if(nullptr == grid.def || grid.def->ID == 0)
		{
			num -= maxStack;
		}
		else if(grid.def->ID == itemid)
		{
			num -= maxStack - grid.getNum();
		}
		if (num <= 0) return true;
	}

	return false;
}

int BackPack::enoughGridForItemMaxNum(int itemid, int num)
{
	if (itemid <= 0) return false;
	auto def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == nullptr) return false;
	PackContainer *backpackContainer = (PackContainer*)getContainer(BACKPACK_START_INDEX);
	PackContainer *shortcutContainer = (PackContainer*)getContainer(getShortcutStartIndex());
	if (backpackContainer == nullptr || shortcutContainer == nullptr) return false;

	int maxStack = def->StackMax;
	int maxNum = num;
	// backpack grid cnt for item
	for (size_t j = 0; j < backpackContainer->m_Grids.size(); j++)
	{
		BackPackGrid grid = backpackContainer->m_Grids[j];
		if (nullptr == grid.def || grid.def->ID == 0)
		{
			num -= maxStack;
		}
		else if (grid.def->ID == itemid)
		{
			num -= maxStack - grid.getNum();
		}
	}

	for (size_t j = 0; j < shortcutContainer->m_Grids.size(); j++)
	{
		BackPackGrid grid = shortcutContainer->m_Grids[j];
		if (nullptr == grid.def || grid.def->ID == 0)
		{
			num -= maxStack;
		}
		else if (grid.def->ID == itemid)
		{
			num -= maxStack - grid.getNum();
		}
	}

	return maxNum - num;
}

int BackPack::getShorCutEmptyGridNum()
{
	PackContainer *shortcutContainer = (PackContainer*)getContainer(getShortcutStartIndex());
	if (!shortcutContainer) { return 0; }

	int iEmptyGrid = 0;
	for (int i = 0; i < (int)shortcutContainer->m_Grids.size(); i++) {
		BackPackGrid& grid = shortcutContainer->m_Grids[i];
		if (grid.isEmpty()) {
			iEmptyGrid++;
		}
	}

	return iEmptyGrid;
}
/**********************************************************************************
触发器事件:容器(箱子)中有道具被放入或取出
参数:	isPutIn:true:放入 false:取出
		girdindex:被操作的容器的格子的索引
		blockpos: 容器位置
**********************************************************************************/
void BackPack::ItemChangeForTrigger(bool isPutIn, int girdindex, int itemid, int itemnum)
{
	if (mPlayer && mPlayer->getWorld() && g_WorldMgr && (g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isOriginAdventureMode()))
	{
		WCoord blockpos;
		BaseContainer* container = getContainer(girdindex);

		if (container){
			WorldContainer* storage = dynamic_cast<WorldContainer*>(container);
			if(storage)
				blockpos = storage->m_BlockPos;
		}

		//BackPackGrid* grid = index2Grid(girdindex);
		World* pworld = mPlayer->getWorld();
		int blockid = pworld->getBlockID(blockpos);

		ItemChangeForTrigger(isPutIn, blockpos, blockid, itemid, itemnum);
	}
}

void BackPack::ItemChangeForTrigger(bool isPutIn, WCoord& blockpos, int blockid, int itemid, int itemnum)
{
	// 观察者事件接口
	ObserverEvent obevent;
	obevent.SetData_Position((float)blockpos.x, (float)blockpos.y, (float)blockpos.z);
	obevent.SetData_Block(blockid);
	obevent.SetData_Item(itemid, itemnum);

	if (isPutIn)
		ObserverEventManager::getSingleton().OnTriggerEvent("Backpack.ItemPutIn", &obevent);
	else
		ObserverEventManager::getSingleton().OnTriggerEvent("Backpack.ItemTakeOut", &obevent);
}

std::string BackPack::getSpecialItemName(int itemId, const char* userdata_str)
{
	std::string result; //可返回临时string值

	if (userdata_str == NULL) return result;
	if (!(itemId == 13439 || itemId == 13440 || itemId == 13441)) return result;

	if (userdata_str != NULL)
	{
		jsonxx::Object jsonObj;
		if (jsonObj.parse(userdata_str))
		{
			if (jsonObj.has<jsonxx::String>("Base_Name"))
			{
				ItemDef *itemDef = GetDefManagerProxy()->getItemDef(itemId);
				string basename = jsonObj.get<jsonxx::String>("Base_Name");
				if (!basename.empty() && itemDef != NULL)
				{
					std::string tmpName = itemDef->Name.c_str();
					size_t pos = tmpName.find("-");
					if (pos != string::npos)
					{
						string preStr = tmpName.substr(0, pos);
						result = preStr + "-" + basename;
					}
				}
			}
		}
	}
	return result;
}
bool BackPack::isBackpackIndex(int toindex)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isUGCEditMode())
	{
		return SHORTCUT_SINGLE_INDEX_EDIT == toindex;
	}

	if (GetWorldManagerPtr() && (GetWorldManagerPtr()->isUGCEditBuildMode()))
	{
		return (toindex >= SHORTCUT_START_INDEX_EDIT && toindex < SHORTCUT_START_INDEX_EDIT + 1000);
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		return (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX) || (toindex >= SHORTCUTEX_START_INDEX && toindex < SHORTCUTEX_START_INDEX + 1000);
	}

	return (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX + 1000);
}

bool BackPack::isBackpackIndexExt(int toindex)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isUGCEditMode())
	{
		return SHORTCUT_SINGLE_INDEX_EDIT == toindex;
	}

	if (GetWorldManagerPtr() && (GetWorldManagerPtr()->isUGCEditBuildMode()))
	{
		return (toindex >= SHORTCUT_START_INDEX_EDIT && toindex < SHORTCUT_START_INDEX_EDIT + 1000);
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		return (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX) || (toindex >= SHORTCUTEX_START_INDEX && toindex < SHORTCUTEX_START_INDEX + 1000);
	}

	return (toindex >= BACKPACK_START_INDEX && toindex < SHORTCUT_START_INDEX + 1000) || (toindex >= EQUIP_START_INDEX && toindex <= EQUIP_START_INDEX + 1000);
}

int BackPack::numberOfItemInShortcut(int itemid)
{
	if (itemid <= 0) return 0;
	auto def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == nullptr) return 0;
	PackContainer *shortcutContainer = getPack(getShortcutStartIndex());
	if (shortcutContainer == nullptr) return 0;

	int num = 0;
	for (size_t j = 0; j < shortcutContainer->m_Grids.size(); j++)
	{
		BackPackGrid grid = shortcutContainer->m_Grids[j];
		if (grid.def && grid.def->ID == itemid)
		{
			num += grid.getNum();
		}
	}

	return num;

}

inline bool BackPack::validServerBackPackLog(){
#ifdef IWORLD_SERVER_BUILD
	if (GetICloudProxyPtr()->IsBackLogValid() && mPlayer)
		return true;
#endif
	return false;
}