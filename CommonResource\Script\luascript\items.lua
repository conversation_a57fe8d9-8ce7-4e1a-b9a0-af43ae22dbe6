
--胡杨树叶变色
function getTreeColorChangeInterval()
	return 20 -- 秒数
end
--用于禁止危险物品的房间模式
g_DangerItemsForBan = {
	-- 834, 1000, 11052, 11055, 717, 12280, 12284, 12823, 12824, 12825, 12826, 12285, 15506, 15519, 1062,
	-- 1063, 1064, 12296, 12297, 12298, 15007, 15008, 15528, 1080, 10500, 10501,930,1066,132,
	-- 500, 835, 1757, 11551, 11552, 11553,11560,11561,11562,15050,15051,15052,15053,15054,15055,15056,15057,
	-- 15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15508,15520,
	-- 5,6,11638,15505,15512,15513,15514,15515,15516,15517,15518,15519,15509,15510,15511,
	-- -- 20230201新增
	-- 13502,13503,13504,13510,13511,13512,13513,13514,13515,13516,13878,15507,906,11654,11554,11555,11556,
	-- 11557,11558,11559,11563,11564,11565,11566,11567,11568,
	-- --ugc蓝图生成器，暂时屏蔽
	-- 10119,	
	-- --20240121虚空之夜版本，新增一个虚空大果实炸弹
	-- 200389,
};

-- 祭司ID列表
local FlamenIds = {
	3873, -- 毒素祭司
	3874, -- 治疗祭司
	3875, -- 雷电祭司
	3876, -- 火焰祭司
	3877, -- 祭司
}

-- 虚空狐狸
local VoidWildFoxIds = {
	3248,	--虚空狐狸  
	3250,	--虚空狐狸（二尾） 
	3252,	--虚空狐狸（三尾）
}

local function GetRandomVoidWildFoxId()
--虚空狐狸蛋 随机生成 [3248,虚空狐狸  3250,虚空狐狸（二尾） 3252,虚空狐狸（三尾）]
	local rand= math.random(1, 100)
	local indx = #VoidWildFoxIds
	if rand <= 60 then
		indx = 1
	elseif rand <= 90 then
		indx = 2
	end
	return VoidWildFoxIds[indx]
end

function IsStillFluid(id)
	if id == BLOCK_STILL_WATER or id == BLOCK_INK_STILL_WATER then
		return true
	end

	return false
end

function IsFlowFluid(id)
	if id == BLOCK_FLOW_WATER or id == BLOCK_INK_FLOW_WATER then
		return true
	end

	return false
end

function IsFluid(id)
	if IsStillFluid(id) or IsFlowFluid(id) then
		return true
	end

	return false
end

function ReplaceGridItem(player, index, newitem)
	local bp = player:getBackPack();

	if bp:getGridNum(index) <= 1 then
		-- bp:replaceItem(index, newitem, 1, -1)
		bp:replaceItemByNum(index, newitem, 1)
	else
		bp:removeItem(index, 1)
		if bp:addItem(newitem, 1) < 1 then
			player:throwItem(newitem, 1)	
		end
	end
end

function IsFirstSetPlayResetBtnMp(val,hasrole)
	if val == 0 and not hasrole then
		return true;
	end 
	return false;
end

--怪物变成宠物蛋的判断
g_MonsterToEgg = {[3400] = 13400,[3401] = 13401,[3402] = 13402,[3403] = 13403,[3409] = 13409,[3421] = 13421,[3423] = 13423,[3424] = 13424,[3419] = 13419,[3418] = 13418,[3881] = 13881,[3883] = 13883,[3885] = 13885,[3886] = 13886,[3887] = 13887,[3888] = 13888,[3889] = 13889,[3890] = 13890,[3891] = 13426,[3410] = 13427,[3405] = 13428,[3406] = 13429,[3882] = 13430,[3884] = 13431,[3408] = 13432,[3871] = 13433,[3426] = 13434,[3422] = 13435,[3124] = 13436,[3125] = 13437,[3126] = 13438,[3200] = 13439,[3201] = 13440,[3202] = 13441,[3415] = 13442,[3896] = 13430,[3870] = 13870,[3895] = 13895,[3910] = 13910};
--生物蛋对应的怪物

g_EggToMonster = (function(tmap)
	local result = {}
	for k, v in pairs(tmap) do result[v] = k; end
	return result
end)(g_MonsterToEgg)

--野人村民生成的生物蛋 名字显示需特殊处理
g_VillageEggs = {[13439]=true,[13440]=true,[13441]=true}

function MonsterToEgg(monsterID)
	local egg = g_MonsterToEgg[monsterID];
    if egg ~= nil then
	   return egg;
	else
	   return 0;
	end
end

function PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(x,y,z,blockid, player) then
		return NeighborBlock(x,y,z,dir)
	end
	return x,y,z
end

--使用空铁桶
--铁桶已经没有没长按收集 点击就收集岩浆
function BucketEmpty_OnUse(player, world, x,y,z,dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	local newitem = 0
	if world:getBlockData(x,y,z) == 0 then
		if id == BLOCK_STILL_LAVA or id == BLOCK_FLOW_LAVA then
			newitem = ITEM_TITANIUM_BUCKET_LAVA
			world:setBlockAll(x,y,z, 0, 0)
		end
	end
	if newitem > 0 then
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
	 	if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitem);
	 	end
		 return true, 0;
	end
	--local id = world:getBlockID(x,y,z)
	--local ret = 1;
	-- newitem = 0
	-- if world:getBlockData(x,y,z) == 0 then
	-- 	if id==BLOCK_STILL_WATER or id==BLOCK_FLOW_WATER then newitem = ITEM_BUCKET_WATER
	-- 	elseif id==BLOCK_STILL_HONEY or id==BLOCK_FLOW_HONEY then newitem = ITEM_BUCKET_HONEY
	-- 	elseif id==BLOCK_HIVE_FULL then newitem = ITEM_BUCKET_HONEY end
	-- 	-- 此处屏蔽铁桶收集岩浆功能， code-by： fukaijian
	-- 	--elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = ITEM_BUCKET_LAVA end
	-- end

	-- if newitem > 0 then
	-- 	index = player:getCurShortcut() + player:getShortcutStartIndex()
	-- 	if not WorldMgr:isGodMode() then
	-- 		ReplaceGridItem(player, index, newitem);
	-- 	end
	-- 	if id==BLOCK_HIVE_FULL then
	-- 		world:setBlockAll(x,y,z, BLOCK_HIVE_EMPTY, 0)
	-- 	else
	-- 		world:setBlockAll(x,y,z, 0, 0)
	-- 	end
	-- 	ret = 0;
	-- end
	--return true, ret;
	return false, 1
end

--改为进度条完成后使用空铁桶
function BucketEmpty_OnUse_ByProgressFinish(player, world, x,y,z,dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	local newitem = 0
	if world:getBlockData(x,y,z) == 0 then
		if IsFluid(id) then newitem = ITEM_BUCKET_WATER
		elseif id==BLOCK_STILL_HONEY or id==BLOCK_FLOW_HONEY then newitem = ITEM_BUCKET_HONEY
		elseif id==BLOCK_HIVE_FULL then newitem = ITEM_BUCKET_HONEY end
		-- 此处屏蔽铁桶收集岩浆功能， code-by： fukaijian
		--elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = ITEM_BUCKET_LAVA end
	end

	if newitem > 0 then
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitem);
		end
		if id==BLOCK_HIVE_FULL then
			world:setBlockAll(x,y,z, BLOCK_HIVE_EMPTY, 0)
		else
			world:setBlockAll(x,y,z, 0, 0)
		end
		ret = 0;
	end
	return true, ret;
end

--新增进度条完成后使用空钛桶
function TitaniumBucketEmpty_OnUse_ByProgressFinish(player, world, x,y,z,dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	local newitem = 0
	if world:getBlockData(x,y,z) == 0 then
		-- if id==BLOCK_STILL_WATER or id==BLOCK_FLOW_WATER then newitem = ITEM_TITANIUM_BUCKET_WATER
		-- elseif id==BLOCK_STILL_HONEY or id==BLOCK_FLOW_HONEY then newitem = ITEM_TITANIUM_BUCKET_HONEY
		-- elseif id==BLOCK_HIVE_FULL then newitem = ITEM_TITANIUM_BUCKET_HONEY
		-- elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = ITEM_TITANIUM_BUCKET_LAVA end
		if id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = ITEM_TITANIUM_BUCKET_LAVA end
	end

	if newitem > 0 then
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitem);
		end
		if id==BLOCK_HIVE_FULL then
			world:setBlockAll(x,y,z, BLOCK_HIVE_EMPTY, 0)
		else
			world:setBlockAll(x,y,z, 0, 0)
		end
		ret = 0;
	end
	return true, ret;
end

function Rice_OnUse(user, world, x, y, z, dir)
	local ret = 1;
	if IsStillFluid(world:getBlockID(x,y,z)) and world:getBlockID(x,y+1,z)==0 then
		local id = world:getBlockID(x,y-1,z)
		if id==BLOCK_DIRT or id==BLOCK_GRASS or id==BLOCK_FARMLAND or id==BLOCK_SAND or id==BLOCK_REDSOIL  or id==BLOCK_SOLIDSAND then
			world:setBlockAll(x,y+1,z, 234, 0)
			if user then
				user:shortcutItemUsed()
			end
			ret = 0;
		end
		return true, ret;
	end
	return false, ret;
end

function Reed_OnUse(user, world, x, y, z, dir)
	local ret = 1;
	local id = world:getBlockID(x,y,z)

    local idAbove = world:getBlockID(x, y + 1, z)
    local idTwoAbove = world:getBlockID(x, y + 2, z)
    if (IsStillFluid(idAbove) and not IsStillFluid(idTwoAbove)) or idAbove == 0 then
		if id==BLOCK_DIRT or id==BLOCK_GRASS or id==BLOCK_FARMLAND or id==BLOCK_SAND or id==BLOCK_REDSOIL  or id==BLOCK_SOLIDSAND then
			world:setBlockAll(x,y+2,z, 150062, 0)
			if user then
				user:shortcutItemUsed()
			end
			ret = 0;
			return true, ret;
		end
	end
	return false, ret;
end

function BucketEmpty_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local x = world:coordDivBlock(posX);
	local y = world:coordDivBlock(posY);
	local z = world:coordDivBlock(posZ);
	local id = world:getBlockID(x,y,z)

	local newitem = 0
	if world:getBlockData(x,y,z) == 0 then
		if IsFluid(id) then newitem = ITEM_BUCKET_WATER
		elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = ITEM_BUCKET_LAVA end
	end

	if newitem > 0 then
		world:setBlockAll(x,y,z, 0, 0)
		if grid:getNum() == 1 then
			grid:setItem(newitem, 1);
			return -1;
		else
			local itemid = grid:getItemID();
			GetWorldActorMgr(world):throwItemMotion(posX, posY, posZ, itemid, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow);
			return 1;
		end
	end

	return 0
end

--使用空木桶
function WoodenBucketEmpty_OnUse(player, world, x,y,z,dir)
	-- local id = world:getBlockID(x,y,z)
	-- local ret = 1;
	-- newitem = 0
	-- if world:getBlockData(x,y,z) == 0 then
	-- 	if id==BLOCK_STILL_WATER or id==BLOCK_FLOW_WATER then newitem = ITEM_WOODEN_BUCKET_WATER
	-- 	elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = -1 end
	-- end

	-- if newitem == 0 then
	-- 	return false, ret;
	-- elseif newitem > 0 then
	-- 	index = player:getCurShortcut() + player:getShortcutStartIndex()
	-- 	if not WorldMgr:isGodMode() then
	-- 		ReplaceGridItem(player, index, newitem);
	-- 	end
	-- 	world:setBlockAll(x,y,z, 0, 0)
	-- 	ret = 0;
	-- else
	-- 	if not WorldMgr:isGodMode() then
	-- 		local bp = player:getBackPack();
	-- 		index = player:getCurShortcut() + player:getShortcutStartIndex()
	-- 		--烧掉木桶
	-- 		bp:removeItem(index, 1)
	-- 		player:consumeItemOnTrigger(ITEM_WOODEN_BUCKET, 1);
	-- 		if GetClientInfo():isPureServer() then
	-- 			local smoke = getglobal("ItemUseEffectBurnedWoodenBucket")
	-- 			smoke:SetUVAnimation(40, false);
	-- 			smoke:Show()
	-- 		end
	-- 	end
	-- end
	-- return true, ret;
	return false, 1
end
--小玻璃瓶打剧毒液进度完成 ，水，蜂蜜进度完成
function SmallGlassBottle_OnUse_ByProgressFinish(player, world, x,y,z,dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	local newitem = 0
	if world:getBlockData(x,y,z) == 3 then
		if id==BLOCK_STILL_VENOM or id==BLOCK_FLOW_VENOM 
			then newitem = ITEM_VENOM_BOTTLE
		end
	elseif IsFluid(id) then
		if world:getBlockData(x,y,z) > 0 then --非源头
				return false, 1; 
		else
			newitem = ITEM_WOODEN_BUCKET_WATER
		end
	elseif id == BLOCK_STILL_HONEY or id== BLOCK_FLOW_HONEY  then
		if world:getBlockData(x,y,z) > 0 then --非源头
				return false, 1; 
		else
			newitem = 12511--小瓶蜂蜜
		end
	end
	
	if newitem == 0 then
		return false, ret;
	elseif newitem > 0 then
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitem);
		end
		world:setBlockAll(x,y,z, 0, 0)
		ret = 0;
	end
	return true, ret;
end
--改为进度条完成后使用空木桶
function WoodenBucketEmpty_OnUse_ByProgressFinish(player, world, x,y,z,dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	local newitem = 0
	if world:getBlockData(x,y,z) == 0 then
		if IsFluid(id) then newitem = ITEM_WOODEN_BUCKET_WATER
		elseif id==BLOCK_STILL_LAVA or id==BLOCK_FLOW_LAVA then newitem = -1 end
	end

	if newitem == 0 then
		return false, ret;
	elseif newitem > 0 then
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitem);
		end
		world:setBlockAll(x,y,z, 0, 0)
		ret = 0;
	else
		if not WorldMgr:isGodMode() then
			local bp = player:getBackPack();
			local index = player:getCurShortcut() + player:getShortcutStartIndex()
			--烧掉木桶
			bp:removeItem(index, 1)
			player:consumeItemOnTrigger(ITEM_WOODEN_BUCKET, 1);
			if GetClientInfo():isPureServer() then
				local smoke = getglobal("ItemUseEffectBurnedWoodenBucket")
				smoke:SetUVAnimation(40, false);
				smoke:Show()
			end
		end
	end
	return true, ret;
end

--使用装水木通
function WoodenBucketWater_OnUse(player, world, x, y, z, dir)
	--code-by:lizb 萌眼星可放置水方块但不能流动
	local currMapId = world:getCurMapID()
	if currMapId > 0 and currMapId ~= 2 then
		player:postInfoTips(5)
		return false, 1;
	end
	local blockid = world:getBlockID(x,y,z)
	if blockid==BLOCK_COLOR_PALETTE_STAND or blockid==BLOCK_COLOR_PALETTE_HANG then
		return false, 1;
	end
	return WoodenBucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_WATER)
end

function WoodenBucket_OnUse(player, world, x, y, z, dir, fluid_id)
	if g_CanUseBucket then
		local x1,y1,z1 = NeighborBlock(x,y,z,dir)
		local solid = world:isBlockSolid(x1,y1,z1)
		local liquid = world:isBlockLiquid(x1,y1,z1)

		if world:getBlockID(x1,y1,z1)==0 or not solid then
			if not solid and not liquid then
				world:destroyBlock(x1, y1, z1, true)
			end

			if world.GetBlockTemperatureAndLevel then
				local pos = WCoord();
				pos.x = x1
				pos.y = y1
				pos.z = z1
				local temp = 0
				local level = 4
				temp, level = world:GetBlockTemperatureAndLevel(world, pos, temp, level)
				pos.x = x1 * BLOCK_SIZE + BLOCK_SIZE / 2
				pos.y = y1 * BLOCK_SIZE
				pos.z = z1 * BLOCK_SIZE + BLOCK_SIZE / 2
				if level == 1 then
					fluid_id = BLOCK_ICE
					world:getEffectMgr():playSound(pos, "misc.freeze", 1.0, 1.0)
				elseif level == 7 then
					fluid_id = 0
					world:getEffectMgr():playParticleEffectAsync("particles/evaporate.ent", pos, 100, BlockMaterialUtil:GenRandomFloat()*360.0, 0);
				end
			end
			world:setBlockAll(x1,y1,z1, fluid_id, 0)

			local index = player:getCurShortcut() + player:getShortcutStartIndex()
			if not WorldMgr:isGodMode() then
				ReplaceGridItem(player, index, ITEM_SMALL_GLASS_BOTTLE);
			end
			return true, 0;
		end
		return false, 1;
	else
		return false, 1;
	end 
end

--使用水桶
function Bucket_AutoUse(world, x, y, z, dir, grid)
	local itemid = grid:getItemID();

	local fluid_id = BLOCK_FLOW_LAVA
	if itemid==ITEM_BUCKET_WATER then fluid_id = BLOCK_FLOW_WATER end
	
	if itemid==ITEM_BUCKET_HONEY then fluid_id = BLOCK_FLOW_HONEY end

	local solid = world:isBlockSolid(x,y,z)
	local liquid = world:isBlockLiquid(x,y,z)

	if world:getBlockID(x,y,z)==0 or not solid then
		if not solid and not liquid then
			world:destroyBlock(x, y, z, true)
		end
		world:setBlockAll(x,y,z, fluid_id, 0)
		grid:setItem(ITEM_BUCKET, 1);
		return -1
	end
	return 0
end

g_CanUseBucket = true

function EnableUseBucket()
	threadpool:delay(1,function ()
   		g_CanUseBucket = true
   	end)
end

function DisableUseBucket()
	g_CanUseBucket = false;
end

function Bucket_OnUse(player, world, x, y, z, dir, fluid_id , bucketType)
	if g_CanUseBucket then
		x1,y1,z1 = NeighborBlock(x,y,z,dir)
		solid = world:isBlockSolid(x1,y1,z1)
		liquid = world:isBlockLiquid(x1,y1,z1)

		if world:getBlockID(x1,y1,z1)==0 or not solid then
			if not solid and not liquid then
				world:destroyBlock(x1, y1, z1, true)
			end

			world:setBlockAll(x1,y1,z1, fluid_id, 0)

			index = player:getCurShortcut() + player:getShortcutStartIndex()
			if not WorldMgr:isGodMode() then
				ReplaceGridItem(player, index, bucketType or ITEM_BUCKET);
			end
			return true, 0;
		end
	end 
	return false, 1;
end

function Bottle_OnUse(player, world, x, y, z, dir)
	local blockid = world:getBlockID(x,y,z)
	local newitemid = 0
	if blockid == BLOCK_PLANTSPACE_CLOUD then
		newitemid = 11095
	elseif IsFluid(blockid) then
		newitemid = ITEM_WOODEN_BUCKET_WATER
	elseif blockid == BLOCK_STILL_HONEY or blockid == BLOCK_FLOW_HONEY then
		newitemid = 12511
	elseif blockid == BLOCK_HIVE_FULL then --满的蜂巢
        world:setBlockAll(x,y,z, BLOCK_HIVE_EMPTY, 0) --空的蜂巢
        newitemid = 12511 --小瓶蜂蜜
	end

	if newitemid > 0 then
		if blockid~=BLOCK_HIVE_FULL then
			if(newitemid == ITEM_WOODEN_BUCKET_WATER or newitemid == 12511)then
	            if(world:getBlockData(x,y,z) == 0)then
	                world:setBlockAll(x,y,z, 0, 0)
				else
					return false, 1;
				end
	        else
	            world:setBlockAll(x,y,z, 0, 0)
	        end
		end

		index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, newitemid);
		end
		return true, 0;
	end
	if blockid==BLOCK_COLOR_PALETTE_STAND or blockid==BLOCK_COLOR_PALETTE_HANG then
		return false, 1;
	end

	local newitem = 0
    if blockid==BLOCK_STILL_VENOM or blockid ==BLOCK_FLOW_VENOM then 
	    if world:getBlockData(x,y,z) == 3 then
			newitem = ITEM_VENOM_BOTTLE
		end
	elseif IsFluid(blockid) then
		if world:getBlockData(x,y,z) > 0 then --非源头
				--return false, 1; 
		else
			newitem = ITEM_WOODEN_BUCKET_WATER
		end
	elseif blockid == BLOCK_STILL_HONEY or blockid== BLOCK_FLOW_HONEY  then
		if world:getBlockData(x,y,z) > 0 then --非源头
				--return false, 1; 
		else
			newitem = 12511--小瓶蜂蜜
		end
	end
	if newitem == 0 and world:getBlockID(x,y + 1, z)==0 then
		world:setBlockAll(x,y + 1, z, 28, 0);
		player:shortcutItemUsed();	
		return true, 0;
	end
	return false, 1;
end

function CloudBottle_OnUse(player, world, x, y, z, dir)
	x1,y1,z1 = NeighborBlock(x,y,z,dir)
	if world:isBlockAir(x1,y1,z1) then
		world:setBlockAll(x1,y1,z1, BLOCK_PLANTSPACE_CLOUD, 0)

		index = player:getCurShortcut() + player:getShortcutStartIndex()
		if not WorldMgr:isGodMode() then
			ReplaceGridItem(player, index, ITEM_SMALL_GLASS_BOTTLE);
		end
		return true
	end
	return false
end

--瓶装蜂蜜
function BottledHoney_OnUse(player, world, x, y, z, dir)
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_HONEY, ITEM_SMALL_GLASS_BOTTLE)
end

--使用装水铁通
function BucketWater_OnUse(player, world, x, y, z, dir)
	--code-by:lizb 萌眼星可放置水方块但不能流动
	local currMapId = world:getCurMapID()
	if currMapId > 0 and currMapId ~= 2 then
		player:postInfoTips(5)
		return false, 1;
	end
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_WATER)
end

--使用岩浆铁桶
function BucketLava_OnUse(player, world, x, y, z, dir)
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_LAVA)
end

--使用蜂蜜铁桶
function BucketHoney_OnUse(player, world, x, y, z, dir)
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_HONEY)
end

--使用装水钛通
function TitaniumBucketWater_OnUse(player, world, x, y, z, dir)
	--code-by:lizb 萌眼星可放置水方块但不能流动
	local currMapId = world:getCurMapID()
	if currMapId > 0 and currMapId ~= 2 then
		player:postInfoTips(5)
		return false, 1;
	end
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_WATER,ITEM_TITANIUM_BUCKET)
end

--使用岩浆钛桶
function TitaniumBucketLava_OnUse(player, world, x, y, z, dir)
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_LAVA,ITEM_TITANIUM_BUCKET)
end

--使用蜂蜜钛桶
function TitaniumBucketHoney_OnUse(player, world, x, y, z, dir)
	return Bucket_OnUse(player, world, x,y,z,dir, BLOCK_FLOW_HONEY,ITEM_TITANIUM_BUCKET)
end

--使用打火石
function FlintSteel_OnUse(player, world, x, y, z, dir)
	-- player:shortcutItemUsed();
	-- x1,y1,z1 = NeighborBlock(x,y,z,dir)

	-- if CanBlockBurnWithOxygen(world,x1,y1,z1) then
	-- 	world:setBlockAll(x1,y1,z1, 500, 0)
	-- 	return true, 0;
	-- end
	--return false, 1;
	return false, 1
end

--改为进度条完成后使用打火石
function FlintSteel_OnUse_ByProgressFinish(player, world, x, y, z, dir)
	player:shortcutItemUsed();
	local x1,y1,z1 = NeighborBlock(x,y,z,dir)

	if CanBlockBurnWithOxygen(world,x1,y1,z1) then
		world:setBlockAll(x1,y1,z1, 500, 0)
		return true, 0;
	end
	return false, 1;
end

--使用睡莲
function LilyPad_OnUse(player, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	if IsStillFluid(id) and world:getBlockID(x,y +1 ,z) == 0 then
		world:setBlockAll(x,y+1,z,  player:getCurToolID(), DIR_NEG_Y)
		player:shortcutItemUsed()
		world:setBlockData(x,y+1,z, math.random(0,7))
		ret = 0;
	end
--还需要修改朝向
	return true, ret;
end

--使用漂浮的花瓣
function WaterLily_OnUse(player, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local ret = 1;
	
	if IsStillFluid(id) and world:getBlockID(x,y +1 ,z) == 0 then
		world:setBlockAll(x,y+1,z,  player:getCurToolID(),  math.random(0, 2) + math.random(0, 1) * 4)
		player:shortcutItemUsed()
		ret = 0;
	end
--还需要修改朝向
	return true, ret;
end
--使用荷叶之类的方块，需要随机朝向，但是固定贴图
function WaterPad_OnUse(player, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local ret = 16
	
	if IsStillFluid(id) and world:getBlockID(x,y +1 ,z) == 0 then
		world:setBlockAll(x,y+1,z,  player:getCurToolID(),  math.random(0,5))
		player:shortcutItemUsed()
		ret = 0;
	end
--还需要修改朝向
	return true, ret;
end

function IsTreesOrFlowersBlock(id)
	return (id >= 212 and id <= 217) or (id >= 300 and id <= 313) 
end


function IsCropsBlock(id)
	return id==229 or id==236 or id==241
end

function GetHoeRange(itemid)
	local toolDef = DefMgr:getToolDef(itemid);

	if toolDef.Level == 3 or toolDef.Level == 5 then return 1
	elseif toolDef.Level == 4 then return 2
	else return 0 end
end

function HarvestWithHoe(user, world, cx, cy, cz, range)
	local count = 0
	for x=cx-range, cx+range do
		for z=cz-range, cz+range do
			local id = world:getBlockID(x, cy, z)
			if IsCropsBlock(id) and world:getBlockData(x,cy,z)>=7 then --成熟的农作物
				world:destroyBlock(x, cy, z, true)
				-- 收获同时更新任务系统
				user:updateTaskSysProcess(TASKSYS_DESTORY_BLOCK, id)
				count = count + 1
				if count == 1 then user:playBlockDigSound(id, x, cy, z) end
			end
		end
	end
	return count
end

function SowingWithHoe(user, world, cx, cy, cz, range)
	local count = 0

	for x=cx-range, cx+range do
		for z=cz-range, cz+range do
			local id = world:getBlockID(x, cy, z)
			if id==BLOCK_FARMLAND and world:canPlaceBlockAt(x,cy+1,z, 229, user) then --空的耕地
				if user:removeItemInNormalPack(11400, 1) == 0 then return count end
				world:setBlockAll(x,cy+1,z, 229, 0)
				count = count + 1
				if count == 1 then user:playBlockPlaceSound(229, x, cy+1, z) end
			end
		end
	end
	return count
end



function HarvestWithScoop(user, world, cx, cy, cz, range)
	local count = 0
	for x=cx-range, cx+range do
		for z=cz-range, cz+range do
			local id = world:getBlockID(x, cy, z)
			if IsTreesOrFlowersBlock(id) and world:getBlockData(x,cy,z)>=7 then --成熟的农作物
				world:destroyBlock(x, cy, z, true)
				count = count + 1
				if count == 1 then user:playBlockDigSound(id, x, cy, z) end
			end
		end
	end
	return count
end

function SowingWithScoop(user, world, cx, cy, cz, range)
	local count = 0

	return count
end


--使用锄头
function Hoe_OnUse(user, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local curtool = user:getCurToolID();

	local ret = 1;
	local toolDef = DefMgr:getToolDef(curtool);
	if toolDef == nil then return false, ret end
	if id==BLOCK_DIRT or id==BLOCK_GRASS then
		-- 增加开垦进度条 code-by:liwentao
		-- if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
		-- 	world:setBlockAll(x, y, z, BLOCK_FARMLAND, 0)
		-- 	user:playBlockPlaceSound(BLOCK_FARMLAND, x, y, z)
		-- 	user:addCurToolDuration(-1)
		-- 	ret = 0;
		-- end
		-- return true, ret;
	elseif id == BLOCK_FARMLAND then
		if toolDef.Level == 5 then	--钻石镐类型
			if SowingWithHoe(user, world, x, y, z, GetHoeRange(curtool)) > 0 then 
				user:addCurToolDuration(-1)
			end
		end
		return true, ret;
	elseif id==BLOCK_GRASS_WOOD_GRAY then----
		-- 增加开垦进度条 code-by:liwentao
        -- if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
        --     world:setBlockAll(x, y, z, BLOCK_GRASS_WOOD_GRAY_FARMLAND, 3)
        --     user:playBlockPlaceSound(BLOCK_GRASS_WOOD_GRAY_FARMLAND, x, y, z)
		-- 	user:addCurToolDuration(-1)
		-- 	ret = 0;
        -- end
        -- return true,ret;
	elseif IsCropsBlock(id) then
		if HarvestWithHoe(user, world, x, y, z, GetHoeRange(curtool)) > 0 then
			user:addCurToolDuration(-1)
		end
		return true,ret;
	end

	return false, ret;
end

function Hoe_OnUse_ByProgressFinish(user, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local curtool = user:getCurToolID();

	local ret = 1;
	local toolDef = DefMgr:getToolDef(curtool);
	if toolDef == nil then return false, ret end
	if id==BLOCK_DIRT or id==BLOCK_GRASS then
		-- 增加开垦进度条 code-by:liwentao
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_FARMLAND, 0)
			user:playBlockPlaceSound(BLOCK_FARMLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true, ret;
	-- elseif id == BLOCK_FARMLAND then
	-- 	if toolDef.Level == 5 then	--钻石镐类型
	-- 		if SowingWithHoe(user, world, x, y, z, GetHoeRange(curtool)) > 0 then 
	-- 			user:addCurToolDuration(-1)
	-- 		end
	-- 	end
	-- 	return true, ret;
	elseif id==BLOCK_GRASS_WOOD_GRAY then
		-- 增加开垦进度条 code-by:liwentao
        if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
            world:setBlockAll(x, y, z, BLOCK_GRASS_WOOD_GRAY_FARMLAND, 3)
            user:playBlockPlaceSound(BLOCK_GRASS_WOOD_GRAY_FARMLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
        end
        return true,ret;
	-- elseif IsCropsBlock(id) then
	-- 	if HarvestWithHoe(user, world, x, y, z, GetHoeRange(curtool)) > 0 then
	-- 		user:addCurToolDuration(-1)
	-- 	end
	-- 	return true,ret;
	elseif id==BLOCK_REDSOIL then
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_FARMLAND_RED, 0)
			user:playBlockPlaceSound(BLOCK_FARMLAND_RED, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true,ret;
	end

	return false, ret;
end

--使用铲子
function Scoop_OnUse(user, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local curtool = user:getCurToolID();
	local toolDef = DefMgr:getToolDef(curtool);
	local ret = 1;
	if toolDef == nil then return false, ret end
	if id==BLOCK_DIRT or id==BLOCK_GRASS then
		-- 增加开垦进度条 code-by:liwentao
		-- if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
		-- 	world:setBlockAll(x, y, z, BLOCK_BURYLAND, 0)
		-- 	user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
		-- 	user:addCurToolDuration(-1)
		-- 	ret = 0;
		-- end
		-- return true, ret;
	elseif id==BLOCK_PLANTSPACE_SOIL then
		-- 增加开垦进度条 code-by:liwentao
		-- if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
		-- 	world:setBlockAll(x, y, z, BLOCK_PLANTSPACE_BURYLAND, 0)
		-- 	user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
		-- 	user:addCurToolDuration(-1)
		-- 	ret = 0;
		-- end
		-- return true, ret;
	elseif id == BLOCK_BURYLAND then
		if toolDef.Level == 5 then	--钻石铲子类型
			if SowingWithScoop(user, world, x, y, z, GetHoeRange(curtool)) > 0 then 
				user:addCurToolDuration(-1)
			end
		end
		return true, ret;
	elseif IsTreesOrFlowersBlock(id) then
		if HarvestWithScoop(user, world, x, y, z, GetHoeRange(curtool)) > 0 then
			user:addCurToolDuration(-1)
		end
		return true, ret;
	end

	return false, ret;
end

function Scoop_OnUse_ByProgressFinish(user, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local curtool = user:getCurToolID();
	local toolDef = DefMgr:getToolDef(curtool);
	local ret = 1;
	if toolDef == nil then return false, ret end
	if id==BLOCK_DIRT or id==BLOCK_GRASS then
		-- 增加开垦进度条 code-by:liwentao
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_BURYLAND, 0)
			user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true, ret;
	elseif id==BLOCK_SNOWPANE then --敲碎积雪
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, 0, 0)
			user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true, ret;
	elseif id==BLOCK_DIRT_FREEZE then
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_DIRT_FREEZE_PIT, 0)
			user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true, ret;
	elseif id==BLOCK_PLANTSPACE_SOIL then
		-- 增加开垦进度条 code-by:liwentao
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_PLANTSPACE_BURYLAND, 0)
			user:playBlockPlaceSound(BLOCK_BURYLAND, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true, ret;
	-- elseif id == BLOCK_BURYLAND then
	-- 	if toolDef.Level == 5 then	--钻石铲子类型
	-- 		if SowingWithScoop(user, world, x, y, z, GetHoeRange(curtool)) > 0 then 
	-- 			user:addCurToolDuration(-1)
	-- 		end
	-- 	end
	-- 	return true, ret;
	-- elseif IsTreesOrFlowersBlock(id) then
	-- 	if HarvestWithScoop(user, world, x, y, z, GetHoeRange(curtool)) > 0 then
	-- 		user:addCurToolDuration(-1)
	-- 	end
	-- 	return true, ret;
	elseif id==BLOCK_REDSOIL then
		if dir~=DIR_NEG_Y and world:isBlockAir(x,y+1,z) then
			world:setBlockAll(x, y, z, BLOCK_FARMLAND_PIT, 0)
			user:playBlockPlaceSound(BLOCK_FARMLAND_PIT, x, y, z)
			user:addCurToolDuration(-1)
			ret = 0;
		end
		return true,ret;
	end

	return false, ret;
end

--空花洒的使用
function SprinklerEmpty_OnUse(player, world, x, y, z, dir)
	local id = world:getBlockID(x,y,z)
	local newitem = 0
	
	if world:getBlockData(x, y, z) == 0 then
		if IsFluid(id) then 
			newitem = ITEM_SPRINKLER
		end
	end

	if player.doSomeChangeAfterEmptySprinklerOnUse then
		player:doSomeChangeAfterEmptySprinklerOnUse(newitem)
	end

	-- if newitem > 0 then
	-- 	local index = player:getCurShortcut() + player:getShortcutStartIndex()
	-- 	local oldGrid = player:getBackPack():index2Grid(index)
	-- 	local oldDuration = oldGrid:getDuration()
	-- 	print("SprinklerEmpty_OnUse oldDuration = "..oldDuration)
	-- 	ReplaceGridItem(player, index, newitem);
		
	-- 	if newitem == ITEM_SPRINKLER then
	-- 		local grid = player:getBackPack():index2Grid(index)
	-- 		local leftCount = LuaConstants:get().number_of_sprinklers or 20 --装一次水 可以浇灌20次 code-by:liwentao
	-- 		grid:setUserDataInt(leftCount)
	-- 		if not WorldMgr:isGodMode() then
	-- 			grid:setDuration(oldDuration-1)
	-- 		end
	-- 	end

	-- end

	return false, 0
end

--获取花洒模型的贴图index code-by:liwentao
function GetSprinkleModelTextureIndex(leftCount)
	if not leftCount then
		return 5
	end

	local texIndex = 5
	if leftCount > 15 then
		texIndex = 5
	elseif leftCount > 10 and leftCount <= 15 then
		texIndex = 4
	elseif leftCount > 5 and leftCount <= 10 then
		texIndex = 3
	elseif leftCount > 3 and leftCount <= 5 then
		texIndex = 2
	else
		texIndex = 1
	end

	return texIndex
end

--花洒的使用
function Sprinkler_OnUse(player, world, x, y, z, dir)
	local opX,opY,opZ = x,y,z
	local id = world:getBlockID(opX, opY, opZ)
	if not (id == BLOCK_FARMLAND or id == BLOCK_BURYLAND or id == BLOCK_FARMLAND_PIT or id == BLOCK_FARMLAND_RED) then
		opX, opY, opZ = x, y-1, z --处理种上植物的耕地 or 土坑
		id = world:getBlockID(opX, opY, opZ)
	end
	print("Sprinkler_OnUse",id)
	if id == BLOCK_FARMLAND or id == BLOCK_BURYLAND or id == BLOCK_FARMLAND_PIT or id == BLOCK_FARMLAND_RED then 
		local blockdata = world:getBlockData(opX, opY, opZ)
		if blockdata == 0 then
			world:setBlockData(opX, opY, opZ, 15) --用15来标记 是花洒让耕地变湿润的

			local containerMgr = world:getContainerMgr()
			if containerMgr then
				local container = containerMgr:getContainer(opX, opY, opZ)
				if container then
					tolua.cast(container, "FarmlandContainer")
					if container then
						local curWorldTime = WorldMgr:getWorldTime()
						container:setWetWorldTime(curWorldTime)
						container:setWetBy(1)
					end
				end
			end

			if player.doSomeChangeAfterSprinklerOnUse then
				player:doSomeChangeAfterSprinklerOnUse()
			end
			--[[
			//逻辑移动到c
			local index = player:getCurShortcut() + player:getShortcutStartIndex()
			local grid = player:getBackPack():index2Grid(index)
			local leftCount = grid:getUserDataInt() - 1 
			grid:setUserDataInt(leftCount) --水量减1
			
			if leftCount < 1 then
				local oldDuration = grid:getDuration()
				ReplaceGridItem(player, index, ITEM_SPRINKLER_EMPTY);
				
				local grid = player:getBackPack():index2Grid(index)
				grid:setDuration(oldDuration)
			else
				local texIndex = GetSprinkleModelTextureIndex(leftCount)
				player:updateToolModelTexture(texIndex)--水量变化
			end
			]]

			-- player:playBlockPlaceSound(115, opX, opY, opZ)

			return true, 0;
		end
	end

	return false, 1
end

--使用甘蔗
function Reeds_OnUse(player, world, x, y, z, dir)
	if dir ~= DIR_POS_Y then return end

	if world:canPlaceBlockAt(x,y+1,z, 228, player) then
		world:setBlockAll(x, y+1, z, 228, 0)
		player:playBlockPlaceSound(228, x, y+1, z)
		player:shortcutItemUsed()
		return true
	end
	return false
end

--公用函数, 使用一般的种子
function Seed_OnUse(user, world, x, y, z, dir, plantid)
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, plantid, user) then
		world:setBlockAll(x,y+1,z, plantid, 0)
		if user then
			user:playBlockPlaceSound(plantid, x, y+1, z)
			user:shortcutItemUsed()
		end
		return true, 0;
	end
	return false, 1;
end

--使用小麦种子
function WheatSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 229)
end

--使用胡萝卜
function Carrots_OnUse(user, world, x,y,z,dir)
	return Seed_OnUse(user, world, x,y,z,dir, 236)
end

--使用马铃薯
function Potato_OnUse(user, world, x,y,z,dir)
	return Seed_OnUse(user, world, x,y,z,dir, 241)
end

--使用南瓜种子
function PumpkinSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 231)
end

--使用西瓜种子
function MelonSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 240)
end

-- 使用果树种子
function FruitTreeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 212)
end

-- 使用金合欢树种子
function AcaciaTreeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151006)
end

-- 使用猴面包树种子
function BreadTreeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151007)
end

-- 使用银杏树种子,151201是树木的id
function GinkgoTreeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151201)
end


-- 使用枫香树种子
function LiquidambarSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151202)
end

-- 使用蓝花楹树种子
function JacarandaSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151203)
end

-- 使用秋胡杨树种子
function PopulusNormalSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 151204)
end

-- 使用松树种子
function LarchSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 213)
end

-- 使用乔木种子
function ArborSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 382)
end

-- 使用珍木种子
function GenWoodSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 259)
end

-- 使用桃树种子
function PeachTreeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 256)
end

-- 使用白杨种子
function PoplarSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 214)
end

-- 使用红杉种子
function RedwoodSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 215)
end

-- 使用胡桃种子
function WalNutSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 217)
end

-- 使用楠木种子
function PhoebeSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 216)
end

-- 使用棉花树种子
function CottonSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 469)
end

-- 使用棕色小蘑菇
function MushroomBrown_OnUse(user, world, x, y, z, dir)
	if Seed_OnUse(user, world, x,y,z,dir, 464) then
		world:setBlockData(x,y+1,z,0)
		return true, 0;
	end
	return false, 1;
end

-- 使用红色小蘑菇
function MushroomRed_OnUse(user, world, x, y, z, dir)
	if Seed_OnUse(user, world, x,y,z,dir, 464) then
		world:setBlockData(x,y+1,z,4)
		return true, 0;
	end
	return false, 1;
end

-- 使用洋红毒菇
function MushroomMagenta_OnUse(user, world, x, y, z, dir)
	if Seed_OnUse(user, world, x,y,z,dir, 464) then
		world:setBlockData(x,y+1,z,8)
		return true, 0;
	end
	return false, 1;
end

-- 使用虚空树种子
function VoidTree_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 200409)
end

--使用可可果
function Cocoa_OnUse(player, world, x, y, z, dir)
	if dir==DIR_POS_Y then return false, 1 end
	if world:getBlockID(x,y,z) ~= BLOCK_LEAVE_JUNGLE then return false, 1 end

	x1, y1, z1 = NeighborBlock(x,y,z,dir)
	if world:canPlaceBlockAt(x1,y1,z1, 237, player) then
		newdir = ReverseDirection(dir)
		if newdir==DIR_POS_Y then
		   newdir=DIR_NEG_Y;	
		end
		world:setBlockAll(x1,y1,z1, 237, newdir)
		player:playBlockPlaceSound(237, x1, y1, z1)
		player:shortcutItemUsed()
		return true, 0;
	end
	return false, 1;
end

--使用松果
function Pincone_OnUse(player, world, x, y, z, dir)
	--if dir==DIR_POS_Y then return false, 1 end
	--if world:getBlockID(x,y,z) ~= BLOCK_LEAVE_SPUCE then return false, 1 end

	x1, y1, z1 = NeighborBlock(x,y,z,dir)
	if world:canPlaceBlockAt(x1,y1,z1, 249, player) then
		newdir = ReverseDirection(dir)
		if newdir==DIR_POS_Y then
		   newdir=DIR_NEG_Y;	
		end
		world:setBlockAll(x1,y1,z1, 249, newdir)
		player:playBlockPlaceSound(249, x1, y1, z1)
		player:shortcutItemUsed()
		return true, 0;
	end
	return false, 1;
end

--使用氧气果
function Oxygen_Fruit_OnUse(player, world, x, y, z, dir)
	if dir==DIR_POS_Y then return false, 1 end
	if world:getBlockID(x,y,z) ~= BLOCK_PLANTSPACE_WOODS and world:getBlockID(x,y,z) ~= BLOCK_PLANTSPACE_HASLEAF_WOODS then return false, 1 end

	x1, y1, z1 = NeighborBlock(x,y,z,dir)
	if world:canPlaceBlockAt(x1,y1,z1, 1051, player) then
		newdir = ReverseDirection(dir)
		if newdir==DIR_POS_Y then
		   newdir=DIR_NEG_Y;	
		end
		world:setBlockAll(x1,y1,z1, 1051, newdir)
		player:playBlockPlaceSound(1051, x1, y1, z1)
		player:shortcutItemUsed()
		return true, 0;
	end
	return false, 1;
end

--使用便便
function BonePowder_OnUse(player, world, x,y,z,dir)
	if world:fertilizeBlock(x,y,z, 11311, player:getObjId()) then
		player:shortcutItemUsed()
	end
	return true, 2;

	--[[
	player:shortcutItemUsed()
	id = world:getBlockID(x,y,z)
	if id==226 or id==227 then
		if RollPoint(10) then
			if id == 226 then PlaceBigMushroomBrown(x,y,z, math.random(5,7), 316, 317, 318)
			else PlaceBigMushroom(x,y,z, math.random(5,7), 325, 326, 327) end
		end
	elseif id>=212 and id<=217 then
		bdata = world:getBlockData(x,y,z) + math.random(2,4)
		if bdata >= 7 then
			world:placeTree(x, y, z, id-212+200)
		else
			world:setBlockData(x,y,z, bdata)
		end
	elseif id==228 or id==229 or id==231 or id==236 or id==240 or id==241 or id==242 then
		bdata = world:getBlockData(x,y,z) + math.random(2,4)
		if bdata > 7 then bdata = 7 end
		world:setBlockData(x,y,z, bdata)
	elseif id==237 then --可可果特殊处理
		bdata = world:getBlockData(x,y,z)
		stage = math.floor(bdata/4)
		d = bdata-stage*4
		stage = stage + math.random(0,1)
		if stage > 2 then stage = 2 end

		world:setBlockData(x,y,z, stage*4+d)
	end
	return true]]
end


--使用雪
function Snow_OnUse(player, world, x,y,z,dir)
	local slabid=world:getBlockID(x,y,z)
	local nx, ny, nz=x,y,z
	if  slabid == 115 then
		blockdata = world:getBlockData(x,y,z)
		if blockdata < 3 then
			world:setBlockData(x,y,z, blockdata+1)
			player:playBlockPlaceSound(115, x, y, z)
			player:shortcutItemUsed()
			return true, 0
		end
	end
	nx, ny, nz=NeighborBlock(x,y,z,dir)
	local curid = world:getBlockID(nx,ny,nz)
	if curid == 115 then
		blockdata = world:getBlockData(nx,ny,nz)
		if blockdata < 3 then
			world:setBlockData(nx,ny,nz, blockdata+1)
			player:playBlockPlaceSound(115,nx,ny,nz)
			player:shortcutItemUsed()
			return true, 0
		end
	end
	return false, 1
end

--使用训狗道具
function Sitdog_OnUse(player, world, x,y,z,dir)
	local use = player:getFlagBit(16);
	if use then	
		player:setFlagBit(16, false);
	else 
		player:setFlagBit(16, true);
	end
	if ActorComponentCallModule then
		--ActorComponentCallModule(player,"SoundComponent","stopSoundFollowActor","misc.dogwhistle")
        -- ActorComponentCallModule(player,"SoundComponent","playSoundFollowActor","misc.dogwhistle", 1.0, 1.0,false)
        ActorComponentCallModule(player,"SoundComponent","stopSoundByTrigger","misc.dogwhistle")
        ActorComponentCallModule(player,"SoundComponent","playSound","misc.dogwhistle", 1.0, 1.0)
	else
		player:playSound("misc.dogwhistle", 1.0, 1.0);
	end

	player:shortcutItemUsed()
	return true, 2;
end

--使用台阶
function Slab_OnUse(player, world, x,y,z,dir)
	slabid = player:getCurToolID()

	if world:getBlockID(x,y,z) == slabid then
		blockdata = world:getBlockData(x,y,z)
		if (dir==DIR_POS_Y and (blockdata==0 or blockdata == 3 or blockdata == 5)) or (dir==DIR_NEG_Y and (blockdata==1 or blockdata==4 or blockdata==6)) then
			if blockdata==0 or blockdata==1 then
				world:setBlockData(x,y,z, blockdata + 5, 19)
			elseif blockdata==3 or blockdata==4 then
				world:setBlockData(x,y,z, blockdata - 3, 19)
			else
				world:setBlockData(x,y,z, 2, 19)
			end
			player:playBlockPlaceSound(slabid, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end

	nx, ny, nz = PlaceBlockOnUse(player, world, x,y,z,dir, slabid)
	curid = world:getBlockID(nx,ny,nz)
	if curid == slabid then
		blockdata = world:getBlockData(nx,ny,nz)
--		if blockdata==0 or blockdata==1 then
		if blockdata~=2 then
			if blockdata==0 or blockdata==1 then
				world:setBlockData(nx,ny,nz, blockdata + 5, 19)
			elseif blockdata==3 or blockdata==4 then
				world:setBlockData(nx,ny,nz, blockdata - 3, 19)
			else
				world:setBlockData(nx,ny,nz, 2, 19)
			end
			player:playBlockPlaceSound(slabid, nx, ny, nz)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return false, 1;
end

--使用三角方块
function TriangleBlock_OnUse(player, world, x,y,z,dir)
	local ret = false
	local triangleB = player:getCurToolID()
	local blockdata = world:getBlockData(x,y,z)
	local blockdir = bit.band(blockdata,3);
	if world:getBlockID(x,y,z) == triangleB and blockdata < 8 then
		if (blockdata<4 and dir == DIR_POS_Y) or (blockdata>=4 and dir == DIR_NEG_Y) or dir == blockdir then
			world:setBlockData(x,y,z, blockdata + 8)
			player:playBlockPlaceSound(triangleB, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return ret, 1;
end
--使用横三角半砖方块
function TriangleHorizontalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local toolId = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	local blockdata = world:getBlockData(x,y,z)
	local updown = bit.band(blockdata,4);
	local blockdir = bit.band(blockdata,3);
	if blockid == toolId or blockid - 1 == toolId or blockid - 1000 == toolId then
		if (updown == 4 and dir == DIR_NEG_Y) or (updown == 0 and dir == DIR_POS_Y) or dir == blockdir then
			if blockdata < 8 then
				world:setBlockData(x,y,z, blockdata + 8)
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			elseif blockid == toolId then
				if blockid>=160000 and toolId>=160000 then
					world:setBlockAll(x,y,z, blockid + 1000, blockdata - 8)
				else
					world:setBlockAll(x,y,z, blockid + 1, blockdata - 8)
				end
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			end
		end
	end
	return ret, 1;
end
--使用竖三角半砖方块
function TriangleVerticalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local toolId = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	local blockdata = world:getBlockData(x,y,z)
	local updown = bit.band(blockdata,4);
	local blockdir = bit.band(blockdata,3);
	if blockid == toolId or blockid - 1 == toolId or blockid - 1000 == toolId then
		if (updown == 4 and dir == DIR_NEG_Y) or (updown == 0 and dir == DIR_POS_Y) or dir == blockdir then
			if blockdata < 8 then
				world:setBlockData(x,y,z, blockdata + 8)
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			elseif blockid == toolId then
				if blockid>=160000 and toolId>=160000 then
					world:setBlockAll(x,y,z, blockid + 1000, blockdata - 8)
				else
					world:setBlockAll(x,y,z, blockid + 1, blockdata - 8)
				end
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			end
		end
	end
	return ret, 1;
end
--使用弧板方块
function ArcBlock_OnUse(player, world, x,y,z,dir)
	local ret = false
	local arcB = player:getCurToolID()
	local blockdata = world:getBlockData(x,y,z)
	local blockdir = bit.band(blockdata,3);
	if world:getBlockID(x,y,z) == arcB and blockdata < 8 then
		if (blockdata<4 and dir == DIR_POS_Y) or (blockdata>=4 and dir == DIR_NEG_Y) or dir == blockdir then
			world:setBlockData(x,y,z, blockdata + 8)
			player:playBlockPlaceSound(arcB, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return ret, 1;
end
--使用横弧板半砖方块
function ArcHorizontalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local toolId = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	local blockdata = world:getBlockData(x,y,z)
	local updown = bit.band(blockdata,4);
	local blockdir = bit.band(blockdata,3);
	if blockid == toolId or blockid - 1 == toolId or blockid - 1000 == toolId then
		if (updown == 4 and dir == DIR_NEG_Y) or (updown == 0 and dir == DIR_POS_Y) or dir == blockdir then
			if blockdata < 8 then
				world:setBlockData(x,y,z, blockdata + 8)
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			elseif blockid == toolId then
				if blockid>=160000 and toolId>=160000 then
					world:setBlockAll(x,y,z, blockid + 1000, blockdata - 8)
				else
					world:setBlockAll(x,y,z, blockid + 1, blockdata - 8)
				end
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			end
		end
	end
	return ret, 1;
end
--使用竖弧板半砖方块
function ArcVerticalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local toolId = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	local blockdata = world:getBlockData(x,y,z)
	local updown = bit.band(blockdata,4);
	local blockdir = bit.band(blockdata,3);
	if blockid == toolId or blockid - 1 == toolId or blockid - 1000 == toolId then
		if (updown == 4 and dir == DIR_NEG_Y) or (updown == 0 and dir == DIR_POS_Y) or dir == blockdir then
			if blockdata < 8 then
				world:setBlockData(x,y,z, blockdata + 8)
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			elseif blockid == toolId then
				if blockid>=160000 and toolId>=160000 then
					world:setBlockAll(x,y,z, blockid + 1000, blockdata - 8)
				else
					world:setBlockAll(x,y,z, blockid + 1, blockdata - 8)
				end
				player:playBlockPlaceSound(toolId, x, y, z)
				player:shortcutItemUsed()
				return true, 0;
			end
		end
	end
	return ret, 1;
end
--使用竖台阶
function VerticalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local slabid = player:getCurToolID()
	local blockdata = world:getBlockData(x,y,z)
	if world:getBlockID(x,y,z) == slabid and blockdata < 8 then
		local blockdir = bit.band(blockdata,3);
		if dir == blockdir then
			world:setBlockData(x,y,z, blockdata + 8)
			player:playBlockPlaceSound(slabid, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return ret, 1;
end
--使用变化竖台阶
function VaryVerticalSlab_OnUse(player, world, x,y,z,dir)
	local ret = false
	local slabid = player:getCurToolID()
	local blockdata = world:getBlockData(x,y,z)
	local h = bit.band(blockdata,12);
	--local l = bit.band(blockdata,4);
	if world:getBlockID(x,y,z) == slabid and h ~= 12 then
		local blockdir = bit.band(blockdata,3);
		if dir == blockdir then
			world:setBlockData(x,y,z, blockdata + 4, 19)
			player:playBlockPlaceSound(slabid, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return ret, 1;
end

function MultiBox_OnUse(player, world, x,y,z,dir)
	local ret = false

	local boxid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, boxid)
	MiniLogWarning("MultiBox_OnUse ----- nx "..nx.." ny "..ny.." nz "..nz)
	local blockdef = DefMgr:getBlockDef(boxid,false)
	if not blockdef then
		MiniLogWarning("MultiBox_OnUse ----- ret")
		return true, 0;
	end

	local BlockSize = WCoord:new_local();
	BlockSize.x = blockdef.BlockSize[0] - 1
	BlockSize.y = blockdef.BlockSize[1] - 1
	BlockSize.z = blockdef.BlockSize[2] - 1

	MiniLogWarning("MultiBox_OnUse ----- BlockSize "..BlockSize.x.." "..BlockSize.y.." "..BlockSize.z)

	local blockpos = WCoord:new_local();
	blockpos.x = nx
	blockpos.y = ny
	blockpos.z = nz

	local range = json2table(world:getMultiBlockRange(player:getCurPlaceDir(),BlockSize,blockpos,true))
	for _,v in ipairs(range) do
		local blockid = world:getBlockID(nx + v.x,ny + v.y,nz + v.z)
		MiniLogWarning("MultiBox_OnUse check pos "..nx + v.x.." "..ny + v.y.." "..nz + v.z.." blockid "..blockid)
		if not world:canPlaceBlockAt(nx + v.x,ny + v.y,nz + v.z,blockid, player) then
			MiniLogWarning("MultiBox_OnUse ----- ret1")
			return true, 0;
		end
	end

	MiniLogWarning("MultiBox_OnUse ----- ret2")
	return ret, 1;
end

function AutoDoor_OnUse(player, world, x,y,z,dir)
	local ret = false
	local doorid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, doorid)

	local placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, doorid, placedir)
	local blockdata = world:getBlockData(nx,ny,nz)
	world:setBlockData(nx,ny,nz, blockdata + 12, 19)
	player:shortcutItemUsed()
	return true, 0;
end

--双开门
function DoubleDoor_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	--MiniLogWarning("DoubleDoor_OnUse ----")
	local doorid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, doorid)
	if not world:canPlaceBlockAt(nx,ny,nz,doorid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()

	local lx, ly, lz = LeftOnPlaceDir(nx,ny,nz, placedir)
	local rx, ry, rz = RightOnPlaceDir(nx,ny,nz, placedir)

	--优先放置右边
	if world:getBlockID(rx,ry,rz) == 0 and world:getBlockID(rx,ry + 1,rz) == 0 then
		--4是是否在上面 1是镜像
		world:setBlockAll(nx,ny+1,nz, doorid, 5)
		world:setBlockAll(nx,ny,nz, doorid, placedir)

		world:setBlockAll(rx,ry+1,rz, doorid, 4)
		world:setBlockAll(rx,ry,rz, doorid, placedir)

		player:shortcutItemUsed()
		return true, 0;
	end

	--放置左边
	if world:getBlockID(lx,ly,lz) == 0 and world:getBlockID(lx,ly + 1,lz) == 0 then
		
		world:setBlockAll(nx,ny+1,nz, doorid, 4)
		world:setBlockAll(nx,ny,nz, doorid, placedir)

		world:setBlockAll(lx,ly+1,lz, doorid, 5)
		world:setBlockAll(lx,ly,lz, doorid, placedir)

		player:shortcutItemUsed()
		return true, 0;
	end

	return true, 0;
end

--使用门
function Door_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local doorid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, doorid)
	if not world:canPlaceBlockAt(nx,ny,nz,doorid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()

	local lx, ly, lz = LeftOnPlaceDir(nx,ny,nz, placedir)
	local rx, ry, rz = RightOnPlaceDir(nx,ny,nz, placedir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	local leftdoor = (world:getBlockID(lx,ly,lz)==doorid) or (world:getBlockID(lx,ly+1,lz)==doorid)
	local rightdoor = (world:getBlockID(rx,ry,rz)==doorid) or (world:getBlockID(rx,ry+1,rz)==doorid)

	local updata = 4
	if left_nc<right_nc or (leftdoor and not rightdoor) then  --镜像
		updata = updata + 0
	else
		updata = updata + 1
	end

	--凯蒂猫门/美乐蒂门/布丁狗门默认使用右边样式
	if doorid == 1834 or doorid == 1853 or doorid == 1868 or doorid == 1879 then updata = 4; end
	if doorid == 390080 then
		world:setBlockAll(nx,ny+3,nz, 390080, updata)
		world:setBlockAll(nx,ny+2,nz, 390080, updata)
	end
	world:setBlockAll(nx,ny+1,nz, doorid, updata)
	world:setBlockAll(nx,ny,nz, doorid, placedir)
	
	player:playBlockPlaceSound(doorid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end
function Door_OnCreate(world, blockid, x, y, z, dir)
	dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir

	local lx, ly, lz = LeftOnPlaceDir(x, y, z, dir)
	local rx, ry, rz = RightOnPlaceDir(x, y, z, dir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	local leftdoor = (world:getBlockID(lx,ly,lz)==blockid) or (world:getBlockID(lx,ly+1,lz)==blockid)
	local rightdoor = (world:getBlockID(rx,ry,rz)==blockid) or (world:getBlockID(rx,ry+1,rz)==blockid)

	local updata = 4
	if left_nc<right_nc or (leftdoor and not rightdoor) then  --镜像
		updata = updata + 0
	else
		updata = updata + 1
	end
	if blockid == 390080 then
		world:setBlockAll(x, y+3, z, blockid, updata)
		world:setBlockAll(x, y+2, z, blockid, updata)
	end
	world:setBlockAll(x, y+1, z, blockid, updata)
	world:setBlockAll(x, y, z, blockid, dir)
	return true
end

--使用屏风
function Screen_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local screenid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, screenid)
	if not world:canPlaceBlockAt(nx,ny,nz,screenid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()

	local lx, ly, lz = LeftOnPlaceDir(nx,ny,nz, placedir)
	local rx, ry, rz = RightOnPlaceDir(nx,ny,nz, placedir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	--local leftdoor = (world:getBlockID(lx,ly,lz)==doorid) or (world:getBlockID(lx,ly+1,lz)==doorid)
	--local rightdoor = (world:getBlockID(rx,ry,rz)==doorid) or (world:getBlockID(rx,ry+1,rz)==doorid)

	local updata = 4
	if left_nc<right_nc then  --镜像
		updata = updata + 0
	else
		updata = updata + 1
	end

	world:setBlockAll(nx,ny+1,nz, screenid, updata)
	world:setBlockAll(nx,ny,nz, screenid, placedir)

	player:playBlockPlaceSound(screenid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--使用屏风
function InkScreen_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local screenid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, screenid)
	if not world:canPlaceBlockAt(nx,ny,nz,screenid, player) then return true, 1 end

	local data = 0;
	local topData = 0
	local playerPos = player:getPosition();
	local disx = nx * BLOCK_SIZE - playerPos.x;
	local disz = nz * BLOCK_SIZE - playerPos.z;
	local sqrt = math.sqrt(disx * disx + disz * disz);
	disx = disx / sqrt;
	disz = disz / sqrt;
	local dot1 = math.abs(0.707 * disx + 0.707 * disz);
	local dot2 = math.abs(0.707 * disx + -0.707 * disz);
	local limit = 0.939 --cos20
	local placedir = player:getCurPlaceDir();
	--数据位不够了
	--把旋转放在上面方块的data里
	if dot1 > limit then
		data = data + 4;
		if DIR_POS_X == placedir or DIR_POS_Z == placedir then
			topData = 2;
		else
			topData = 3;
		end
	elseif dot2 > limit then
		data = data + 4;
		if DIR_POS_X == placedir or DIR_NEG_Z == placedir  then
			topData = 0;
		else
			topData = 1;
		end
	else
		placedir = ReverseDirection(placedir)
	end
	data = data + placedir;

	world:setBlockAll(nx,ny+1,nz, screenid, topData + 8)
	world:setBlockAll(nx,ny,nz, screenid, data)

	player:playBlockPlaceSound(screenid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function Screen_OnCreate(world, blockid, x, y, z, dir)
	dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir

	local lx, ly, lz = LeftOnPlaceDir(x, y, z, dir)
	local rx, ry, rz = RightOnPlaceDir(x, y, z, dir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	local updata = 4
	if left_nc<right_nc then  --镜像
		updata = updata + 0
	else
		updata = updata + 1
	end

	world:setBlockAll(x, y+1, z, blockid, updata)
	world:setBlockAll(x, y, z, blockid, dir)
	return true
end

--使用窗户
function CanPutWindows(world, x,y,z, blockid, player)
	if not world:canPlaceBlockAt(x,y,z,blockid, player) then return false end
	
	if world:getBlockID(x,y+1,z) == blockid and world:getBlockID(x,y-1,z) == blockid then return false end
	if world:getBlockID(x+1,y,z) == blockid and world:getBlockID(x-1,y,z) == blockid then return false end
	if world:getBlockID(x,y,z+1) == blockid and world:getBlockID(x,y,z-1) == blockid then return false end
	
	if world:getBlockID(x,y,z+1) == blockid and world:getBlockID(x,y,z+2) == blockid then return false end
	if world:getBlockID(x,y,z-1) == blockid and world:getBlockID(x,y,z-2) == blockid then return false end
	
	if world:getBlockID(x+1,y,z) == blockid and world:getBlockID(x+2,y,z) == blockid then return false end
	if world:getBlockID(x-1,y,z) == blockid and world:getBlockID(x-2,y,z) == blockid then return false end
	
	if world:getBlockID(x,y+1,z) == blockid and world:getBlockID(x,y+2,z) == blockid then return false end
	if world:getBlockID(x,y-1,z) == blockid and world:getBlockID(x,y-2,z) == blockid then return false end
	
	return true
end

function IsWindowsBlock(blockid)
	return blockid == 555 or blockid == 556 or blockid == 1229 or 
			blockid == 1230 or blockid == 150023 or blockid == 390012;
end

function XWindows_OnUse(player, world, x,y,z,dir)

	local windowid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, windowid)

	local blockid = world:getBlockID(nx, ny, nz)
	if IsWindowsBlock(blockid) then return false, 1 end

	local ret = 1;
	local downData = world:getBlockData(nx, ny - 1, nz)
	local placedir = player:getCurPlaceDir()
	if not world:canPlaceBlockAt(nx,ny,nz,windowid, player) then
		return true, 0;
	end

	local data = 0;
	if world:getBlockID(nx, ny - 1, nz) == windowid and bit.band(downData,3) == placedir  then
		if bit.band(downData,4) ~= 0 then
			data = 0
		else
			data = 4
		end
	else
		local bData = world:getBlockData(nx, ny + 1, nz)
		if world:getBlockID(nx, ny + 1, nz) == windowid and bit.band(bData,3) == placedir then
			if bit.band(bData, 4) ~= 0 then
				bData = 0
			else
				bData = 4
			end
			world:setBlockAll(nx, ny + 1, nz, windowid, placedir + bData)
		end

	end


	
	if placedir > 3 then 
		placedir = 0;
	end
	
	world:setBlockAll(nx,ny,nz, windowid, placedir + data)
	ret = 0;
	
	player:playBlockPlaceSound(windowid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, ret;
end

function Windows_OnUse(player, world, x,y,z,dir)

	local windowid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, windowid)

	local blockid = world:getBlockID(nx, ny, nz)
	if IsWindowsBlock(blockid) then return false, 1 end

	local ret = 1;

	--if not CanPutWindows(world,nx,ny,nz,windowid, player) then return true, ret end
	--摆放上面窗户
	if world:getBlockID(nx, ny - 1, nz)==windowid then
		if world:getBlockID(nx, ny - 2, nz)==windowid then
			return true, ret;
		end
		world:setBlockAll(nx, ny, nz, windowid, world:getBlockData(nx, ny - 1, nz));
		ret = 0;
	else
		--摆放下面窗户
		if world:getBlockID(nx, ny + 1, nz)==windowid then
			if world:getBlockID(nx, ny + 2, nz)==windowid then
				return true, ret;
			end
			world:setBlockAll(nx, ny, nz, windowid, world:getBlockData(nx, ny + 1, nz));
			ret = 0;
		else
			local placedir = player:getCurPlaceDir()

			local lx, ly, lz = LeftOnPlaceDir(nx,ny,nz, placedir)
			local rx, ry, rz = RightOnPlaceDir(nx,ny,nz, placedir)

			local left_nc = 0
			local right_nc = 0
			if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
			if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

			if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
			if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

			local leftdoor = world:getBlockID(lx,ly,lz)==windowid ;
			local rightdoor = world:getBlockID(rx,ry,rz)==windowid ;
			local updata = 0;
			if left_nc<right_nc or (leftdoor and not rightdoor) then  --镜像
				updata = updata + 0
			else
				updata = updata + 4
			end
			
			if placedir > 3 then 
				placedir = 0;
			end
			local checkIsAir = world:getBlockID(nx,ny,nz) ;
			if(checkIsAir~=0)then
				return false, 1
			end
			world:setBlockAll(nx,ny,nz, windowid, placedir + updata)
			ret = 0;
		end

	end
	
	player:playBlockPlaceSound(windowid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, ret;
end

function Windows_OnCreate(world, blockid, x, y, z, dir)
	dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir

	--摆放上面窗户
	if world:getBlockID(x, y - 1, z)==blockid then
		world:setBlockAll(x, y, z, blockid, world:getBlockData(x, y - 1, z))
		return true
	end

	--摆放下面窗户
	if world:getBlockID(x, y + 1, z)==blockid then
		world:setBlockAll(x, y, z, blockid, world:getBlockData(x, y + 1, z))
		return true
	end

	local lx, ly, lz = LeftOnPlaceDir(x,y,z, dir)
	local rx, ry, rz = RightOnPlaceDir(x,y,z, dir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	local leftdoor = world:getBlockID(lx,ly,lz)==blockid
	local rightdoor = world:getBlockID(rx,ry,rz)==blockid
	local updata = 0
	if left_nc<right_nc or (leftdoor and not rightdoor) then  --镜像
		updata = updata + 0
	else
		updata = updata + 4
	end
	
	if dir > 3 then 
		dir = 0
	end
	
	world:setBlockAll(x,y,z, blockid, dir + updata)
	return true
end

--需要精确控制门的摆放位置
--[[
--使用活板门
function TrapDoor_OnUse(player, world, x,y,z,dir)

	if dir==DIR_NEG_Y or dir==DIR_POS_Y then return false end
	local trapdoorId = player:getCurToolID();
	x1, y1, z1 = NeighborBlock(x,y,z,dir);
	if world:canPlaceBlockAt(x1,y1,z1, trapdoorId, player) then
		newdir = ReverseDirection(dir);
		world:setBlockAll(x1,y1,z1, trapdoorId, newdir);
		player:playBlockPlaceSound(trapdoorId, x1, y1, z1);
		player:shortcutItemUsed();
		return true
	end
	return false



	local doorid = player:getCurToolID()
	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	if not world:canPlaceBlockAt(nx,ny,nz,doorid, player) then return true end

	local placedir = player:getCurPlaceDir()

	local lx, ly, lz = LeftOnPlaceDir(nx,ny,nz, placedir)
	local rx, ry, rz = RightOnPlaceDir(nx,ny,nz, placedir)

	local left_nc = 0
	local right_nc = 0
	if world:isBlockNormalCube(lx,ly,lz) then left_nc = left_nc + 1 end
	if world:isBlockNormalCube(lx,ly+1,lz) then left_nc = left_nc + 1 end

	if world:isBlockNormalCube(rx,ry,rz) then right_nc = right_nc + 1 end
	if world:isBlockNormalCube(rx,ry+1,rz) then right_nc = right_nc + 1 end

	local leftdoor = (world:getBlockID(lx,ly,lz)==doorid) or (world:getBlockID(lx,ly+1,lz)==doorid)
	local rightdoor = (world:getBlockID(rx,ry,rz)==doorid) or (world:getBlockID(rx,ry+1,rz)==doorid)

	local updata = 4
	if left_nc<right_nc or (leftdoor and not rightdoor) then  --镜像
		updata = updata + 0
	else
		updata = updata + 1
	end

	world:setBlockAll(nx,ny,nz, doorid, placedir)
	world:setBlockAll(nx,ny+1,nz, doorid, updata)

	player:playBlockPlaceSound(doorid, nx, ny, nz)
	player:shortcutItemUsed()
	return true
end
]]

--使用床
function Bed_OnUse(player, world, x,y,z,dir)
	if(world:getCurMapID() == 1 ) then
		-- player:postInfoTips(6)
		-- return true, 1;
	--[[ //萌眼星取消床方块限制 code-by:lizb
	elseif world:getCurMapID() == 2 then
		player:postInfoTips(20201)
		return true, 1;
	--]]
	end

	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()
	local nx2, ny2, nz2 = NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)

	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()

	return true, 0;
end
function Bed_OnCreate(world, blockid, x, y, z, dir)
	dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir

	local nx2, ny2, nz2 = NeighborBlock(x,y,z, ReverseDirection(dir))
	world:setBlockAll(x,y,z, blockid, dir)
	world:setBlockAll(nx2,ny2,nz2, blockid, dir+4)
	return true
end

--使用载具驾驶座
function DriverSeat_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true end

	local blockid = player:getCurToolID()
	nx,ny,nz = NeighborBlock(x,y,z,dir)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true end
	local ret1 = -100
	local bNeedCal = true
	if VehicleMgr then
		ret1 = VehicleMgr:checkIfSpecialBlockCanPlace(player, blockid, nx, ny, nz, bNeedCal)
		bNeedCal = false
		if ret1 ~= -100 then
			return true
		end
	end

	placedir = player:getCurPlaceDir()
	nx2, ny2, nz2 = NeighborBlock2(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true end
	local ret2 = -100
	if VehicleMgr then
		ret2 = VehicleMgr:checkIfSpecialBlockCanPlace(player, blockid, nx2, ny2, nz2, bNeedCal)
		bNeedCal = false
		if ret2 ~= -100 then
			return true
		end
	end

	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)
	world:setBlockAll(nx,ny,nz, blockid, placedir)
	
	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()
	return true
end
function DriverSeat_OnCreate(world, blockid, x, y, z, dir)
	return Bed_OnCreate(world, blockid, x, y, z, dir)
end

--使用投石车
function Catapult_OnUse(player, world, x,y,z,dir)

	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	nx,ny,nz = NeighborBlock(x,y,z,dir)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir()
	nx2, ny2, nz2 = NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)

	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end
function Catapult_OnCreate(world, blockid, x, y, z, dir)
	return Bed_OnCreate(world, blockid, x, y, z, dir)
end

--使用图腾
function Totem_OnUse(player, world, x,y,z,dir)
	if(world:getCurMapID() > 0) then
		local stringId = 9064;
		if(world:getCurMapID() == 1) then
			--在地底世界
			stringId = 9064;
		else
			--在星球:在星球不能放置桃木图腾.
			stringId = 20209;
		end

		player:postInfoTips(stringId)
		return true, 1;
	end
	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if(0 == world:canPlace(nx, ny, nz)) then
		return true, 1;
	end
	
	local placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, blockid, placedir)
	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--使用图腾装饰
function TotemGarnish_OnUse(player, world, x,y,z,dir)
	-- if(world:getCurMapID() > 0) then
	-- 	player:postInfoTips(9064)
	-- 	return true
	-- end
	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if(0 == world:canPlace(nx, ny, nz)) then
		return true, 1
	end
	placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, blockid, placedir)
	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0
end

function StorageBoxGrade(world, x,y,z, blockid)
	if world:getBlockID(x,y,z) ~= blockid then return 0 end

	if world:getBlockID(x-1,y,z) == blockid then return 2 end
	if world:getBlockID(x+1,y,z) == blockid then return 2 end
	if world:getBlockID(x,y,z-1) == blockid then return 2 end
	if world:getBlockID(x,y,z+1) == blockid then return 2 end
	if world:getBlockID(x,y-1,z) == blockid then return 2 end
	if world:getBlockID(x,y+1,z) == blockid then return 2 end
	return 1
end

function StorageBox_OnUse(player, world, x,y,z,dir)
	-- local curtool = player:getCurToolID()
	-- local nx,ny,nz = NeighborBlock(x,y,z,dir)
	-- if not world:canPlaceBlockAt(nx,ny,nz, curtool, player) then return false, 1 end
	
	-- negxgrade = StorageBoxGrade(world, nx-1, ny, nz, curtool)
	-- posxgrade = StorageBoxGrade(world, nx+1, ny, nz, curtool)
	-- negzgrade = StorageBoxGrade(world, nx, ny, nz-1, curtool)
	-- poszgrade = StorageBoxGrade(world, nx, ny, nz+1, curtool)
	-- negygrade = StorageBoxGrade(world, nx, ny-1, nz, curtool)
	-- posygrade = StorageBoxGrade(world, nx, ny+1, nz, curtool)

	-- if negxgrade + posxgrade + negzgrade + poszgrade >= 2 then return false, 1 end

	-- placedir = player:getCurPlaceDir()
	-- if negxgrade > 0 then
	-- 	if placedir==DIR_NEG_X or placedir==DIR_POS_X then placedir = DIR_NEG_Z end
	-- 	world:setBlockData(nx-1,ny,nz, placedir)
	-- elseif posxgrade > 0 then
	-- 	if placedir==DIR_NEG_X or placedir==DIR_POS_X then placedir = DIR_NEG_Z end
	-- 	world:setBlockData(nx+1,ny,nz, placedir)
	-- elseif negzgrade > 0 then
	-- 	if placedir==DIR_NEG_Z or placedir==DIR_POS_Z then placedir = DIR_NEG_X end
	-- 	world:setBlockData(nx,ny,nz-1, placedir)
	-- elseif poszgrade > 0 then
	-- 	if placedir==DIR_NEG_Z or placedir==DIR_POS_Z then placedir = DIR_NEG_X end
	-- 	world:setBlockData(nx,ny,nz+1, placedir)
	-- elseif negygrade > 0 then
	-- 	world:setBlockData(nx,ny-1,nz, placedir)
	-- elseif posygrade > 0 then
	-- 	world:setBlockData(nx,ny+1,nz, placedir)
	-- end

	-- world:setBlockAll(nx,ny,nz, curtool, placedir)
	-- player:playBlockPlaceSound(curtool, nx, ny, nz)
	-- player:shortcutItemUsed()
	-- return true, 0;

	local curtool = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, curtool)
	if not world:canPlaceBlockAt(nx,ny,nz, curtool, player) then return false, 1 end
	local placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, curtool, placedir)
	player:shortcutItemUsed()
	return true, 0;
end
function StorageBox_OnCreate(world, blockid, x, y, z, dir)
	dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir
	
	-- local negxgrade = StorageBoxGrade(world, x-1, y, z, blockid)
	-- local posxgrade = StorageBoxGrade(world, x+1, y, z, blockid)
	-- local negzgrade = StorageBoxGrade(world, x, y, z-1, blockid)
	-- local poszgrade = StorageBoxGrade(world, x, y, z+1, blockid)
	-- local negygrade = StorageBoxGrade(world, x, y-1, z, blockid)
	-- local posygrade = StorageBoxGrade(world, x, y+1, z, blockid)

	-- if negxgrade + posxgrade + negzgrade + poszgrade >= 2 then return true end

	-- if negxgrade > 0 then
	-- 	if dir==DIR_NEG_X or dir==DIR_POS_X then dir = DIR_NEG_Z end
	-- 	world:setBlockData(x-1,y,z, dir)
	-- elseif posxgrade > 0 then
	-- 	if dir==DIR_NEG_X or dir==DIR_POS_X then dir = DIR_NEG_Z end
	-- 	world:setBlockData(x+1,y,z, dir)
	-- elseif negzgrade > 0 then
	-- 	if dir==DIR_NEG_Z or dir==DIR_POS_Z then dir = DIR_NEG_X end
	-- 	world:setBlockData(x,y,z-1, dir)
	-- elseif poszgrade > 0 then
	-- 	if dir==DIR_NEG_Z or dir==DIR_POS_Z then dir = DIR_NEG_X end
	-- 	world:setBlockData(x,y,z+1, dir)
	-- elseif negygrade > 0 then
	-- 	world:setBlockData(x,y-1,z, dir)
	-- elseif posygrade > 0 then
	-- 	world:setBlockData(x,y+1,z, dir)
	-- end

	world:setBlockAll(x,y,z, blockid, dir)
	return true
end

-- 横竖储物箱道具摆放 code-by:lizb
function StorageBIGBox_OnUse(player, world, x,y,z,dir)
	local curtool = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, curtool)
	if not world:canPlaceBlockAt(nx,ny,nz, curtool, player) then return false, 1 end
	
	local nx2, ny2, nz2;
	local placedir = player:getCurPlaceDir()
	local data1, data2 = placedir, placedir + 4;
	if curtool == 1180 or curtool == 390103 or curtool == 2428 then --横箱先右后左
		nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)
		if not world:canPlaceBlockAt(nx2,ny2,nz2, curtool, player) then
			nx2, ny2, nz2 = LeftOnPlaceDir(nx,ny,nz, placedir)
			data1, data2 = data2, data1
			if not world:canPlaceBlockAt(nx2,ny2,nz2, curtool, player) then
				return false, 1;
			end
		end
	elseif curtool == 1181 or curtool == 390008 or curtool == 390105 or curtool == 2431 then
		nx2, ny2, nz2 = nx, ny+1, nz --竖箱先上后下
		if not world:canPlaceBlockAt(nx2,ny2,nz2, curtool, player) then
			nx2, ny2, nz2 = nx, ny-1, nz
			data1, data2 = data2, data1
			if not world:canPlaceBlockAt(nx2,ny2,nz2, curtool, player) then
				return false, 1;
			end
		end
	end
	
	world:setBlockAll(nx,ny,nz, curtool, data1)
	world:setBlockAll(nx2,ny2,nz2, curtool, data2)	
	
	player:playBlockPlaceSound(curtool, nx, ny, nz)
	player:shortcutItemUsed()

	return true, 0;
end

-- 大箱子触发器创建 先清理2格方块再创建
function StorageBIGBox_OnCreate(world,blockid, x,y,z,dir)
	local isH2SzieBlock = function (id)
		if	id == 582 or id == 722 or id == 720 or id == 883 or id == 828 or	id == 884 or id == 885 or id == 1180 then
			return true
		end
		return false
	end

	local isV2SzieBlock = function (id)
		local def = DefMgr:getBlockDef(id,false)
		if def and def.Height >= 2 then
			return true
		end
		return false
	end

	if blockid == 1180 then
		dir = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir
		local cenid = world:getBlockID(x,y,z)
		if isH2SzieBlock(cenid) or isV2SzieBlock(cenid)  then
			world:destroyBlockEx(x,y,z,false)
		end

		local nx2, ny2, nz2 = RightOnPlaceDir(x,y,z, dir)
		local rightid = world:getBlockID(nx2,ny2,nz2)
		local lx2, ly2, lz2 = LeftOnPlaceDir(x,y,z, dir)
		local leftid = world:getBlockID(lx2,ly2,lz2)
		if rightid == 0 then
			world:setBlockAll(x,y,z, blockid, dir)
			world:setBlockAll(nx2,ny2,nz2, blockid, dir+4)
		elseif leftid == 0 then
			world:setBlockAll(x,y,z, blockid, dir + 4)
			world:setBlockAll(lx2,ly2,lz2, blockid, dir)
		else
			local x1,y2,z2 = nx2, ny2, nz2
			if isV2SzieBlock(rightid) then
				world:destroyBlockEx(nx2,ny2,nz2,false)
				x1,y2,z2 = nx2,ny2,nz2
			elseif isV2SzieBlock(leftid) then
				world:destroyBlockEx(lx2,ly2,lz2,false)
				x1,y2,z2 = lx2,ly2,lz2
			end
			world:setBlockAll(x,y,z, blockid, dir)
			world:setBlockAll(x1,y2,z2, blockid, dir+4)
		end
	elseif blockid == 1181 or blockid == 390008 then
		local updata = (dir == DIR_POS_Y or dir == DIR_NEG_Y) and DIR_NEG_Z or dir
		local cenid = world:getBlockID(x,y,z)
		if isH2SzieBlock(cenid) or isV2SzieBlock(cenid)  then
			world:destroyBlockEx(x,y,z,false)
		end

		--竖箱先上后下
		local nx2, ny2, nz2 = x, y+1, z 
		local upid = world:getBlockID(nx2,ny2,nz2)
		local dx2, dy2, dz2 = x, y-1, z 
		local downid = world:getBlockID(dx2, dy2, dz2)

		local x1,y2,z2 = nx2,ny2,nz2
		if upid ~= 0 and isV2SzieBlock(upid) then
			world:destroyBlockEx(nx2,ny2,nz2,false)
			x1,y2,z2 = nx2,ny2,nz2
			world:setBlockAll(x,y,z, blockid, dir )
			world:setBlockAll(x1,y2,z2, blockid, dir + 4)
		elseif downid ~= 0 and isV2SzieBlock(downid) then
			world:destroyBlockEx(dx2, dy2, dz2,false)
			x1,y2,z2 = dx2, dy2, dz2
			world:setBlockAll(x,y,z, blockid, dir + 4)
			world:setBlockAll(x1,y2,z2, blockid, dir)
		else
			if downid == 0 then
				world:setBlockAll(x,y,z, blockid, updata + 4)
				world:setBlockAll(x,y-1,z, blockid, updata)
			elseif upid == 0 then
				world:setBlockAll(x,y,z, blockid, updata)
				world:setBlockAll(x,y+1,z, blockid, updata + 4)
			end
		end
	end
	return true;
end

function Furnace_OnUse(player, world, x,y,z,dir)
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, 802)
	if not world:canPlaceBlockAt(nx,ny,nz, 802, player) then return false end

	placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, 802, placedir)
	player:playBlockPlaceSound(802, nx, ny, nz)
	player:shortcutItemUsed()

	return true;
end

function BombDir_OnUse(player, world, x, y, z, dir)
	local bx, by, bz = CoordDivBlock(player:getPosition(0, 0, 0))
	local itemdef = DefMgr:getItemDef(player:getCurToolID())
	player:placeBlock(itemdef.MeshType, bx, by, bz, DIR_POS_Y, 0, 0, 0)

	return true, 0;
end

function TrainCar_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	if blockid==BLOCK_RAIL or blockid==BLOCK_RAIL_ACCEL then
		local dx, dy, dz = player:getFaceDir(0, 0, 0);
		local cart = ActorTrainCar:create(itemid, world, x*100+50, y*100+50, z*100+50, dx, dy, dz);
		player:playBlockPlaceSound(blockid, x, y, z)
		player:shortcutItemUsed();
		return true, 2;
	end
	return false, 2;
end

function TrainCar_AutoUse(world, x, y, z, dx, dy, dz, itemid)
	local blockid = world:getBlockID(x,y,z)
	if blockid==BLOCK_RAIL or blockid==BLOCK_RAIL_ACCEL then
		local cart = ActorTrainCar:create(itemid, world, x*100+50, y*100+50, z*100+50, dx, dy, dz);
		return 1
	end
	return 0
end

function Boat_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	if IsFluid(blockid) then
		local boat = ActorBoat:create(itemid, world, x*100+50, y*100+150, z*100+50);
		player:playBlockPlaceSound(blockid, x, y, z)
		player:shortcutItemUsed();
		return true, 2;
	end
	return false, 2;
end

function Boat_AutoUse(world, x, y, z, dir, itemid)
	local blockid = world:getBlockID(x,y,z)
	if blockid == 0 then blockid = world:getBlockID(x,y-1,z) end

	if IsFluid(blockid) then
		local boat = ActorBoat:create(itemid, world, x*100+50, y*100+150, z*100+50);
		return 1
	end
	return 0
end

function Research_OnUse(player, world, x, y, z, dir)
	player:UseSelectTechBlueprints()
	--local itemid = player:getCurToolID()

	--player:shortcutItemUsed()
	return true, 2;
end

function RiverLantern_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	local blockid = world:getBlockID(x,y,z)
	if IsFluid(blockid) then
		local boat = ActorRiverLantern:create(itemid, world, x*100+50, y*100+150, z*100+50);
		player:playBlockPlaceSound(blockid, x, y, z)
		player:shortcutItemUsed();
		return true, 2;
	end
	return false, 2;
end

function RiverLantern_AutoUse(world, x, y, z, dir, itemid)
	local blockid = world:getBlockID(x,y,z)
	if blockid == 0 then blockid = world:getBlockID(x,y-1,z) end

	if IsFluid(blockid) then
		local boat = ActorRiverLantern:create(itemid, world, x*100+50, y*100+150, z*100+50);
		return 1
	end
	return 0
end

function getBallRealCratePos(x, y, z, dir)
	local t = {
		[DIR_NEG_X] = {offsetX=-50, offsetY=50, offsetZ=50},
		[DIR_POS_X] = {offsetX=60, offsetY=50, offsetZ=50},
		[DIR_NEG_Z] = {offsetX=50, offsetY=50, offsetZ=-50},
		[DIR_POS_Z] = {offsetX=50, offsetY=50, offsetZ=50},
		[DIR_NEG_Y] = {offsetX=50, offsetY=-50, offsetZ=50},
		[DIR_POS_Y] = {offsetX=50, offsetY=150, offsetZ=50},
	}

	x = x*100 + t[dir].offsetX
	y = y*100 + t[dir].offsetY
	z = z*100 + t[dir].offsetZ

	return {x=x, y=y, z=z}
end

function Ball_OnUse(player, world, x, y, z, dir)
	
	local pos = getBallRealCratePos(x, y, z, dir);
	--print("kekeke Ball_OnUse", x, y, z, dir, pos);
	ActorBall:create(world, pos.x, pos.y, pos.z);
	player:shortcutItemUsed();
	return true, 2;
end

function BasketBall_OnUse(player, world, x, y, z, dir)
	local pos = getBallRealCratePos(x, y, z, dir);
	ActorBasketBall:create(world, player, pos.x, pos.y, pos.z);
	player:shortcutItemUsed();
	return true, 2;
end

function Item_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local itemid = grid:getItemID();
	local x = world:coordDivBlock(posX);
	local y = world:coordDivBlock(posY);
	local z = world:coordDivBlock(posZ);

	if itemid == ITEM_BOAT or itemid == ITEM_BAMBOO_RAFT then return Boat_AutoUse(world, x, y, z, dir, itemid)
	elseif itemid == ITEM_TRAINCAR or itemid==ITEM_TRAINCAR_HEAD then return TrainCar_AutoUse(world, x, y, z, dirX, dirY, dirZ, itemid)
	elseif itemid == 13808 then return RiverLantern_AutoUse(world, x, y, z, dir, itemid)
	elseif itemid==ITEM_BUCKET_WATER or itemid==ITEM_BUCKET_LAVA then return Bucket_AutoUse(world, x, y, z, dir, grid)
	end
	return 0;
end

function Ball_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)

	local s = 400.0;
	ActorBall:create(world, posX, posY, posZ, dirX*s, dirY*s, dirZ*s);
	return 1
end

function BasketBall_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local s = 400.0;
	ActorBasketBall:create(world, nil, posX, posY, posZ, dirX*s, dirY*s, dirZ*s);
	return 1
end

function Notice_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	local itemid1 = 0
	local itemid2 = 0
	if itemid==BLOCK_SIGNS_STAND or itemid==BLOCK_SIGNS_HANG then
		itemid1 = BLOCK_SIGNS_STAND
		itemid2 = BLOCK_SIGNS_HANG
	else
		itemid1 = math.floor(itemid/2)*2
		itemid2 = itemid1 + 1
	end

	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	local face = ReverseDirection(dir)
	local ret = 1;
	if dir == DIR_POS_Y then
		player:placeBlock(itemid1, nx, ny, nz, DIR_NEG_Y)
		player:playBlockPlaceSound(itemid1, nx, ny, nz)
		ret = 0;
	elseif dir ~= DIR_NEG_Y then
		player:placeBlock(itemid2, nx, ny, nz, face)
		player:playBlockPlaceSound(itemid2, nx, ny, nz)
		ret = 0;
	end
	return true, ret;
end

function EnderEye_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	player:createEnderEye()
	player:shortcutItemUsed()
	return true, 2;
end

function Firework_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	
	--家园地图是使用道具要通知服务器消耗
	if IsInHomeLandMap and IsInHomeLandMap() then
		if not GetInst("HomeLandService"):UseItemByNotHomeLandFunc(player, itemid, 1) then
			return false, 2;
		end
	end

	if itemid == 12831 then
		player:createFirework(0, 999);
	elseif itemid == 12832 then
		player:createFirework(0, 4999);
	elseif itemid == 12833 then
		player:createFirework(2, 0);
	elseif itemid == 12834 then
		player:createFirework(1, 0);
	elseif itemid == 12835 then
		player:createFirework(3, 0);
	elseif itemid == 12830 then
		player:createFirework(4, 0);
	elseif itemid == 12828 then
		player:createFirework(5, 0);
	elseif itemid == 12827 then
		player:createFirework(5, 1);
	elseif itemid == 12750 then
		player:createFirework(6, 0);	
	elseif itemid == 12781 then
		player:createFirework(7, 0);
	elseif itemid == 15528 then
        player:createFirework(8, 0);
    elseif itemid == 12751 then
        player:createFirework(9, 0);
    elseif itemid == 12752 then
        player:createFirework(10, 0);
	elseif itemid == 12754 then
		player:createFirework(12, 0);
	elseif itemid >= 150500 and itemid <= 150509 then
		player:createFirework(13, itemid);
	elseif itemid == 150510 then
		player:createFirework(14, itemid);

	end	
	player:shortcutItemUsed()

	return true, 2;
end

function TPScroll_OnUse(player, world, x, y, z, dir)
	player:teleportHome()
	player:shortcutItemUsed()
	return true, 2;
end

function MobEgg_OnUse_base(player, world, x, y, z, dir, monsterId)
	local itemDef = DefMgr:getItemDef(player:getCurToolID());
	if itemDef and itemDef.InvolvedID > 0 then
		monsterId = itemDef.InvolvedID;
		print('MobEgg_OnUse',player:getCurToolID(), itemDef.InvolvedID, monsterId);
	end

	if DefMgr:getMonsterDef(monsterId) == nil then return false, 2, 0; end

	local objId = 0;
	local x1 = x*BLOCK_SIZE + BLOCK_SIZE/2
	local y1 = y*BLOCK_SIZE + BLOCK_SIZE
	local z1 = z*BLOCK_SIZE + BLOCK_SIZE/2
	local mob = nil 
	--1000000 //自定义生物起始ID（100w）
	--1999999 //自定义生物结束ID（200w）
	if monsterId < 1000000 then
		mob = world:getActorMgr():spawnMonster(x1,y1,z1, monsterId)
	else
		local szCfgId = ModPackEditorMgr:GetInstancePtr():GetResIdByCfgId(Mod_Monster, monsterId)
		mob = world:getActorMgr():spawnMobByPrefab(x1,y1,z1, szCfgId)
	end
	if mob ~= nil then
		mob:setPersistance(true);
		player:shortcutItemUsed()
		objId = mob:getObjId() --新增objid返回值
		local mobobject = tolua.cast(mob, "MNSandbox::GameObject")
		if mobobject:GetComponentByName("VacantComponent") then -- 如果是虚空生物
			ActorComponentCallModule(mob,"VacantComponent","setIsUseItemToChange", true) -- 设置之后在虚空之夜过后不会变回普通生物
		end
	end

	--新手优化任务上报
	local NoviceTaskInterface = GetInst("NoviceTaskInterface")
	if NoviceTaskInterface and monsterId == 3401 then
		NoviceTaskInterface:SubmitTaskFinish(4016,1)
	end
	
	return true, 2, objId;--返回值0为修改了block数据，1为没有修改，2为使用
end

function MobEgg_OnUse(player, world, x, y, z, dir)
	local curtoolId = player:getCurToolID()
	local monsterId = g_EggToMonster[curtoolId]
	if monsterId == nil then 
		if curtoolId == 13248 then
			monsterId = GetRandomVoidWildFoxId()
		else
			--1000000 //自定义生物起始ID（100w）
			--1999999 //自定义生物结束ID（200w）
			if curtoolId < 1000000 then
				monsterId = curtoolId-10000
			else 
				monsterId = curtoolId
			end
		end 
	end

	return MobEgg_OnUse_base(player, world, x, y, z, dir, monsterId)
end

function FlamenEgg_OnUse(player, world, x, y, z, dir)
	local randidx = math.random(1, #FlamenIds)
	local monsterId = FlamenIds[randidx]
	return MobEgg_OnUse_base(player, world, x, y, z, dir, monsterId)
end

function AquaticEgg_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()

	local x1 = x*BLOCK_SIZE + BLOCK_SIZE/2
	local y1 = y*BLOCK_SIZE + BLOCK_SIZE
	local z1 = z*BLOCK_SIZE + BLOCK_SIZE/2
	local mob = GetWorldActorMgr(world):spawnMonster(x1,y1 - 100,z1, itemid-10000)
	if  mob ~= nil then
		mob:setPersistance(true);
		player:shortcutItemUsed()
		local mobobject = tolua.cast(mob, "MNSandbox::GameObject")
		if mobobject:GetComponentByName("VacantComponent") then -- 如果是虚空生物
			ActorComponentCallModule(mob,"VacantComponent","setIsUseItemToChange", true) -- 设置之后在虚空之夜过后不会变回普通生物
		end
	end
	if(itemid==13628)then
		SharkSpawnReport(2)--生成鲨鱼埋点上报
	end
	return true, 2;
end


function FishItem_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()

	local x1 = x*BLOCK_SIZE + BLOCK_SIZE/2
	local y1 = y*BLOCK_SIZE + BLOCK_SIZE
	local z1 = z*BLOCK_SIZE + BLOCK_SIZE/2
	if GetWorldActorMgr(world):spawnMonster(x1,y1,z1, itemid-10000) then
		player:shortcutItemUsed()
	end
	return true
end

function SnowBall_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()

	MiniLog("SnowBall_OnUse.....",itemid)

	--家园地图是使用道具要通知服务器消耗
	if IsInHomeLandMap and IsInHomeLandMap() then
		if not GetInst("HomeLandService"):UseItemByNotHomeLandFunc(player, itemid, 1) then
			return false, 2;
		end
	end

	player:throwBall(itemid)
	player:shortcutItemUsed()
	return true, 2;
end

function AirDropBall_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	MiniLog("AirDropBall_OnUse.....",itemid,x,y,z)
	player:callAirDrop(itemid,110000,x,z)
	return true, 2;
end

function FireRocket_OnUse(player, world, x, y, z, dir)
	player:moveItemInner(player:getShortcutStartIndex() + player:getCurShortcut(), EQUIP_START_INDEX + 4, 1);
	if ActorComponentCallModule then
		ActorComponentCallModule(player,"SoundComponent","playSound","item.12822.launch", 1.0, 1.0)
	else
		player:playSound("item.12822.launch", 1.0, 1.0);
	end
	return true, 2;
end

function Moss_OnUse(player, world, x, y, z, dir)
	local toolId = player:getCurToolID()
	local ret = false
	local blockId=world:getBlockID(x,y,z)
	local placedir = player:getCurPlaceDir()
	local blockdata = world:getBlockData(x,y,z)
	if  blockId == 397 then--两种苔藓
		world:setBlockAll(x, y, z, 262, blockdata)
		player:playBlockPlaceSound(toolId, x, y, z)
		player:shortcutItemUsed()
		return true, 0;
	elseif(blockId == 262)then
		local level=bit.band(blockdata, 8)
		if(level==0)then
			world:setBlockData(x, y, z, bit.bor(blockdata, 8))
			player:playBlockPlaceSound(toolId, x, y, z)
			player:shortcutItemUsed()
		end
		return true, 0;
	end
	return ret, 1;
end


function Gun_OnUse(player, world, x, y, z, dir)
	--local itemid = player:getCurToolID()

	player:throwBall(0)
	--player:shortcutItemUsed()
	return true
end

--玩家-战斗号角
function BattleHorn_OnUse(player, world, x, y, z, dir)
	player:battleHornVillager()
	if ActorComponentCallModule then
		ActorComponentCallModule(player,"SoundComponent","playSound","item.12711.battlehorn", 1.0, 1.0)
	else
		player:playSound("item.12711.battlehorn", 1.0, 1.0);
	end
	return true
end

function Horn_OnUse(player, world, x, y, z, dir)
	local mobnum = player:selectAllCacheMobsNum(3401, 0, 1600);--16米也就是1600厘米
	if mobnum == 0 then
		if ActorComponentCallModule then
			ActorComponentCallModule(player,"SoundComponent","playSound","misc.horn", 1.0, 1.0)
		else
			player:playSound("misc.horn", 1.0, 1.0)
		end

		return true, 2;
	end
	if mobnum > 64 then
		mobnum = 64;
	end
	for t=0, mobnum - 1 do
	    local mob = player:getCacheMob(t);
		mob:tryMoveToXYZ(x, y, z, 2);
	end
	if ActorComponentCallModule then
		ActorComponentCallModule(player,"SoundComponent","playSound","misc.horn", 1.0, 1.0)
	else
		player:playSound("misc.horn", 1.0, 1.0)
	end

	player:shortcutItemUsed()
	return true, 2;
end

--使用增加帐号道具数量的物品
function AccountItem_OnUse(player, world, x, y, z, dir)
	local grid_index = player:getCurShortcut() + player:getShortcutStartIndex();
	local itemid = player:getCurToolID()
	
	AccountItemUse(grid_index, itemid, player);
	return true, 2;
end

function Paper_OnUse(player, world, x, y, z, dir)
    local itemid = player:getCurToolID()

    -- 信纸
    if itemid == ITEM_LETTERS and player:isPlayerControl() then
        --getglobal("LettersFrame"):Show()
		GetInst("MiniUIManager"):OpenUI("lettersFramePage", "miniui/miniworld/adventure/", "lettersFramePageAutoGen", {});
		LettersFrame_OnShow()
    end

	return true, 2;
end

function RandomPaper_OnUse(player, world, x, y, z, dir)
	local numPaper=DefMgr:getLettersOfficalTextNum()
	if numPaper <= 0 then
		return false
	end
	local index = math.random(1, numPaper);
	local driftDef = DefMgr:getLettersOfficialTextByIndex(index - 1);
	if not driftDef then
		return false
	end
    local itemid = player:getCurToolID()
	player:shortcutItemUsed()
    local grid = BackPackGrid:new_local();
    local content = {};
    local wcoord = WCoord:new_local();
    local jsonData = {};
    jsonData.title = driftDef.title
    jsonData.context = driftDef.context
    jsonData.authorname = driftDef.author
    jsonData.fromDriftBottle = 2
    jsonData.shouldAnonymity = driftDef.shouldAnonymity and 1 or 0;
    jsonData.uin = 1000;
	jsonData.changetime = GetServerCurrentTime();
    jsonData.serviceID = "";
	--MiniLog("RandomPaper_OnUse- driftDef.title:"..driftDef.title.."  driftDef.context:"..driftDef.context.."  driftDef.author:"..driftDef.author.."  driftDef.shouldAnonymity:"..tostring(driftDef.shouldAnonymity))
    content.str = JSON:encode(jsonData)
    wcoord.x = x
    wcoord.y = y
    wcoord.z = z
    grid:setItem(11806, 1);
    grid:setUserdataStr(content.str);
	player:getBackPack():tryAddItem_byGrid(11806,1,grid)
	return true, 2;
end

function Book_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
    -- 书
    if itemid == ITEM_BOOK and player:isPlayerControl() then
    	print("Book_OnUse2", itemid)
        getglobal("BookFrame"):Show()
    end

	return true, 2;
end

--三丽鸥大沙发使用
function BigShafa_OnUse(player, world, x,y,z,dir)

	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()
	local nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)

	player:playBlockPlaceSound(828, nx, ny, nz)
	player:shortcutItemUsed()

	return true, 0;
end

--乐谱使用
function MusicPu_OnUse(player, world, x, y, z, dir)
	if not ResourceCenterMidiEntryIsOpen() then
		ShowGameTips(GetS(120101))
		return true,2
	end
    local itemid = player:getCurToolID()

	local grid_index = player:getCurShortcut() + player:getShortcutStartIndex();

	local userdata = ClientBackpack:getGridUserdataStr(grid_index)
	local tab = JSON:decode(userdata)
	
	
    if itemid == ITEM_MUSIC_PU and player:isPlayerControl() then
		GetInst("ResourceService"):ReqAddOtherRes(tab.ori_uin, tab.ori_id, function(data)
			if data.ret == 0 then
				local str = string.format(GetS(130033), tab.yqname or GetS(4896))
				ShowGameTips(str)
			else
				if data.msg == "error_same_res_md5" then
					ShowGameTips(GetS(130030))
				else
					ShowGameTips(data.msg)
				end
			end
		end)
    end

	return true, 2;
end

function Instruction_OnUse(player, world, x, y, z, dir)
    local itemid = player:getCurToolID()
    -- 指令
    if itemid == ITEM_INSTRUCTION and player:isPlayerControl() then
        -- getglobal("SignalParserFrame"):Show()

		--新的指令芯片fgui renjie
		GetInst("MiniUIManager"):AddPackage(
			{
				"miniui/miniworld/common",
				"miniui/miniworld/c_ingame",
				"miniui/miniworld/common_comp",
				"miniui/miniworld/MessageBox"
			},
			"signalParserFrameAutoGen"
		)
		GetInst("MiniUIManager"):OpenUI("signalParserFrame","miniui/miniworld/adventure","signalParserFrameAutoGen",{ge})	


    end

	return true, 2;
end

function MapEdit_OnUse(player, world, x, y, z, dir)
	--玩法模式屏蔽
	if WorldMgr and (WorldMgr:isGameMakerRunMode() or WorldMgr:isCreateRunMode()) then
		return false, 2;
	end

	if GetRecordPkgManager():isRecordPlaying() then
		return true, 2;
	end

	local itemid = player:getCurToolID();

	-- 地图编辑
	if itemid == ITEM_MAPEDIT and player:isPlayerControl() then
		local isPlayerCtrlExist = MapEditManager:IsPlayerCtrlExist()
		if isPlayerCtrlExist then
			-- 查看是否有放置方块的权限
			local uin = AccountManager:getUin();
			if not PermitsCallModuleScript("canPermit", uin, itemid, CS_PERMIT_PLACE_BLOCK) then
				if PermitsCallModuleScript("isHost", uin) then
					notifyAuthorityGameInfo2Player(uin, PLAYER_NOTIFYINFO_TIPS, 9725, CS_PERMIT_PLACE_BLOCK);
				else
					notifyAuthorityGameInfo2Player(uin, PLAYER_NOTIFYINFO_TIPS, 9720, CS_PERMIT_PLACE_BLOCK);
				end
				return false, 2;
			end

			--云服暫時沒有PlayerCtrl，不開放使用
			local isStartMapEdit = MapEditManager:GetIsStartEdit()
			if not isStartMapEdit then 
				local param = {disableOperateUI = true}
				-- GetInst("UIManager"):Open("MapEdit", param)
				if not GetInst("MiniUIManager"):IsShown("MapEditAutoGen") then
					GetInst("MiniUIManager"):OpenUI("MapEditMainFrame", "miniui/miniworld/ugc_mapEdit", "MapEditAutoGen", param)
				end
			else
				MapEditManager:ExcuteCmdWithRBClicked()
                -- if GetInst("UIManager"):GetCtrl("MapEdit") then
                --     GetInst("UIManager"):GetCtrl("MapEdit"):MapEditUseStatistics()
                -- end
				if GetInst("MiniUIManager"):GetCtrl("MapEdit") then
					GetInst("MiniUIManager"):GetCtrl("MapEdit"):MapEditUseStatistics()
				end
			end
		end 
	end
	return true, 2;
end

function CustomCamera_OnUse(player, world, x, y, z, dir)
	--联机的时候不显示
	if AccountManager:getMultiPlayer() == 0 then
    	CameraFrameShow()
	end
    return true, 2;
end

function Item_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir)
	local itemid = grid:getItemID();
	local x = world:coordDivBlock(posX);
	local y = world:coordDivBlock(posY);
	local z = world:coordDivBlock(posZ);

	if itemid == ITEM_BOAT or itemid == ITEM_BAMBOO_RAFT then return Boat_AutoUse(world, x, y, z, dir, itemid)
	elseif itemid == ITEM_TRAINCAR or itemid==ITEM_TRAINCAR_HEAD then return TrainCar_AutoUse(world, x, y, z, dirX, dirY, dirZ, itemid)
	elseif itemid == 13808 then return RiverLantern_AutoUse(world, x, y, z, dir, itemid)
	elseif itemid==ITEM_BUCKET_WATER or itemid==ITEM_BUCKET_LAVA then return Bucket_AutoUse(world, x, y, z, dir, grid)
	end
	return 0;
end
function CrabEgg_OnUse(player, world, x, y, z, dir)
	local curtoolId = player:getCurToolID()
	local monsterId = g_EggToMonster[curtoolId]
	if monsterId == nil then monsterId = curtoolId-10000 end
	return MobEgg_OnUse_base(player, world, x, y, z, dir, monsterId)
	--local mob = MobEgg_OnUse_base(player, world, x, y, z, dir, monsterId)
	--mob:setPersistance(true);
	--mob:setFlagBit(ACTORFLAG_SNEAK,true)
	--return mob
end
function F3623_Init(mob)
	if math.random(1,10)==1 then
		tolua.cast(mob,"ActorHippocampus"):setIsMother(true)
		tolua.cast(mob,"ActorHippocampus"):refreshModel()
	end
end
function HippocampusEgg_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	
	local x1 = x*BLOCK_SIZE + BLOCK_SIZE/2
	local y1 = y*BLOCK_SIZE + BLOCK_SIZE
	local z1 = z*BLOCK_SIZE + BLOCK_SIZE/2
	local mob = GetWorldActorMgr(world):spawnMonster(x1,y1 - 100,z1, itemid-10000)
	if  mob ~= nil then
		mob:setPersistance(true);
		player:shortcutItemUsed()
		
	end
	
	return true, 2;
end
function MobEgg_AutoUse_base(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, monsterId, mob_check)
	local itemid = grid:getItemID();
	local itemDef = DefMgr:getItemDef(itemid);
	if itemid == 13248 then
		--虚空狐狸特殊处理
		monsterId = GetRandomVoidWildFoxId()
	else
		if itemDef and itemDef.InvolvedID > 0 then
			monsterId = itemDef.InvolvedID;
		end
	end

	if DefMgr:getMonsterDef(monsterId) == nil then return end
	
	--[[
	local x = world:coordDivBlock(posX);
	local y = world:coordDivBlock(posY);
	local z = world:coordDivBlock(posZ);
	]]
	if nil == mob_check then
		mob_check = true;
	end
	local mob = GetWorldActorMgr(world):spawnMonster(posX, posY, posZ, monsterId, -1, 0, "", mob_check)
	if  mob ~= nil then
		mob:setPersistance(true);
		local mobobject = tolua.cast(mob, "MNSandbox::GameObject")
		if mobobject:GetComponentByName("VacantComponent") then -- 如果是虚空生物
			ActorComponentCallModule(mob,"VacantComponent","setIsUseItemToChange", true) -- 设置之后在虚空之夜过后不会变回普通生物
		end
		
		return 1
	end
		return -1;
end

--durable, speedRate暂时没作用, 只是和ShootProjectile_Auto对齐
function MobEgg_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, durable, speedRate, mob_check)--(world, x, y, z, dir, itemid)
	local itemid = grid:getItemID();
	local monsterId = itemid - 10000
	return MobEgg_AutoUse_base(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, monsterId, mob_check)
end

function FlamenEgg_AutoUse(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local randidx = math.random(1, #FlamenIds)
	local monsterId = FlamenIds[randidx]
	return MobEgg_AutoUse_base(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, monsterId)
end

function ShootArrow_Auto(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	GetSandboxActorSubsystem():shootArrowAuto(posX, posY, posZ, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow,world);
	return 1;
end

function ShootProjectile_Auto(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, durable, speedRate, mob_check)
	local itemid = grid:getItemID();
	if nil == durable then 
		durable = -1;
	end
	if nil == speedRate then 
		speedRate = 1;
	end
	GetSandboxActorSubsystem():shootProjectileAuto(itemid, posX, posY, posZ, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow, world, durable, speedRate);
	return 1;
end

function ShootProjectileDurable_Auto(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow, durable)
	local itemid = grid:getItemID();
	GetSandboxActorSubsystem():shootProjectileAuto(itemid, posX, posY, posZ, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow, world, durable);
	return 1;
end

function ShootImpulse_Auto(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local itemid = grid:getItemID();
	GetSandboxActorSubsystem():shootImpulseAuto(itemid, posX, posY, posZ, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow,world);
	return 1;
end

function ThrowItem_Auto(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local itemid = grid:getItemID();
	GetSandboxActorSubsystem():throwItemAuto(itemid, posX, posY, posZ, dirX, dirY, dirZ, shooterObjIdHigh, shooterObjIdLow, world);
	return 1;
end

function Firework_Create(world, grid, posX, posY, posZ, dirX, dirY, dirZ, dir, shooterObjIdHigh, shooterObjIdLow)
	local fireType;
	local fireData;
	local result;

	local itemid = grid:getItemID();
	if itemid == 12829 then
		return 0;
	else
		result = 1;
		if itemid == 12831 then
			fireType = 0;
			fireData = 999;
		elseif itemid == 12832 then
			fireType = 0;
			fireData = 4999;
		elseif itemid == 12833 then
			fireType = 3;
			fireData = 0;
		elseif itemid == 12834 then
			fireType = 1;
			fireData = 0;
		elseif itemid == 12830 then
			fireType = 4;
			fireData = 0;
		elseif itemid == 12828 then
			fireType = 5;
			fireData = 0;
		elseif itemid == 12835 then
			fireType = 3;
			fireData = 0;
		elseif itemid == 12750 then
			fireType = 6;
			fireData = 0;
		elseif itemid == 12781 then
			fireType = 7;
			fireData = 0;
		elseif itemid == 15528 then
			fireType = 8;
			fireData = 0;
		elseif itemid >= 150500 and itemid <= 150509 then
			fireType = 13
			fireData = itemid
		elseif itemid == 150510 then
			fireType = 14
			fireData = itemid
		end
	end

	GetSandboxActorSubsystem():createActorFirework(posX, posY, posZ, dirX, dirY, dirZ, fireType, fireData, world);
	return result;
end

function Rocket_OnUse(player, world, x, y, z, dir)
	local dx, dy, dz = player:getFaceDir(0, 0, 0);
	local rocket = ActorRocket:create(world, x*100+50, y*100+100, z*100+50, dx, dy, dz);
	player:shortcutItemUsed();
	if rocket then
		if ActorComponentCallModule then
			ActorComponentCallModule(rocket,"EffectComponent","playBodyEffectByName","acchorse", true)
			ActorComponentCallModule(rocket,"SoundComponent","playSound","ent.3803.drop", 1.0, 1.0)
		else
			rocket:playBodyEffectByName("acchorse", true);
			rocket:playSound("ent.3803.drop", 1.0, 1.0);
		end
	end
	return true, 2;
end

function MagicWand_OnUse(player, world, x, y, z, dir)
	local index = player:getCurShortcut() + player:getShortcutStartIndex()
	if not WorldMgr:isGodMode() then
		ReplaceGridItem(player, index, ITEM_SMALL_GLASS_BOTTLE);
	end

	return player:useMagicWand(), 2;
end

function Vehicle_OnUse(player, world, x, y, z, dir)
	if VehicleMgr then
		VehicleMgr:createVehicleWithItem(player, x, y, z, dir, player:getCurShortcut() + player:getShortcutStartIndex())
	end
	return true, 2;
end

function CurItemStatistics(itemid,playerid)
	
	--连接器
	if itemid == ITEM_VEHICLE_LINK_TOOL then
		local gameType = AccountManager:getMultiPlayer() > 0 and "multi" or "single"

		local ownMap = "0";
		local worldDesc = AccountManager:getCurWorldDesc();
		if AccountManager:getMultiPlayer() == 0 or IsRoomOwner() then
			if worldDesc and worldDesc.realowneruin == AccountManager:getUin() then
				ownMap = "1";
			end
		end

		local staTime = getkv("last_use_VehicleLinkTool_timeStatistics") or 0
   		local nowTime = os.date("%d", AccountManager:getSvrTime())
   		if nowTime ~= staTime then
   			-- statisticsGameEventNew(62000, ownMap, gameType)
   			setkv("last_use_VehicleLinkTool_timeStatistics", nowTime)
   		end
	end
end
--微缩组合装置
function CustomModelPackingTool_OnUse(player, world, x, y, z, dir)
	Log("CustomModelPackingTool_OnUse ======================================")
	--玩法模式屏蔽
	if WorldMgr and not WorldMgr:isGodMode() then
		ShowGameTips(GetS(16016))
		return false, 2;
	end

	local itemid = player:getCurToolID();
	-- 地图编辑
	if itemid == ITEM_CUSTOMMODELPACKING_TOOL and player:isPlayerControl() then
		if  not CustomModelPacking:isStartPacking() then
			--打开微缩打包界面
			local param = {disableOperateUI = true};
			if GetInst("MiniUIManager"):GetCtrl("PackingCM") then
				local ctrl = GetInst("MiniUIManager"):GetCtrl("PackingCM")
				ctrl:OnShow()
			else
				GetInst("MiniUIManager"):OpenUI("packingCM", "miniui/miniworld/ugc_packingCM", "PackingCMAutoGen", {disableOperateUI = true})
				--local ctrl = GetInst("MiniUIManager"):GetCtrl("MiniuiPackingCM")
				--ctrl:OnShow()
			end
		else
			CustomModelPacking:excuteCmdWithRBClicked();
		end  
	end
	return true, 2;
end

--道具放在方块上的接口
function ItemPlace_OnBlock(itemId)
	Log("===========ItemPlace_OnBlock=======:"..itemId)
end

function MonsterSummoner_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end

	local totemid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, totemid)
	local sx,sy,sz = NeighborBlock(nx,ny,nz,dir)

	if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end

	local updata = player:getCurPlaceDir()
	world:setBlockAll(nx,ny+2,nz, totemid, updata + 8)
	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end
function MonsterSummoner_OnCreate(world, blockid, x, y, z, dir)
	local updata=dir
	world:setBlockAll(x,y+2,z, blockid, updata + 8)
	world:setBlockAll(x,y+1,z, blockid, updata + 4)
	world:setBlockAll(x,y,z, blockid, updata)
	return true
end

--使用领地
function Territory_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end

	local totemid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, totemid)
	local sx,sy,sz = NeighborBlock(nx,ny,nz,dir)

	if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end

	local updata = player:getCurPlaceDir()
	--注意这里的顺序，耕地的时候会notify到附近的方块，顺序反过来的话会导致放置的图腾消失
	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--使用村庄图腾
function VillageTotem_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end

	local totemid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, totemid)
	local sx,sy,sz = NeighborBlock(nx,ny,nz,dir)

	if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end

	local updata = player:getCurPlaceDir()
	--注意这里的顺序，耕地的时候会notify到附近的方块，顺序反过来的话会导致放置的图腾消失
	world:setBlockAll(nx,ny+2,nz, totemid, updata + 8)
	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end
function VillageTotem_OnCreate(world, blockid, x, y, z, dir)
	local updata=dir
	world:setBlockAll(x,y+2,z, blockid, updata + 8)
	world:setBlockAll(x,y+1,z, blockid, updata + 4)
	world:setBlockAll(x,y,z, blockid, updata)
	return true
end

--使用村民旗帜
function VillagerFlag_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end

	local totemid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, totemid)
	local id = world:getBlockID(x,y,z)
	if id == 115 then
		--if totemid == 150004 or totemid == 150005 then
			world:setBlockAll(x,y,z,0,0)
			ny = ny-1
		--end
	end
    if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end

	local updata = player:getCurPlaceDir()

	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function VillagerFlag_OnCreate(world, blockid, x, y, z, dir)
	local updata=dir
	world:setBlockAll(x,y+1,z, blockid, updata + 4)
	world:setBlockAll(x,y,z, blockid, updata)
	return true
end

--创建横向2个方块大小的模型
function SetTwoBlockSizeData(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir()
	if DIR_POS_Y == placedir or DIR_NEG_Y == placedir then
		local eee = 1;
	end
	nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 墙壁上横向两个方块大小的模型（壁画使用）
function SetTwoBlockSizeDataInWall(player, world, x,y,z,dir)
	if dir == DIR_POS_Y or dir == DIR_NEG_Y then return true, 1 end

    -- 检测放置位置正对方块是否可放置
	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

    -- 检测右边一个方块位置是否可以放置
	placedir = player:getCurPlaceDir()
	nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 墙壁上四个方块正方向大小的模型（壁画是一个模型，四个方块大小）
function SetFourBlockSizeDataSquareInWall(player, world, x,y,z,dir)
	if dir == DIR_POS_Y or dir == DIR_NEG_Y then return true, 1 end

    -- 检测放置位置正对方块是否可放置
	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end
    -- 检测上方一格方块位置是否可以放置
	if not world:canPlaceBlockAt(nx,ny + 1,nz,blockid, player) then return true, 1 end

    -- 检测右边一个方块位置是否可以放置
	placedir = player:getCurPlaceDir()
	nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end
    -- 检测上方一格方块位置是否可以放置
	if not world:canPlaceBlockAt(nx2,ny2 + 1,nz2,blockid, player) then return true, 1 end

    world:setBlockAll(nx2,ny2 + 1,nz2, blockid, placedir + 12)
    world:setBlockAll(nx,ny + 1,nz, blockid, placedir + 8)
    world:setBlockAll(nx2,ny2,nz2, blockid, placedir + 4)
	world:setBlockAll(nx,ny,nz, blockid, placedir)
    
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 墙壁上横向两个方块大小的模型（牌匾使用）
function SetTwoBlockSizeDataInWallLeft(player, world, x,y,z,dir)
	if dir == DIR_POS_Y or dir == DIR_NEG_Y then return true, 1 end

    -- 检测放置位置正对方块是否可放置
	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

    -- 检测左边一个方块位置是否可以放置
	nx2, ny2, nz2 = LeftOnPlaceDir(nx,ny,nz, dir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end
	player:placeBlock(blockid, nx, ny, nz, ReverseDirection(dir))
	world:setBlockAll(nx2,ny2,nz2, blockid, ReverseDirection(dir)+4)
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--上下方块
function SetBlockUpAndDown(player, world, x,y,z, dir)

	if dir ~= DIR_POS_Y then return true, 1 end

	local windowid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, windowid)
	if not world:canPlaceBlockAt(nx,ny,nz,windowid, player) then return true, 1 end

	local placedir = player:getCurPlaceDir()


	world:setBlockAll(nx,ny+1,nz, windowid, placedir + 4)
	world:setBlockAll(nx,ny,nz, windowid, placedir)

	player:playBlockPlaceSound(windowid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--放置珊瑚贝壳吊灯方块
function SetBlockUpAndDown2(player, world, x,y,z, dir)
	-- if dir ~= DIR_POS_Y then return true, 1 end

	local windowid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, windowid)
	local placedir = player:getCurPlaceDir()
	if not world:canPlaceBlockAt(nx,ny,nz,windowid, player) then return true, 1 end
	if not world:canPlaceBlockAt(nx,ny + 1,nz,windowid, player) and not world:canPlaceBlockAt(nx,ny - 1,nz,windowid, player) then
		return true, 1
	end
	if world:canPlaceBlockAt(nx,ny + 1,nz,windowid, player) then 
		world:setBlockAll(nx,ny,nz, windowid, placedir)
		world:setBlockAll(nx,ny + 1,nz, windowid, placedir + 4)
	else
		world:setBlockAll(nx,ny - 1,nz, 1378, placedir + 4)
		world:setBlockAll(nx,ny,nz, 1378, placedir)
	end
	player:playBlockPlaceSound(windowid, nx, ny, nz)
	player:shortcutItemUsed()

	return true, 0;
end

function SetFourBlockSizeRectangle(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end
	
	local blockid = player:getCurToolID();
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir();
	if DIR_POS_Y == placedir or DIR_NEG_Y == placedir then
		placedir = DIR_POS_X;
	end
	nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end
	if not world:canPlaceBlockAt(nx2,ny2 + 1,nz2,blockid, player) or not world:canPlaceBlockAt(nx,ny+1,nz,blockid, player) then return true, 1 end
	if blockid == 1234 then
		if not world:canPlaceBlockAt(nx2,ny2 + 2,nz2,blockid, player) or not world:canPlaceBlockAt(nx,ny+2,nz,blockid, player) then return true, 1 end
	end
	world:setBlockAll(nx,ny,nz, blockid, placedir + 8)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir + 8 + 4)
	world:setBlockAll(nx2,ny2+1,nz2, blockid, placedir + 4)
	world:setBlockAll(nx,ny+1,nz, blockid, placedir)
	if blockid == 1234 then
		world:setBlockAll(nx2,ny2+2,nz2, blockid, placedir + 4)
		world:setBlockAll(nx,ny+2,nz, blockid, placedir)
	end
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0
end

function SetFourBlockSizeRectangle_OnCreate(world, blockid, x, y, z, placedir)
	local nx,ny,nz = PlaceBlockOnUse(nil, world, x,y,z, placedir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, nil) then return false end
	if DIR_POS_Y == placedir or DIR_NEG_Y == placedir then
		placedir = DIR_POS_X;
	end
	local nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, nil) then return false end
	if not world:canPlaceBlockAt(nx2,ny2 + 1,nz2,blockid, nil) or not world:canPlaceBlockAt(nx,ny+1,nz,blockid, nil) then return false end
	if blockid == 1234 then
		if not world:canPlaceBlockAt(nx2,ny2 + 2,nz2,blockid, nil) or not world:canPlaceBlockAt(nx,ny+2,nz,blockid, nil) then return false end
	end
	world:setBlockAll(nx,ny,nz, blockid, placedir + 8)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir + 8 + 4)
	world:setBlockAll(nx2,ny2+1,nz2, blockid, placedir + 4)
	world:setBlockAll(nx,ny+1,nz, blockid, placedir)
	if blockid == 1234 then
		world:setBlockAll(nx2,ny2+2,nz2, blockid, placedir + 4)
		world:setBlockAll(nx,ny+2,nz, blockid, placedir)
	end
	return true
end
--长3格，宽2格，高1格
function SetLong3Width2Height1BlockSize(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir()

	-- 右
	local nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	local wx1,wy1,wz1 = NeighborBlock(nx,ny,nz, ReverseDirection( placedir))
	if not world:canPlaceBlockAt(wx1,wy1,wz1,blockid, player) then return true, 1 end

	local wx2,wy2,wz2 = NeighborBlock(nx2, ny2, nz2, ReverseDirection( placedir))
	if not world:canPlaceBlockAt(wx2,wy2,wz2,blockid, player) then return true, 1 end

	local wx3,wy3,wz3 = NeighborBlock(wx1,wy1,wz1, ReverseDirection( placedir))
	if not world:canPlaceBlockAt(wx3,wy3,wz3,blockid, player) then return true, 1 end

	local wx4,wy4,wz4 = NeighborBlock(wx2,wy2,wz2, ReverseDirection( placedir))
	if not world:canPlaceBlockAt(wx4,wy4,wz4,blockid, player) then return true, 1 end
	print("place dir ", placedir)
	world:setBlockAll(nx,ny,nz, blockid, 1)
	world:setBlockAll(nx2,ny2,nz2, blockid, 2)
	world:setBlockAll(wx1,wy1,wz1, blockid, 3)
	world:setBlockAll(wx2,wy2,wz2, blockid, 4)
	world:setBlockAll(wx3,wy3,wz3, blockid, 5)
	world:setBlockAll(wx4,wy4,wz4, blockid, 6)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--长2格，宽1格，高1格（地毯）
function SetLong2Width1Height1BlockSize(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir()

	local wx1,wy1,wz1 = NeighborBlock(nx,ny,nz, ReverseDirection( placedir))
	if not world:canPlaceBlockAt(wx1,wy1,wz1,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(wx1,wy1,wz1, blockid, placedir + 4)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--长3格，宽1格，高2格
function SetLong3Width1Height2BlockSize(player, world, x,y,z,dir)

	if dir ~= DIR_POS_Y then return true, 0; end

	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 0; end

	placedir = player:getCurPlaceDir()

	local nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 0; end

	local nx3, ny3, nz3 = RightOnPlaceDir(nx2, ny2, nz2, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx3, ny3, nz3,blockid, player) then return true, 0; end

	local wx1,wy1,wz1 = NeighborBlock(nx,ny,nz,dir)
	if not world:canPlaceBlockAt(wx1,wy1,wz1,blockid, player) then return true, 0; end

	local wx2,wy2,wz2 = NeighborBlock(nx2, ny2, nz2, dir)
	if not world:canPlaceBlockAt(wx2,wy2,wz2,blockid, player) then return true, 0; end

	local wx3,wy3,wz3 = NeighborBlock(nx3, ny3, nz3, dir)
	if not world:canPlaceBlockAt(wx3,wy3,wz3,blockid, player) then return true, 0; end
	world:setBlockAll(wx1,wy1,wz1, blockid, placedir)
	world:setBlockAll(wx2,wy2,wz2, blockid, placedir)
	world:setBlockAll(wx3,wy3,wz3, blockid, placedir)

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir)
	world:setBlockAll(nx3, ny3, nz3, blockid, placedir)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

--底两格，
--- []
--- [][]
---
function BlockHuaXiaoLou_OnUse(player, world, x,y,z, dir)
	if dir ~= DIR_POS_Y then return true end

	local placedir = player:getCurPlaceDir()
	local blockid = player:getCurToolID()
	local nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 0; end

	local nx2, ny2, nz2 = NeighborBlock(nx,ny,nz,ReverseDirection((placedir)))--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 0; end

	local nx3, ny3, nz3 = NeighborBlock(nx2, ny2, nz2, dir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx3, ny3, nz3,blockid, player) then return true, 0; end

	--local wx1,wy1,wz1 = NeighborBlock(nx,ny,nz,dir)
	--if not world:canPlaceBlockAt(wx1,wy1,wz1,blockid, player) then return true, 0; end

	world:setBlockAll(nx,ny,nz, blockid, placedir + 4 + 8)
	--world:setBlockAll(wx1,wy1,wz1, blockid, placedir + 4 )

	world:setBlockAll(nx2,ny2,nz2, blockid, placedir + 8)
	world:setBlockAll(nx3, ny3, nz3, blockid, placedir)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()

	return true, 0;
end

--使用台阶
function HalfCabinet_OnUse(player, world, x,y,z,dir)
	local ret = false
	slabid = player:getCurToolID()
	local placedir = player:getCurPlaceDir();
	if world:getBlockID(x,y,z) == slabid then
		blockdata = world:getBlockData(x,y,z)
		local modelID = math.floor(blockdata / 4)
		if (dir==DIR_POS_Y and modelID==0) or (dir==DIR_NEG_Y and modelID==1) then
			world:setBlockData(x,y,z, placedir + 8)
			player:playBlockPlaceSound(slabid, x, y, z)
			player:shortcutItemUsed()
			return true, 0;
		end
	end

	nx, ny, nz = PlaceBlockOnUse(player, world, x,y,z, dir, slabid)
	curid = world:getBlockID(nx,ny,nz)
	if curid == slabid then
		blockdata = world:getBlockData(nx,ny,nz)
		local modelID = math.floor(blockdata / 4)
		if modelID==0 or modelID==1 then
			world:setBlockData(nx,ny,nz, placedir + 8)
			player:playBlockPlaceSound(slabid, nx, ny, nz)
			player:shortcutItemUsed()
			return true, 0;
		end
	end
	return ret, 1;
end

--宠物蛋使用
function PetEgg_OnUse(player, world, x, y, z, dir)
	if IsMyHomeMap and IsMyHomeMap() then
		local function callback(ret, userdata)
			--生成宠物
			if ret and ret.data then
				local functionIdCfgEnum = GetInst("HomeLandConfig").define.functionIdCfg
				for _, v in pairs(ret.data or {}) do
					if v.ret == 0 then
						local findGetPet = GetInst("HomeLandConfig"):findHomeItemFunctionInfo(userdata.itemId, functionIdCfgEnum.GetPet) --使用道具得到一个宠物
						if findGetPet then
							player:shortcutItemUsed()
							if v.data and v.data.petData and next(v.data.petData) then
								local petName = "" --41490 获得了宠物@1，请前往宠物窝查看
								local petDef = DefMgr:getPetDef(v.data.petData.pet_id, v.data.petData.pet_state,v.data.petData.pet_quality)
								if petDef then
									local monsterDef = DefMgr:getMonsterDef(petDef.MonsterID)
                                    if monsterDef then
										petName = v.data.petData.examine_state and v.data.petData.pet_name or monsterDef.Name
										HomelandCallModuleScript("HomelandPetModule","spawnPet",monsterDef.ID,
												v.data.petData.pet_server_id,
												petName,
												false,
												petDef.PetID,
												petDef.Stage,
												petDef.PetQuality,
												x ,
												y+1 ,
												z)
										HomelandCallModuleScript("HomelandPetModule","updateHomelandPetData",v.data.petData.pet_server_id,
												petName,
												monsterDef.ID,
												petDef.PetID,
												petDef.Stage,
												petDef.PetQuality,
												x ,
												y+1 ,
												z,
												false)
										HomelandCallModuleScript("HomelandCommonModule","saveHomeLandFBS")
									end
								end
								ShowGameTipsWithoutFilter(GetS(41490, petName))
							end
						end
					end
				end
			end
		end
		local itemId = player:getCurToolID()
		local iteminfo = GetInst("HomeLandDataManager"):GetBackpackItemData(itemId)
		local sid = 0
		if iteminfo and #iteminfo > 0 then
			sid = iteminfo[1].goods_id

			-- 没上报客户端埋点 就清空上一次的log_id 和 scene_id
			resetLogIdAndSceneId()
			
			GetInst("HomeLandService"):ReqUsePlayingItem(itemId, sid, 1, callback)
		end
	end
	return true, 2;
end

--家园胡杨茶几 2*1*1 茶几
function Homeland_TeaTable_OnUse(player, world, x, y, z, dir)
	if dir ~= DIR_POS_Y then return true, 1 end

	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end

	placedir = player:getCurPlaceDir()
	nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)--NeighborBlock(nx,ny,nz, ReverseDirection(placedir))
	if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end

	world:setBlockAll(nx,ny,nz, blockid, placedir)
	world:setBlockAll(nx2,ny2,nz2, blockid, placedir+4)
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 家园雕塑使用（两个1*2*1：1747 1749 一个1*2*2：1748）
function HomelandSculpture_OnUse(player, world, x, y, z, dir)
    if dir ~= DIR_POS_Y then return true, 1 end

    -- 检测放置位置正对方块是否可放置
	local blockid = player:getCurToolID()
	nx,ny,nz = PlaceBlockOnUse(player, world, x,y,z, dir, blockid)
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return true, 1 end
    -- 检测上方一格方块位置是否可以放置
	if not world:canPlaceBlockAt(nx,ny + 1,nz,blockid, player) then return true, 1 end

    placedir = player:getCurPlaceDir()
    -- 九层塔雕塑
    if blockid == 1748 then
        -- 检测右边一个方块位置是否可以放置
        nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)
        if not world:canPlaceBlockAt(nx2,ny2,nz2,blockid, player) then return true, 1 end
        -- 检测上方一格方块位置是否可以放置
        if not world:canPlaceBlockAt(nx2,ny2 + 1,nz2,blockid, player) then return true, 1 end

        world:setBlockAll(nx2,ny2 + 1,nz2, blockid, placedir + 12) 
        world:setBlockAll(nx2,ny2,nz2, blockid, placedir + 4)
    end

    world:setBlockAll(nx,ny + 1,nz, blockid, placedir + 8)
    world:setBlockAll(nx,ny,nz, blockid, placedir)
    
	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 创造锤工具对应的创造锤方块
local hammerTool2Block = {
	[11613] = 781,
	[11614] = 782,
	[11615] = 783,
	[11616] = 784,
}

-- 创造锤右键使用
function Hammer_OnUse(player, world, x, y, z, dir)
	if dir == DIR_NEG_Y then return true, 1 end

	local toolid = player:getCurToolID()

	local blockid = hammerTool2Block[ toolid ]
	if not blockid then return true, 1 end

	local nx,ny,nz = NeighborBlock(x,y,z,dir)
    if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then
		return true, 1
	end

	local placedir = player:getCurPlaceDir()
	if placedir < DIR_NEG_X or placedir > DIR_POS_Z then
		placedir = DIR_NEG_X
	end

	-- 放墙上
	if dir ~= DIR_POS_Y then
		placedir = placedir + 4
	end
	world:setBlockAll(nx,ny,nz, blockid, placedir)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

-- 创造锤点击方块对应音效
local hammer2sound = {
	[ 11613 ] = { 	-- 石质创造锤 -- C2 ~ B2
		[ 100 ] 		= "music.instrument.C2_1",	-- 草快
		[ 104 ] 		= "music.instrument.C2_2",	-- 岩石
		[ 106 ] 		= "music.instrument.C2_3",	-- 细沙块
		[ 123 ] 		= "music.instrument.C2_4",	-- 冰块
		[ 114 ] 		= "music.instrument.C2_5",	-- 灰砂土
		[ 124 ] 		= "music.instrument.C2_6",	-- 硫磺岩
		[ 116 ] 		= "music.instrument.C2_7",	-- 萌眼星石块
		[ "default" ] 	= "music.note.drum8",
	},
	[ 11614 ] = { 	-- 黄铜创造锤 -- C3 ~ B3
		[ 100 ] 		= "music.instrument.C3_1",
		[ 104 ] 		= "music.instrument.C3_2",
		[ 106 ] 		= "music.instrument.C3_3",
		[ 123 ] 		= "music.instrument.C3_4",
		[ 114 ] 		= "music.instrument.C3_5",
		[ 124 ] 		= "music.instrument.C3_6",
		[ 116 ] 		= "music.instrument.C3_7",
		[ "default" ] 	= "music.note.drum8",
	},
	[ 11615 ] = { 	-- 铸铁创造锤 -- C4 ~ B4
		[ 100 ] 		= "music.instrument.C4_1",
		[ 104 ] 		= "music.instrument.C4_2",
		[ 106 ] 		= "music.instrument.C4_3",
		[ 123 ] 		= "music.instrument.C4_4",
		[ 114 ] 		= "music.instrument.C4_5",
		[ 124 ] 		= "music.instrument.C4_6",
		[ 116 ] 		= "music.instrument.C4_7",
		[ "default" ] 	= "music.note.drum8",
	},
	[ 11616 ] = { 	-- 钛金创造锤 -- C5 ~ B5
		[ 100 ] 		= "music.instrument.C5_1",
		[ 104 ] 		= "music.instrument.C5_2",
		[ 106 ] 		= "music.instrument.C5_3",
		[ 123 ] 		= "music.instrument.C5_4",
		[ 114 ] 		= "music.instrument.C5_5",
		[ 124 ] 		= "music.instrument.C5_6",
		[ 116 ] 		= "music.instrument.C5_7",
		[ "default" ] 	= "music.note.drum8",
	},
}

-- 创造锤左键点击方块 播放音效
function Hammer_OnClickBlock(player, itemid, blockid, x, y, z)
	if not itemid or not blockid or not hammer2sound[ itemid ] then
		return
	end
	local soundPath = hammer2sound[ itemid ][ blockid ] or hammer2sound[ itemid ][ "default" ]
	if ActorComponentCallModule then
		ActorComponentCallModule(player,"SoundComponent","playSound",soundPath, 1.0, 1.0)
	else
		player:playSound(soundPath, 1.0, 1.0)
	end
end

--全息显示
function Holographic_OnUse(player, world, x, y, z, dir)
	local itemID = player:getCurToolID()
	if itemID == 12011 then
		-- GetInst("UIManager"):Open("HolographicMainmenu")
		GetInst("UIManager"):Open("HolographicCartoon") --第一期只显示漫画，不需要菜单选择
	else
		return false
	end
	return true
end

-- --三角门道具使用
function SanJiaoMen_OnUse(player, world, x, y, z, dir)

	if not CurMainPlayer or not player then
		return false
	end
	if CurMainPlayer:getObjId() ~= player:getObjId() then
		return  false
	end

	local srcStarStationID = 0
	local destMapID =  world and world:getCurMapID() or 0
	local destStarStationList = GetInst("StarStationManager"):GetDestStarStationTransferList(srcStarStationID, destMapID)

	local list = {}
	for index, station in ipairs(destStarStationList) do
		if station.starStationID > 0 then
			table.insert(list, station)
		end
	end

	if #list == 0 then
		ShowGameTips(GetS(21497), 3)
		return false
	end 
	local param = {destMapID=destMapID, srcStarStationID=srcStarStationID, destStarStationList=list,isSanjiaomen = true}
	-- if GetInst("UIManager"):GetCtrl("StarStationTransfer") then
		GetInst("StarStationManager"):openStarStationView(2,param)
	-- local ctrl = GetInst("UIManager"):GetCtrl("StarStationTransfer")
	
	return false
end


local balalaSkinBuffID = { -- 已购买皮肤使用
	[12301] = 98, 
	[12302] = 98, 
	[12303] = 98, 
	[12304] = 98,
	[12305] = 98,
	[12306] = 98,
	[12307] = 98,
}
local balalaSkinBuffLevel = {
	[12301] = 2, 
	[12302] = 3, 
	[12303] = 4, 
	[12304] = 5,
	[12305] = 6,
	[12306] = 7,
	[12307] = 8,		
}
local buffIDNoSkin = {
	[12301] = 98, 
	[12302] = 98, 
	[12303] = 98, 
	[12304] = 98,
	[12305] = 98,
	[12306] = 98,
	[12307] = 98,	} -- 没购买皮肤使用
local buffIDDress = {
	[12301] = 98, 
	[12302] = 98, 
	[12303] = 98, 
	[12304] = 98,
	[12305] = 98,
	[12306] = 98,
	[12307] = 98,	}  -- 已穿上皮肤使用
local balalaSkinId = {
	[12301] = 141, 
	[12302] = 143, 
	[12303] = 144, 
	[12304] = 145,
	[12305] = 156,
	[12306] = 155,
	[12307] = 154,
}
local isBalalaSkinID ={
	[141] = 1,
	[143] = 1,
	[144] = 1,
	[145] = 1,
	[156] = 1,
	[155] = 1,
	[154] = 1,
}
local balalaTipStr = {
	[12301] = 40806, 
	[12302] = 40807, 
	[12303] = 40808, 
	[12304] = 40809,
	[12305] = 40810,
	[12306] = 40811,
	[12307] = 40812,
}
local PLAYER_NOTIFYINFO_TIPS = 1
local soundPath = 
{
	[12301] = "buff.buff_balala_xiaolan",
	[12302] = "buff.buff_balala_meiqi",
	[12303] = "buff.buff_balala_meixue",
	[12304] = "buff.buff_balala_youle",
	[12305] = "buff.buff_balala_caili",
	[12306] = "buff.buff_balala_qiaoling",
	[12307] = "buff.buff_balala_xiaomin",
}
-- 巴啦啦魔法棒
function Balala_UseSkill(player, world, clientParam)
	local itemID = player:getCurToolID()
	local function HasBuff()
		if player:getLivingAttrib():hasBuff(balalaSkinBuffID[itemID], balalaSkinBuffLevel[itemID]) then
			return true
		end
		return false
	end
	--[[
	if player:getLivingAttrib():hasBuff(buffIDNoSkin[itemID], 1) then
		return true
	end]]
	local skinTime = 0
	if player == CurMainPlayer then
		skinTime = AccountManager:getAccountData():getSkinTime(balalaSkinId[itemID])
	else
		local skinTimeJson = JSON:decode(clientParam)
		skinTime = skinTimeJson["skinTime"]
	end

	if skinTime == 0 then -- 没购买
		player:getLivingAttrib():addBuff(buffIDNoSkin[itemID], 1)
		player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, balalaTipStr[itemID])
		if ActorComponentCallModule then
			ActorComponentCallModule(player,"SoundComponent","playSound",soundPath[itemID], 1, 1)
		else
			player:playSound(soundPath[itemID], 1, 1)
		end
	else
		local curSkinID = player:getBody():getSkinID()
		if balalaSkinId[itemID] == curSkinID then	--已穿戴
			player:getLivingAttrib():addBuff(buffIDDress[itemID], 1)
			player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 40805)
			if ActorComponentCallModule then
				ActorComponentCallModule(player,"SoundComponent","playSound",soundPath[itemID], 1, 1)
			else
				player:playSound(soundPath[itemID], 1, 1)
			end

		else
			player:getLivingAttrib():addBuff(balalaSkinBuffID[itemID], balalaSkinBuffLevel[itemID])
		end
	end
	player:playAnim(0)
end

function GetBalalaSkinTime(itemID)
	local skinTime = AccountManager:getAccountData():getSkinTime(balalaSkinId[itemID])
	return JSON:encode({itemID = itemID, skinTime = skinTime})
end

function Erhu_OnUse(player, world, x,y,z,dir)
	PlayErhuEffect(player)
	return true,0
end

function OpenMiniClub(player, world, x, y, z, dir)
	print ("---wzlog---OpenMiniClub start")
	
	-- 非编辑模式不能设置
	if CurWorld and not CurWorld:isGameMakerMode() then 
		print ("---wzlog---OpenMiniClub not isGameMakerMode")
		
		return false
	end 
		
	-- 联机模式不能设置
    if AccountManager and AccountManager:getMultiPlayer() > 0 then
		print ("---wzlog---OpenMiniClub getMultiPlayer > 0")
		
        return false
	end
	
	
	-- 工具模式不能设置
	if CurWorld and CurWorld:isGameMakerToolMode() then
		print ("---wzlog---OpenMiniClub isGameMakerToolMode")
		
		return false
	end
	
	print ("---wzlog---OpenMiniClub AddMiniClubPos start")
	
	-- 缓存音乐方块坐标
	local blockId = string.format("%s_%s_%s_miniclub", x, y, z)
	GetInst("MiniClubInterface"):AddMiniClubPos(blockId, x, y, z, dir)
	
	print ("---wzlog---OpenMiniClub AddMiniClubPos end")
	
	GetInst("MiniUIManager"):CloseUI("MiniClubAreaSetAutoGen")  
	GetInst("MiniUIManager"):AddPackage(
		{
			"miniui/miniworld/common", 
			"miniui/miniworld/common_comp"
		}, 
		"MiniClubAreaSetAutoGen"
	)
	GetInst("MiniUIManager"):OpenUI(
		"MiniClubAreaSet", 
		"miniui/miniworld/musicclub", 
		"MiniClubAreaSetAutoGen"
	)
	
	print ("---wzlog---OpenMiniClub OpenUI end")
	
	return true
end

function MiniClub_OnUse(player, world, x, y, z, dir)
	if CurWorld and CurWorld:isGameMakerToolMode() then
		print ("---wzlog---MiniClub_OnUse isGameMakerToolMode")
		
		return false, 0
	end
	
	if if_can_use_music_block() then
		print ("---wzlog---MiniClub_OnUse if_can_use_music_block 1")
	
		local curtool = player:getCurToolID()
		local placedir = player:getCurPlaceDir()

		local nx, ny, nz = NeighborBlock(x, y, z, dir)
		local nx2, ny2, nz2 = RightOnPlaceDir(nx, ny, nz, placedir)
		
		print ("---wzlog---MiniClub_OnUse if_can_use_music_block 2")
		
		if world:getBlockID(nx2, ny2, nz2) == curtool then
			return false, 0;
		end 
		
		print ("---wzlog---MiniClub_OnUse if_can_use_music_block 3")

		local data1, data2 = placedir, placedir + 4;
		world:setBlockAll(nx, ny, nz, curtool, data1)
		world:setBlockAll(nx2, ny2, nz2, curtool, data2)	

		player:playBlockPlaceSound(curtool, nx, ny, nz)
		player:shortcutItemUsed()
		
		print ("---wzlog---MiniClub_OnUse if_can_use_music_block 4")
		
		OpenMiniClub(player, world, nx, ny, nz, placedir)
		
		print ("---wzlog---MiniClub_OnUse if_can_use_music_block 5")
	
		return false, 0;
	end
	
	print ("---wzlog---MiniClub_OnUse if_can_use_music_block false")
	
	return true, 0;	
end

function CloudPortal_OnUse(player, world, x, y, z, dir)
	local item_canused = true --if_can_use_cportal_block();
	if item_canused then
		local curtool = player:getCurToolID()
		local placedir = player:getCurPlaceDir()

		local nx, ny, nz = NeighborBlock(x,y,z,dir)
		local nx2, ny2, nz2;
		nx2, ny2, nz2 = RightOnPlaceDir(nx,ny,nz, placedir)
		
		local data1, data2 = placedir, placedir + 4;
		world:setBlockAll(nx,ny,nz, curtool, data1)
		world:setBlockAll(nx2,ny2,nz2, curtool, data2)	
		
		player:playBlockPlaceSound(curtool, nx, ny, nz)
		player:shortcutItemUsed()

		return false, 0;
	end
	
	return true, 0;	
end

--使用仙人掌种子
function CactusSeed_OnUse(player, world, x, y, z, dir)
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, 458, player) then
		world:setBlockAll(x,y+1,z, 458, 0)
		if player then
			player:shortcutItemUsed()
		end
		return true, 0;
	end
	return false, 1;
end

-- 使用胡杨树种子
function PopulusSeed_OnUse(user, world, x, y, z, dir)
	return Seed_OnUse(user, world, x,y,z,dir, 476)
end

function OstrichEgg_OnUse(player, world, x, y, z, dir)

	local id = world:getBlockID(x, y, z)
	if id == 1019 then
		world:destroyBlock(x, y, z, false)
		world:setBlockAll(x,y,z, 462, 0)
		player:shortcutItemUsed();
	end


	return true, 0;
end

function Leather_OnUse(player, world, x, y, z, dir)
	local repairOk = WorldCanvas:repair(player,world,x,y,z);
	if repairOk then
		player:shortcutItemUsed();
		return true,0;
	end
	return false,0;
end

function Canvas_OnUse(player, world, x, y, z, dir)

	local itemid = player:getCurToolID();

	local stage = 0;
	if itemid == 11912 then
		stage = 1;
	elseif itemid == 11913 then
		stage = 2;
	else
		stage = 0;
	end

	player:placeBlock(484, x, y+1, z, DIR_NEG_Y, 0, 0, 0,false,false,stage);

	return true, 0;
end

-- 珊瑚虫使用
function NewCoralPolyp_OnUse(player, world, x, y, z, dir)
	-- 种植
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, BLOCK_CORAL_LARVA, player) then
		world:setBlockAll(x,y + 1,z, BLOCK_CORAL_LARVA, math.random(0, 3))
		player:playBlockPlaceSound(BLOCK_CORAL_LARVA, x, y + 1, z)
		player:shortcutItemUsed()
		return true, 0
	end

	local id = world:getBlockID(x, y, z)
	-- 复活白化珊瑚
	if id == BLOCK_HORN_CORAL_BLEACHING or id == BLOCK_BUBBLE_CORAL_BLEACHING 
		or id == BLOCK_TURBAN_CORAL_BLEACHING or id == BLOCK_TREE_CORAL_BLEACHING then
		local blockdata = world:getBlockData(x, y, z)
		world:setBlockAll(x, y, z, id - 1, blockdata)
		player:shortcutItemUsed()
		return true, 0
	end

	return false, 1
end

-- 放置珊瑚幼体
function NewCoralLarva_OnUse(player, world, x, y, z, dir)
	-- 种植
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, BLOCK_CORAL_LARVA, player) then
		world:setBlockAll(x,y + 1,z, BLOCK_CORAL_LARVA, math.random(0, 3))
		player:playBlockPlaceSound(BLOCK_CORAL_LARVA, x, y + 1, z)
		player:shortcutItemUsed()
		return true, 0
	end

	return false, 1
end


-- 放置珊瑚
function NewCoral_OnUse(player, world, x,y,z, dir)
	local blockid = player:getCurToolID()
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y + 1,z, blockid, player) then
		-- 贴图类型
		local typ = math.random(0, 1)
		local placedir = math.random(0, 5)
		-- 朝向放在blockdata高三位
		placedir = placedir * 2
		world:setBlockAll(x, y + 1, z, blockid, placedir + typ)
		player:playBlockPlaceSound(blockid, x, y + 1, z)
		player:shortcutItemUsed()
		return true, 0
	end

	return false, 1
end

-- 放置幼体珊瑚
function NewCoralLarva_OnUse(player, world, x,y,z, dir)
	local blockid = player:getCurToolID()
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y + 1,z, blockid, player) then
		world:setBlockAll(x, y + 1, z, blockid, math.random(0, 3))
		player:playBlockPlaceSound(blockid, x, y + 1, z)
		player:shortcutItemUsed()
		return true, 0
	end

	return false, 1
end



--刺球撅
function SetBlockSawtooth(player, world, x,y,z, dir)

	if dir ~= DIR_POS_Y then return false, 1 end

	local windowid = player:getCurToolID()
	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	if not world:canPlaceBlockAt(nx,ny,nz,windowid, player) then return false, 1 end
	local placedir = player:getCurPlaceDir()

	--world:setBlockAll(nx,ny+1,nz, windowid, placedir + 4)
	world:setBlockAll(nx,ny,nz, windowid, placedir)

	player:playBlockPlaceSound(windowid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end
function PoseidonStatue_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end

	local totemid = player:getCurToolID()
	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end

	local updata = player:getCurPlaceDir()

	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function PoseidonStatue_OnCreate(world, blockid, x, y, z, dir)
	local updata=dir
	world:setBlockAll(x,y+1,z, blockid, updata + 4)
	world:setBlockAll(x,y,z, blockid, updata)
	return true
end


function PoseidonStatueBad_OnUse(player, world, x,y,z,dir)
	if dir ~= DIR_POS_Y then return false, 1 end	
	local totemid = player:getCurToolID()
	local nx,ny,nz = NeighborBlock(x,y,z,dir)

	if not world:canPlaceBlockAt(nx,ny,nz,totemid, player) then return false, 1 end


	local updata = player:getCurPlaceDir()
	
	world:setBlockAll(nx,ny+1,nz, totemid, updata + 4)
	world:setBlockAll(nx,ny,nz, totemid, updata)

	player:playBlockPlaceSound(totemid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function PoseidonStatueBad_OnCreate(world, blockid, x, y, z, dir)
	local updata=dir
	world:setBlockAll(x,y+1,z, blockid, updata + 4)
	world:setBlockAll(x,y,z, blockid, updata)
	return true
end

--椰子
function Cocount_OnUse(player, world, x,y,z, dir)	
	if dir ~= DIR_POS_Y then return false, 1 end

	local nx, ny, nz = NeighborBlock(x, y, z, dir)
	if not player then
		if world:canPlaceBlockAt(nx, ny, nz, 292) then
			world:setBlockAll(nx, ny, nz, 292, 0)
			return true, 0;
		else
			return false, 1;
		end
	end
	
	local windowid = player:getCurToolID()
	print("Cocount_OnUse",windowid)
	if not world:canPlaceBlockAt(nx,ny,nz,windowid, player) then return false, 1 end
	local placedir = player:getCurPlaceDir()
	world:setBlockAll(nx,ny,nz, 292, 0)
	print("Cocount_OnUs111e",windowid)

	player:playBlockPlaceSound(292, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function UseSeaSpiritStone(player, world, x, y, z, dir)
	if not player or not world then return true, 0 end
	player:getLivingAttrib():addBuff(1025, 1)
	player:setSkillCD(ITEM_SEA_SPIRIT_STONE, 30)
	player:syncSkillCD(ITEM_SEA_SPIRIT_STONE, 30)
	if not WorldMgr:isGodMode() then
		local bp = player:getBackPack()
		local index = player:getCurShortcut() + player:getShortcutStartIndex()
		bp:removeItem(index, 1)
		player:consumeItemOnTrigger(ITEM_WOODEN_BUCKET, 1)
		player:updateTaskSysProcess(TASKSYS_USE_ITEM, ITEM_SEA_SPIRIT_STONE)
	end
	TransformStoneUseReport()
	return true, 0

end
function Grayleaf_OnUse(player, world, x,y,z, dir)

	--if dir ~= DIR_POS_Y then return false, 1 end
	
	local id = player:getCurToolID()
	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	local placedir = player:getCurPlaceDir()
	local blockid = 0
	if id == ITEM_FRESH_FRUIT then
		blockid = 324
	elseif id == ITEM_OXY_FRUIT then
		blockid = 327
	elseif id == ITEM_REDWOOD then
		blockid = 328
	elseif id == ITEM_WALNUT then
		blockid = 329
	elseif id == ITEM_PEACH then
		blockid = 330
	elseif id == ITEM_POPLAR then
		blockid = 331
	end
	if not world:canPlaceBlockAt(nx,ny,nz,blockid, player) then return false, 1 end
	world:setBlockAll(nx,ny,nz, blockid, 0)

	player:playBlockPlaceSound(blockid, nx, ny, nz)
	player:shortcutItemUsed()
	return true, 0;
end

function TreasureDrift_OnUse(player, world, x, y, z, dir)
	if world and not world:isRemoteMode() then
		local grid = player:getBackPack():index2Grid(player:getCurShortcut() + player:getShortcutStartIndex())
		local dataStr = "";
		if grid then
			dataStr = grid:getUserDataStrLua();
		end
		SurviveReport_TreasureGetReport(dataStr, player:getUin(), 1);
		-- if not dataStr then
		-- 	return false, 1;
		-- end
		player:shortcutItemUsed();
		player:gainItemsUserdata(12617, 1, dataStr, 1);
		player:gainItems(11320, 1);
	end
	return true, 0;
end

function TreasureMap_OnUse(player, world, x, y, z, dir)
	local newX;
	local newZ;
	local dataStr;
	local grid = player:getBackPack():index2Grid(player:getCurShortcut() + player:getShortcutStartIndex())
	if grid then
		local str = grid:getUserdataStr();
		dataStr = str;
		if str and #str > 0 then
			local _, _, _, cx, cz = string.find(str, "^(%d?)x(-?%d*)z(-?%d*)");
			if cx and cz then
				newX = tonumber(cx);
				newZ = tonumber(cz);
			end
		end
	end
	if newX and newZ then

	else
		-- if player:isPlayerControl() then
		-- 	x = 0;
		-- 	z = 0;
		-- else
		-- 	return false, 1;
		-- end
		-- local pos = player:getPosition();
		-- local px = BlockDivSection(pos.x);
		-- local pz = BlockDivSection(pos.z);
		-- local have = false;
		-- have, x, z = CurWorld:genTreasureBox(px, pz, x, z);
		-- dataStr = string.format("0x%dz%d", x, z);
		-- grid:setUserdataStr(dataStr);
		return false, 1;
	end
	-- x = x * 16 + 0;
	-- z = z * 16 + 0;
	-- have = have and 1 or 0;
	--ClientCurGame:sendChat("have: "..have..",posx:" .. x .. ", posz:"..z, 1, 0)
	if player:isPlayerControl() and newX and newZ then
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/c_ingame"}, "TreasureMainMapAutoGen")
		GetInst("MiniUIManager"):OpenUI("TreasureMainMap", 
		"miniui/miniworld/treasureMap", "TreasureMainMapAutoGen", {x = newX, z = newZ});
		SurviveReport_TreasureGetReport(dataStr, player:getUin(), 2);
	end
	return true, 2;
end

function TerrainMap_OnUse(player, world, x, y, z, dir)
	-- LuaPanda.BP()
	local x;
	local z;
	local t;
	local dataStr;
	local grid = player:getBackPack():index2Grid(player:getCurShortcut() + player:getShortcutStartIndex())
	if grid then
		local str = grid:getUserdataStr();
		if str and #str > 0 then
			local info = JSON:decode(str)
			x = info.x
			z = info.z
			t = info.t
		end
	end
	if x and z then

	else
		-- 测试代码
		-- if player:isPlayerControl() then
		-- 	x = 0;
		-- 	z = 0;
		-- else
		-- 	return false, 1;
		-- end
		-- local pos = player:getPosition();
		-- local px = BlockDivSection(pos.x);
		-- local pz = BlockDivSection(pos.z);
		-- local have = false;
		-- x = px
		-- z = pz
		-- t = 1013
		return false, 1;
	end
	if player:isPlayerControl() and x and z then
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/treasureMap"}, "TerrainMapAutoGen")
		GetInst("MiniUIManager"):OpenUI("TerrainMap", "miniui/miniworld/adventure/", "TerrainMapAutoGen", {x = x, z = z, t= t});
	end
	return true, 2;
end

function ColorPalette_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID()
	local itemid1 = 0
	local itemid2 = 0
	local ret = 1;
	if itemid==BLOCK_COLOR_PALETTE_STAND or itemid==BLOCK_COLOR_PALETTE_HANG then
		itemid1 = BLOCK_COLOR_PALETTE_STAND
		itemid2 = BLOCK_COLOR_PALETTE_HANG

		local grid = player:getBackPack():index2Grid(player:getCurShortcut() + player:getShortcutStartIndex())
		local str = nil
		if grid then
			local tempstr = grid:getUserdataStr();
			str = tempstr
		end

		local nx,ny,nz = NeighborBlock(x,y,z,dir)
		--local face = ReverseDirection(dir)
		local face = dir
		local placedir = player:getCurPlaceDir()
		if dir == DIR_POS_Y then
			player:placeBlock(itemid1, nx, ny, nz, placedir)
			player:playBlockPlaceSound(itemid1, nx, ny, nz)
			ret = 0;
		elseif dir ~= DIR_NEG_Y then
			local blockid = world:getBlockID(x,y,z)
			if blockid == BLOCK_COLOR_PALETTE_STAND or  blockid == BLOCK_COLOR_PALETTE_HANG then
				player:placeBlock(itemid1, nx, ny, nz, placedir)
				player:playBlockPlaceSound(itemid1, nx, ny, nz)

			else
				player:placeBlock(itemid2, nx, ny, nz, face)
				player:playBlockPlaceSound(itemid2, nx, ny, nz)
			end

			ret = 0;
		end
		

		if str then
			local containerMgr = world:getContainerMgr()
			if containerMgr then
				local container = containerMgr:getContainer(nx, ny, nz)
				if container then
					tolua.cast(container, "WorldColorPaletteContainer")
					if container then
						container:SetColorSaveData(str)
					end
				end
			end
		end

	end


	return true, ret;
end

function IsDyeableBlockLua(blockId)
	if type(blockId) == "number" then
		return (blockId>=600 and blockId<=682)or (blockId >= 1120 and blockId <= 1135) or (blockId >= 1206 and blockId <= 1222) or (blockId >= 1900 and blockId <= 1983);
	end
end

function Dye_OnUse(player, world, x, y, z, dir)
	local blockid = world:getBlockID(x, y, z)
	if IsDyeableBlockLua(blockid) then

		local itemid = player:getCurToolID()
		local color = DefMgr:GetDyeColor(itemid)
		local id,data = Color2BlockInfo(color,blockid)
		world:destroyBlock(x, y, z, false)
		world:setBlockAll(x,y,z, id, data, 2);
		player:shortcutItemUsed()
		if not WorldMgr:isGodMode() then
			player:getBackPack():addItem(11320,1);
		end
		if id ~= blockid then
			local param = {}
			param.eventobjid = player:getUin() --事件中的玩家
			param.blockid = blockid --事件中的方块类型
			param.x = x
			param.y = y
			param.z = z
			ScriptSupport_TriggerEvent("Block.ChangeColor", param)
		end
	elseif blockid == BLOCK_PERISTELE or blockid == BLOCK_TOP_PERISTELE then
		local itemid = player:getCurToolID()
		local color = DefMgr:GetDyeColor(itemid)
		
		local containerMgr = world:getContainerMgr()
		if containerMgr then
			local container = containerMgr:getContainer(x, y, z)
			if container and container:getObjType() == OBJ_TYPE_PERISTELE then 
                local blockContainer = tolua.cast(container, "PeristeleContainer")     
				if blockContainer then 
					blockContainer:setColor(color)
					blockContainer:markRelativeBlocksForUpdate()
				end
			end
		end

		player:shortcutItemUsed()
		if not WorldMgr:isGodMode() then
			player:getBackPack():addItem(11320,1);
		end
	end
	return true, 0;

end

function ProjectileCreateActor(actorName, num, x, y, z, attackerId)
	if actorName == "ActorPortal" then-- 传送门特殊逻辑需要创建2个关联的actor
		if  CurWorld and attackerId and attackerId > 0 then
			local player = GetWorldActorMgr(CurWorld):findPlayerByUin(attackerId)
			if not player then
				return
			end
			
			local portalTarget = ActorPortal:create(CurWorld._cptr or CurWorld, x, y, z, false)

			local px,py,pz = player:getPosition(0,0,0)

			local face = Yaw2FowardDir(player:getFaceYaw())
			
			local portalSrc = ActorPortal:create(CurWorld._cptr or CurWorld, px + face.x * 250 , py, pz + face.z * 250, true)

			portalSrc:setPortal(portalTarget)
			portalTarget:setPortal(portalSrc)

			
			-- 碰撞范围（高，宽）
			portalSrc:setBound(250, 40)
			portalTarget:setBound(250, 40)

			-- 设置传送门存在时间
			portalSrc:setExistTime(5)
			portalTarget:setExistTime(5)

			-- 设置同个玩家在同个门的传送cd时间（秒）
			portalSrc:setTeleportCd(1.5)
			portalTarget:setTeleportCd(1.5)
		end
	else
		-- 其他
	end
end

-- 冰菇孢子使用接口
function IceCrystalShroomSeed_OnUse(player, world, x, y, z, dir)
	-- return Seed_OnUse(player, world, x,y,z,dir, 150007)
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, 150007, player) then
		world:setBlockAll(x,y+1,z, 150007, math.random(0, 3))
		player:playBlockPlaceSound(150007, x, y+1, z)
		player:shortcutItemUsed()
		return true, 0;
	end
	return false, 1;
end

-- 冰晶蕨种子使用接口
function IceCrystalFernSeed_OnUse(player, world, x, y, z, dir)
	-- return Seed_OnUse(player, world, x,y,z,dir, 150010)
	if dir==DIR_POS_Y and world:canPlaceBlockAt(x,y+1,z, 150010, player) then
		world:setBlockAll(x,y+1,z, 150010, math.random(0, 3))
		player:playBlockPlaceSound(150010, x, y+1, z)
		player:shortcutItemUsed()
		return true, 0;
	end
	return false, 1;
end

-- 灌木从
function BushSeed_OnUse(player, world, x, y, z, dir)
	local blockid = world:getBlockID(x, y, z)
	if blockid == 121 then
		return Seed_OnUse(player, world, x,y,z,dir, 762)
	else
		return Seed_OnUse(player, world, x,y,z,dir, 380)
	end
end

--



function WinterFlowerSeed_OnUse(player, world, x, y, z, dir)
	local plantid = 760
	local blockid = world:getBlockID(x, y, z)
	if blockid == BLOCK_DIRT_FREEZE_PIT then
		return Seed_OnUse(player, world, x,y,z,dir, plantid)
	elseif blockid == BLOCK_BURYLAND or blockid == BLOCK_FARMLAND_PIT or blockid ==BLOCK_PLANTSPACE_BURYLAND then
		world:setBlockAll(x,y+1,z, plantid, 0)
		player:playBlockPlaceSound(plantid, x, y+1, z)
		player:shortcutItemUsed()
		world:destroyBlock(x,y+1,z, true)
		return true, 1;
	else
		return true, 1;
	end
end

function Dummy_OnUse(player, world, x, y, z, dir)
	local nx,ny,nz = NeighborBlock(x,y,z,dir);
	if world:isBlockAir(nx,ny,nz) and world:isBlockAir(nx,ny+1,nz) then
		local placeDir = player:getCurPlaceDir();
		world:setBlockAll(nx,ny,nz, 200001, placeDir)
		world:setBlockAll(nx,ny+1,nz, 200001, 8 + placeDir)
		player:shortcutItemUsed()
		return true, 1;
	end
	return true, 0;
end

function ManualEmitter_OnUse(player, world, x, y, z, dir)
	--先找出左下角
	local nx, ny, nz = NeighborBlock(x, y, z, dir);
	local placeDir = ReverseDirection(player:getCurPlaceDir());
	local dirX = DIR_POS_X;
	local dirY = DIR_POS_Y;
	local dirZ = DIR_POS_Z;
    if placeDir == DIR_NEG_X then
		nx = nx - 1;
	end
	if placeDir == DIR_POS_Y or placeDir == DIR_NEG_Y then
		return true, 1;
	end
    if placeDir == DIR_NEG_Z then
		nz = nz - 1;
	end
	--2 x 2 x 2
	for offsetx = 0, 1 do
		for offsety = 0, 1 do
			for offsetz = 0, 1 do
				local aimx = NeighborBlockXYZ(nx, dirX, offsetx);
				local aimy = NeighborBlockXYZ(ny, dirY, offsety);
				local aimz = NeighborBlockXYZ(nz, dirZ, offsetz);
				if (not world:canPlaceBlockAt(aimx,aimy,aimz, 200002)) then
					return true, 1;
				end
			end
		end
	end
	--只有4个位不够存, 前三个位存序列号,后一个位存方向,由方块和它上边的那个方块存取
	local offsetDir = placeDir;
	local dirHD = {bit.band(offsetDir, 1) * 8, bit.band(offsetDir, 2) * 4};
	local offsetx = 1;
	local offsety = 1;
	local offsetz = 1;
	while offsety >= 0 do
		offsetx = 1;
		while offsetx >= 0 do
			offsetz = 1;
			while offsetz >= 0 do
				local aimx = NeighborBlockXYZ(nx, dirX, offsetx);
				local aimy = NeighborBlockXYZ(ny, dirY, offsety);
				local aimz = NeighborBlockXYZ(nz, dirZ, offsetz);
				world:setBlockAll(aimx,aimy,aimz, 200002, offsetx + offsetz * 2 + offsety * 4 + dirHD[offsety + 1]);
				offsetz = offsetz -1;
			end
			offsetx = offsetx - 1;
		end
		offsety = offsety - 1;
	end
	player:shortcutItemUsed()
	return true, 0;
end

--获取便便等肥料的加速值 单位 小时h
function GetFertilizerValueByItemId(itemid)
	if itemid == 11311 then
		return 5;
	end
	return 0;
end

local function PileCheck(world, disx, disy, disz, placeBlockx, placeBlocky, placeBlockz, dir)
	disx = disx - 1;
	disy = disy - 1;
	disz = disz - 1;
	if disx < 0 then disx = 0 end;
	if disy < 0 then disy = 0 end;
	if disz < 0 then disz = 0 end;
	--先求出左下点
	local bottomx = placeBlockx;
	local bottomy = placeBlocky;
	local bottomz = placeBlockz;
	if dir == DIR_POS_X then
		
	elseif dir == DIR_NEG_X then
		bottomx = placeBlockx - disx;
	elseif dir == DIR_POS_Z then

	else
		bottomz = placeBlockz - disz;
	end

	local aimx = 0;
	local aimy = 0;
	local aimz = 0;
	for offsetx = 0, disx do
		for offsety = 0, disy do
			for offsetz = 0, disz do
				aimx = bottomx + offsetx;
				aimy = bottomy + offsety;
				aimz = bottomz + offsetz;
				--这里都拿bottom方块做测试
				if (not world:canPlaceBlockAt(aimx,aimy,aimz, 200102)) then
					return false;
				end
			end
		end
	end
	return true;
end

local function isMonsterStatueId(id)
	if id >= 200012 and id <= 200100 then
		return true;
	end
	return false;
end

local function PilePlace(world, disx, disy, disz, placeBlockx, placeBlocky, placeBlockz, dir, blockid)
	disx = disx - 1;
	disy = disy - 1;
	disz = disz - 1;
	if disx < 0 then disx = 0 end;
	if disy < 0 then disy = 0 end;
	if disz < 0 then disz = 0 end;
	--先求出左下点
	local bottomx = placeBlockx;
	local bottomy = placeBlocky;
	local bottomz = placeBlockz;
	if dir == DIR_POS_X then
		
	elseif dir == DIR_NEG_X then
		bottomx = placeBlockx - disx;
	elseif dir == DIR_POS_Z then

	else
		bottomz = placeBlockz - disz;
	end

	local aimx = 0;
	local aimy = bottomy;
	local aimz = 0;
	--能摆放的都是底层blockid
	local bottomid = 200102;
	local centerid = 200103;
	local topid    = 200101;
	local specialId = blockid;
	--只有一层
	if disy <= 0 then
		bottomid = 200104;
		if isMonsterStatueId(blockid) then
			bottomid = 200107;
		end
	else
		if isMonsterStatueId(blockid) then
			bottomid = 200105;
		end
	end
	--先铺底层
	local blockdata = 0;
	for offsetx = 0, disx do
		for offsetz = 0, disz do
			blockdata = 0;
			if (offsetx ~= 0 or offsetz ~= 0) then
				aimx = bottomx + offsetx;
				aimz = bottomz + offsetz;
				--查看周边是否有相邻的block
				--左边有
				if offsetx < disx  then
					blockdata = blockdata + 1;
				end
				if offsetx > 0 then
					blockdata = blockdata + 2;
				end
				if offsetz < disz then
					blockdata = blockdata + 4;
				end
				if offsetz > 0 then
					blockdata = blockdata + 8;
				end
				world:setBlockAll(aimx,aimy,aimz, bottomid, blockdata, 2);
			end
		end
	end
	world:setBlockAll(bottomx,aimy,bottomz, specialId, dir, 2);
	--在铺中间层
	local centeryMax = disy - 1;
	for offsetx = 0, disx do
		for offsety = 1, centeryMax do
			for offsetz = 0, disz do
				blockdata = 0;
				aimx = bottomx + offsetx;
				aimz = bottomz + offsetz;
				aimy = bottomy + offsety;
				--查看周边是否有相邻的block
				--左边有
				if offsetx < disx  then
					blockdata = blockdata + 1;
				end
				if offsetx > 0 then
					blockdata = blockdata + 2;
				end
				if offsetz < disz then
					blockdata = blockdata + 4;
				end
				if offsetz > 0 then
					blockdata = blockdata + 8;
				end
				world:setBlockAll(aimx,aimy,aimz, centerid, blockdata, 2);
			end
		end
	end
	if disy > 0 then
		--在铺顶层
		aimy = disy + bottomy;
		for offsetx = 0, disx do
			for offsetz = 0, disz do
				blockdata = 0;
				aimx = bottomx + offsetx;
				aimz = bottomz + offsetz;
				--查看周边是否有相邻的block
				--左边有
				if offsetx < disx  then
					blockdata = blockdata + 1;
				end
				if offsetx > 0 then
					blockdata = blockdata + 2;
				end
				if offsetz < disz then
					blockdata = blockdata + 4;
				end
				if offsetz > 0 then
					blockdata = blockdata + 8;
				end
				world:setBlockAll(aimx,aimy,aimz, topid, blockdata, 2);
			end
		end
	end
	return true, bottomx, bottomy, bottomz;
end

local function StatueTileRangeBlock(bottomx, bottomy, bottomz, world, disx, disy, disz, blockid)
	--只铺一层
	disx = disx - 1;
	disz = disz - 1;
	local aimx;
	local aimy = bottomy;
	local aimz;
	local blockdata = 0;
	for offsetx = 0, disx do
		for offsetz = 0, disz do
			blockdata = 0;
			aimx = bottomx + offsetx;
			aimz = bottomz + offsetz;
			--查看周边是否有相邻的block
			--左边有
			if offsetx < disx  then
				blockdata = blockdata + 1;
			end
			if offsetx > 0 then
				blockdata = blockdata + 2;
			end
			if offsetz < disz then
				blockdata = blockdata + 4;
			end
			if offsetz > 0 then
				blockdata = blockdata + 8;
			end
			world:setBlockAll(aimx, aimy, aimz, blockid, blockdata, 2);
		end
	end
end

local function PileUseHandle(player, world, x, y, z, dir, disx, disy, disz)
	if not player then
		return;
	end
	local id = player:getCurToolID()
	local nx, ny, nz = NeighborBlock(x, y, z, dir);
	local placeDir = ReverseDirection(player:getCurPlaceDir());
	local ret;
	local bottomx;
	local bottomy;
	local bottomz;
	if (isMonsterStatueId(id)) then
		if not PileCheck(world, disx, disy + 1, disz, nx, ny, nz, placeDir) then
			return true , 1;
		end
		--先放上部分的雕像部分
		ret, bottomx, bottomy, bottomz = PilePlace(world, disx, disy, disz, nx, ny + 1, nz, placeDir, id);
		if (not ret) then
			return true, 1;
		end
		StatueTileRangeBlock(bottomx, bottomy - 1, bottomz, world, disx, disy, disz, 200106);
	else
		if not PileCheck(world, disx, disy, disz, nx, ny, nz, placeDir) then
			return true , 1;
		end
		ret, bottomx, bottomy, bottomz = PilePlace(world, disx, disy, disz, nx, ny, nz, placeDir, id);
		if (not ret) then
			return true, 1;
		end
	end
	player:shortcutItemUsed()
	return true, 0;
end

function PileUseX1Y1Z1(player, world, x, y, z, dir)
	return PileUseHandle(player, world, x, y, z, dir, 1, 1, 1);
end

function PileUseX1Y2Z1(player, world, x, y, z, dir)
	return PileUseHandle(player, world, x, y, z, dir, 1, 2, 1)
end

function PileUseX2Y2Z2(player, world, x, y, z, dir)
	return PileUseHandle(player, world, x, y, z, dir, 2, 2, 2)
end

function PileUseX3Y3Z3(player, world, x, y, z, dir)
	return PileUseHandle(player, world, x, y, z, dir, 3, 3, 3)
end

--只是一个普通物品使用,简单设置下方向就行了
function NormalItemUse(player, world, x, y, z, dir)
	local nx,ny,nz = NeighborBlock(x,y,z,dir);
	if world:isBlockAir(nx,ny,nz) then
		local id = player:getCurToolID()
		local placeDir = player:getCurPlaceDir();
		world:setBlockAll(nx,ny,nz, id, placeDir)
		player:shortcutItemUsed()
		return true, 0;
	end
	return true, 1;
end

--使用水墨荷花
function InkLotus_OnUse(player, world, x, y, z, dir)
	local id = world:getBlockID(x, y, z)
	local itemId = player:getCurToolID()

	
	-- if id == BLOCK_STILL_WATER and world:getBlockID(x, y+1, z) == 0 then
	-- 	world:setBlockAll(x, y+1, z, itemId,  math.random(0,3))
	-- 	player:shortcutItemUsed()
	-- 	return true, 0 
	-- else
		if dir == DIR_POS_Y and world:canPlaceBlockAt(x, y+1, z, itemId, player) and world:getBlockID(x, y+1, z) == 0 then 
			world:setBlockAll(x,y+1,z, itemId, math.random(0, 3))
			player:shortcutItemUsed()
			return true, 0 
		end
	-- end

	return false, 1
end

function TallGrassModel_OnUse(player, world, x, y, z, dir)
	local placedir = player:getCurPlaceDir()

	local blockdata = world:getBlockData(x,y,z)
	local blockdir = bit.band(blockdata,3);

	local blockid = player:getCurToolID()

	local def = DefMgr:getBlockDef(blockid,false)

	local ret = 1;
	local nx,ny,nz = NeighborBlock(x,y,z,dir)
	if dir == DIR_POS_Y then
        world:setBlockAll(nx, ny, nz, blockid, ReverseDirection(placedir))

		if def.Height > 1 then
			for i=1, def.Height do
				world:setBlockAll(nx, ny + i, nz, blockid, 4)  --上方block
			end
		end
        
		player:shortcutItemUsed()
        player:playBlockPlaceSound(blockid, nx, ny, nz)
		ret = 0;
        return true, ret;
	end

	return true, 1;
end

--相机使用
function PolaroidCamera_OnUse(player, world, x, y, z, dir)
	if not player:isPlayerControl() then
		return true, 1;
	end
	if GetInst("MiniUIManager"):GetUI("PolaroidMainFrameAutoGen") then
		GetInst("MiniUIManager"):ShowUI("PolaroidMainFrameAutoGen")
	else
		GetInst("MiniUIManager"):OpenUI("PolaroidMainFrame", "miniui/miniworld/ugc_polaroid", "PolaroidMainFrameAutoGen", {disableOperateUI = true})
	end

	return true, 1;
end

--相片使用
function PolaroidPicture_OnUse(player, world, x, y, z, dir)
	if not player:isPlayerControl() then
		return true, 1;
	end
	local itemid = player:getCurToolID();
	local curGrid = CurMainPlayer:getBackPack():index2Grid(CurMainPlayer:getCurShortcut() + CurMainPlayer:getShortcutStartIndex());
	if not curGrid then
		return false, 1;
	end
    local userdataStr = curGrid:getUserdataStr();
	if type(userdataStr) ~= "string" or string.len(userdataStr) == 0 then
		return false, 1;
	end
	local photoInfo = JSON:decode(userdataStr);
	if not photoInfo or type(photoInfo.pngpath) ~= "string" or string.len(photoInfo.pngpath) == 0 then
		return false, 1;
	end
	--
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common", "miniui/miniworld/commonTexture"}, "PhotographFrameAutoGen");
	GetInst("MiniUIManager"):OpenUI("PhotographFrame", "miniui/miniworld/ugc_polaroid", "PhotographFrameAutoGen",
		{disableOperateUI = false, allPhotos = photoInfo, curPhotoIndex = 1, from = 3});
	return true, 1;
end

--相册使用
function PolaroidAlbum_OnUse(player, world, x, y, z, dir)
	if not player:isPlayerControl() then
		return true, 1;
	end
	local itemid = player:getCurToolID();
	local curGrid = CurMainPlayer:getBackPack():index2Grid(CurMainPlayer:getCurShortcut() + CurMainPlayer:getShortcutStartIndex());
	if not curGrid then
		return false, 1;
	end
    local userdataStr = curGrid:getUserdataStr();
	local isAlbum = itemid == ITEM_POLAROID_RARE_ALBUM or itemid == ITEM_POLAROID_ALBUM
    if isAlbum and player:isPlayerControl() then
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/commonTexture"}, "PhotoAlbumFrameAutoGen")
		GetInst("MiniUIManager"):OpenUI("PhotoAlbumFrame", "miniui/miniworld/ugc_polaroid", "PhotoAlbumFrameAutoGen", {disableOperateUI = false, albumInfo = userdataStr});
	end

	return true, 1
end

--相框使用
function PolaroidFrame_OnUse(player, world, x, y, z, dir)
	local itemid = player:getCurToolID();
	if itemid ~= ITEM_POLAROID_FRAME and itemid ~= ITEM_POLAROID_RARE_FRAME and itemid ~= ITEM_POLAROID_INNER_FRAME then
		return false, 1;
	end
	local nx, ny, nz = PlaceBlockOnUse(player, world, x, y, z, dir, itemid)
	if not world:canPlaceBlockAt(nx, ny, nz, itemid, player) then 
		return false, 1;
	end

	local placeX = x;
	local placeY = y;
	local placeZ = z; 
	if dir == DIR_NEG_X then
		placeX = placeX - 1;
	elseif dir == DIR_POS_X then
		placeX = placeX + 1;
	elseif dir == DIR_NEG_Z then
		placeZ = placeZ - 1;
	elseif dir == DIR_POS_Z then
		placeZ = placeZ + 1;
	elseif dir == DIR_NEG_Y then
		placeY = placeY - 1;
	elseif dir == DIR_POS_Y then
		placeY = placeY + 1;
	end
	CurWorld:setBlockAll(placeX, placeY, placeZ, itemid, dir + 8);
	player:shortcutItemUsed()
	--是自己打开的
	if player:isPlayerControl() then
		OpenPolaroidFrameUI(placeX, placeY, placeZ, itemid, false);
	elseif not world:isRemoteMode() then
		--不是自己打开的，但是是主机，就通知对应的客机去打开自己的界面,这里打开界面的时候可能容器还没有创建
		world:openPolaroidFrameUIClient(placeX, placeY, placeZ, player:getUin(), itemid);
	end
	return true, 1;
end

--打开拍立得相框操作界面 isClientOpen是否客机的打开
function OpenPolaroidFrameUI(placeX, placeY, placeZ, itemid, isClientOpen)
	--高级创造不显示交互界面
	if UGCModeMgr and UGCModeMgr:IsEditing() and UGCModeMgr:GetGameType() == UGCGAMETYPE_HIGH then
		return;
	end

	if isClientOpen then
		--客机打开要等主机先同步方块放置信息，容器创建信息
		if not CurWorld then
			return;
		end
		local containerMgr = CurWorld:getContainerMgr()
		if not containerMgr then
			return;
		end
		local frameContainer = containerMgr:GetContainer(placeX, placeY, placeZ, OBJ_TYPE_STRING);
		if not frameContainer then
			--隔0.1秒去查一次容器是否同步过来了
			openPolaroidFrameUIScheduler = GetInst("MiniUIScheduler"):regGloabel(function ()
				frameContainer = containerMgr:GetContainer(placeX, placeY, placeZ, OBJ_TYPE_STRING);
				if not frameContainer then
					return;
				end

				GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/commonTexture"}, "PhotoFrameFrameAutoGen")
				GetInst("MiniUIManager"):OpenUI("PhotoFrameFrame", "miniui/miniworld/ugc_polaroid", "PhotoFrameFrameAutoGen", 
					{disableOperateUI = false, isopen = false, blockID = itemid, 
					size={width = 1, height = 1},pos = {x= placeX, y=placeY, z=placeZ}}
				);

				if openPolaroidFrameUIScheduler then
                    GetInst("MiniUIScheduler"):unreg(openPolaroidFrameUIScheduler)
                    openPolaroidFrameUIScheduler = nil
                end
			end, 0.1, 10);
		end
	else	
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/commonTexture"}, "PhotoFrameFrameAutoGen")
		GetInst("MiniUIManager"):OpenUI("PhotoFrameFrame", "miniui/miniworld/ugc_polaroid", "PhotoFrameFrameAutoGen", 
			{disableOperateUI = false, isopen = false, blockID = itemid, 
			size={width = 1, height = 1},pos = {x= placeX, y=placeY, z=placeZ}}
		);
	end

end