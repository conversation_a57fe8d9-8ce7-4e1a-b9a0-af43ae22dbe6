//中中中中中中文
//二进制应该看到 2F 2F E4 B8 AD E4 B8 AD E4 B8 AD E4 B8 AD
//此文件包含了UTF8字符串，必须保持UTF8编码格式，文件中打印和字符串都尽量使用英文
//char letterUpper[] = "АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ";
//char letterLower[] = "абвгдеёжзийклмнопрстуфхцчшщъыьэюя";

#include "defmanager.h"
#include "world_types.h"
#include "Utilities/Logs/LogAssert.h"
#include "CsvParser.h"
//#include "OgreMath.h"
#include "OgreUtils.h"
#include "Common/OgreTimer.h"
#include <time.h>
#include <algorithm>
#include "Common/OgreStringUtil.h"
#include "ModManager.h"
#include "WorldManager.h"
#include <vector>
#include "CoreCommonDef.h"
#include "CustomModelItem_generated.h"
#include "CustomModelMgr.h"
#include "DirtyWordFilter.h"
#include "keywordfilter/KeywordFilter.h"
#include <ctype.h>
#include <algorithm>
#include "Download/LegacyFileManagerWeb.h"
#include "GlobalFunctions.h"
#include "OgreScriptLuaVM.h"
#include "GameStatic.h"
#include "Common/GameStatic.h"
#include "IWorldConfigProxy.h"
#include "ClientInfoProxy.h"
#include "OWorldList/OWorldList.h"
#include "UgcAssetMgr.h"
#include "CsvAutoInclude.h"
#include "SandboxCoreDriver.h"
#include "SprayPaintDefCsv.h"	//20211020 导入喷漆头文件 codeby:keguanqiang
#include "special_blockid.h"
#include "MultiLocalMgr.h"
#include "MultiLanCSVParser.h"
#include "ScoreCsv.h"
#include "SoundStrDefCsv.h"
#include "ParticlesStrDefCsv.h"
#include "BlockEffectDefCsv.h"
#include "DieInfoCsv.h"
#include "DisassembleCsv.h"
#include "SandboxEventObjectManager.h"
#include "keywordfilter/KeywordFilterMgr.h"
#include "WorldStringTranslateMgr.h"
#include "ColorMixDefCsv.h"
#include "world.h"
#include "BiomeGroupDefCsv.h"
#include "BotConversationsDefCsv.h"
#include "ClientApp.h"
#include "ClientActorPet.h"
#include "DevUIResourceCsv.h"
#include "ClientAccount.h"
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
#include "HomeCsvManagerProxy.h"
//这里暂时要引用家园沙盒合并后在处理lua层   目前的lua还会读defMgr   2021年9月26日14:55:56
#endif
#include "ImportCustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "ItemInHandDefCsv.h"
#include "Utilities/HashStringFunctions.h"
#include "PlayerAttribCsv.h"
#include "ArchitecturalBlueprintCsv.h"
#include "ChestSpawnCsv.h"
#include "SkinningToolCsv.h"
#include "EquipGroupDefCsv.h"
#include "WorkbenchTechCsv.h"
#include "OperateUIDataCsv.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

const char* DefManager::s_LandPrefix[] = {"", "EN", "TW", "THA", "ESN", "PTB", "FRA", "JPN", "ARA", "KOR", "VIE", "RUS", "TUR", "ITA", "GER", "IND" };
const char* DefManager::ColumnLang(const CSVParser::TableLine &row, const char *col, int lang)
{
	return row[col].Str();
	/*
	char realcol[64];
	sprintf(realcol, "%s%s", s_LandPrefix[lang], col);

	//haima TODO
	if ( lang <= 2 ) {
		return row[realcol].Str();
	}
	else 
	{
		bool  has_ = row.hasColumn(realcol);
		if (has_ == false) 
		{
			sprintf(realcol, "%s%s", "EN", col);
			return row[realcol].Str();
		}
		else 
		{
			return row[realcol].Str();
		}
	}
	*/
}
static const char *BiomeTypeNames[] = // MAX_BIOME_TYPE
{
	"OCEAN", 
	"PLAINS", 
	"DESERT",
	"FOREST",
	"EXTREMEHILLS",

	"SWAMPLAND",
	"TAIGA",
	"JUNGLE",
	"ICE_PLAINS",
	"FROZEN_OCEAN",

	"FROZEN_RIVER",
	"REDSOIL",
	"REDSOIL_SHORE", 
	"DESERT_HILLS", 
	"FOREST_HILLS",

	"TAIGA_HILLS", 
	"ICE_MOUNTAINS",
	"JUNGLE_HILLS",
	"RIVER", 
	"BEACH", 

	"EXTREMEHILLS_EDGE", 
	"Earthcore", 
	"AIR_ISLAND", 
	"BASIN", 
	"BASIN_EDGE", 

	"BASIN_BAMBOO",
	"BASIN_PEACH",
	"BIOME_PLANET_SAPCE",//星球空间 
	"BIOME_PLANET_SAPCE_HILLS",
	"BIOME_PLANET_SAPCE_SUB1",

	"BIOME_PLANET_SAPCE_SUB2",
	"BIOME_PLANET_SAPCE_SUB3",
	"BIOME_PLANET_SAPCE_AIRLANDS_BASE",
	"BIOME_PLANET_SAPCE_AIRLANDS",
	"BIOME_PLANET_SAPCE_AIRLANDS_SUB1",

	"BIOME_PLANET_SAPCE_AIRLANDS_SUB2",
	"BIOME_PLANET_SAPCE_AIRLANDS_SUB3",
	"BIOME_PLANET_SAPCE_AIRLANDS_SUB4",
	"BIOME_PLANET_SAPCE_AIRLANDS_SUB5",
	"RAINFOREST", // 39 雨林

	"BIOME_AIR_PLAINS",
	"BIOME_AIR_PLAINS_H",
	"VOLCANO", // 42 火山(口)
	"VOLCANO_PLAIN", // 43 火山平原
	"VOLCANO_MOUNTAIN", // 44 火山山脉
	"VOLCANO_RIVER", // 45 火山岩浆流
	"VOLCANO_CORE", // 46 火山口核心区域
	"BIOME_DESERT_OASIS", // 47 沙漠绿洲
	"BIOME_DESERT_LAKE", // 48 沙漠湖

	"DEEPOCEAN", // 49 深海
	"ISLAND_SHORE_DESERT", // 50 荒岛海岸
	"ISLAND_LAND_DESERT", // 51 荒岛岛心
	"ISLAND_SHORE_REDSOIL", // 52 红土岛海岸
	"ISLAND_LAND_REDSOIL", // 53 红土岛心
	"ISLAND_SHORE_REEF", // 54 珊瑚岛海岸
	"ISLAND_LAND_REEF", // 55 珊瑚岛岛心

	"ICE_PLAINS_CONIFEROUS_FOREST",//56 迷拉星 - 冰原 - 覆雪针叶林	覆雪针叶林
	"ICE_PLAINS_HIGHEST_PEAK",  //57 迷拉星 - 冰原 - 雪山主峰	雪山主峰
	"ICE_PLAINS_SECOND_PEAK",	//58 迷拉星 - 冰原 - 雪山副峰	雪山副峰
	"ICE_PLAINS_MOUNTAIN",	//59 迷拉星 - 冰原 - 雪山山脉	雪山山脉
	"ICE_PLAINS_MOUTAINSIDE",	//60 迷拉星 - 冰原 - 雪山中部	雪山中部
	"ICE_PLAINS_PEAK_PLAIN",	//61 迷拉星 - 冰原 - 雪山底部	雪山底部
	"ICE_PLAINS_SECOND_MOUTAINSIDE",	//62 迷拉星 - 冰原 - 副峰雪山中部	副峰雪山中部
	"ICE_PLAINS_FRIZEB_LAKE",         //63 迷拉星 - 冰原 - 冻湖	副峰雪山中部
	"CANYON",	//64 峡谷
	"CANYON_EAGE",         //65 峡谷边缘
	"PLAINS_ARID",         //66 迷拉星-草原-干旱草原
	"PLAINS_DANDELION",         //67 迷拉星-草原-蒲公英花海
	"PLAINS_RAPESEED",         //68 迷拉星-草原-油菜花海
	"FOREST_LAVENDER",         //69 迷拉星-森林-薰衣草花海
	"FOREST_FOXTAIL",         //70 迷拉星-森林-狗尾巴草
	"FOREST_CHRYSANTH",         //71 ,迷拉星-森林-菊花花海
	"BASIN_RICE",         //72 迷拉星-盆地-稻田
	"SWAMPLAND_RIVERSIDE",         //73 迷拉星-沼泽-沼泽河畔
	"AIRISLAND_SHINE",         //74 迷拉星-发光空岛
	"EXTREMEHILLS_PLUM",         //75 迷拉星-三角梅峭壁
	"ISLAND_LAND_TULIP",         //76 迷拉星-海洋-郁金香岛心
	"ISLAND_SHORE_TULIP",         //77 迷拉星-海洋-郁金香海岸

	"DESERT_CITY",				    //78 城市
	"DESERT_HUYANG",				//79 迷拉星-沙漠-胡杨林
	"JUNGLE_LANHUAYING",			//80 迷拉星-丛林-蓝花楹树林
	"EXTREMEHILLS_FENGYE",			//81 迷拉星-峭壁-枫叶林
	"EXTREMEHILLS_YINXING",			//82 迷拉星-峭壁-银杏林
	"PLAINS_LAKE",	//草原湖泊
	"BASIN_LAKE",   //盆地湖泊
	"FOREST_LAKE",  //森林湖泊
	"TAIGA_LAKE",   //针叶林湖泊
	"RAINFOREST_LAKE", //雨林湖泊
	/*
		这个是生态最大标签 添加新标签在这个上面添加
	*/
	"MAX_BIOME_TYPE",
};

unsigned int DefManager::CalItemCrc(const ItemDef *itemdef, const BlockDef *blockdef)
{
    OPTICK_EVENT();
	unsigned int crc = 0;
	const int N = 31;

	crc = crc*N + itemdef->ID;
	crc = crc*N + itemdef->StackMax;
	crc = crc*N + itemdef->UnlockType;
	crc = crc*N + itemdef->UnlockFlag;
	crc = crc*N + *(unsigned int *)&itemdef->Score;
	crc = crc*N + *(unsigned int *)itemdef->UseScript.c_str();
	crc = crc*N + *(unsigned int *)(itemdef->UseScript.c_str()+4);

	if(blockdef)
	{
		crc = crc*N + blockdef->ID;
		crc = crc*N + blockdef->DropExp;
		crc = crc*N + blockdef->HandMineDrops.item;
		crc = crc*N + blockdef->PreciseDrop;
		for(int i=0; i<MAX_TOOLMINE_DROP; i++)
		{
			crc = crc*N + blockdef->ToolMineDrops[i].item;
		}
	}

	return crc;
}

//判断国内版本
static bool IsDomesticVer()
{
	int game_env = GetIWorldConfigProxy()->getGameData("game_env");
	
	//正式，开发，先遣
	return (game_env == 0 || game_env == 1 || game_env == 2);
}

ModManager* s_ModManagerCachePtr = nullptr; 

DefManager::DefManager() : m_BiomePlantTries(), m_planetBiomeGeneDef(),
                           m_CurLanguage(LANGUAGE_ZH), m_CsvLoadConfig(NULL)
{
	time_t t;
	srand((unsigned int)time(&t));

	m_RandGen = ENG_NEW(ChunkRandGen);
	m_RandGen->setSeed((unsigned int)time(&t));

	letterUpperUtf8 = NULL;
	letterLowerUtf8 = NULL;
	char letterUpper[] = "АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ";
	char letterLower[] = "абвгдеёжзийклмнопрстуфхцчшщъыьэюя";

	int len = sizeof(letterUpper);
	wchar_t* pwchar = new wchar_t[len + 2];
	if (pwchar != NULL)
	{
		memset(pwchar, 0, len + 2);
		StringUtil::AnsiToUnicode(pwchar, len + 1, letterUpper);
		int tmpint = len * 4 + 1;
		char* putf8 = (char*)malloc(tmpint);
		if (putf8 != NULL)
		{
			memset(putf8, 0, tmpint);
			StringUtil::UnicodeToUTF8(putf8, len * 4, pwchar);
			len = strlen(putf8);
			letterUpperUtf8 = new char[len + 1];
			if (letterUpperUtf8 != NULL)
			{
				memcpy(letterUpperUtf8, putf8, len + 1);
			}
			free(putf8);
		}
		OGRE_DELETE_ARRAY(pwchar);
	}

	len = sizeof(letterLower);
	pwchar = new wchar_t[len + 2];
	if (pwchar != NULL)
	{
		memset(pwchar, 0, len + 2);
		StringUtil::AnsiToUnicode(pwchar, len + 1, letterLower);
		int tmpint = len * 4 + 1;
		char* putf8 = (char*)malloc(tmpint);
		if (putf8 != NULL)
		{
			memset(putf8, 0, tmpint);
			StringUtil::UnicodeToUTF8(putf8, len * 4, pwchar);
			len = strlen(putf8);
			letterLowerUtf8 = new char[len + 1];
			if (letterLowerUtf8 != NULL)
			{
				memcpy(letterLowerUtf8, putf8, len + 1);
			}
			free(putf8);
		}
		OGRE_DELETE_ARRAY(pwchar);
	}


	//m_pDirtyFilter = ENG_NEW(DirtyWordFilter)();   
	//m_pDirtyFliterWithFullMatch = ENG_NEW(DirtyWordFilter)(true);

	m_mLoadedFlag.clear();

	initCsvLoadConfig();

	//m_vCsvList.reserve(113);
	includeCsvAuto(m_vCsvList);

	if (SandboxCoreDriver::GetInstancePtr() != nullptr)
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("Homeland_InitCsv", SandboxContext(nullptr).SetData_Usertype(&m_vCsvList));
}

DefManager::~DefManager()
{
	clear();
	ENG_DELETE(m_RandGen);
	//ENG_DELETE(m_planetBiomeGeneDef);
	//m_planetBiomeGeneDef = NULL;
	if (letterUpperUtf8)
	   OGRE_DELETE_ARRAY(letterUpperUtf8);
	if (letterLowerUtf8)
		OGRE_DELETE_ARRAY(letterLowerUtf8);

	ENG_DELETE(m_CsvLoadConfig);
	//delete m_pDirtyFilter;
	//delete m_pDirtyFliterWithFullMatch;

	m_mLoadedFlag.clear();
	m_CurrentStatusDefs.clear();
	//m_vCsvList会在GameStatic中进行释放，不需要重复释放
	/*for (size_t i = 0; i < m_vCsvList.size(); ++i)
	{
		AbsCsv* absCsv = m_vCsvList[i];
		m_vCsvList[i] = NULL;
		ENG_DELETE(absCsv);
	}*/
	m_vCsvList.clear();

	//auto iter = m_WildmanNameTable.m_Records.begin();
	//for (; iter != m_WildmanNameTable.m_Records.end(); iter++)
	//{
	//	const StringDef& def = iter->second;
	//	ENG_FREE_LABEL(def.str, kMemGame);
	//}

	m_WildmanNameTable.clear();
}

//要早一点构造
MINIW::GameStatic<DefManager>  s_DefManager(MINIW::kInitManual);
DefManager & GetDefManager()
{
	return *s_DefManager.EnsureInitialized();
}

bool IsInitDefManager()
{
	return s_DefManager.IsInitialized();
}


DefManager* DefManager::getSingletonPtr()
{
	return &GetDefManager();
}

void DefManager::HomelandInitCsv()
{
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("Homeland_InitCsv", SandboxContext(nullptr).SetData_Usertype(&m_vCsvList));
}

std::map<std::string, int>& DefManager::GetParticlesStrDefCsvStrMap()
{
	return ParticlesStrDefCsv::getInstance()->m_strmap;
}

std::map<int, std::string>& DefManager::GetParticlesStrDefCsvIdMap()
{
	return ParticlesStrDefCsv::getInstance()->m_idmap;
}

std::map<std::string, int>& DefManager::GetSoundStrDefCsvStrMap()
{
	return SoundStrDefCsv::getInstance()->m_strmap;
}

std::map<int, std::string>& DefManager::GetSoundStrDefCsvIdMap()
{
	return SoundStrDefCsv::getInstance()->m_idmap;
}

void DefManager::initCsvLoadConfig() {
	if (m_CsvLoadConfig) {
		return;
	}
	if (!IsDomesticVer())
	{
		m_CsvLoadConfig = ENG_NEW(OverseasCsvLoadConfig)();
	}
	else
	{
		m_CsvLoadConfig = ENG_NEW(DefaultCsvLoadConfig)();
	}
}

bool DefManager::IsInit()
{
	return IsInitDefManager();
}

ICsvLoadConfig& DefManager::getCsvLoadConfig()
{
	assert(m_CsvLoadConfig);
	return *m_CsvLoadConfig;
}

void DefManager::setUserTypePointer()
{
	ScriptVM& gameVM = *ScriptVM::game();
	gameVM.setUserTypePointer("DefMgr", "DefManager", this);
	std::vector<AbsCsv*>::iterator csvIt = m_vCsvList.begin();
	for (; csvIt != m_vCsvList.end(); ++csvIt)
	{
		AbsCsv* absCsv = *csvIt;
		gameVM.setUserTypePointer(absCsv->getClassName(), absCsv->getClassName(), absCsv);
	}
}


void DefManager::setLanguage(int lang)
{
	m_CurLanguage = lang;
	AbsCsv::setLanguage(lang);
    MultiLocalMgr::getSingleton()->onLanguage();
}

void DefManager::clear()
{
	clearRandomNames();
	for(size_t i=0; i<m_FilterStrings.size(); i++)
	{
		free(m_FilterStrings[i]);
	}

	for (size_t i = 0; i<m_FilterStringsWhole.size(); i++)
	{
		free(m_FilterStringsWhole[i]);
	}
	
	m_TreeTable.clear();
	m_OreTable.clear();
	m_Biomes.clear_dealloc();
	
	//Rainbow::DeletePointerArray(m_GameRuleTable);
	m_GameRuleTable.clear();
	m_RoleActionTable.clear();

	for (size_t i = 0; i < m_VoxPalettes.size(); i++)
	{
		ENG_FREE(m_VoxPalettes[i]);
	}

	//auto iter = m_StringDefTable.m_Records.begin();
	//for(; iter!=m_StringDefTable.m_Records.end(); iter++)
	//{
	//	free((void *)iter->second.str);
	//}
	m_StringDefTable.clear();
	m_RoleTable.clear();

	/*for(size_t i=0; i<m_StorePropArray.size(); i++)
	{
		ENG_DELETE(m_StorePropArray[i]);
	}*/
	m_StorePropArray.clear();

	/*for(size_t i=0; i<m_StoreHorseArray.size(); i++)
	{
		ENG_DELETE(m_StoreHorseArray[i]);
	}*/
	m_StoreHorseArray.clear();
	m_StoreHorseIdToHave.clear();

	/*for(size_t i=0; i<m_BookArray.size(); i++)
	{
		ENG_DELETE(m_BookArray[i]);
	}*/
	m_BookArray.clear();
	//for(size_t i=0; i<m_AvatarSkinArray.size(); i++)
	//{
	//	ENG_DELETE(m_AvatarSkinArray[i]);
	//}
	m_AvatarSkinArray.clear();

	m_mLoadedFlag.clear();

	//for(size_t i=0; i<m_OverseasGrayTable.size(); i++)
	//{
	//	ENG_DELETE(m_OverseasGrayTable[i]);
	//}
	m_OverseasGrayTable.clear();
	m_wikiConfigTable.clear();
	

	//m_vCsvList里面的类，都是注册进GameStatic，程序退出，会自动释放,onClear函数放在函数中执行
	/*std::vector<AbsCsv*>::iterator csvIt = m_vCsvList.begin();
	for (; csvIt != m_vCsvList.end(); ++csvIt)
	{
		AbsCsv* absCsv = *csvIt;
		absCsv->onClear();
	}*/
	m_CurrentStatusDefs.clear();

	for (auto p : m_DriftBottleOfficialText)
	{
		OGRE_DELETE(p);
	}
	m_DriftBottleOfficialText.clear();

	for (auto p : m_LettersOfficialText)
	{
		OGRE_DELETE(p);
	}
	m_LettersOfficialText.clear();

}

const BiomeMapGrid &DefManager::getBiomeDef(int heat, int humid)
{
	assert(heat>=0 && heat<=100 && humid>=0 && humid<=100);

	return m_BiomeMap[humid][heat];
}

int DefManager::getEnchantNum()
{
	loadEnchantCSV();

	return m_EnchantTable.GetRecordSize();
}

const EnchantDef *DefManager::getEnchantDefByIndex(int index)
{
	loadEnchantCSV();

	assert(index>=0 && index<m_EnchantTable.GetRecordSize());

	return m_EnchantTable.GetRecordByIndex(index);
}

const EnchantDef *DefManager::getEnchantDef(int id)
{	
	loadEnchantCSV();

	return m_EnchantTable.GetRecord(id);
}
//code by:tanzhenyu
const RuneDef* DefManager::getRuneDef(int id)//id = type*100 + level
{
	loadRuneCSV();
	return m_RuneTable.GetRecord(id);
}

void DefManager::setCurAccordEnchants(int tooltype)
{	
	loadEnchantCSV();

	m_AccordEnchants.clear();
	auto iter = m_EnchantTable.m_Records.begin();
	for(; iter!=m_EnchantTable.m_Records.end(); iter++)
	{
		for(int i=0; i<MAX_TOOL_TYPE; i++)
		{
			if(iter->second.ToolType[i] == tooltype && iter->second.EnchantLevel == 1)
			{
				m_AccordEnchants.push_back(&iter->second);
			}
		}
	}
}

int DefManager::getCurAccordEnchantsNum()
{	
	loadEnchantCSV();

	return m_AccordEnchants.size();
}

const EnchantDef *DefManager::getCurAccordEnchantDef(int index)
{	
	loadEnchantCSV();

	assert(index < getCurAccordEnchantsNum());
	return m_AccordEnchants[index];
}

const EnchantMentDef *DefManager::getEnchantMentDef(int type)
{	
	loadEnchantMentCSV();

	return m_EnchantMentTable.GetRecord(type);
}

//真实id获取buffdef
const BuffDef *DefManager::getBuffDef(int id)
{	
	return BuffDefCsv::getInstance()->get(id);
}

const GunDef * DefManager::getGunDef(int id)
{
	loadGunDef();

	GunDef *gunDef = g_ModMgr.tryGetGunDef(id);
	if (gunDef != nullptr)
	{
		return gunDef;
	}
	return m_GunTable.GetRecord(id);
}

GunDef* DefManager::getOrignalGunDef(int id)
{
	loadGunDef();

	return m_GunTable.GetRecord(id);
}

int DefManager::getBuffDefNum()
{	
	return BuffDefCsv::getInstance()->getNum();
}

//通过"id"和"level"拼接获取buffdef
const BuffDef *DefManager::getBuffDef(int id, int level)
{
	return BuffDefCsv::getInstance()->get(id, level);
}

const BuffDef *DefManager::getBuffDefByIndex(int index)
{	
	return BuffDefCsv::getInstance()->getByIndex(index);
}

BuffDef* DefManager::getStatusDefRaw(int id)
{
	return BuffDefCsv::getInstance()->getStatusDefRaw(id);
}

BuffDef* DefManager::addStatusDefRawByCopy(int id, int copyId)
{
	return BuffDefCsv::getInstance()->addStatusDefRawByCopy(id, copyId);
}

bool DefManager::finalizeStatusDefRaw()
{
	return BuffDefCsv::getInstance()->finalizeStatusDefRaw();
}

void DefManager::removeStatusDefRaw(int id)
{
	BuffDefCsv::getInstance()->remove(id);
}

int DefManager::getStatusNum(int type)
{	
	return BuffDefCsv::getInstance()->getStatusNum(type);
}

int DefManager::getRealStatusId(int id, int lv)
{
	if (id < USER_MOD_NEWID_BASE_STATUS) {//为了兼容新老版本,老版本的id是拆分后的id，所以需要先合并 id*1000+lv
		return (id * 1000 + lv);
	}

	return id;
}

bool DefManager::isCustomStatus(int id)
{
	return (id >= USER_MOD_NEWID_BASE_STATUS);
}

bool DefManager::isEquipStatus(int id)
{
	return (id >= USER_MOD_NEWID_BASE_STATUS * 10);
}

const BuffDef* DefManager::getStatusDef(int id)
{
	return BuffDefCsv::getInstance()->getStatusDef(id);
}

const BuffDef *DefManager::getStatusDefByIndex(int index, int type)
{
	return BuffDefCsv::getInstance()->getStatusDefByIndex(index, type);
}
const BuffDef* DefManager::getToolStatusDef(int id)
{
	return m_CurrentStatusDefs.GetRecord(id);
}

const std::vector<int> DefManager::GetBuffByNature(char nature)
{
	return BuffDefCsv::getInstance()->GetBuffByNature(nature);
}

std::vector<int> DefManager::GetBuffNatureNameByNature(char nature)
{
	return BuffDefCsv::getInstance()->GetBuffNatureNameByNature(nature);
}

int DefManager::getBuffEffectDefNum()
{	
	return BuffEffectBankCsv::getInstance()->getNum();
}

const BuffEffectDef* DefManager::getBuffEffectDef(int id)
{	
	return BuffEffectBankCsv::getInstance()->get(id);
}

const BuffEffectDef* DefManager::getBuffEffectDefByIndex(int index)
{	
	return BuffEffectBankCsv::getInstance()->getByIndex(index);
}

int DefManager::getBuffEffectEnumDefNum()
{
	return BuffEffectEnumCsv::getInstance()->getNum();
}

const BuffEffectEnumDef* DefManager::getBuffEffectEnumDef(int id)
{	
	return BuffEffectEnumCsv::getInstance()->get(id);
}

const BuffEffectEnumDef* DefManager::getBuffEffectEnumDefByIndex(int index)
{	
	return BuffEffectEnumCsv::getInstance()->getByIndex(index);
}

const BuffEffectSliderDef* DefManager::getBuffEffectSliderDef(int id)
{	
	return BuffEffectSlidingCsv::getInstance()->get(id);
}

int DefManager::getIconLibDefNum()
{	
	loadIconLibCSV();

	return m_IconLibTable.GetRecordSize();
}

const IconLibDef* DefManager::getIconLibDef(int id)
{	
	loadIconLibCSV();

	return m_IconLibTable.GetRecord(id);
}

const IconLibDef* DefManager::getIconLibDefByIndex(int index)
{	
	loadIconLibCSV();

	if (index < 0 || index >= (int)m_IconLibIds.size())
		return NULL;

	return getIconLibDef(m_IconLibIds[index]);
}

//std::vector<BiomeDef *> &DefManager::getBiomes()
//{
//	return m_Biomes;
//}

BiomeDef *DefManager::getBiomeDef(int id)
{
	s_ModManagerCachePtr = ModManager::GetInstancePtr();
	assert(s_ModManagerCachePtr);
	BiomeDef *biomeDef = s_ModManagerCachePtr->tryGetBiomeDef(id);
	if (biomeDef != nullptr)
	{
		return biomeDef;
	}

	const int id_base = s_ModManagerCachePtr->GetCustomBiome();
	if(id >= id_base){ //此时应该为地形插件的地形id
		id = id - id_base;
	}
	if(id >= m_Biomes.size())
	{
		assert(false);
		return nullptr;
	}

	assert(id>=0 && id<int(m_Biomes.size()));
	assert(&m_Biomes[id]);
	return &m_Biomes[id];
}

const BiomeDef *DefManager::getOriginalBiomeDefById(int terrainId)
{  
	assert(terrainId>=0 && terrainId<int(m_Biomes.size()));
	return &m_Biomes[terrainId];
}

BiomeDef* DefManager::addBiomeDefByCopy(int id, int copyId)
{
	const BiomeDef* templateDef = getOriginalBiomeDefById(copyId);
	if (templateDef == NULL)
	{
		return NULL;
	}

	BiomeDef newBiomeDef;
	newBiomeDef.copy(templateDef);
	newBiomeDef.ID = id;

	if (newBiomeDef.ID >= int(m_Biomes.size())) 
		m_Biomes.resize_initialized(newBiomeDef.ID + 1);
	m_Biomes[newBiomeDef.ID] = newBiomeDef;
	return &m_Biomes[newBiomeDef.ID];
}

int DefManager::getBiomeDefNum(int fieldid, int id,int index)
{
	if(1 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->biomeMonsterNum[index];
		}else{
			return 0;
		}
	}
	else if(2 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkGrassNum[index];
		}else{
			return 0;
		}
	}
	else if(3 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkFlowerNum[index];
		}else{
			return 0;
		}
	}
	else if(4 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkCoralNum[index];
		}else{
			return 0;
		}
	}
	else if(5 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkSeaPlantNum[index];
		}else{
			return 0;
		}
	}else if(6 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkTreeNum[index];
		}else{
			return 0;
		}
	}
	else if (7 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if (biome) {
			return biome->ChunkNewCoralNum[index];
		}
		else {
			return 0;
		}
	}
	else if (8 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if (biome) {
			return biome->ChunkJaggedFernNum[index];
		}
		else {
			return 0;
		}
	}
	else{
		return 0;
	}

	
}

int DefManager::getBiomeDefId(int fieldid, int id,int index){
	if(1 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->biomeMonster[index];
		}else{
			return 0;
		}
	}
	else if(2 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkGrass[index];
		}else{
			return 0;
		}
	}
	else if(3 == fieldid){

		if(id == 52){
			int a = 0;

		}
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkFlowers[index];
		}else{
			return 0;
		}
	}
	else if(4 == fieldid){
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkCorals[index];
		}else{
			return 0;
		}
	}
	else if(5 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkSeaPlants[index];
		}else{
			return 0;
		}
	}
	else if(6 == fieldid)
	{
		BiomeDef* biome = getBiomeDef(id);
		if(biome){
			return biome->ChunkTreex[index];
		}else{
			return 0;
		}
	}
	else if (7 == fieldid) {
		BiomeDef* biome = getBiomeDef(id);
		if (biome) {
			return biome->ChunkNewCorals[index];
		}
		else {
			return 0;
		}
	}
	else if (8 == fieldid) {
		BiomeDef* biome = getBiomeDef(id);
		if (biome) {
			return biome->ChunkJaggedFern[index];
		}
		else {
			return 0;
		}
	}
	else{
		return 0;
	}
}

const int DefManager::getOriginalBiomeArrayVal(int terrainId, int blockId, const char* strType)
{	
	assert(terrainId>=0 && terrainId<int(m_Biomes.size()));
	assert(NULL != strType);

	int* chunkArray = NULL;
	int* chunkNumArray = NULL;
	int nMaxNum = 0;
	BiomeDef* def = &m_Biomes[terrainId];

	if(NULL != def)
	{
		if(strcmp("ChunkGrass", strType) == 0)
		{
			chunkArray = def->ChunkGrass;
			chunkNumArray = def->ChunkGrassNum;
			nMaxNum = MAX_BIOME_GRASS;
		}
		else if(strcmp("ChunkFlowers", strType) == 0)
		{
			chunkArray = def->ChunkFlowers;
			chunkNumArray = def->ChunkFlowerNum;
			nMaxNum = MAX_BIOME_FLOWER;
		}
		else if(strcmp("ChunkCorals", strType) == 0)
		{
			chunkArray = def->ChunkCorals;
			chunkNumArray = def->ChunkCoralNum;
			nMaxNum = MAX_BIOME_CORAL;
		}
		else if(strcmp("ChunkSeaPlants", strType) == 0)
		{
			chunkArray = def->ChunkSeaPlants;
			chunkNumArray = def->ChunkSeaPlantNum;
			nMaxNum = MAX_BIOME_SEAPLANT;
		}
		else if (strcmp("ChunkNewCorals", strType) == 0)
		{
			chunkArray = def->ChunkNewCorals;
			chunkNumArray = def->ChunkNewCoralNum;
			nMaxNum = MAX_BIOME_NEWCORAL;
		}
		else if (strcmp("ChunkJaggedFern", strType) == 0)
		{
			chunkArray = def->ChunkJaggedFern;
			chunkNumArray = def->ChunkJaggedFernNum;
			nMaxNum = MAX_JAGGED_FERN;
		}
		else if(strcmp("MonsterNum", strType) == 0)
		{
			//生物数量
			chunkArray = def->biomeMonster;
			unsigned short* chunkNumArray = def->biomeMonsterNum;
			nMaxNum = MAX_BIOME_MONSTERS;

			if(NULL != chunkArray && NULL != chunkNumArray)
			{
				for(int i= 0 ;i < nMaxNum; i++)
				{
					if(chunkArray[i] == blockId){
						//找到了, 返回对应的值.
						return chunkNumArray[i];
					}
				}
			}
		}
		else
		{}

		if(NULL != chunkArray && NULL != chunkNumArray)
		{
			for(int i= 0 ;i < nMaxNum; i++)
			{
				if(chunkArray[i] == blockId){
					//找到了, 返回对应的值.
					return chunkNumArray[i];
				}
			}
		}
	}
	
	return 0;
}

DefDataTable<OreDef> &DefManager::getOriginalOreTable()
{
	loadOreCSV();

	return m_OreTable;
}

const OreDef *DefManager::getOriginalOreDefById(int blockId, bool defaultOreId /* = true */, bool igoremapid/* =false */)
{
	loadOreCSV();
	OreDef *defaultdef = nullptr;
	auto iter = m_OreTable.m_Records.begin();
	for (; iter != m_OreTable.m_Records.end(); iter++)
	{
		int id = iter->second.ID;
		if(igoremapid)						//去掉地图id的干扰，code by : keguanqiang
			id = iter->second.ID & 0xffff;  

		if (id == blockId)
		{
			return &(iter->second);
		}
		else if (401 == iter->second.ID)
		{
			defaultdef = &(iter->second);
		}
	}
	
	if (defaultOreId)
	{
		return defaultdef;
	}
	else
		return NULL;

	/*OreDef *def = m_OreTable.GetRecord(blockId);
	if(def == NULL)
	{
		return m_OreTable.GetRecord(401);
	}

	return def;*/
}

PlanetBiomeGeneDef *DefManager::getPlanetBoimeGeneDef()
{	
	loadPlanetBiomeCSV();

	return &m_planetBiomeGeneDef;
}

int DefManager::getBiomeDefCount()
{	
	return m_Biomes.size();
}

struct BiomeSortUnit
{
	const BiomeDef *def;
	float weight;
};

inline bool operator<(const BiomeSortUnit &unit1, const BiomeSortUnit &unit2)
{
	return unit1.weight > unit2.weight;
}

static float GetBiomeUnitWeight(int heat, int humid, const BiomeDef *def)
{
	float weight = 1.0f;
	/*
	int falloff = def->Falloff;

	if(heat<def->MinHeat-falloff || heat>def->MaxHeat+falloff) return 0;
	else if(heat < def->MinHeat) weight *= 1.0f - float(def->MinHeat-heat)/falloff;
	else if(heat > def->MaxHeat) weight *= 1.0f - float(heat-def->MaxHeat)/falloff;

	if(humid<def->MinHumid-falloff || humid>def->MaxHumid+falloff) return 0;
	else if(humid < def->MinHumid) weight *= 1.0f - float(def->MinHumid-humid)/falloff;
	else if(humid > def->MaxHumid) weight *= 1.0f - float(humid-def->MaxHumid)/falloff;
	*/

	return weight;
}

void DefManager::calBiomeMap()
{
	memset(&m_BiomeMap[0][0], 0, 101*101*sizeof(BiomeMapGrid));

	for(int humid=0; humid<=100; humid++)
	{
		for(int heat=0; heat<=100; heat++)
		{
			BiomeSortUnit units[16];
			int count = 0;

			float r = sqrt(float(humid*humid + (100-heat)*(100-heat)));
			float r2 = 100.0f;//-m_BiomeTable[0]->Falloff;
			if(r >= r2)
			{
				units[count].def = &m_Biomes[0];
				if(r >= 100.0f) units[count].weight = 1.0f;
				//else units[count].weight = (r-r2)/m_Biomes[0]->Falloff;
				count++;
			}

			if(r < 100.0f)
			{
				for(size_t i=1; i< m_Biomes.size(); i++)
				{
					const BiomeDef *def = &m_Biomes[i];
					if(def == NULL) continue;

					float weight = GetBiomeUnitWeight(heat, humid, def);
					if(weight == 0.0f) continue;

					units[count].def = def;
					units[count].weight = weight;
					count++;
					if(count >= 15) break;
				}
			}

			float sumweight = 0.0f;
			for(int i=0; i<count; i++) sumweight += units[i].weight;
			if(sumweight < 1.0f)
			{
				units[count].def = &m_Biomes[0];
				units[count].weight = 1.0f - sumweight;
				count++;
			}

			std::sort(&units[0], &units[count]);
			if(count > 4) count = 4;

			BiomeMapGrid &grid = m_BiomeMap[humid][heat];
			sumweight = 0.0f;
			for(int i=0; i<count; i++) sumweight += units[i].weight;

			for(int i=0; i<count; i++)
			{
				grid.def[i] = units[i].def;
				grid.weight[i] = units[i].weight/sumweight;
			}
		}
	}
}

bool DefManager::load()
{
	loadFirst(); 
	loadInLoading();
	return true;
}

bool DefManager::loadEx() {
	uint64_t startTime = Rainbow::GetTimeMS();


	loadFilterString();

    bool ret = false;

    ret = loadFuncSwitchDef();
    if (ret) LOG_INFO("load csv succeed: FuncSwitch");
    else LOG_WARNING("load csv failed: FuncSwitch");

    ret = loadFurnaceCSV();
    if (ret) LOG_INFO("load csv succeed: Furnace");
    else LOG_WARNING("load csv failed: Furnace");

    ret = loadGameRuleDef();
    if (ret) LOG_INFO("load csv succeed: GameRule");
    else LOG_WARNING("load csv failed: GameRule");

    ret = loadHeadIconDef();
    if (ret) LOG_INFO("load csv succeed: HeadIcon");
    else LOG_WARNING("load csv failed: HeadIcon");

    ret = SprayPaintDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: SprayPaint");
    else LOG_WARNING("load csv failed: SprayPaint");

    ret = SoundStrDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: SoundStr");
    else LOG_WARNING("load csv failed: SoundStr");

    ret = ParticlesStrDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: ParticlesStr");
    else LOG_WARNING("load csv failed: ParticlesStr");
	
    ret = loadFuncSwitchDef();
    if (ret) LOG_INFO("load csv succeed: FuncSwitch");
    else LOG_WARNING("load csv failed: FuncSwitch");

    ret = loadFurnaceCSV();
    if (ret) LOG_INFO("load csv succeed: Furnace");
    else LOG_WARNING("load csv failed: Furnace");

    ret = loadGameRuleDef();
    if (ret) LOG_INFO("load csv succeed: GameRule");
    else LOG_WARNING("load csv failed: GameRule");

    // Block related
    ret = BlockDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: Block");
    else LOG_WARNING("load csv failed: Block");

    ret = loadOreCSV();
    if (ret) LOG_INFO("load csv succeed: Ore");
    else LOG_WARNING("load csv failed: Ore");

    // Creature related
    ret = MonsterCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: Monster");
    else LOG_WARNING("load csv failed: Monster");

    ret = loadBiomeCSV();
    if (ret) LOG_INFO("load csv succeed: Biome");
    else LOG_WARNING("load csv failed: Biome");

	ret = loadBuildReplaceCSV();
	if (ret) LOG_INFO("load csv succeed: BuildingSpecialBlock");
	else LOG_WARNING("load csv failed: BuildingSpecialBlock");

    ret = loadMonsterBiomeCSV();
    if (ret) LOG_INFO("load csv succeed: MonsterBiome");
    else LOG_WARNING("load csv failed: MonsterBiome");

	ret = loadMonsterSpawnDef();
	if (ret) LOG_INFO("load csv succeed: MonsterSpawn");
	else LOG_WARNING("load csv failed: MonsterSpawn");

    ret = loadMobSpawnerDef();
    if (ret) LOG_INFO("load csv succeed: MobSpawner");
    else LOG_WARNING("load csv failed: MobSpawner");

    ret = loadPlanetBiomeCSV();
    if (ret) LOG_INFO("load csv succeed: PlanetBiome");
    else LOG_WARNING("load csv failed: PlanetBiome");

    ret = ProjectileDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: Projectile");
    else LOG_WARNING("load csv failed: Projectile");

    ret = loadGunDef();
    if (ret) LOG_INFO("load csv succeed: Gun");
    else LOG_WARNING("load csv failed: Gun");

    ret = loadCraftingCSV();
    if (ret) LOG_INFO("load csv succeed: Crafting");
    else LOG_WARNING("load csv failed: Crafting");

    ret = loadHorseCSV();
    if (ret) LOG_INFO("load csv succeed: Horse");
    else LOG_WARNING("load csv failed: Horse");

    ret = loadHorseEggDef();
    if (ret) LOG_INFO("load csv succeed: HorseEgg");
    else LOG_WARNING("load csv failed: HorseEgg");

    ret = loadCharacterDef();
    if (ret) LOG_INFO("load csv succeed: Character");
    else LOG_WARNING("load csv failed: Character");

    // Items
    ret = ItemDefCsv::getInstance()->load();
	if (ret) {
		LOG_INFO("load csv succeed: Item");
	}
    else LOG_WARNING("load csv failed: Item");


    ret = ItemSkillDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: ItemSkill");
    else LOG_WARNING("load csv failed: ItemSkill");

    ret = ToolDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: Tool");
    else LOG_WARNING("load csv failed: Tool");

    // Buffs and Physics
    ret = loadBuffCSV();
    if (ret) LOG_INFO("load csv succeed: Buff");
    else LOG_WARNING("load csv failed: Buff");

    ret = BuffDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: BuffDef");
    else LOG_WARNING("load csv failed: BuffDef");

    ret = loadPhysicsPartsCSV();
    if (ret) LOG_INFO("load csv succeed: PhysicsParts");
    else LOG_WARNING("load csv failed: PhysicsParts");

    g_DefMgr.SetSleepBiomeWeight();

    // Battle Pass
    ret = loadBPDrawCSV();
    if (ret) LOG_INFO("load csv succeed: BPDraw");
    else LOG_WARNING("load csv failed: BPDraw");

    ret = loadBPMissionCSV();
    if (ret) LOG_INFO("load csv succeed: BPMission");
    else LOG_WARNING("load csv failed: BPMission");

    ret = loadBPRewardCSV();
    if (ret) LOG_INFO("load csv succeed: BPReward");
    else LOG_WARNING("load csv failed: BPReward");

    ret = loadBPSettingCSV();
    if (ret) LOG_INFO("load csv succeed: BPSetting");
    else LOG_WARNING("load csv failed: BPSetting");

    // Homeland
    if (SandboxCoreDriver::GetInstancePtr() != nullptr) {
        SandboxEventDispatcherManager::GetGlobalInstance().Emit("Homeland_LoadCsv", SandboxContext(nullptr));
    }

    // Platform specific resources
#ifdef IWORLD_ADVANCE_BUILD
    ret = loadResNoToLoadDef();
    if (ret) LOG_INFO("load csv succeed: ResNoToLoad");
    else LOG_WARNING("load csv failed: ResNoToLoad");
#endif

#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
    ret = loadSpecialUinDef();
    if (ret) LOG_INFO("load csv succeed: SpecialUin");
    else LOG_WARNING("load csv failed: SpecialUin");
#endif

    // Region specific resources
    if (GetClientInfoProxy()->getIsOverseasVer()) {
        ret = loadOverseasGrayCSV();
        if (ret) LOG_INFO("load csv succeed: OverseasGray");
        else LOG_WARNING("load csv failed: OverseasGray");
    }

    // Additional resources
    ret = loadWikiConfigCSV();
    if (ret) LOG_INFO("load csv succeed: WikiConfig");
    else LOG_WARNING("load csv failed: WikiConfig");

    ret = BotConversationsDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: BotConversations");
    else LOG_WARNING("load csv failed: BotConversations");

    ret = SurviveTaskDefCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: SurviveTask");
    else LOG_WARNING("load csv failed: SurviveTask");

    // UGC models
    ret = UgcModelCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: UgcModel");
    else LOG_WARNING("load csv failed: UgcModel");

    ret = UgcMaterialCsv::getInstance()->load();
    if (ret) LOG_INFO("load csv succeed: UgcMaterial");
    else LOG_WARNING("load csv failed: UgcMaterial");

	ret = OperateUIDataCsv::getInstance()->load();

	uint64_t endTime = Rainbow::GetTimeMS();
	LOG_WARNING("load csv: total time: %llu ms", endTime - startTime);
	
	return true;
}

bool DefManager::loadFirst() {
	loadFilterString();
	return true;
}

bool DefManager::loadInLoading(int step/*=0*/){

	switch(step)
	{
	case 1:
		{
			//公共
			loadFuncSwitchDef();
			loadFurnaceCSV();
			loadGameRuleDef();
			loadHeadIconDef();
			loadChestSpawnDef();
			loadSkinningToolDef();
			SprayPaintDefCsv::getInstance()->load();	//20211020 加载喷漆表格 codeby:keguanqiang
			break;
		}
	case 2:
		{
			//方块
			BlockDefCsv::getInstance()->load();
			loadOreCSV();
			break;
		}
	case 3:
		{
			//生物
			MonsterCsv::getInstance()->load();
			loadBiomeCSV();
			loadMonsterBiomeCSV();
			loadMobSpawnerDef();
			loadBuildReplaceCSV();
			break;
		}
	case 4:
		{
			loadPlanetBiomeCSV();
			ProjectileDefCsv::getInstance()->load();
			loadGunDef();
			loadCraftingCSV();
			break;
		}
	case 5:
		{
			loadHorseCSV();
			loadHorseEggDef();
			loadCharacterDef();
			break;
		}
	case 6:
		{
			//道具
			ItemDefCsv::getInstance()->load();
			ItemSkillDefCsv::getInstance()->load();
			ToolDefCsv::getInstance()->load();
			EquipGroupDefCsv::getInstance()->load();
			break;
		}
	case 7:
		{
			//BUFF
			loadBuffCSV();
			BuffDefCsv::getInstance()->load();
			//物理
			loadPhysicsPartsCSV();
			g_DefMgr.SetSleepBiomeWeight();
			break;
		}
	case 8:
		{
			loadBPDrawCSV();
			loadBPMissionCSV();
			loadBPRewardCSV();
			loadBPSettingCSV();
			break;
		}
	case 9:
		{	
			break;
		}
	case 10:
		{
#ifdef IWORLD_ADVANCE_BUILD
			loadResNoToLoadDef();
#endif
#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
			loadSpecialUinDef();
#endif

			if(GetClientInfoProxy()->getIsOverseasVer())
			{
				loadOverseasGrayCSV();
			}
			break;
		}
	default:
		{
			//公共
			loadFuncSwitchDef();
			loadFurnaceCSV();
			loadGameRuleDef();
			loadHeadIconDef();
			SoundStrDefCsv::getInstance()->load();
			ParticlesStrDefCsv::getInstance()->load();
			//方块
			BlockDefCsv::getInstance()->load();
			loadOreCSV();
			LOG_INFO("Load BuildingSpecialBlock_new from loadInLoading: default");
			loadBuildReplaceCSV();
			//生物
			MonsterCsv::getInstance()->load();
			PlayerAttribCsv::getInstance()->load();
			ArchitecturalBlueprintCsv::getInstance()->load();
			loadBiomeCSV();
			loadMonsterBiomeCSV();
			loadMonsterSpawnDef();
			loadMobSpawnerDef();
			loadPlanetBiomeCSV();
			ProjectileDefCsv::getInstance()->load();
			loadGunDef();
			loadCraftingCSV();
			loadHorseCSV();
			loadHorseEggDef();
			loadCharacterDef();
			//道具
			ItemDefCsv::getInstance()->load();
			ItemSkillDefCsv::getInstance()->load();
			ToolDefCsv::getInstance()->load();
			//BUFF
			loadBuffCSV();
			BuffDefCsv::getInstance()->load();
			g_DefMgr.SetSleepBiomeWeight();
			//物理
			loadPhysicsPartsCSV();
			loadBPDrawCSV();
			loadBPMissionCSV();
			loadBPRewardCSV();
			loadBPSettingCSV();
			OperateUIDataCsv::getInstance()->load();
			//家园
			if (SandboxCoreDriver::GetInstancePtr() != nullptr)
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("Homeland_LoadCsv", SandboxContext(nullptr));

			#ifdef IWORLD_ADVANCE_BUILD
				loadResNoToLoadDef();
			#endif

			#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
				loadSpecialUinDef();
			#endif

			if(GetClientInfoProxy()->getIsOverseasVer())
			{
				loadOverseasGrayCSV();
			}

			loadWikiConfigCSV();

			SprayPaintDefCsv::getInstance()->load();	//20211020 加载喷漆表格 codeby:keguanqiang
			BotConversationsDefCsv::getInstance()->load();
			SurviveTaskDefCsv::getInstance()->load();
			SurviveObjectiveDefCsv::getInstance()->load();

			//ugcmodel
			UgcModelCsv::getInstance()->load();
			UgcMaterialCsv::getInstance()->load();

			WorkbenchTechCsv::getInstance()->load();
			WorkbenchTechCsv::getInstance()->buildWorkbenchTree();
			break;
		}
	}
	return true;
}

void DefManager::clearRandomNames()
{
	for(size_t i=0; i<m_RandomSurnames.size(); i++)
	{
		free(m_RandomSurnames[i]);
	}
	for(size_t i=0; i<m_RandomMaleNames.size(); i++)
	{
		free(m_RandomMaleNames[i]);
	}
	for(size_t i=0; i<m_RandomFemaleNames.size(); i++)
	{
		free(m_RandomFemaleNames[i]);
	}
}

static void AddOneName(std::vector<char *> &names, const char *src)
{
	if(src[0])
	{
		names.push_back(strdup(src));
	}
}

bool DefManager::loadRandomNames()
{
    static NoFreeFixedString random_names("random_names");
	//const char* filename = "random_names";
	if (hasLoaded(random_names))
	{
		return true;
	}
	MultiLanCSVParser parser(false, true);
	char filepath[64] = {0};
	m_CsvLoadConfig->getRandomNamePath(filepath);
	if (!parser.Load(filepath, true))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);

	std::string tmp;
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		AddOneName(m_RandomSurnames, parser[i]["Surname"].Str());
		AddOneName(m_RandomMaleNames, parser[i]["Male"].Str());
		AddOneName(m_RandomFemaleNames, parser[i]["Female"].Str());
	}
	
 	LOG_INFO("loadRandomNames(): %s loading finished", filepath);
	return true;
}


bool isAllEnglish(String str,int env)
{
	if(env >= 10)
	{		
		if(str[0] == '\0')
			return false;
	}else
	{
		if(str[0] == '\0' || str[0] == ' ')
		return false;
	}

	bool isAllEnglish_ = true;
	for ( int j=0; j<(int)str.length(); j++ )
	{
		if ((unsigned char)str[j] > 127) {
			isAllEnglish_ = false;
			break;
		}
	}
	return isAllEnglish_;
}
bool DefManager::hasLoaded(const Rainbow::NoFreeFixedString& filename) {
    auto iter = m_mLoadedFlag.find(filename);
    if (iter == m_mLoadedFlag.end()) {
        m_mLoadedFlag.insert(make_pair(filename, true));
        return false;
    }
    return true;
}
bool DefManager::hasLoaded(const char* filename)
{	
    FixedString str(filename);
	auto iter = m_mLoadedFlag.find(str);
	if(iter == m_mLoadedFlag.end())
	{
		m_mLoadedFlag.insert(make_pair(str, true));
		return false;
	}
	return true;
}

bool DefManager::loadFilterString()
{	
    static NoFreeFixedString filterstring("filterstring");
	if (hasLoaded(filterstring))
	{
		return false;
	}

	KeywordFilterMgr:GetKeywordFilterMgr();

	char filepath[64] = { 0 };
	m_CsvLoadConfig->getFilterStringPath(filepath);

	if (dynamic_cast<OverseasCsvLoadConfig*>(m_CsvLoadConfig))
	{
		if (!loadFilterStringEN(filepath))
		{
			LOG_WARNING("loadFilterString(): %s loading failed", filepath);
			DefaultCsvLoadConfig* defaultConfig = ENG_NEW(DefaultCsvLoadConfig)();
			defaultConfig->getFilterStringPath(filepath);
			ENG_DELETE(defaultConfig);
			if (!loadFilterStringCN(filepath))
			{
				LOG_WARNING("loadFilterString(): %s loading failed", filepath);
				#ifdef IWORLD_DEV_BUILD
				{
					PCDebugCsvLoadConfig* debugConfig = ENG_NEW(PCDebugCsvLoadConfig)();
					debugConfig->getFilterStringPath(filepath);
					ENG_DELETE(debugConfig);
					if (!loadFilterStringCN(filepath))
					{
						LOG_WARNING("loadFilterString(): %s loading failed", filepath);
					}
				}
				#endif
			}
		}
	}
	else
	{
		loadFilterStringCN(filepath);
	}
	//loadFilterStringCN(filepath);

	return true;
}

bool DefManager::preloadVoxelPalette()
{	
    static NoFreeFixedString voxpal("voxpal");
	if (hasLoaded(voxpal))
	{
		return false;
	}

	char format[32];
	m_CsvLoadConfig->getPath("voxpal_%d", format);
	//动态变化
	for (int i = 0; i <= 100; ++i)
	{
		char path[32];
		sprintf(path, format, i);
		if (!loadVoxelPalette(path)) {
			break;
		}
	}
	
 	LOG_INFO("preloadVoxelPalette(): loading finished");
	return true;
}

// 简体中文词库
bool DefManager::loadFilterStringCN(const char *filepath)
{
	if (GetClientInfoProxy()->isPureServer())
	{
		return true;
	}
    MultiLanCSVParser parser(false, true);
	if (!parser.Load(filepath, true))
	{
 		LOG_WARNING("loadFilterStringCN(): %s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);

	core::string tmp;
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		if ( ! parser[i]["FilterString"] ) {
			continue;
		}
		tmp = core::string(parser[i]["FilterString"].Str());

		if (tmp.length() <= 0  )
		{			
			continue;
		}
		if ( tmp[0] == '\0' || tmp[0] == ' ' )
		{
			continue;
		}
		if (i == 2) 
		{	
			MNSandbox::GetGlobalEvent().Emit<core::string &, int>("Filter_setInvalidCodes", tmp, 2);
		}
		else if (i == 3)
		{
			MNSandbox::GetGlobalEvent().Emit<core::string &, int>("Filter_setInvalidCodes", tmp, 4);
		}
		else if (i == 4)
		{
			if (parser[i]["filtertype"] && parser[i]["filtertype"].Str())
			{
				core::string str = parser[i]["filtertype"].Str();
				MNSandbox::GetGlobalEvent().Emit<core::string, core::string&>("Filter_setDivideTokens", str, tmp);
			}
		}
		else
		{
			int filtertype = parser[i]["filtertype"].Int();
			int filternum = parser[i]["filternum"].Int();
			if (filternum == 0)
				filternum = 6;
			toLowerCaseMultilingual( tmp );
			MNSandbox::GetGlobalEvent().Emit<core::string &, int, int>("Filter_addOneFilterString", tmp, filtertype, filternum);
		}
	}

	int env = GetIWorldConfigProxy()->getGameData("game_env");
	if (env >= 10)
	{
		MNSandbox::GetGlobalEvent().Emit<>("Filter_setCheckPingyin");
	}

 	LOG_INFO("loadFilterStringCN(): %s loading finished", filepath);
	return true;
}

static std::string language_filter[16] = { "FilterString", "ENFilterString", "TWFilterString",
	"THAFilterString","ESNFilterString",  "PTBFilterString",
	"FRAFilterString","JPNFilterString",  "ARAFilterString",
	"KORFilterString","VIEFilterString",  "RUSFilterString",
	"TURFilterString","ITAFilterString",  "GERFilterString",
	"IDFilterString" 
};

// 海外 公共敏感词 分拆单个文件
bool DefManager::loadFilterStringEN(const char *filepath)
{
	if (GetClientInfoProxy()->isPureServer())
	{
		return true;
	}
    MultiLanCSVParser parser(false, true);
	if (!parser.Load(filepath, true))
	{
 		LOG_WARNING("loadFilterStringEN(): %s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	core::string tmp;
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		// 没有的字段 就不处理了
		std::string filters = "FilterString";
		if ( !parser[i][filters.c_str()]) {
			continue;
		}
		tmp =  String(parser[i][filters.c_str()].Str());
		if (tmp.length() <= 0  )
		{			
			continue;
		}
		if ( tmp[0] == '\0' ) // || tmp[0] == ' ' )
		{
			break;
		}
		if (i == 2) 
		{	
			MNSandbox::GetGlobalEvent().Emit<core::string &, int>("Filter_setInvalidCodes", tmp, 2);
		}
		else if (i == 3)
		{
			MNSandbox::GetGlobalEvent().Emit<core::string &, int>("Filter_setInvalidCodes", tmp, 4);
		}
		else if (i == 4)
		{
			if (parser[i]["filtertype"])
			{
				core::string str = parser[i]["filtertype"].Str();
				MNSandbox::GetGlobalEvent().Emit<core::string, core::string&>("Filter_setDivideTokens", str, tmp);
			}
		}
		else
		{
			toLowerCaseMultilingual( tmp );
			int filtertype = parser[i]["filtertype"].Int();
			int filternum = parser[i]["filternum"].Int();
			if (filternum == 0)
				filternum = 5;
			if (tmp.length() <= 0  )
			{			
				continue;
			}
			if ( tmp[0] == '\0' ) // || tmp[0] == ' ' )
			{
				break;
			}

			MNSandbox::GetGlobalEvent().Emit<core::string &, int, int>("Filter_addOneFilterString", tmp, filtertype, filternum);
		}
	}
 	int env = GetIWorldConfigProxy()->getGameData("game_env");
	if (env >= 10)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("Filter_setCheckPingyin", SandboxContext(nullptr));
	}

 	LOG_INFO("loadFilterStringEN(): %s loading finished", filepath);
	return true;
}
/*
static int GetNoiseType(const char *name)
{
	if(strcmp(name, "BILLOW") == 0) return anl::BILLOW;
	else if(strcmp(name, "FBM") == 0) return anl::FBM;
	else if(strcmp(name, "RIDGEDMULTI") == 0) return anl::RIDGEDMULTI;
	else
	{
		assert(0);
		return 0;
	}
}*/

static void CheckBiomeBlock(BiomeDef *def, int blockid)
{
	if(blockid>0 && BlockDefCsv::getInstance()->get(blockid)==NULL)
	{
		LOG_SEVERE("biome %s use not-exist block: %d", def->Name.c_str(), blockid);
	}
}

static BIOME_TYPE Name2BiomeType(const char *name)
{
	assert(sizeof(BiomeTypeNames) / sizeof(BiomeTypeNames[0]) > MAX_BIOME_TYPE); // 新增的生态，名称未配置，会导致无法加载
	for(int i=0; i<MAX_BIOME_TYPE; i++)
	{
		if(strcmp(name, BiomeTypeNames[i]) == 0) return (BIOME_TYPE)i;
	}
	return MAX_BIOME_TYPE;
}

BIOME_TYPE DefManager::getbiot(const char *name) {

	return Name2BiomeType(name);
}

int DefManager::getCurBiomeBuff(int biomeid) 
{//获取当前生态buff
	if (biomeid < 0 || biomeid >= MAX_BIOME_TYPE)
	{
		assert(false);
		return 0;
	}

	std::map<int, float> BuffProbability;
	std::vector<int> sleepids = BuffDefCsv::getInstance()->getSleepIds();
	for (int i = 0; i < sleepids.size(); i++) {
		auto iter = BiomeBuffWeight[sleepids[i]].find(BiomeTypeNames[biomeid]);
		if (iter != BiomeBuffWeight[sleepids[i]].end()) {
			BuffProbability[sleepids[i]] = iter->second / (BiomeTotalWeight[BiomeTypeNames[biomeid]] * 1.0f);
		}
	}
	srand(time(0));
	float rand_probability = std::rand() % 100 / (float)101;
	float num = 0.0f;
	for (auto BuffProbabilityIter = BuffProbability.begin(); BuffProbabilityIter != BuffProbability.end(); BuffProbabilityIter++) {
		num = num + BuffProbabilityIter->second;
		if (rand_probability < num) {
			return BuffProbabilityIter->first;
		}
	}

	return 0;
}

int DefManager::getCurBiomeBuffFixed(int biomeid, float fixed,std::vector<int>SleepDebuff)
{
	if (biomeid < 0 || biomeid >= MAX_BIOME_TYPE)
	{
		assert(false);
		return 0;
	}
	auto FixedBiomeBuffWeight = BiomeBuffWeight;
	auto contains = [](std::vector<int>SleepDebuff, int id) -> bool {
		return std::find(SleepDebuff.begin(), SleepDebuff.end(), id) != SleepDebuff.end();
	};
	for (auto it = FixedBiomeBuffWeight.begin(); it != FixedBiomeBuffWeight.end(); ++it) {
		if (contains(SleepDebuff, it->first))
		{
			for (auto it2 = it->second.begin(); it2 != it->second.end(); ++it2)
			{
				it2->second *= (2.0f - fixed);
			}
		}
	}
	std::map<int, float> BuffProbability;
	std::vector<int> sleepids = BuffDefCsv::getInstance()->getSleepIds();
	for (int i = 0; i < sleepids.size(); i++) {
		auto iter = FixedBiomeBuffWeight[sleepids[i]].find(BiomeTypeNames[biomeid]);
		if (iter != FixedBiomeBuffWeight[sleepids[i]].end()) {
			BuffProbability[sleepids[i]] = iter->second / (BiomeTotalWeight[BiomeTypeNames[biomeid]] * 1.0f);
		}
	}
	srand(time(0));
	float rand_probability = rand() % 100 / (float)101;
	float num = 0.0f;
	for (auto BuffProbabilityIter = BuffProbability.begin(); BuffProbabilityIter != BuffProbability.end(); BuffProbabilityIter++) {
		num = num + BuffProbabilityIter->second;
		if (rand_probability < num) {
			return BuffProbabilityIter->first;
		}
	}

	return 0;
}

void DefManager::SetSleepBiomeWeight() {//计算每个生态的总权重和在当前buff的生态权重
	std::vector<int> sleepids = BuffDefCsv::getInstance()->getSleepIds();
	std::map<std::string, int> AllBiomeWeight;
	for (int i = 0; i < MAX_BIOME_TYPE; i++) {
		int TotalWeight = 0;
		for (int j = 0; j < sleepids.size(); j++) {
			int buffid = sleepids[j];
			auto buff = BuffDefCsv::getInstance()->get(buffid);
			auto BuffBiomeIter = buff->BiomeWeight.find(BiomeTypeNames[i]);
			if (BuffBiomeIter != buff->BiomeWeight.end()) {
				if (BuffBiomeIter->second < 0 && Abs(BuffBiomeIter->second) > buff->SleepWeight) {

				}
				else {
					TotalWeight = TotalWeight + BuffBiomeIter->second + buff->SleepWeight;
				}
			}
			else {
				TotalWeight = TotalWeight + buff->SleepWeight;
			}
		}
		BiomeTotalWeight[BiomeTypeNames[i]] = TotalWeight;
	}
	for (int i = 0; i < sleepids.size(); i++) {
		auto buff = BuffDefCsv::getInstance()->get(sleepids[i]);
		for (int j = 0; j < MAX_BIOME_TYPE; j++) {
			auto buffIter = buff->BiomeWeight.find(BiomeTypeNames[j]);
			if (buffIter != buff->BiomeWeight.end()) {
				if (buffIter->second < 0 && Abs(buffIter->second) > buff->SleepWeight) {
					AllBiomeWeight[BiomeTypeNames[j]] = 0;
				}
				else {
					AllBiomeWeight[BiomeTypeNames[j]] = buffIter->second + buff->SleepWeight;
				}

			}
			else {
				AllBiomeWeight[BiomeTypeNames[j]] = buff->SleepWeight;
			}
		}
		BiomeBuffWeight[sleepids[i]] = AllBiomeWeight;
	}

}

static void ReadChunkGenParam(int retid[], int retnum[], int maxnum, const CSVParser::TableLine &csvline, int *srcids, const size_t srcids_count, const char *prefix)
{
	memset(retid, 0, maxnum*sizeof(int));
	memset(retnum, 0, maxnum*sizeof(int));

	int count = 0;
	char name[256];
	size_t srcids_index = 0;
	while(srcids_index < srcids_count && *srcids > 0)
	{
		sprintf(name, "%s%d", prefix, *srcids);
		int n = csvline[name].Int();
		if(n >= 0)
		{
			retid[count] = *srcids;
			retnum[count] = n;
			count++;

			if(count >= maxnum) return;
		}

		srcids++;
		srcids_index++;
	}
}

static int grass_ids[] = {224,225, 243, 238, 110,321,322,323, 2008, 2009, 2010, 2011,151023,151024,151025,151026 };
static int flower_ids[] = {
	300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 761, 3751, 3752, 3753, 3754, 3755,
	3756, 3757, 3758, 3759, 3760, 3761, 3762, 3763, 3764, 3765, 3766,3767, 3771, 3772,
	3773, 150072, 150073, 150074, 150075, 150076
};
static int jar_ids[] = { 737, 738, 739, 0 };
static int coral_ids[] = { 314, 315, 316, 317, 318, 319, 320, 0 };
static int seaplant_ids[] = { 245, 246, 495, 0 };
static int newcoral_ids[] = { 486, 487, 488, 489, 490, 491, 492, 493 };
static int jaggedfern_ids[] = { 287 };

static void LoadBiomePlantTryDef(BiomePlantTryDef &def, const CSVParser::TableLine &csvline)
{
	def.Trees = csvline["ChunkTrees"].Int();

	ReadChunkGenParam(def.Grass, def.GrassNum, MAX_BIOME_GRASS, csvline, grass_ids, ARRAY_SIZE(grass_ids), "ChunkGrass");
	ReadChunkGenParam(def.Flowers, def.FlowerNum, MAX_BIOME_FLOWER, csvline, flower_ids, ARRAY_SIZE(flower_ids), "ChunkFlowers");
	ReadChunkGenParam(def.Jars, def.JarNum, 4, csvline, jar_ids, ARRAY_SIZE(jar_ids), "ChunkJar");
	ReadChunkGenParam(def.Corals, def.CoralNum, 8, csvline, coral_ids, ARRAY_SIZE(coral_ids), "ChunkCorals");
	ReadChunkGenParam(def.SeaPlants, def.SeaPlantNum, 4, csvline, seaplant_ids, ARRAY_SIZE(seaplant_ids), "ChunkSeaPlants");
	ReadChunkGenParam(def.NewCorals, def.NewCoralNum, 8, csvline, newcoral_ids, ARRAY_SIZE(newcoral_ids), "ChunkNewCorals");
	ReadChunkGenParam(def.JaggedFern, def.JaggedFernNum, 1, csvline, jaggedfern_ids, ARRAY_SIZE(jaggedfern_ids), "ChunkJaggedFern");

	def.Pumpkin = csvline["ChunkPumpkin"].Int();
	def.Watermelon = csvline["ChunkWatermelon"].Int();
	def.DeadBush = csvline["ChunkDeadBush"].Int();
	def.Reeds = csvline["ChunkReeds"].Int();
	def.Rice = csvline["ChunkRice"].Int();
	def.Cactus = csvline["ChunkCactus"].Int();
	def.Mushroom = csvline["ChunkMushroom"].Int();
	def.RedPoisonousMushroom = csvline["ChunkRedPoisonousMushroom"].Int();
	def.Crystal = csvline["ChunkCrystal"].Int();
	def.Cotton = csvline["ChunkCotton"].Int();
	def.giantScallops = csvline["ChunkGiantScallops346"].Int();
	def.Pineapple = csvline["ChunkPineapple1187"].Int();
	def.Shoreflower = csvline["ChunkShoreflower1186"].Int();
	def.NewCoralCluster = csvline["ChunkRandomCorals"].Int();
	def.NewCoralDeathCluster = csvline["ChunkRandomDeadCorals"].Int();
	def.WitheredThicket = csvline["ChunkDeadThicket763"].Int();

	def.Waterlily = csvline["ChunkWaterlily"].Int();
	def.Duckweed = csvline["ChunkDuckweed"].Int();
	def.Waterweed = csvline["ChunkWaterweed"].Int();
	def.LotusFlower = csvline["ChunkLotusFlower"].Int();
	def.FloatingFlower = csvline["ChunkFloatingFlower"].Int();
	def.Floatingplank = csvline["ChunkFloatingplank"].Int();
}

bool DefManager::loadPlanetBiomeCSV()
{
    static NoFreeFixedString filename("planetbiomegenedef");
	//const char* filename = "planetbiomegenedef";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	//m_planetBiomeGeneDef = ENG_NEW(PlanetBiomeGeneDef)();
	//PlanetBiomeGeneDef def;
	//m_planetBiomeGeneDef = &def;
	parser.SetTitleLine(2);
	m_planetBiomeGeneDef.bottom_base_height_y =  parser[3]["BOTTOM_BASE_HEIGHT_Y"].Int();
	m_planetBiomeGeneDef.bottom_base_max_height_y =  parser[3]["BOTTOM_BASE_MAX_HEIGHT_Y"].Int();
	m_planetBiomeGeneDef.airland_min_height =  parser[3]["AIRLAND_MIN_HEIGHT"].Int();
	m_planetBiomeGeneDef.airland_base_height_y  =  parser[3]["AIRLAND_BASE_HEIGHT_Y"].Int();
	m_planetBiomeGeneDef.bottom_noise_data =  parser[3]["BOTTOM_NOISE_DATA"].Int();
	m_planetBiomeGeneDef.airland_noise_data =  parser[3]["AIRLAND_NOISE_DATA"].Int();
	m_planetBiomeGeneDef.airland_bottom_noise_data =  parser[3]["AIRLAND_BOTTOM_NOISE_DATA"].Int();
	m_planetBiomeGeneDef.totem_chunk_offset =  parser[3]["TOTEMGEN_CHUNK_MOD_NUM"].Int();
	m_planetBiomeGeneDef.totem_chunk_trynum =  parser[3]["TOTEMGEN_MAX_LOOP_NUM"].Int();
	m_planetBiomeGeneDef.totem_oxygen_fruit_prob =  parser[3]["TOTEMGEN_OXYGEN_FRUIT_PROB"].Int();
	m_planetBiomeGeneDef.commicshop_chunk_offset = parser[3]["COMMICSSHOP_CHUNK_MOD_NUM"].Int();
	m_planetBiomeGeneDef.commicshop_chunk_trynum = parser[3]["COMMICSHOP_MAX_LOOP_NUM"].Int();
	m_planetBiomeGeneDef.commicshop_min_height = parser[3]["COMMICSHOP_MIN_Y"].Int();
	
	return true;
}

void DefManager::praseBiome(MultiLanCSVParser& parser)
{
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	if (strcmp(parser[2]["TypeName"].Str(), "Times") != 0)
	{
		return;
	}
	LoadBiomePlantTryDef(m_BiomePlantTries, parser[2]);

	for (int i = 3; i < numLines; ++i)
	{
		BIOME_TYPE biometype = Name2BiomeType(parser[i]["TypeName"].Str());

		if (biometype == MAX_BIOME_TYPE) continue;

		//BiomeDef *def = ENG_NEW(BiomeDef);
		BiomeDef def;
		memset(def.biomeMonster, 0, sizeof(def.biomeMonster));
		memset(def.biomeMonsterNum, 0, sizeof(def.biomeMonsterNum));

		memset(def.ChunkTreex, 0, sizeof(def.ChunkTreex));
		memset(def.ChunkTreeNum, 0, sizeof(def.ChunkTreeNum));

		def.Name = parser[i]["Name"].Str();

		def.ID = biometype;
		def.copyId = biometype;
		def.MinHeight = parser[i]["MinHeight"].Float();
		def.MaxHeight = parser[i]["MaxHeight"].Float();
		def.Heat = parser[i]["Heat"].Float() / 100.0f;
		def.Humid = parser[i]["Humid"].Float() / 100.0f;

		def.FillBlock = parser[i]["FillBlock"].Int();
		def.FillDepth = parser[i]["FillDepth"].Int();
		def.TopBlock = parser[i]["TopBlock"].Int();
		sscanf(parser[i]["WaterColor"].Str(), "%x", &def.WaterColor);
		sscanf(parser[i]["GrassColor"].Str(), "%x", &def.GrassColor);

		for (int k = 0; k < MAX_BIOME_WATER_FOG; k++)
		{
			def.WaterFogColor[k] = 0;
			def.WaterFogDepth[k] = -1;
		}
		vector<string> vColor;
		vColor.clear();
		std::string sColor = parser[i]["WaterFogColor"].Str();
		StringUtil::split(vColor, sColor, "|");
		if (vColor.size() != 0)
		{
			for (int k = 0; k < MAX_BIOME_WATER_FOG && k < (int)vColor.size(); k++)
			{
				sscanf(vColor[k].c_str(), "%x", &def.WaterFogColor[k]);
			}
		}
		vector<string> vDepth;
		vDepth.clear();
		std::string sDepth = parser[i]["WaterFogDepth"].Str();
		StringUtil::split(vDepth, sDepth, "|");
		if (vDepth.size() != 0)
		{
			for (int k = 0; k < MAX_BIOME_WATER_FOG && k < (int)vDepth.size(); k++)
			{
				sscanf(vDepth[k].c_str(), "%i", &def.WaterFogDepth[k]);
			}
		}
		for (int k = 0; k < 2; k++)
		{
			def.Temperature[k] = 0;
		}
		vector<string> vTemp;
		vTemp.clear();
		std::string sTemp = parser[i]["Temperature"].Str();
		StringUtil::split(vTemp, sTemp, "|");
		if (vTemp.size() != 0)
		{
			for (int k = 0; k < 2 && k < (int)vTemp.size(); k++)
			{
				sscanf(vTemp[k].c_str(), "%i", &def.Temperature[k]);
			}
		}
		def.EnableRain = parser[i]["EnableRain"].Int() > 0;
		def.EnableSnow = parser[i]["EnableSnow"].Int() > 0;

		def.ClearModulus = parser[i]["ClearModulus"].Float();
		def.RainModulus = parser[i]["RainModulus"].Float();
		def.ThunderModulus = parser[i]["ThunderModulus"].Float();

		def.ChunkTrees = parser[i]["ChunkTrees"].Int();

		std::map<int, int> idkv;
		std::string sChunkObj = parser[i]["ChunkObj"].Str();
		std::vector<std::string> vChunkTemp;
		StringUtil::split(vChunkTemp, sChunkObj, "|");
		for (auto it = vChunkTemp.begin(); it != vChunkTemp.end(); it++)
		{
			std::vector<std::string> vTemp2;
			StringUtil::split(vTemp2, *it, "-");
			if (vTemp2.size() != 2)
			{
				LOG_WARNING("ChunkObj line:%d sChunkObj:%s it:%s is invalid", i, sChunkObj.c_str(), it->c_str());
				continue;
			}
			int id = atoi(vTemp2[0].c_str());
			int weight = atoi(vTemp2[1].c_str());
			idkv[id] = weight;
		}

		ReadChunkGenParam(def.ChunkGrass, def.ChunkGrassNum, MAX_BIOME_GRASS, parser[i], grass_ids, ARRAY_SIZE(grass_ids), "ChunkGrass");
		ReadChunkGenParam(def.ChunkFlowers, def.ChunkFlowerNum, MAX_BIOME_FLOWER, parser[i], flower_ids, ARRAY_SIZE(flower_ids), "ChunkFlowers");
		ReadChunkGenParam(def.ChunkCorals, def.ChunkCoralNum, MAX_BIOME_CORAL, parser[i], coral_ids, ARRAY_SIZE(coral_ids), "ChunkCorals");
		ReadChunkGenParam(def.ChunkSeaPlants, def.ChunkSeaPlantNum, MAX_BIOME_SEAPLANT, parser[i], seaplant_ids, ARRAY_SIZE(seaplant_ids), "ChunkSeaPlants");
		ReadChunkGenParam(def.ChunkNewCorals, def.ChunkNewCoralNum, MAX_BIOME_NEWCORAL, parser[i], newcoral_ids, ARRAY_SIZE(newcoral_ids), "ChunkNewCorals");
		ReadChunkGenParam(def.ChunkJaggedFern, def.ChunkJaggedFernNum, MAX_JAGGED_FERN, parser[i], jaggedfern_ids, ARRAY_SIZE(jaggedfern_ids), "ChunkJaggedFern");

		def.ChunkPumpkin = parser[i]["ChunkPumpkin"].Int();
		def.ChunkWatermelon = parser[i]["ChunkWatermelon"].Int();
		def.ChunkDeadBush = parser[i]["ChunkDeadBush"].Int();
		def.ChunkReeds = parser[i]["ChunkReeds"].Int();
		def.ChunkCactus = parser[i]["ChunkCactus"].Int();
		def.ChunkShoreflower = parser[i]["ChunkShoreflower1186"].Int();
		def.ChunkPineapple = parser[i]["ChunkPineapple1187"].Int();
		def.ChunkMushroom = parser[i]["ChunkMushroom"].Int();
		def.ChunkRedPoisonousMushroom = parser[i]["ChunkRedPoisonousMushroom"].Int();
		def.ChunkWaterlily = parser[i]["ChunkWaterlily"].Int();
		def.ChunkRice = parser[i]["ChunkRice"].Int();
		def.ChunkLotusFlower = parser[i]["ChunkLotusFlower"].Int();
		def.ChunkWaterweed = parser[i]["ChunkWaterweed"].Int();
		def.ChunkDuckweed = parser[i]["ChunkDuckweed"].Int();
		def.ChunkFloatingFlower = parser[i]["ChunkFloatingFlower"].Int();
		def.ChunkFloatingplank = parser[i]["ChunkFloatingplank"].Int();
		def.ChunkCotton = parser[i]["ChunkCotton"].Int();
		def.ChunkNewCoralsCluster = parser[i]["ChunkRandomCorals"].Int();
		def.ChunkNewCoralsDeathCluster = parser[i]["ChunkRandomDeadCorals"].Int();

		//炽晶(新加)
		def.ChunkCrystal = parser[i]["ChunkCrystal"].Int();
		def.ChunkNest = parser[i]["ChunkNest"].Int();
		def.ChunkPile = parser[i]["ChunkPile"].Int();
		//星球-荧光彩虹草
		def.ChunkRainbowGrass = parser[i]["ChunkRainbowGrass"].Int();
		ReadChunkGenParam(def.ChunkJars, def.ChunkJarNum, MAX_BIOME_JARS, parser[i], jar_ids, ARRAY_SIZE(jar_ids), "ChunkJar");
		def.ChunkThicket = parser[i]["ChunkBush"].Int();
		def.ChunkWitheredThicket = parser[i]["ChunkDeadThicket763"].Int();
		def.ChunkSnowLotus = parser[i]["ChunkFlowers764"].Int();

		//星站
		def.ChunkStarStation = parser[i]["ChunkStarStation"].Int();

		// 巨型扇贝
		def.ChunkGiantScallops = parser[i]["ChunkGiantScallops346"].Int();
		CheckBiomeBlock(&def, def.FillBlock);
		CheckBiomeBlock(&def, def.TopBlock);

		def.mapId = parser[i]["MapId"].Int();
		//地形组
		def.BiomeGroupID = parser[i]["BiomeGroupID"].Int();
		def.skyboxType = parser[i]["skyboxType"].Int();
		def.CausticsPower = parser[i]["CausticsPower"].Float();

		vector<string> vWaterStorage;
		vWaterStorage.clear();
		std::string sWaterStorage = parser[i]["WaterStorage"].Str();
		StringUtil::split(vWaterStorage, sWaterStorage, "|");
		if (vWaterStorage.size() != 0)
		{
			sscanf(vWaterStorage[0].c_str(), "%d", &def.WaterStorageCollectOnceTime);
			sscanf(vWaterStorage[1].c_str(), "%d", &def.WaterStorageCollectOnceVolume);
		}

		if (def.ID >= int(m_Biomes.size())) m_Biomes.resize_initialized(def.ID + 1);
		m_Biomes[def.ID] = def;
		//m_BiomeTable.AddRecord(def.ID,def);
	}
}

bool DefManager::loadBiomeCSV()
{
    static NoFreeFixedString filename = "biomedef";
	//const char* filename = "biomedef";
// #ifndef DEDICATED_SERVER   // 下面的csvParser在linux上编译有点问题，晚点再看
	if (hasLoaded(filename))
		// #endif
	{
		return true;
	}
	
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath); 
		return false;
	}

	//Rainbow::DeletePointerArray(m_Biomes);
	m_Biomes.clear_dealloc();

	praseBiome(parser);

	const char* filenameNew = "biomedef_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepathNew);
		return true;
	}
	praseBiome(parserNew);
	
	//calBiomeMap();

	return true;
}

bool DefManager::loadOreCSV()
{
    static NoFreeFixedString filename = "oredef";
	//const char* filename = "oredef";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		int numLines = (int)parser.GetNumLines();
		for (int i=2; i<numLines; ++i)
		{
			OreDef def;
			def.Index = parser[i]["Index"].Int();
			def.ID = parser[i]["ID"].Int();
			if(def.ID == 0) continue;

			int mapid = parser[i]["MapID"].Int();
			def.ID |= mapid<<16;

			def.MinHeight = parser[i]["MinHeight"].Int();
			def.MaxHeight = parser[i]["MaxHeight"].Int();
			def.MinFalloff = parser[i]["MinFalloff"].Int();
			def.MaxFalloff = parser[i]["MaxFalloff"].Int();
			def.GenMethod = parser[i]["GenMethod"].Int();
			def.TryGenCount = parser[i]["Lode"].Int();
			def.MaxVeinBlocks = parser[i]["MaxNum"].Int();
			def.ReplaceBlock = parser[i]["ReplaceBlock"].Int();
		
			int nIndex = 0;
			std::istringstream iss(parser[i]["Genbiome"].Str());
			std::string szGenbiomeItem;
			while (std::getline(iss, szGenbiomeItem, ','))
			{
				if (!szGenbiomeItem.empty())
				{
					int biomeType = (int)Name2BiomeType(szGenbiomeItem.c_str());
					def.emGenbiome[nIndex++] = biomeType;
				}
			}
			m_OreTable.AddRecord(def.Index, def);
		}
	};
	csvParser(parser);

	const char* filenameNew = "oredef_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);

	return true;
}

bool DefManager::loadGunDef()
{
    static NoFreeFixedString filename = "gundef";
	//const char* filename = "gundef";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	m_GunTable.clear();

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		int numLines = (int)parser.GetNumLines();
		for (int i = 2; i < numLines; ++i)
		{
			int id = parser[i]["ID"].Int();
			if (id == 0) continue;

			GunDef def;
			def.ID = id;
			MyStringCpy(def.Name, sizeof(def.Name), parser[i]["Name"].Str());
			MyStringCpy(def.ShootEffect, sizeof(def.ShootEffect), parser[i]["ShootEffect"].Str());
			MyStringCpy(def.ShootSound, sizeof(def.ShootSound), parser[i]["ShootSound"].Str());
			MyStringCpy(def.ReloadSound, sizeof(def.ReloadSound), parser[i]["ReloadSound"].Str());
			MyStringCpy(def.EmptyShootSound, sizeof(def.EmptyShootSound), parser[i]["EmptyShootSound"].Str());

			def.Attack = parser[i]["Attack"].Short();
			def.FireInterval = parser[i]["FireInterval"].Short();
			def.Magazines = parser[i]["Magazines"].Short();
			def.InitSpread = parser[i]["InitSpread"].Short();
			def.MaxSpread = parser[i]["MaxSpread"].Short();
			def.SpreadSpeed = parser[i]["SpreadSpeed"].Float();
			def.SpreadRecoverySpeed = parser[i]["SpreadRecoverySpeed"].Float();
			def.MaxRecoil = parser[i]["MaxRecoil"].Float();
			def.RecoilSpeed = parser[i]["RecoilSpeed"].Float();
			def.RecoilRecoverySpeed = parser[i]["RecoilRecoverySpeed"].Float();
			def.Aim = parser[i]["Aim"].Int();
			def.Crosshair = parser[i]["Crosshair"].Int();

			def.BulletID = parser[i]["BulletID"].Int();
			def.CostItemID = parser[i]["CostItemID"].Int();
			def.NeedBullet = parser[i]["NeedBullet"].Int();
			def.ContinuousFire = parser[i]["ContinuousFire"].Int();
			//拉栓的动作和音效和时间
			def.ManualAnimFps = parser[i]["ManualAnimFps"].Int();
			//def.PullingAnimTps = parser[i]["PullingAnimTps"].Int();
			MyStringCpy(def.ManualSound, sizeof(def.ManualSound), parser[i]["ManualSound"].Str());
			def.ManualTime = parser[i]["ManualTime"].Float();
			def.ManualDelayTime = parser[i]["ManualDelayTime"].Float();

			def.ReloadTime = parser[i]["ReloadTime"].Float();
			def.Weight = parser[i]["Weight"].Float();

			def.IdleAnimFps = parser[i]["IdleAnimFps"].Int();
			def.ShootAnimFps = parser[i]["ShootAnimFps"].Int();
			def.ReloadAnimFps = parser[i]["ReloadAnimFps"].Int();
			def.DrawAnimFps = parser[i]["DrawAnimFps"].Int();
			def.AimAnimFps = parser[i]["AimAnimFps"].Int();
			def.AimShootAnimFps = parser[i]["AimShootAnimFps"].Int();

			def.IdleAnimTps = parser[i]["IdleAnimTps"].Int();
			def.ShootAnimTps = parser[i]["ShootAnimTps"].Int();
			def.ReloadAnimTps = parser[i]["ReloadAnimTps"].Int();

			def.SpeedAdd = parser[i]["SpeedAdd"].Float();
			def.GunType = parser[i]["GunType"].Int();

			def.AutoAim = parser[i]["AutoAim"].Int();

			def.RecoilSpeedFirst = parser[i]["RecoilSpeedFirst"].Float();
			def.MaxRecoilFirst = parser[i]["MaxRecoilFirst"].Float();
			def.RecoilRecoverySpeedFirst = parser[i]["RecoilRecoverySpeedFirst"].Float();

			def.RecoilSpeedThird = parser[i]["RecoilSpeedThird"].Float();
			def.MaxRecoilThird = parser[i]["MaxRecoilThird"].Float();
			def.RecoilRecoverySpeedThird = parser[i]["RecoilRecoverySpeedThird"].Float();

			m_GunTable.AddRecord(def.ID, def);
		}
	};

	csvParser(parser);

	const char* filenameNew = "gundef_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);
	
	return true;
}

bool DefManager::loadCraftingCSV()
{
    static NoFreeFixedString filename = "crafting";
	//const char* filename = "crafting";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_CraftingTable.clear();
	m_CookbookTable.clear();

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		bool isDomestic = IsDomesticVer();
		int numLines = (int)parser.GetNumLines();
		for (int i=2; i<numLines; ++i)
		{
			int id = parser[i]["ID"].Int();
			if(id == 0) continue;

			//int regionType = parser[i]["RegionType"].Int();
			//if ((isDomestic && regionType == REGION_TYPE_UNIVERSE) || (!isDomestic && regionType == REGION_TYPE_DOMESTIC)) 
			//	continue;

			CraftingDef def;
			def.CopyID = 0;
			def.gamemod = nullptr;

			def.ID = id;
			jsonxx::Value jsonClassificationType;
			auto typeChar = (char*)parser[i]["Type"].Str();
			if (strlen(typeChar) > 0 && typeChar[0] != '[')
			{
				def.Type.push_back(atoi(typeChar));//旧版本数据，不包含"[]",单个数字
			}
			else if (jsonClassificationType.parse((char*)parser[i]["Type"].Str()))
			{
				const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
				for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
				{
					def.Type.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
				}
			}
			auto editTypeChar = (char *)parser[i]["EditType"].Str();
			if (strlen(editTypeChar) > 0 && editTypeChar[0] != '[')
			{
				def.EditType.push_back(atoi(editTypeChar));//旧版本数据，不包含"[]",单个数字
			}
			else if (jsonClassificationType.parse((char*)parser[i]["EditType"].Str()))
			{
				const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
				for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
				{
					def.EditType.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
				}
			}
			def.ResultID = parser[i]["ResultID"].Int();
			//LOG_WARNING("dCrafting ID ... %d", def.ID);

			def.TechId = parser[i]["TechId"].Int();
			def.ResultCount = parser[i]["ResultCount"].Int();
			def.UseExp = parser[i]["UseExp"].Int();
			def.MoneyID = parser[i]["MoneyID"].Int();
			def.MoneyCount = parser[i]["MoneyCount"].Int();
			def.GridX = parser[i]["GridX"].Int();
			def.GridY = parser[i]["GridY"].Int();
			def.IsGroup = parser[i]["IsGroup"].Int() > 0;
			//def.IsFollowMe = parser[i]["IsFollowMe"].Int() == 11000;这个值的对应功能用CraftingItemID代替了  code-by:DemonYan
			def.IsTemplate = parser[i]["IsTemplate"].Int() > 0;
			def.EnglishName = parser[i]["Key"].Str(); // Crafting ENName -> Key
			def.UnlockLevel = parser[i]["UnlockLevel"].Int();
			def.MaterialNum = 0;
			for(int y=0; y<def.GridY; y++)
			{
				for(int x=0; x<def.GridX; x++)
				{
					int index = y*def.GridX+x;
					char tmpname[64];
					sprintf(tmpname, "MaterialID%d", index+1);
					def.MaterialID[index] = parser[i][tmpname].Int();
					sprintf(tmpname, "MaterialCount%d", index+1);
					def.MaterialCount[index] = parser[i][tmpname].Int();
					sprintf(tmpname, "ContainerID%d", index+1);
					def.ContainerID[index] = parser[i][tmpname].Int();
					if (def.MaterialID[index] > 0 && def.MaterialCount[index] > 0)
					{
						def.MaterialNum += 1;
					}
				}
			}
			def.Score = parser[i]["Score"].Float();
			def.HelperCheck = parser[i]["HelperCheck"].Int();
			def.CookingTick = parser[i]["Cooktime"].Int();
			def.CraftingItemID = parser[i]["IsFollowMe"].Int();
			def.DisplayOrder = parser[i]["DispOrder"].Int();
			auto SubTypeChar = (char*)parser[i]["SubType"].Str();
			if (strlen(SubTypeChar) > 0 && SubTypeChar[0] != '[')
			{
				def.SubType.push_back(atoi(SubTypeChar));//旧版本数据，不包含"[]",单个数字
			}
			else if (jsonClassificationType.parse((char*)parser[i]["SubType"].Str()))
			{
				const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
				for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
				{
					def.SubType.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
				}
			}

			auto SubTypeOrderChar = (char*)parser[i]["SubTypeOrder"].Str();
			if (strlen(SubTypeOrderChar) > 0 && SubTypeOrderChar[0] != '[')
			{
				def.SubTypeOrder.push_back(atoi(SubTypeOrderChar));//旧版本数据，不包含"[]",单个数字
			}
			else if (jsonClassificationType.parse((char*)parser[i]["SubTypeOrder"].Str()))
			{
				const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
				for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
				{
					def.SubTypeOrder.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
				}
			}
			m_CraftingTable.AddRecord(def.ID, def);
		}
	};

	csvParser(parser);

	// load crafting_new.csv
	const char* filenameNew = "crafting_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);

	finalizeCrafting();
	resetCrcCode(CRCCODE_CRAFTING);
	return true;
}

void DefManager::removeCraftingDef(int id)
{
	m_CraftingTable.RemoveRecord(id);
}

void DefManager::finalizeCrafting()
{
	m_Craftings.clear();
	m_CookbookTable.clear();
	m_CraftingMaterialIDSet.clear();
	auto iter = m_CraftingTable.m_Records.begin();
	for (; iter != m_CraftingTable.m_Records.end(); iter++)
	{
		m_Craftings.push_back(&iter->second);
		if (iter->second.CraftingItemID == 794 || iter->second.CraftingItemID == 795)
		{
			if (iter->second.IsGroup)
			{
				m_CookbookTable.push_back(&iter->second);
			}
			else
			{
				m_CookbookTable.insert(m_CookbookTable.begin(), &iter->second); // 非组匹配的需要优先放到前去匹配菜谱
			}
		}
		for (int i = 0; i < sizeof(iter->second.MaterialID) /sizeof(iter->second.MaterialID[0]); ++i)
		{
			auto metId = iter->second.MaterialID[i];
			if (metId > 0)
			{
				m_CraftingMaterialIDSet.insert(metId);
				std::vector<int> out;
				if (getItemsByGroup(metId, out))
				{
					for (int j = 0; j < out.size(); ++j)
					{
						m_CraftingMaterialIDSet.insert(out[j]);
					}
				}
			}
		}
	}
}

bool DefManager::isCraftingMaterial(int id)
{
	bool bRet = m_CraftingMaterialIDSet.find(id) != m_CraftingMaterialIDSet.end();
	if (!bRet)
		bRet = g_ModMgr.isCraftingMaterial(id);

	return bRet;
}

bool DefManager::loadHorseCSV()
{
    static NoFreeFixedString filename = "horse";
	//const char* filename = "horse";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_Horses.clear();

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		int numLines = (int)parser.GetNumLines();
		for (int i=2; i<numLines; ++i)
		{
			int id = parser[i]["ID"].Int();
			if(id == 0) continue;

			HorseDef def;
			def.ID = id;

			def.RideHeight = parser[i]["RideHeight"].Int();
			def.UIScale = parser[i]["UIScale"].Float();
			def.ShopUIScale = parser[i]["ShopUIScale"].Float();
			def.MinHP = parser[i]["MinHP"].Int();
			def.MaxHP = parser[i]["MaxHP"].Int();
			def.MinLandSpeed = parser[i]["MinLandSpeed"].Int();
			def.MaxLandSpeed = parser[i]["MaxLandSpeed"].Int();
			def.MinFlySpeed = parser[i]["MinFlySpeed"].Int();
			def.MaxFlySpeed = parser[i]["MaxFlySpeed"].Int();
			def.MinSwimSpeed = parser[i]["MinSwimSpeed"].Int();
			def.MaxSwimSpeed = parser[i]["MaxSwimSpeed"].Int();
			def.MinJumpHeight = parser[i]["MinJumpHeight"].Int();
			def.MaxJumpHeight = parser[i]["MaxJumpHeight"].Int();
			def.EggBlock = parser[i]["EggBlock"].Int();
			def.EggGenTicks = parser[i]["EggGenTicks"].Int();
			def.SaddleModel = parser[i]["SaddleModel"].Int();
			def.BornSaddle = parser[i]["BornSaddle"].Int();
			def.BornArmor = parser[i]["BornArmor"].Int();
			def.ArmorSlotProb = parser[i]["ArmorSlotProb"].Int();

			memset(def.BornSkills, 0, sizeof(def.BornSkills));
			for(int j=0; j< 3;j++)
			{
				char colname[64];
				sprintf(colname, "BornSkill%d", j+1);
				def.BornSkills[j] = parser[i][colname].Int();
			}

			def.ChangeFPS = parser[i]["ArmorSlotProb"].Int() > 0;
			def.HorseType = parser[i]["HorseType"].Int();
			def.RidingBindID = parser[i]["RidingBindID"].Int();
			def.PlayerCenterPosition = parser[i]["PlayerCenterPosition"].Str();
			m_Horses.AddRecord(def.ID, def);
		}	
	};

	csvParser(parser);

	// load horse_new.csv
	const char* filenameNew = "horse_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);

	return true;
}

bool DefManager::loadHorseEggDef()
{
	
	return true;
}

bool DefManager::loadMonsterBiomeCSV()
{
    static NoFreeFixedString filename = "monsterbiomedef";
	//const char* filename = "monsterbiomedef";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;
		monsterBiomesIDs.push_back(id);
		MonsterDef *mondef = MonsterCsv::getInstance()->m_Monsters.GetRecord(id);
		if(mondef == NULL) continue;

		for(int biome=0; biome<MAX_BIOME_TYPE; biome++)
		{
			if(!parser[i].hasColumn(BiomeTypeNames[biome])) continue;

			const CSVParser::TableItem &csvitem = parser[i][BiomeTypeNames[biome]];
			if(csvitem.IsValid())
			{
				int n = csvitem.Int();
				assert(n>=0 && n<=65535);
				mondef->SpawnWeight[biome] = (unsigned short)n;
				
			}
		}
	}
	
	for(int biome=0; biome<MAX_BIOME_TYPE; biome++)
	{
		BiomeDef *biomedef = getBiomeDef(biome);
		if(biomedef == NULL) continue;
		int count = monsterBiomesIDs.size();
		for(int k = 0 ; k < count ; k++)
		{
			if(k > MAX_BIOME_MONSTERS) break;
			int id = monsterBiomesIDs.at(k);
			MonsterDef* monsterDef = MonsterCsv::getInstance()->m_Monsters.GetRecord(id);
			if(monsterDef == NULL) continue;
			biomedef->biomeMonster[k] = id;
			biomedef->biomeMonsterNum[k] = monsterDef->SpawnWeight[biome];
		

			BiomeDef* def = &m_Biomes[biome];
			def->biomeMonster[k] = id;
			def->biomeMonsterNum[k] = monsterDef->SpawnWeight[biome];
		}
	}
   

	return true;
}

bool DefManager::loadPlantTreesDef()
{
    static NoFreeFixedString filename = "planttrees";
	//const char* filename = "planttrees";
	if (hasLoaded(filename))
	{
		return true;
	}
    MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_PlantTreesDefArray.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		PlantTreesDef coordinate;

		coordinate.x = parser[i]["x"].Int();
		coordinate.y = parser[i]["y"].Int();
		coordinate.z = parser[i]["z"].Int();

		m_PlantTreesDefArray.push_back(coordinate);
	}
	
	return true;
}

int DefManager::getPlantTreesNum()
{
	loadPlantTreesDef();

   return m_PlantTreesDefArray.size();
}

PlantTreesDef *DefManager::getPlantTreesDef(int index)
{	
	loadPlantTreesDef();

	return &m_PlantTreesDefArray[index];
}

int DefManager::getRoleActionNum() {
	loadRoleActionDef();
	return  m_RoleActionTable.size();
}
RoleActionDef * DefManager::getRoleActionDef(int index) {
	loadPlantTreesDef();
	
	return &m_RoleActionTable[index];
}

BPMissionDef * DefManager::getBPMissionDef(int index)
{
	loadBPMissionCSV();
	return &m_BPMissionTable[index];
}

BPRewardDef * DefManager::getBPRewardDef(int index)
{
	loadBPRewardCSV();
	return &m_BPRewardTable[index];
}

BPDrawDef * DefManager::getBPDrawDef(int index)
{
	loadBPDrawCSV();
	return &m_BPDrawTable[index];
}

BPSettingDef * DefManager::getBPSettingDef(int index)
{
	loadBPSettingCSV();
	return &m_BPSettingTable[index];
}

int DefManager::getBPSettingDefNum()
{
	loadBPSettingCSV();
	return m_BPSettingTable.size();
}

int DefManager::getBPSettingMissionNum(int season)
{
	return m_BPSettingTable[season].Mission.size();
}

short DefManager::getBPSettingMaxIntegral(int season, int index)
{
	return m_BPSettingTable[season].MaxIntegral[index];
}

short DefManager::getBPSettingMission(int season, int index)
{
	return m_BPSettingTable[season].Mission[index];
}

int DefManager::getBPMissionDefNum()
{
	loadBPSettingCSV();
	return m_BPMissionTable.size();
}

int DefManager::getBPRewardDefNum()
{
	loadBPSettingCSV();
	return m_BPRewardTable.size();
}

int DefManager::getBPDrawDefNum()
{
	loadBPSettingCSV();
	return m_BPDrawTable.size();
}

//根据赛季、卡类型和等级获取奖励，type==1为普通卡
short DefManager::getBPSettingReward(int season, int type,int index)
{
	if(type == 1)
		return m_BPSettingTable[season].GeneralReward[index];
	else
		return m_BPSettingTable[index].BattlePassReward[type - 2][index];
}

static const char *s_ModAttrNames[MAX_MOD_ATTRIB] = 
{
	"MOVE_SPEED",
	"SWIM_SPEED",
	"JUMP_SPEED",

	"ATTACK_PUNCH", //攻击别人时加成的百分比
	"ATTACK_RANGE",
	"ATTACK_EXPLODE",
	"ATTACK_FIRE",
	"ATTACK_POISON",
	"ATTACK_WITHER",
	"ATTACK_PLAYER",
	"ATTACK_UNDEAD",
	"ATTACK_ANIMAL",
	"ATTACK_ICE",
	"DAMAGED_PUNCH", //被攻击时加成的百分比
	"DAMAGED_RANGE",
	"DAMAGED_EXPLODE",
	"DAMAGED_FIRE",
	"DAMAGED_POISON",
	"DAMAGED_WITHER",
	"DAMAGED_FALLING",

	"ARMOR_PUNCH",
	"ARMOR_RANGE",
	"ARMOR_EXPLODE",

	"DAMAGE_ABSORB",  //吸收伤害
	"CRITICAL_HIT",  //暴击伤害加成
	"KNOCK",         //增加攻击别人的击退距离
	"KNOCK_RESIST", //减少击退距离
	"KNOCK_RESIST_PROB", //击退概率抵抗值", 20%表示有20%概率不被击退
	"ACTOR_SCALE", //角色大小
	"DIG_SPEED",
	"LUCK_DIG",
	"LUCK_KILLMOB",  //怪物掉落
	"VIEW_BRIGHT", //视野亮度
	"OXYGEN_SUPPLY",//氧气提供 0-1.0
	"DAMAGED_ZOMBIE", // 受僵尸攻击伤害减免
	"ATTACK_GUN"	// 枪械伤害加成
};

MODATTRIB_TYPE DefManager::Name2ModAttrib(const char *name)
{
	for(int i=0; i<MAX_MOD_ATTRIB; i++)
	{
		if(stricmp(s_ModAttrNames[i], name) == 0) return (MODATTRIB_TYPE)i;
	}

	if(stricmp(name, "SCRIPT_VAR") == 0) return MAX_MOD_ATTRIB;

	LOG_SEVERE("loadBuffCSV failed: %s", name);
	return (MODATTRIB_TYPE)(-1);
}

bool DefManager::loadBuffCSV()
{
	return true;
}

bool DefManager::loadBuffEffectCSV()
{
	

	return true;
}

bool DefManager::loadBuffEffectEnumCSV()
{

	return true;
}

bool DefManager::loadBuffEffectSliderCSV()
{
	
	return true;
}

bool DefManager::loadIconLibCSV()
{
    static NoFreeFixedString filename = "IconBank";
	//const char* filename = "IconBank";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_IconLibTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if (id == 0) continue;

		IconLibDef def;
		def.ID = id;
		def.Type = parser[i]["Classification"].Int();
		def.Editing = parser[i]["BuffEditing"].Int();
		def.IconName = parser[i]["FilePath"].String().c_str();

		m_IconLibTable.AddRecord(def.ID, def);
	}

	auto iter = m_IconLibTable.m_Records.begin();
	for (; iter != m_IconLibTable.m_Records.end(); iter++)
	{
		m_IconLibIds.push_back(iter->first);
	}

	return true;
}

bool DefManager::loadFurnaceCSV()
{
    static NoFreeFixedString filename = "furnace";
	//const char* filename = "furnace";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_FurnaceTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		FurnaceDef def;
		def.CopyID = 0;
		def.gamemod = nullptr;

		def.ID = id;
		def.MaterialID = parser[i]["MaterialID"].Int();

		def.Name = parser[i]["Name"].Str();
		def.EditType = parser[i]["EditType"].Int();
		def.Heat = parser[i]["Heat"].Int();
		def.Result = parser[i]["Result"].Int();
		def.ContainerID = parser[i]["ContainerID"].Int();
		def.Score = parser[i]["Score"].Float();
		def.EnglishName = parser[i]["Key"].Str(); //Furnace ENName -> Key
		def.IsTemplate = parser[i]["IsTemplate"].Int() > 0;
		def.Type = parser[i]["Type"].Int();
		def.OxyValue = parser[i]["OxyValue"].Int();
		if (parser[i].hasColumn("Combustion")) {
			def.BonfireHeat = parser[i]["Combustion"].Int();
		}
		else {
			def.BonfireHeat = 0;
		}
		def.Result2 = parser[i]["Result2"].Int();
		def.Result3 = parser[i]["Result3"].Int();
		def.ResultNum = parser[i]["ResultNum"].Int();
		def.ResultNum2 = parser[i]["ResultNum2"].Int();
		def.ResultNum3 = parser[i]["ResultNum3"].Int();

		def.ProvideHeat = parser[i]["ProvideHeat"].Int();
		def.BurnTime = parser[i]["BurnTime"].Int();

		m_FurnaceTable.AddRecord(def.ID, def);
	}

	auto iter = m_FurnaceTable.m_Records.begin();
	for (; iter != m_FurnaceTable.m_Records.end(); iter++)
	{
		m_Furnaces.push_back(&iter->second);
		if (iter->second.Type == 2 && iter->second.MaterialID > 0)
		{
			m_BakeTable[iter->second.MaterialID] = &iter->second;
		}
	}
	
	resetCrcCode(CRCCODE_FURNACE);
	return true;
}

int DefManager::getItemBakeTo(int materialId)
{
	if (m_BakeTable.find(materialId) != m_BakeTable.end())
	{
		if (m_BakeTable[materialId] != NULL)
		{
			return m_BakeTable[materialId]->Result;
		}
	}
	return -1;
}
DefDataTable<DungeonsDef>& DefManager::getDungeonsDefTable()
{
	loadDungeonsDef();

	return m_DungeonsTable;
}

 DungeonsDef* DefManager::getDungeonsDefById(int id)
{
	 return getDungeonsDefTable().GetRecord(id);
}

//获取怪物雕像配置
DefDataTable<MonsterStatueDef>& DefManager::getMonsterStatueDefTable()
{
	loadMonsterStatueDef();

	return m_MonsterStatueDefTable;
}

MonsterStatueDef* DefManager::getMonsterStatueDefById(int id)
{
	return getMonsterStatueDefTable().GetRecord(id);
}

const PlayerAttribCsvDef* DefManager::getPlayerAttribCsvDef(PlayerAttributeType id)
{
	return PlayerAttribCsv::getInstance()->get(id);
}
const ArchitecturalBlueprintCsvDef* DefManager::getArchitecturalBlueprintCsvDef(int id)
{
	return ArchitecturalBlueprintCsv::getInstance()->get(id);
}

const DieInfoCsvDef* DefManager::getDieinfoCsvDef(int id)
{
	return DieInfoCsv::getInstance()->get(id);
}

bool DefManager::loadAchievementCSV( )
{
    static NoFreeFixedString filename = "achievement";
	//const char* filename = "achievement";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_AchievementTable.clear();

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		int numLines = (int)parser.GetNumLines();
		for (int i=2; i<numLines; ++i)
		{
			int id = parser[i]["ID"].Int();
			if(id == 0) continue;

			AchievementDef def;
			def.ID = id;

			for(int ifront=0; ifront<4; ifront++)
			{
				char tmpname[64];
				sprintf(tmpname, "FrontID%d", ifront+1);
				def.FrontID[ifront] = parser[i][tmpname].Int();
			}
			def.IconID = parser[i]["IconID"].Int();
			def.GridX = parser[i]["GridX"].Int();
			def.GridY = parser[i]["GridY"].Int();
			def.Name = ColumnLang(parser[i],"Name",m_CurLanguage);
			def.Desc = ColumnLang(parser[i],"Desc",m_CurLanguage);
			def.TrackDesc = ColumnLang(parser[i],"TrackDesc",m_CurLanguage);
			
			def.Group = parser[i]["Group"].Int();
			def.IsGroup = parser[i]["IsGroup"].Int() > 0;
			def.Type = parser[i]["Type"].Int();
			def.Goal = parser[i]["Goal"].Int();
			def.GoalId = parser[i]["GoalId"].Int();
			def.GoalNum = parser[i]["GoalNum"].Int();
			for(int ireward=0; ireward<2; ireward++)
			{
				char tmpname[64];
				sprintf(tmpname, "RewardType%d", ireward+1);
				def.RewardType[ireward] = parser[i][tmpname].Int();
				sprintf(tmpname, "RewardID%d", ireward+1);
				def.RewardID[ireward] = parser[i][tmpname].Int();
				sprintf(tmpname, "RewardNum%d", ireward+1);
				def.RewardNum[ireward] = parser[i][tmpname].Int();
			}
			def.Point = parser[i]["Point"].Int();
			def.NextTrackID = parser[i]["NextTrack"].Int();//新增下一个追踪的字段 code_by:huangfubin
			def.GuidePicture = parser[i]["GuidePicture"].Str();
			def.RewardDistributionType = parser[i]["RewardDistributionType"].Int();
			m_AchievementTable.AddRecord(def.ID, def);
		}
	};

	csvParser(parser);

	const char* filenameNew = "achievement_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);

	resetCrcCode(CRCCODE_ACHIEVE);
	return true;
}

static const char *s_EnchantNames[MAX_ENCHANT_TYPE] =
{
	"NULL",
	"SHARP",   //锋利
	"KNOCK",   //击退: 增加击退别人的格子数
	"KNOCKUP", //击飞: 增加往上高度
	"ROB",     //掠夺
	"TARGET_FIRE", //攻击时给目标附加燃烧
	"MULTIATTACK",  //多重攻击
	"VAMPIRE",      //吸血
	"DURABLE",      //耐久

	"PROTECTION",   //保护
	"THORNS",       //荆棘
	"ATTACKER_BUFF", //被攻击时给攻击者附加状态
	"KNOCK_RESIST", //减少被击退距离
	"KNOCK_ATTACKER", //受到近身攻击击退攻击者

	"BOW_ATTACK",     //提高弓伤害
	"ARROW_FREE",     //不消耗箭

	"DIG_SPEED",      //采集效率
	"DIG_PROB",       //时运(矿物)
	"DIG_MAXNUM",     //时运(植物)
	"DIG_PRECISE",    //精准采集

	"ARROW_EXPLODE",  //爆炸箭
	"FALL_PROTECT",   //掉落保护

	"FALL_SPEED",     //速降
	"CLIMB",         //爬墙

	"TARGET_BLEED",		//攻击时给目标加流血buff
	"LIGHTNING_CHAIN",	//闪电链
	"FISH",				// 钓鱼幸运
	"DURABLE_PROTECT",      //耐久为0不删除道具
	"TEMPDEF",	// 附魔温度抗性
	"TARGET_ICE",	// 冰霜攻击附魔

	"PUNCH_INC",	// 近程伤害加成
	"DAMAGE_DEC",	// 所有伤害减免
	"RANGE_DAMAGE_DEC",	// 远程伤害减免
	"EXPLODE_DEC",	// 爆炸伤害减免
	"TOUGH_REC",	// 韧性恢复速度
	"JUMP_INC",	// 增加跳跃能力
	"TRANSFORM1",	// 概率使敌方变身
	"DURABLE_PROB",	// 耐久保护
	"FISHING_TIME_DEC",	// 减少钓鱼时间
	"SLOWDOWN_PROB",	// 概率迟缓
	"DIGGING_STRENGTH_DEC",	// 挖掘体力降低
	"FALL_DAMAGE",	// 落地造成范围伤害
	"STAR_INC",	// 星星经验获取加成
	"WEAPON_SKILL_CD_DEC",	// 武器技能冷却时间降低
	"COMBO_LAST_INC",	// 使连招的最后一击伤害提升
	"REPEL_RES",	// 击退抗性
	"MONSTER_DAMAGE",	// 对怪物伤害加成
	"ADDBUFF",//概率给对象添加buff
};

ENCHANT_TYPE DefManager::Name2EnchantType(const char *name)
{
	for(int i=1; i<MAX_ENCHANT_TYPE; i++)
	{
		if(stricmp(name, s_EnchantNames[i]) == 0) return (ENCHANT_TYPE)i;
	}
	LOG_SEVERE("load enchants.csv failed: type=%s", name);
	return ENCHANT_NULL;
}

bool DefManager::loadEnchantCSV()
{
    static NoFreeFixedString filename = "enchant";
	//const char* filename = "enchant";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_EnchantTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		EnchantDef def;
		def.ID = id;

		def.Name = ColumnLang(parser[i],"Name",m_CurLanguage);
		def.AttrDesc = ColumnLang(parser[i],"AttrDesc",m_CurLanguage);
		def.IconName = ColumnLang(parser[i], "Icon", m_CurLanguage);

		def.EnchantType = DefManager::Name2EnchantType(parser[i]["EnchantType"].Str());
		def.EnchantLevel = parser[i]["EnchantLevel"].Int();
		def.EnchantValue[0] = parser[i]["EnchantValue1"].Float();
		def.EnchantValue[1] = parser[i]["EnchantValue2"].Float();
		def.AttackType = parser[i]["AttackType"].Int();
		def.TargetType = parser[i]["TargetType"].Int();
		def.ConflictID = parser[i]["ConflictID"].Int();


		def.Weight = parser[i]["Weight"].Int();

		for(int itool=0; itool<MAX_TOOL_TYPE; itool++)
		{
			char tmpname[64];
			sprintf(tmpname, "ToolType%d", itool+1);
			def.ToolType[itool] = parser[i][tmpname].Int();
		}

		sscanf(parser[i]["Color"].Str(), "%x", &def.Color);

		def.effect = parser[i]["effect"].Str();
		def.effect2 = parser[i]["effect2"].Str();

		m_EnchantTable.AddRecord(def.ID*100+def.EnchantLevel, def);
	}
	
	return true;
}
std::map<int, std::set<int>>& DefManager::GetMaterialIdSet()
{
	loadRuneCSV();
	return m_materialIds;
}

int DefManager::addToolStatus(int id)
{
	auto pDef = getStatusDef(1001000);//先取空白模版的配置
	if (!pDef) { return 0; }
	auto toolDef = getToolDef(id);

	if (!toolDef) { return 0; }

	bool noEffect = true;
	for (int i = 0; i < MAX_EQUIP_EFFECT_COUNT; i++)
	{
		if (toolDef->EffInfo[i].CopyID > 0)
		{
			noEffect = false;
			break;
		}
	}

	if (noEffect)
		return 0;

	int iStatusId = id + USER_MOD_NEWID_BASE_STATUS * 100;
	auto def = m_CurrentStatusDefs.GetRecord(iStatusId);
	if (def) {
		for (int i = 0; i < MAX_EQUIP_EFFECT_COUNT; i++) {
			def->Status.EffInfo[i] = toolDef->EffInfo[i];
		}
	}
	else {
		BuffDef stStatusDef = *pDef;
		stStatusDef.CopyID = 1001000;
		stStatusDef.ID = iStatusId;
		stStatusDef.BuffType = 2;
		stStatusDef.Status.LimitTime = 0;
		stStatusDef.Status.Priority = 2;
		stStatusDef.Status.DeathClear = 1;
		stStatusDef.Status.AttackClear = 2;
		stStatusDef.Status.DamageClear = 2;
		for (int i = 0; i < MAX_EQUIP_EFFECT_COUNT; i++) {
			EffectInfo info = toolDef->EffInfo[i];
			if (info.CopyID == 0) continue;
			auto effectDef = getBuffEffectDef(info.CopyID);
			if (effectDef == NULL) continue;
			for (int j = 0; j < MAX_BUFF_ATTRIBS; j++)
			{
				if (info.Value[j] == -1000000)
				{
					info.Value[j] = effectDef->EffectParam[j].Default;
				}
			}
			stStatusDef.Status.EffInfo[i] = info;
		}

		m_CurrentStatusDefs.AddRecord(iStatusId, stStatusDef);
	}

	return iStatusId;
}

int DefManager::getStatusIdByToolId(int id)
{
	const ToolDef* toolDef = getToolDef(id);
	if (!toolDef) { return 0; }

	return id + USER_MOD_NEWID_BASE_STATUS * 100;
}
//code by:tanzhenyu
bool DefManager::loadRuneCSV()
{
    static NoFreeFixedString filename = "tunestone";
	//const char* filename = "tunestone";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
#ifdef IWORLD_UNIVERSE_BUILD
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
#else
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
#endif
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_RuneTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		RuneDef def;
		def.ID = id;
		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.AttrDesc, sizeof(def.AttrDesc), ColumnLang(parser[i],"AttrDesc",m_CurLanguage));
		MyStringCpy(def.IconName, sizeof(def.IconName), ColumnLang(parser[i], "Icon", m_CurLanguage));

		MyStringCpy(def.TypeName, sizeof(def.TypeName), parser[i]["EnchantType"].Str());
		def.Kind = parser[i]["Tunestone"].Int();
		def.EnchantType = DefManager::Name2EnchantType(parser[i]["EnchantType"].Str());
		def.Level = parser[i]["Tunestonelevel"].Int();

		def.Value[0] = parser[i]["EnchantValue1Min"].Float();
		def.Value[1] = parser[i]["EnchantValue1Max"].Float();
		def.Value[2] = parser[i]["EnchantValue2Min"].Float();
		def.Value[3] = parser[i]["EnchantValue2Max"].Float();

		def.AttackType = parser[i]["AttackType"].Int();
		def.TargetType = parser[i]["TargetType"].Int();
		def.ConflictID = parser[i]["ConflictID"].Int();
		def.Weight = parser[i]["Weight"].Int();
		def.Category = parser[i]["Category"].Int();
        def.activateItemID = parser[i]["ActivateItemID"].Int();
		for(int itool=0; itool<MAX_TOOL_TYPE; itool++)
		{
			char tmpname[64];
			sprintf(tmpname, "ToolType%d", itool+1);
			def.ToolType[itool] = parser[i][tmpname].Int();
            if (def.activateItemID != 0 && def.ToolType[itool] != 0) {
                InsertMaterialSet(def.activateItemID, def.ToolType[itool]);
				InsertToolTypeSet(def.activateItemID, def.ToolType[itool]);
            }
		}
		sscanf(parser[i]["Color"].Str(), "%x", &def.Color);
		MyStringCpy(def.effect, sizeof(def.effect), parser[i]["effect"].Str());
		MyStringCpy(def.effect2, sizeof(def.effect2), parser[i]["effect2"].Str());
		def.DisplayType = parser[i]["DisplayType"].Int();
        m_RuneTable.AddRecord(def.ID * 100 + def.Level, def);
    }
	return true;
}
//code by:tanzhenyu
DefDataTable<RuneDef>& DefManager::getRuneDefTable()
{
	loadRuneCSV();
	return m_RuneTable;
}

void DefManager::InsertMaterialSet(int activateItemID, int toolType)
{
    auto iter = m_materialIds.find(activateItemID);
    if (iter == m_materialIds.end()) {
        std::set<int> toolTypeSet;
        toolTypeSet.insert(toolType);
        m_materialIds.insert({activateItemID , toolTypeSet});
    } else {
        iter->second.insert(toolType);
    }
}

void DefManager::InsertToolTypeSet(int activateItemID, int toolType)
{
	auto iter = m_toolTypeNeedMaterialIds.find(toolType);
	if (iter == m_toolTypeNeedMaterialIds.end()) {
		std::vector<int> materialSet;
		materialSet.push_back(activateItemID);
		m_toolTypeNeedMaterialIds.insert({ toolType , materialSet });
	}
	else {
		if (std::find(iter->second.begin(), iter->second.end(), activateItemID) == iter->second.end())
		{
			iter->second.push_back(activateItemID);
		}
	}
}

int DefManager::getToolTypeNeedMaterialIdsNum(int tooltype)
{
	loadRuneCSV();
	auto iter = m_toolTypeNeedMaterialIds.find(tooltype);
	if (iter == m_toolTypeNeedMaterialIds.end()) {
		return 0;
	}
	else {
		return iter->second.size();
	}
}


int DefManager::getToolTypeActiveIdByIndex(int tooltype, int index)
{
	auto iter = m_toolTypeNeedMaterialIds.find(tooltype);
	if (iter == m_toolTypeNeedMaterialIds.end()) {
		return -1;
	}
	else {
		if (index >= iter->second.size())
		{
			return 0;
		}
		return iter->second[index];
	}
}

bool DefManager::loadEnchantMentCSV()
{
    static NoFreeFixedString filename = "enchantment";
	//const char* filename = "enchantment";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_EnchantMentTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		if(parser[i]["StuffType"].String().empty()) continue;

		int type = parser[i]["StuffType"].Int();
		if(type < 0) continue;

		EnchantMentDef def;
		def.StuffType = type;
		def.CurrencyType = parser[i]["CurrencyType"].Str();

		def.Cost = parser[i]["Cost"].Int();

		for(int imerge=0; imerge<5; imerge++)
		{
			char tmpname[64];
			sprintf(tmpname, "MergeCost%d", imerge+1);
			def.MergeCost[imerge] = parser[i][tmpname].Int();
		}
		for(int iattr=0; iattr<5; iattr++)
		{
			char tmpname[64];
			sprintf(tmpname, "AttrWeight%d", iattr+1);
			def.AttrWeight[iattr] = parser[i][tmpname].Int();
		}
		for(int ilevel=0; ilevel<5; ilevel++)
		{
			char tmpname[64];
			sprintf(tmpname, "LevelWeight%d", ilevel+1);
			def.LevelWeight[ilevel] = parser[i][tmpname].Int();
		}
		for(int inpcattr=0; inpcattr<5; inpcattr++)
		{
			char tmpname[64];
			sprintf(tmpname, "NpcAttrWeight%d", inpcattr+1);
			def.NpcAttrWeight[inpcattr] = parser[i][tmpname].Int();
		}
		for(int inpclevel=0; inpclevel<5; inpclevel++)
		{
			char tmpname[64];
			sprintf(tmpname, "NpcLevelWeight%d", inpclevel+1);
			def.NpcLevelWeight[inpclevel] = parser[i][tmpname].Int();
		}

		m_EnchantMentTable.AddRecord(type, def);
	}
	
	return true;
}

bool DefManager::loadChestDef()
{
    static NoFreeFixedString filename = "chestdef";
	//const char* filename = "chestdef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ChestDefTable.clear();

	auto csvParser = [this](MultiLanCSVParser& parser) {
		parser.SetTitleLine(1);
		int numLines = (int)parser.GetNumLines();
		for (int i=2; i<numLines; ++i)
		{
			int id = parser[i]["ID"].Int();
			if(id == 0) continue;

			ChestDef def;
			def.ID = id*100 + parser[i]["GroupID"].Int();
			def.Key = parser[i]["Key"].Int();
			def.GroupOdds = parser[i]["GroupOdds"].Int();
			def.OddsMethod = parser[i]["OddsMethod"].Int();

			for(int j=0; j<MAX_CHESTDEF_ITEMS; j++)
			{
				char name[64];
				sprintf(name, "ItemID%d", j+1);
				def.ItemID[j] = parser[i][name].Int();
				sprintf(name, "ItemNum%d", j+1);
				def.ItemNum[j] = parser[i][name].Int();
				sprintf(name, "ItemOdds%d", j+1);
				def.ItemOdds[j] = parser[i][name].Int();
				sprintf(name, "ItemQuality%d", j+1);
				def.ItemQuality[j] = parser[i][name].Int();
			}

			m_ChestDefTable.AddRecord(def.ID, def);
		}
	};

	csvParser(parser);

	const char* filenameNew = "chestdef_new";
    MultiLanCSVParser parserNew;
	char filepathNew[64] = {0};
	m_CsvLoadConfig->getPath(filenameNew, filepathNew);
	if (!parserNew.Load(filepathNew, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	csvParser(parserNew);
	
	return true;
}


bool DefManager::loadCharacterDef()
{
    static NoFreeFixedString filename = "character";
	//const char* filename = "character";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_CharDefTable.clear();
	m_CharDefTable.reserve(10);

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		if(id > (int)m_CharDefTable.size()) m_CharDefTable.resize(id);

		CharacterDef &def = m_CharDefTable[id-1];
		def.ID = id;
		MyStringCpy(def.Name, sizeof(def.Name), parser[i]["Name"].Str());
		for(int j=0; j<MAX_CHARINIT_ITEMS; j++)
		{
			char name[64];
			sprintf(name, "ItemID%d", j+1);
			def.ItemID[j] = parser[i][name].Int();
			sprintf(name, "ItemNum%d", j+1);
			def.ItemNum[j] = parser[i][name].Int();
			sprintf(name, "ItemEquip%d", j + 1);
			def.ItemEquip[j] = parser[i][name].Int();
			sprintf(name, "EquipPart%d", j + 1);
			def.EquipPart[j] = parser[i][name].Int();
		}
	}
	
	return true;
}

DefDataTable<MobSpawnerDef> &DefManager::getMobSpawnerTable()
{
	loadMobSpawnerDef();

	return m_MobSpawnerTable;
}

//刷怪房随机获取图腾
int DefManager::getRandomDungeonSpawner(ChunkRandGen *randgen)
{	
	loadMobSpawnerDef();

	MobSpawnerDef *defs[256];
	int count = 0;
	int sum = 0;

	auto iter = m_MobSpawnerTable.m_Records.begin();
	for(; iter!=m_MobSpawnerTable.m_Records.end(); iter++)
	{
		MobSpawnerDef *def = &iter->second;
		if(def->DungeonOdds > 0)
		{
			defs[count++] = def;
			sum += def->DungeonOdds;
		}
	}

	int n = randgen->nextInt(sum);
	sum = 0;
	for(int i=0; i<count; i++)
	{
		sum += defs[i]->DungeonOdds;
		if(n < sum) return defs[i]->ID;
	}
	assert(0);
	return m_MobSpawnerTable.m_Records.begin()->second.ID;
}

bool DefManager::loadMonsterSpawnDef()
{
	static NoFreeFixedString filename = "monster_spawn";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_MonsterSpawnTable.clear();
	
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		MonsterSpawnDef def;
		def.type = parser[i]["type"].Int();
		def.name = parser[i]["name"].Str();
		def.refresh_interval = parser[i]["refresh_interval"].Int();
		def.density = parser[i]["density"].UShort();
		def.max_num = parser[i]["max_num"].UShort();

		m_MonsterSpawnTable.AddRecord(def.type, def);
	}

	return true;
}

bool DefManager::loadMobSpawnerDef()
{
    static NoFreeFixedString filename = "mobspawner";
	//const char* filename = "mobspawner";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_MobSpawnerTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		MobSpawnerDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.MobResID = parser[i]["MobResID"].Int();
		def.Duration = parser[i]["Duration"].Int();
		def.MinSpawnDelay = parser[i]["MinSpawnDelay"].Int();
		def.MaxSpawnDelay = parser[i]["MaxSpawnDelay"].Int();
		def.SpawnCount = parser[i]["SpawnCount"].Int();
		def.MaxSameMob = parser[i]["MaxSameMob"].Int();
		def.ForceSpawn = parser[i]["ForceSpawn"].Int();
		def.DungeonOdds = parser[i]["DungeonOdds"].Int();

		m_MobSpawnerTable.AddRecord(def.ID, def);
	}
	
	resetCrcCode(CRCCODE_MOBSPAWNER);
	return true;
}

void DefManager::ParseTypeCollides(const std::string& jsonColliedes, std::vector<TypeCollideAABB>& collides)
{
	jsonxx::Value jsonCollideBoxs;
	if (!jsonCollideBoxs.parse(jsonColliedes))
	{
		// LOG_WARNING("ParseTypeCollides failed, jsonColliedes: %s", jsonColliedes.c_str());
		return;
	}

	jsonxx::Array& boxsArray = jsonCollideBoxs.get<jsonxx::Array>();
	for (int j = 0; j < (int)boxsArray.size(); j++)
	{
		jsonxx::Object& jbox = boxsArray.get<jsonxx::Object>(j);
		if (jbox.has<jsonxx::Array>("pos") && jbox.has<jsonxx::Array>("dim") && jbox.has<jsonxx::String>("part"))
		{
			jsonxx::Array& posArray = jbox.get<jsonxx::Array>("pos");
			jsonxx::Array& dimArray = jbox.get<jsonxx::Array>("dim");
			TypeCollideAABB tbox;
			tbox.box.pos = WCoord((float)posArray.get<jsonxx::Number>(0), (float)posArray.get<jsonxx::Number>(1), (float)posArray.get<jsonxx::Number>(2));
			tbox.box.dim = WCoord((float)dimArray.get<jsonxx::Number>(0), (float)dimArray.get<jsonxx::Number>(1), (float)dimArray.get<jsonxx::Number>(2));
			tbox.part = jbox.get<jsonxx::String>("part");
			collides.push_back(tbox);
		}
	}
}

bool DefManager::loadRoleDef()
{
    static NoFreeFixedString filename = "role";
	//const char* filename = "role";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_RoleTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		RoleDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));
		MyStringCpy(def.GeniusName, sizeof(def.GeniusName), ColumnLang(parser[i],"GeniusName",m_CurLanguage));
		MyStringCpy(def.GeniusDesc, sizeof(def.GeniusDesc), ColumnLang(parser[i],"GeniusDesc",m_CurLanguage));
		MyStringCpy(def.GeniusLvDesc, sizeof(def.GeniusLvDesc), ColumnLang(parser[i],"GeniusLvDesc",m_CurLanguage));

		def.Model = parser[i]["Model"].Int();
		def.GeniusLv = parser[i]["GeniusLv"].Int();
		def.GeniusType = parser[i]["GeniusType"].Int();
		def.GeniusValue[0] = parser[i]["GeniusValue1"].Float();
		def.GeniusValue[1] = parser[i]["GeniusValue2"].Float();
		def.GeniusValue[2] = parser[i]["GeniusValue3"].Float();
		def.ULConsumeType = parser[i]["ULConsumeType"].Int();
		def.ULConsumeID = parser[i]["ULConsumeID"].Int();
		def.Height = parser[i]["Height"].Int();
		def.Width = parser[i]["Width"].Int();
		def.HitHeight = parser[i]["HitHeight"].Int();
		def.HitWidth = parser[i]["HitWidth"].Int();
		def.HitThickness = parser[i]["HitThickness"].Int();

		ParseTypeCollides(parser[i]["CollideBoxs"].Str(), def.CollideBoxs);
		ParseTypeCollides(parser[i]["SneakCollideBoxs"].Str(), def.SneakCollideBoxs);

		def.ULConsumeValue = parser[i]["ULConsumeValue"].Int();
		def.Ratio = parser[i]["Ratio"].Float();

		def.BuyTipsType = parser[i]["BuyTipsType"].Int();
		//MyStringCpy(def.BuyTips, sizeof(def.BuyTips), parser[i]["BuyTips"].Str());
		MyStringCpy(def.BuyTips, sizeof(def.BuyTips), ColumnLang(parser[i],"BuyTips",m_CurLanguage));

		def.CarryingHeight = parser[i]["CarryingHeight"].Int();
		def.Arbody = parser[i]["Arbody"].Int();

		m_RoleTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadStorePropDef()
{
    static NoFreeFixedString filename = "storeprop";
	//const char* filename = "storeprop";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_StorePropArray.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		//StorePropDef *def = ENG_NEW(StorePropDef);
		StorePropDef def;
		def.ProductID = parser[i]["ProductID"].Int();
		def.ID = parser[i]["ID"].Int();
		def.Num = parser[i]["Num"].Int();
		MyStringCpy(def.CurrencyType, sizeof(def.CurrencyType), parser[i]["CurrencyType"].Str());
		def.Cost = parser[i]["Cost"].Int();

		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));

		def.Tag = parser[i]["Tag"].Int();
		def.PurchaseLimitID = parser[i]["PurchaseLimitID"].Int();
		def.BuyNum = parser[i]["BuyNum"].Int();
		def.StringID = parser[i]["StringID"].Int();

		m_StorePropArray.push_back(def);
	}
	
	return true;
}

bool DefManager::loadMiniCoinDef()
{
    static NoFreeFixedString filename = "minicoin";
	//const char* filename = "minicoin";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64];
	m_CsvLoadConfig->getMiniCoinPath(filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_MiniCoinTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();

	for (int i= 2; i<numLines; ++i)
	{
		MiniCoinDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));

		def.IconId = parser[i]["IconId"].Int();
		def.Num = parser[i]["Num"].Int();
		def.Cost = parser[i]["Cost"].Float();
		def.BuyNum = parser[i]["BuyNum"].Int();
		def.Tag = parser[i]["Tag"].Int();
		def.PurchaseLimitID = parser[i]["PurchaseLimitID"].Int();
		def.IsHide = parser[i]["IsHide"].Int()>0;
		if (parser[i].hasColumn("Order")) 
			def.Order = parser[i]["Order"].Int();
		else 
			def.Order = 0;

		if (parser[i].hasColumn("Sort"))
			def.Sort = parser[i]["Sort"].Int();
		else
			def.Sort = 0;
			
		def.MiniCoinWithLimit = parser[i]["MiniCoinWithLimit"].Int();
		m_MiniCoinTable.AddRecord(def.ID, def);
	}
	
 	LOG_INFO("%s loading finished", filepath);
	return true;
}

bool DefManager::loadStoreHorseDef()
{
    static NoFreeFixedString filename = "storehorse";
	//const char* filename = "storehorse";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_StoreHorseArray.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	char tmpname[64];
	for (int i=2; i<numLines; ++i)
	{
		//StoreHorseDef *def = ENG_NEW(StoreHorseDef);
		StoreHorseDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.BaseHorseID = parser[i]["BaseHorseID"].Int();
		def.HeadID = parser[i]["HeadID"].Int();
		def.Level = parser[i]["Level"].Int();
		def.UnlockType = parser[i]["UnlockType"].Int();
		def.UnlockItemID = parser[i]["UnlockItemID"].Int();
		def.UnlockNum = parser[i]["UnlockNum"].Int();
		def.Ratio = parser[i]["Ratio"].Int();
		def.BuyTipsType = parser[i]["BuyTipsType"].Int();
		def.Show = parser[i]["Show"].Int();
		def.HorseTag = parser[i]["HorseTag"].Int(); //20210802：增加新参数  codeby： wanyu
		def.GoodsID = parser[i]["GoodsID"].Int(); //20211115：增加新参数坐骑解锁道具  codeby： wanyu
		def.GoodsNum = parser[i]["GoodsNum"].Int(); //20211115：增加新参数坐骑解锁道具数量  codeby： wanyu
		def.Type = parser[i]["Type"].Int(); //20220330：增加新参数坐骑类型  codeby： wanyu
		def.FinalUnlockItem = parser[i]["FinalUnlockItem"].Int(); //20220407：增加新参数坐骑碎片最后解锁的坐骑  codeby： wanyu

		if (parser[i].hasColumn("Quality"))
		{
			def.Quality = parser[i]["Quality"].Int();
		}
		if (parser[i].hasColumn("BackgroundPic"))
		{
			def.BackgroundPic = parser[i]["BackgroundPic"].Str();
		}
		if (parser[i].hasColumn("Position"))
		{
			def.Position = parser[i]["Position"].Str();
		}

		def.ShopTabBkg = parser[i]["ShopTabBkg"].Str();

		MyStringCpy(def.BuyTips, sizeof(def.BuyTips), ColumnLang(parser[i], "BuyTips", m_CurLanguage));
		MyStringCpy(def.Sound, sizeof(def.Sound), parser[i]["Sound"].Str());
		MyStringCpy(def.giflogo, sizeof(def.giflogo), parser[i]["giflogo"].Str());//20210802：增加新参数  codeby： wanyu
		
		for(int j=0; j<MAX_HORSE_SKILL; j++)
		{
			sprintf(tmpname, "Skill%d", j+1);
			def.Skill[j] = parser[i][tmpname].Int();
		}

		if (parser[i].hasColumn("Move") && parser[i]["Move"].IsValid())
		{
			def.Move = parser[i]["Move"].Int();
		}
		m_StoreHorseArray.push_back(def);
		m_StoreHorseIdToHave[def.BaseHorseID + def.Level] = true;
	}
	
	return true;
}

bool DefManager::loadNpcTradeDef()
{
    static NoFreeFixedString filename = "npctrade";
	//const char* filename = "npctrade";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_NpcTradeTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		NpcTradeDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.GroupID = parser[i]["GroupID"].Int();
		def.ItemID = parser[i]["ItemID"].Int();
		def.NpcID = parser[i]["NpcID"].Int();
		def.TradeType = parser[i]["TradeType"].Int();
		def.Weight = parser[i]["Weight"].Int();
		def.NumFloor = parser[i]["NumFloor"].Int();
		def.NumCeil = parser[i]["NumCeil"].Int();
		def.PriceFloor = parser[i]["PriceFloor"].Int();
		def.PriceCeil = parser[i]["PriceCeil"].Int();
		def.PayItemID = parser[i]["PayItemID"].Int();
		def.PayItemNumFloor = parser[i]["PayItemNumFloor"].Int();
		def.PayItemNumCeil = parser[i]["PayItemNumCeil"].Int();
		def.LockNum = parser[i]["LockNum"].Int();
		def.EnchantFlag = parser[i]["EnchantFlag"].Int();
		def.Price = parser[i]["Price"].Int();
		def.EnchPriceCeil = parser[i]["EnchPriceCeil"].Int();
		def.EnchPriceFloor = parser[i]["EnchPriceFloor"].Int();
		def.ADShow = parser[i]["EnchPriceFloor"].Int() > 0;

		m_NpcTradeTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadSignInDef()
{
    static NoFreeFixedString filename = "signin";
	//const char* filename = "signin";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.LinesPairLoad(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_SignInTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		SignInDef def;
		def.SignInType = parser[i]["SignInType"].Int();
		if(def.SignInType == 0) continue;

		def.WhatDay = parser[i]["WhatDay"].Int();
		MyStringCpy(def.RewardName, sizeof(def.RewardName), ColumnLang(parser[i],"RewardName",m_CurLanguage));
		MyStringCpy(def.RewardTips, sizeof(def.RewardTips), ColumnLang(parser[i],"RewardTips",m_CurLanguage));

		MyStringCpy(def.RewardIcon, sizeof(def.RewardIcon), parser[i]["RewardIcon"].Str());
		def.RewardType = parser[i]["RewardType"].Int();
		def.RewardID = parser[i]["RewardID"].Int();
		def.RewardNum = parser[i]["RewardNum"].Int();
		def.RewardIntegral = parser[i]["RewardIntegral"].Int();

		m_SignInTable.AddRecord(def.SignInType*100+def.WhatDay, def);
	}
	
	return true;
}

bool DefManager::loadExtremityScoreDef()
{
    static NoFreeFixedString filename = "extremityscore";
	//const char* filename = "extremityscore";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ExtremityScoreTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		ExtremityScoreDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), parser[i]["Name"].Str());
		def.Type = parser[i]["Type"].Int();
		def.GoalID = parser[i]["GoalID"].Int();
		def.Num = parser[i]["Num"].Int();
		def.Score = parser[i]["Score"].Float();

		m_ExtremityScoreTable.AddRecord(def.Type*10000+def.GoalID, def);
	}
	
	return true;
}

bool DefManager::loadHeadIconDef()
{
    static NoFreeFixedString filename = "headicon";
	//const char* filename = "headicon";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_HeadIconTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		HeadIconDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.IconID = parser[i]["IconID"].Int();
		MyStringCpy(def.Name, sizeof(def.Name), parser[i]["Name"].Str());
		def.Color = parser[i]["Color"].Int();
		def.SeriesID = parser[i]["SeriesID"].Int();
		MyStringCpy(def.SeriesName, sizeof(def.SeriesName), parser[i]["SeriesName"].Str());
		MyStringCpy(def.Desc, sizeof(def.Desc), parser[i]["Desc"].Str());
		def.UnlockID = parser[i]["UnlockID"].Int();
		def.UnlockNum = parser[i]["UnlockNum"].Int();

		m_HeadIconTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadPlantDef()
{
    static NoFreeFixedString filename = "plant";
	//const char* filename = "plant";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_PlantTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		PlantDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.ModelID = parser[i]["ModelID"].Int();
		def.Exp = parser[i]["Exp"].Int();
		def.ArchiveNum = parser[i]["ArchiveNum"].Int();
		def.BuddyNum = parser[i]["BuddyNum"].Int();
		for(int j=0; j<PLANT_LVUP_REWARD; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "RewardID%d", j+1);
			def.RewardID[j] = parser[i][tmpname].Int();
			sprintf(tmpname, "RewardNum%d", j+1);
			def.RewardNum[j] = parser[i][tmpname].Int();
		}

		m_PlantTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadFruitDef()
{
    static NoFreeFixedString filename = "fruit";
	//const char* filename = "fruit";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_FruitTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		FruitDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));
		
		MyStringCpy(def.Icon, sizeof(def.Icon), parser[i]["Icon"].Str());


		def.OpCost = parser[i]["OpCost"].Float();
		def.RipeCost1 = parser[i]["RipeCost1"].Float();
		def.RipeCost2 = parser[i]["RipeCost2"].Float();

		m_FruitTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadHorseAblityDef()
{
    static NoFreeFixedString filename = "horseability";
	//const char* filename = "horseability";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_HorseAbilityTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		HorseAbilityDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));

		MyStringCpy(def.Icon, sizeof(def.Icon), parser[i]["Icon"].Str());

		def.EffectID = parser[i]["EffectID"].Int();
		for(int j=0; j<7; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "Effect%d", j+1);
			def.Effect[j] = parser[i][tmpname].Float();
		}

		m_HorseAbilityTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadBookDef()
{
    static NoFreeFixedString filename = "book";
	//const char* filename = "book";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BookArray.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		//BookDef *def = ENG_NEW(BookDef);
		BookDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.ItemID = parser[i]["ItemID"].Int();
		MyStringCpy(def.ItemName, sizeof(def.ItemName), parser[i]["ItemName"].Str());
		def.SeriesID = parser[i]["SeriesID"].Int();
		def.TypeID = parser[i]["TypeID"].Int();
		MyStringCpy(def.TypeName, sizeof(def.TypeName), ColumnLang(parser[i], "TypeName", m_CurLanguage));
		
		m_BookArray.push_back(def);
	}
	
	return true;
}

bool DefManager::loadBookSeriesDef()
{
    static NoFreeFixedString filename = "bookseries";
	//const char* filename = "bookseries";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BookSeriesTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		BookSeriesDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		def.RewardType = parser[i]["RewardType"].Int();
		def.RewardID = parser[i]["RewardID"].Int();
		def.RewardNum = parser[i]["RewardNum"].Int();

		m_BookSeriesTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadRecycleDef()
{
    static NoFreeFixedString filename = "recycle";
	//const char* filename = "recycle";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_RecycleTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		RecycleDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.InvolvedID = parser[i]["InvolvedID"].Int();
		def.Type = parser[i]["Type"].Int();

		for(int j=0; j<MAX_RECYCLE_GET; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "GetID%d", j+1);
			def.GetID[j] = parser[i][tmpname].Int();
			sprintf(tmpname, "GetNum%d", j+1);
			def.GetNum[j] = parser[i][tmpname].Int();
		}
		//def.GetID = parser[i]["GetID"].Int();
		//def.GetNum = parser[i]["GetNum"].Int();

		m_RecycleTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadGameRuleDef()
{
    static NoFreeFixedString filename = "gamerule";
	//const char* filename = "gamerule";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	//Rainbow::DeletePointerArray(m_GameRuleTable);
	m_GameRuleTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();		
		if(id == 0) continue;

		//GameRuleDef *def = ENG_NEW(GameRuleDef);
		GameRuleDef def;
		def.ID = parser[i]["ID"].Int();
		def.OrderID = parser[i]["OrderID"].Int();

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.Desc, sizeof(def.Desc), ColumnLang(parser[i],"Desc",m_CurLanguage));
		MyStringCpy(def.TypeName, sizeof(def.TypeName), ColumnLang(parser[i],"TypeName",m_CurLanguage));

		def.TypeID = parser[i]["TypeID"].Int();

		for(int j=0; j<MAX_RULE_DEFOPT; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "DefOption%d", j+1);
			def.DefOption[j] = parser[i][tmpname].Int();
		}
	
		for(int j=0; j<MAX_RULE_OPTION; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "OptionID%d", j+1);
			def.OptionID[j] = parser[i][tmpname].Int();
		}

		m_GameRuleTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadFuncSwitchDef()
{
    static NoFreeFixedString filename = "funcswitch";
	//const char* filename = "funcswitch";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_FuncSwitchTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		FuncSwitchDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.AccEncode = parser[i]["AccEncode"].Int();
		def.AccSwitch = parser[i]["AccSwitch"].Int();
		def.HomeChest = parser[i]["HomeChest"].Int();
		def.SdkPay = parser[i]["SdkPay"].Int();
		def.SmsPay = parser[i]["SmsPay"].Int();
		def.Share = parser[i]["Share"].Int();
		def.FeedBack = parser[i]["FeedBack"].Int();
		def.Reservation = parser[i]["Reservation"].Int();
		def.MobileBinding = parser[i]["MobileBinding"].Int();
		def.EmailBinding = parser[i]["EmailBinding"].Int();
		def.SecurityBinding = parser[i]["SecurityBinding"].Int();
		def.QQWalletPay = parser[i]["QQWalletPay"].Int();
		m_FuncSwitchTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadTaskDef()
{
    static NoFreeFixedString filename = "task";
	//const char* filename = "task";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TaskDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TaskDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Icon, sizeof(def.Icon), parser[i]["Icon"].Str());
		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		def.TargetNum = parser[i]["TargetNum"].Int();

		for(int j=0; j<MAX_REWARD_TASK; j++)
		{
			char tmpname[64];
			sprintf(tmpname, "Reward%d", j+1);
			def.Reward[j] = parser[i][tmpname].Int();
			sprintf(tmpname, "RewardNum%d", j+1);
			def.RewardNum[j] = parser[i][tmpname].Int();
		}

		m_TaskDefTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadPlotDef()
{
    static NoFreeFixedString filename = "plot";
	//const char* filename = "plot";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_PlotDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		PlotDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.PlotID = parser[i]["PlotID"].Int();
		MyStringCpy(def.PlotDialog, sizeof(def.PlotDialog), ColumnLang(parser[i],"PlotDialog",m_CurLanguage));
		MyStringCpy(def.Icon, sizeof(def.Icon), parser[i]["Icon"].Str());
		def.Face = parser[i]["Face"].Int();
		def.Position = parser[i]["Position"].Int();
		def.SoundIdx = parser[i]["SoundIdx"].Int();

		m_PlotDefTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadARGradeDef()
{
    static NoFreeFixedString filename = "ARgradepercent";
	//const char* filename = "ARgradepercent";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ARGradeDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		ARGradeDef def;
		def.ID = parser[i]["id"].Int();
		if(def.ID == 0) continue;

		def.Grade = parser[i]["grade"].Float();
		def.Percent = parser[i]["percent"].Int();
		def.Group = parser[i]["group"].Int();
		def.Probability = parser[i]["probability"].Int();
		def.WordID = parser[i]["tagid"].Int();
		m_ARGradeDefTable.push_back(def);
	}

	return true;
}

bool DefManager::loadARStyleDef()
{
    static NoFreeFixedString filename = "ARstylesetting";
	//const char* filename = "ARstylesetting";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ARStyleDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		ARStyleDef def;
		def.ID = parser[i]["id"].Int();
		if(def.ID == 0) continue;
		
		def.Hue = parser[i]["hue"].Float();
		def.Saturability = parser[i]["saturability"].Float();
		def.Lightness = parser[i]["lightness"].Float();
		def.Sharpen = parser[i]["sharpen"].Float();
		def.Solid = parser[i]["shade"].Int();
		def.Tinting = parser[i]["tinting"].Int();
		m_ARStyleDefTable.push_back(def);
	}

	return true;
}

bool DefManager::loadTriggerItemDef()
{
    static NoFreeFixedString filename = "TriggerItem";
	//const char* filename = "TriggerItem";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerItemDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TriggerItemDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.Name = ColumnLang(parser[i], "Name", m_CurLanguage);
		def.Type = parser[i]["Type"].Int();
		def.ChildType = parser[i]["ChildType"].Int();
		def.ChildName = ColumnLang(parser[i], "ChildName", m_CurLanguage);
		def.Desc = ColumnLang(parser[i], "Desc", m_CurLanguage);
		def.Remarks = ColumnLang(parser[i], "Remarks", m_CurLanguage);
		def.Param1 = parser[i]["Param1"].Str(); 
		def.Param2 = parser[i]["Param2"].Str(); 
		def.Param3 = parser[i]["Param3"].Str(); 
		def.Param4 = parser[i]["Param4"].Str(); 
		def.Param5 = parser[i]["Param5"].Str(); 
		def.Param6 = parser[i]["Param6"].Str(); 
		def.Param7 = parser[i]["Param7"].Str(); 
		def.Param8 = parser[i]["Param8"].Str(); 
		def.Param9 = parser[i]["Param9"].Str(); 
		def.Param10 = parser[i]["Param10"].Str(); 
		def.DefaultParam1 = parser[i]["DefaultParam1"].Str(); 
		def.DefaultParam2 = parser[i]["DefaultParam2"].Str(); 
		def.DefaultParam3 = parser[i]["DefaultParam3"].Str(); 
		def.DefaultParam4 = parser[i]["DefaultParam4"].Str(); 
		def.DefaultParam5 = parser[i]["DefaultParam5"].Str(); 
		def.DefaultParam6 = parser[i]["DefaultParam6"].Str(); 
		def.DefaultParam7 = parser[i]["DefaultParam7"].Str(); 
		def.DefaultParam8 = parser[i]["DefaultParam8"].Str(); 
		def.DefaultParam9 = parser[i]["DefaultParam9"].Str(); 
		def.DefaultParam10 = parser[i]["DefaultParam10"].Str();
		def.TriggerEventParams = parser[i]["TriggerEventParams"].Str();
		def.Display = parser[i]["Display"].Int();
		def.DisplayFilter = parser[i]["DisplayFilter"].Str() ;
		def.Version = parser[i]["Version"].Str() ;
		m_TriggerItemDefTable.push_back(def);
	}

	return true;
}

TriggerItemDef *DefManager::getTriggerItemDef(int id)
{	
	loadTriggerItemDef();

	for(int i = 0; i<(int)m_TriggerItemDefTable.size(); i++)
	{
		if(m_TriggerItemDefTable[i].ID == id)
			return &m_TriggerItemDefTable[i];
	}
	return NULL;
}

bool DefManager::loadTriggerEnumDef()
{
    static NoFreeFixedString filename = "TriggerEnum";
	//const char* filename = "TriggerEnum";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerEnumDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TriggerEnumDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.ID = parser[i]["ID"].Int();
		def.EnumName = ColumnLang(parser[i], "EnumName", m_CurLanguage);
		def.Type = parser[i]["Type"].Int();
		def.Display = parser[i]["Display"].Int();
		
		m_TriggerEnumDefTable.push_back(def);
	}

	return true;
}

TriggerEnumDef *DefManager::getTriggerEnumDef(int id)
{
	loadTriggerEnumDef();

	for(int i = 0; i<(int)m_TriggerEnumDefTable.size(); i++)
	{
		if(m_TriggerEnumDefTable[i].ID == id)
			return &m_TriggerEnumDefTable[i];
	}
	return NULL;
}

bool DefManager::loadTriggerFunctionDef()
{
    static NoFreeFixedString filename = "TriggerFunction";
	//const char* filename = "TriggerFunction";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerFunctionDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TriggerFunctionDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.Name = ColumnLang(parser[i], "Name", m_CurLanguage);
		def.Type = parser[i]["Type"].Int();
		def.TypeName = ColumnLang(parser[i], "TypeName", m_CurLanguage);
		def.Desc = ColumnLang(parser[i], "Desc", m_CurLanguage);
		def.ReturnType = parser[i]["ReturnType"].Str();
		def.Param1 = parser[i]["Param1"].Str(); 
		def.Param2 = parser[i]["Param2"].Str(); 
		def.Param3 = parser[i]["Param3"].Str(); 
		def.Param4 = parser[i]["Param4"].Str(); 
		def.Param5 = parser[i]["Param5"].Str(); 
		def.Param6 = parser[i]["Param6"].Str(); 
		def.Param7 = parser[i]["Param7"].Str(); 
		def.Param8 = parser[i]["Param8"].Str(); 
		def.Param9 = parser[i]["Param9"].Str(); 
		def.DefaultParam1 = parser[i]["DefaultParam1"].Str(); 
		def.DefaultParam2 = parser[i]["DefaultParam2"].Str(); 
		def.DefaultParam3 = parser[i]["DefaultParam3"].Str(); 
		def.DefaultParam4 = parser[i]["DefaultParam4"].Str(); 
		def.DefaultParam5 = parser[i]["DefaultParam5"].Str(); 
		def.DefaultParam6 = parser[i]["DefaultParam6"].Str(); 
		def.DefaultParam7 = parser[i]["DefaultParam7"].Str(); 
		def.DefaultParam8 = parser[i]["DefaultParam8"].Str(); 
		def.DefaultParam9 = parser[i]["DefaultParam9"].Str(); 
		def.DefaultParam10 = parser[i]["DefaultParam10"].Str();
		def.Param10 = parser[i]["Param10"].Str(); 
		def.Display = parser[i]["Display"].Int();
		def.Version = parser[i]["Version"].Str();
		m_TriggerFunctionDefTable.push_back(def);
	}

	return true;
}

TriggerFunctionDef *DefManager::getTriggerFunctionDef(int id)
{
	loadTriggerFunctionDef();

	for(int i = 0; i<(int)m_TriggerFunctionDefTable.size(); i++)
	{
		if(m_TriggerFunctionDefTable[i].ID == id)
			return &m_TriggerFunctionDefTable[i];
	}
	return NULL;
}

bool DefManager::loadTriggerParamDef()
{
    static NoFreeFixedString filename = "TriggerParam";
    //const char* filename = "TriggerParam";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerParamDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TriggerParamDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.Desc = ColumnLang(parser[i], "Desc", m_CurLanguage);
		def.UseInput = parser[i]["UseInput"].Str();
		def.UseLibrary = parser[i]["UseLibrary"].Str();
		def.UseFunction = parser[i]["UseFunction"].Str();
		def.UseVariable = parser[i]["UseVariable"].Str();
		def.UseEnum = parser[i]["UseEnum"].Str();
		def.UseObject = parser[i]["UseObject"].Str();
		def.UseTrigger = parser[i]["UseTrigger"].Int();
		def.DefaultValue = parser[i]["DefaultValue"].Str();
		def.ObjFilter = parser[i]["ObjFilter"].Str();

		m_TriggerParamDefTable.push_back(def);
	}

	return true;
}

TriggerParamDef *DefManager::getTriggerParamDef(int id)
{
	loadTriggerParamDef();

	for(int i = 0; i<(int)m_TriggerParamDefTable.size(); i++)
	{
		if(m_TriggerParamDefTable[i].ID == id)
			return &m_TriggerParamDefTable[i];
	}
	return NULL;
}

bool DefManager::loadScriptAPIDef()
{
    static NoFreeFixedString filename = "ScriptAPI";
	//const char* filename = "ScriptAPI";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ScriptAPIDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		ScriptAPIDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.ID = parser[i]["ID"].Int();
		def.Type = parser[i]["Type"].Int();
		def.TypeName = ColumnLang(parser[i], "TypeName", m_CurLanguage);
		def.FunctionName = ColumnLang(parser[i], "FunctionName", m_CurLanguage);
		def.FunctionDesc = ColumnLang(parser[i], "FunctionDesc", m_CurLanguage);
		def.Desc1 = ColumnLang(parser[i], "Desc1", m_CurLanguage);
		def.Desc2 = ColumnLang(parser[i], "Desc2", m_CurLanguage);
		def.Desc3 = ColumnLang(parser[i], "Desc3", m_CurLanguage);
		def.Desc4 = ColumnLang(parser[i], "Desc4", m_CurLanguage);

		m_ScriptAPIDefTable.push_back(def);
	}

	return true;
}

ScriptAPIDef *DefManager::getScriptAPIDef(int id)
{
	loadScriptAPIDef();

	for(int i = 0; i<(int)m_ScriptAPIDefTable.size(); i++)
	{
		if(m_ScriptAPIDefTable[i].ID == id)
			return &m_ScriptAPIDefTable[i];
	}
	return NULL;
}

int DefManager::getParticleDefNum()
{	
	return ParticlesCsv::getInstance()->getParticleDefNum();
}

ParticleDef* DefManager::getParticleDef(int id)
{	
	return ParticlesCsv::getInstance()->getParticleDef(id);
}

ParticleDef* DefManager::getParticleDefByIndex(int index)
{	
	return ParticlesCsv::getInstance()->getParticleDefByIndex(index);
}

ParticleDef* DefManager::getParticleDefByPath(const char* path)
{
	return ParticlesCsv::getInstance()->getParticleDefByPath(path);
}

ParticleDef* DefManager::getParticleDefByPath2(const char* path)
{
	return ParticlesCsv::getInstance()->getParticleDefByPath2(path);
}

ParticleDef* DefManager::getParticleDefByName(const char* name)
{
	return ParticlesCsv::getInstance()->getParticleDefByName(name);
}
int DefManager::getParticleDefTypeNum(int type)
{
	return ParticlesCsv::getInstance()->getParticleDefTypeNum(type);
}

ParticleDef* DefManager::getParticleDefByType(int type, int index)
{
	return ParticlesCsv::getInstance()->getParticleDefByType(type, index);
}

bool DefManager::loadSoundDef()
{
    static NoFreeFixedString filename = "Sound";
	//const char* filename = "Sound";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	int nameIdList[] = { 0, 15389, 15390, 15391, 15392, 15393, 15394, 15395, 15385, 15399};
	int nIndexList[] = { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
	char szIndex[64] = "";

	m_SoundDefTable.clear();
    m_SoundDefPathMap.clear();
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		SoundDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.ID = parser[i]["ID"].Int();
		def.Type = parser[i]["Type"].Int();
		def.TriggerType = parser[i]["TriggerType"].Int();
		def.AtkEffectTypeSID = parser[i]["AtkEffectTypeSID"].Int();
		def.Name = parser[i]["Name"].Str();
		def.IconName = parser[i]["IconName"].Str();
		def.SoundPath = parser[i]["SoundPath"].Str();
		def.Duration = parser[i]["Duration"].Int();

		if (def.TriggerType > 0)
		{
			//构造Name: 类型 + 索引(如:脚本声1, 脚本声2...)
			std::string strName = getStringDef(nameIdList[def.Type]);
			sprintf(szIndex, "%d", nIndexList[def.Type]);
			strName += szIndex;
			def.Name = strName.c_str();
			nIndexList[def.Type] += 1;
		}

		m_SoundDefTable.push_back(def);
		m_SoundDefTypeTableMap[def.Type].push_back(def.ID);
        m_SoundDefPathMap.insert({ def.SoundPath.GetHash(), m_SoundDefTable.size() - 1 });
	}

	return true;
}

int DefManager::getSoundDefNum()
{	
	loadSoundDef();

	return m_SoundDefTable.size();
}

SoundDef* DefManager::getSoundDef(int id)
{	
    OPTICK_EVENT();
	loadSoundDef();

	for (int i = 0; i < (int)m_SoundDefTable.size(); i++)
	{
		if (m_SoundDefTable[i].ID == id)
			return &m_SoundDefTable[i];
	}

	return NULL;
}

SoundDef* DefManager::getSoundDefByIndex(int index)
{	
	loadSoundDef();

	assert(index >= 0);
	assert(index < (int)m_SoundDefTable.size());

	if(index >= 0 && index < (int)m_SoundDefTable.size())
		return &m_SoundDefTable[index];

	return NULL;
}

SoundDef* DefManager::getSoundDefByPath(const char* path)
{
    OPTICK_EVENT();
	loadSoundDef();
    //不使用fixedstring，避免构造开销
    auto it = m_SoundDefPathMap.find(Rainbow::ComputeFNV1aHash(path));
    if (it != m_SoundDefPathMap.end()){
        Assert(it->second < m_SoundDefTable.size());
        return &m_SoundDefTable[it->second];
    }
	/*for (int i = 0; i < (int)m_SoundDefTable.size(); i++)
	{
		if (m_SoundDefTable[i].SoundPath == path)
			return &m_SoundDefTable[i];
	}*/

	return nullptr;
}

int	DefManager::getSoundIdByTypeAndIndex(int type, int index)
{
    OPTICK_EVENT();
	loadSoundDef();

	std::map<int, std::vector<int>>::iterator iter = m_SoundDefTypeTableMap.find(type);
	if (m_SoundDefTypeTableMap.end() != iter)
	{
		assert(index >= 0);
		assert(index < (int)iter->second.size());
		if (index >= 0 && index < (int)iter->second.size())
			return iter->second[index];
	}
	return -1;
}

int DefManager::getSoundTypeDefNum(int type)
{
	loadSoundDef();

	std::map<int, std::vector<int>>::iterator iter = m_SoundDefTypeTableMap.find(type);
	if (m_SoundDefTypeTableMap.end() != iter)
	{
		return iter->second.size();
	}
	return 0;
}
//素材库
bool DefManager::loadTriggerResourceTypeDef()
{
    static NoFreeFixedString filename = "TriggerResourceType";
	//const char* filename = "TriggerResourceType";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerResourceTypeDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		TriggerResourceType def;
		def.ID = parser[i]["ID"].Int();

		if (def.ID == 0)
			continue;

		def.Type = parser[i]["Type"].Int();
		def.Class = parser[i]["Class"].Int();
		def.NameId = parser[i]["NameId"].Int();

		m_TriggerResourceTypeDefTable.push_back(def);
	}

	return true;
}

int DefManager::getTriggerResourceTypeDefNum()
{	
	loadTriggerResourceTypeDef();

	return m_TriggerResourceTypeDefTable.size();
}

TriggerResourceType* DefManager::getTriggerResourceTypeDefByIndex(int index)
{	
	loadTriggerResourceTypeDef();

	assert(index >= 0);
	assert(index < (int)m_TriggerResourceTypeDefTable.size());

	if (index >= 0 && index < (int)m_TriggerResourceTypeDefTable.size())
		return &m_TriggerResourceTypeDefTable[index];

	return NULL;
}


int DefManager::getARGradeDefSize()
{	
	loadARGradeDef();

	return m_ARGradeDefTable.size();
}

ARGradeDef *DefManager::getARGradeDef(int id)
{	
	loadARGradeDef();

	for(int i = 0; i<(int)m_ARGradeDefTable.size(); i++)
	{
		if(m_ARGradeDefTable[i].ID == id)
			return &m_ARGradeDefTable[i];
	}
	return NULL;
}

int DefManager::getARStyleDefSize()
{	
	loadARStyleDef();

	return m_ARStyleDefTable.size();
}

ARStyleDef *DefManager::getARStyleDef(int id)
{	
	loadARStyleDef();

	if(id >= 0 && id <= (int)m_ARStyleDefTable.size())
		return &m_ARStyleDefTable[id];
	return NULL;
}
const SkinActDef* DefManager::getSkinActDef(int id, int skinid)
{
	return SkinActCsv::getInstance()->getBySeqIdAndSkinId(id,skinid);
}

const SummonDef* DefManager::getSummonByID(int id)
{
	return SummonDefCsv::getInstance()->get(id);
}

const ResourcePackDef* DefManager::getResourcePackDef(int id)
{
	return ResourcePackDefCsv::getInstance()->get(id);
}

const FishingDef* DefManager::getFishingDef(int id)
{
	return FishingDefCsv::getInstance()->get(id);
}

bool DefManager::isFishNeedUp(int id)
{
	return FishingDefCsv::getInstance()->isFishNeedUp(id);
}

int DefManager::getOneFishingResult(int biome, int toolId, bool isTempest)
{
	return FishingDefCsv::getInstance()->getOneFishingResult(biome, toolId, isTempest);
}

STGameZone* DefManager::countryGameZone(const char* country)
{
	return GameZoneCsv::getInstance()->countryGameZone(country);
}

const std::string& DefManager::getCountryCode(const std::string* code)
{
	return GameZoneCsv::getInstance()->getCountryCode(code);
}


const ColorMixDef* DefManager::getColorMixDef(int id)
{
	return ColorMixDefCsv::getInstance()->get(id);
}

int DefManager::getColorMixDefDefNum()
{
	return ColorMixDefCsv::getInstance()->getNum();
}

int DefManager::getColorMixDefMixColorID(int id, int mixid)
{
	return ColorMixDefCsv::getInstance()->getMixColorID(id, mixid);
}

unsigned int DefManager::getColorMixDefColor(int id)
{
	return ColorMixDefCsv::getInstance()->getColor(id);
}

bool DefManager::loadHotkeyDef()
{
    static NoFreeFixedString filename = "hotkey";
	//const char* filename = "hotkey";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_HotkeyDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		HotkeyDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		MyStringCpy(def.Name, sizeof(def.Name), ColumnLang(parser[i],"Name",m_CurLanguage));
		MyStringCpy(def.TypeName, sizeof(def.TypeName), ColumnLang(parser[i],"TypeName",m_CurLanguage));

		MyStringCpy(def.FuncName, sizeof(def.FuncName), parser[i]["FuncName"].Str());
		def.Type = parser[i]["Type"].Int();
		def.DefaultCode = parser[i]["DefaultCode"].Int();

		m_HotkeyDefTable.AddRecord(def.ID, def);
	}
	
	return true;
}

bool DefManager::loadKeyDef()
{
    static NoFreeFixedString filename = "key";
	//const char* filename = "key";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_KeyDefTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		KeyDef def;
		def.Code = parser[i]["Code"].Int();
		if(def.Code == 0)
			continue;
		const char* name = ColumnLang(parser[i], "Name", m_CurLanguage);
		MyStringCpy(def.Name, sizeof(def.Name), name);

		assert(!m_KeyDefTable.GetRecord(def.Code));
		m_KeyDefTable.AddRecord(def.Code, def);
	}
	
	return true;
}

bool DefManager::loadResToLoadDef()
{
    static NoFreeFixedString filename = "restoload";
	//const char* filename = "restoload";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id==0 && (parser[i]["ID"].Str())[0]==0) continue;
		FileToLoad def;
		MyStringCpy(def.strPathName, sizeof(def.strPathName), parser[i]["strPathName"].Str());
		def.uiLoad = parser[i]["IsAsk"].Int();
		m_FileToLoadDefTable.push_back(def);
	}

	return true;
}

bool DefManager::loadResNoToLoadDef()
{
    static NoFreeFixedString filename = "restnooload";
	//const char* filename = "restnooload";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		m_FileNoToLoadDefTable.push_back(parser[i]["strPathName"].Str());
	}
	return true;
}

bool DefManager::loadSpecialUinDef()
{
    static NoFreeFixedString filename = "specialuin";
	//const char* filename = "specialuin";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int uin = parser[i]["uin"].Int();
		m_SpecialUinDefTable[uin] = uin;
	}
	return true;
}

int DefManager::getFileNoToLoadNum()
{
	return 	m_FileNoToLoadDefTable.size();
}

std::string &DefManager::getFileNoToLoadDef(int id)
{
	return m_FileNoToLoadDefTable[id];
}

bool DefManager::loadModsToLoadDef()
{
    static NoFreeFixedString filename = "modstoload";
	//const char* filename = "modstoload";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	//LOG_INFO("loadModsToLoadDef(): has loaded");
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id==0 && (parser[i]["ID"].Str())[0]==0) continue;
		ModsToLoad def;
		MyStringCpy(def.strPathName, sizeof(def.strPathName), parser[i]["strPathName"].Str());
		m_ModsToLoadDefTable.push_back(def);
	}

	return true;
}

bool DefManager::loadAntiCrackToLoadDef()
{
    static NoFreeFixedString filename = "AntiCrack";
	//const char* filename = "AntiCrack";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int type = parser[i]["Type"].Int();
		int id = parser[i]["ID"].Int();
		AntiCrack def;
		def.ID = id;
		def.Type = type;
		m_AntiCrackDefTable.push_back(def);
	}

	return true;
}

int DefManager::getAntiCrackToLoadNum()
{
   return 	m_AntiCrackDefTable.size();
}

AntiCrack *DefManager::getAntiCrackToLoadDef(int id)
{
	return &m_AntiCrackDefTable[id];
}

int DefManager::getFileToLoadNum()
{
	loadResToLoadDef();

	return 	m_FileToLoadDefTable.size();
}

FileToLoad *DefManager::getFileToLoadDef(int id)
{	
	loadResToLoadDef();

	return &m_FileToLoadDefTable[id];
}

std::vector<ModsToLoad> &DefManager::getModsToLoadDefTable()
{
	loadModsToLoadDef();

	return m_ModsToLoadDefTable;
};

int DefManager::getModsToLoadNum()
{	
	loadModsToLoadDef();

	return 	m_ModsToLoadDefTable.size();
}

ModsToLoad *DefManager::getModsToLoadDef(int id)
{	
	loadModsToLoadDef();

	return &m_ModsToLoadDefTable[id];
}

bool DefManager::loadVoxelPalette(const char *filepath)
{
	MultiLanCSVParser parser;
	if (!parser.Load(filepath))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	int numLines = (int)parser.GetNumLines();
	if(numLines > 32) numLines = 32;

	VoxelPalette *palette =(VoxelPalette*)ENG_MALLOC(sizeof(VoxelPalette));
	//VoxelPalette palette;
	memset(palette, 0, sizeof(VoxelPalette));

	for(int i=0; i<numLines; ++i)
	{
		for(int col=0; col<8; col++)
		{
			int offset = (31-i)*8+col;
			if(offset > 255) continue;

			palette->BlockID[offset] = parser[i][col].UInt();
		}
	}

	m_VoxPalettes.push_back(palette);
	
	return true;
}

VoxelPalette *DefManager::getVoxlPalette(int i)
{
	preloadVoxelPalette();

	if(i<0 || i>=int(m_VoxPalettes.size())) return NULL;
	else return m_VoxPalettes[i];
}

unsigned int DefManager::calCrcCode(int t)
{
    OPTICK_EVENT();
    register unsigned int crc = 0;
	const int N = 31;

	if(t == CRCCODE_CRAFTING)
	{
        OPTICK_EVENT("CRCCODE_CRAFTING");
		for(auto iter=m_CraftingTable.m_Records.begin(); iter!=m_CraftingTable.m_Records.end(); iter++)
		{
			CraftingDef &def = iter->second;
			crc = crc*N + def.ID;
			crc = crc*N + def.ResultID;
			crc = crc*N + def.ResultCount;
			crc = crc*N + def.UseExp;
			crc = crc*N + def.MoneyID;
			crc = crc*N + def.MoneyCount;
			for(int i=0; i<9; i++)
			{
				crc = crc*N + def.MaterialID[i];
				crc = crc*N + def.MaterialCount[i];
			}
		}
	}
	else if(t == CRCCODE_ACHIEVE)
	{
        OPTICK_EVENT("CRCCODE_ACHIEVE");
		for(auto iter=m_AchievementTable.m_Records.begin(); iter!=m_AchievementTable.m_Records.end(); iter++)
		{
			AchievementDef &def = iter->second;
			crc = crc*N + def.Goal;
			crc = crc*N + def.GoalId;
			crc = crc*N + def.GoalNum;
			crc = crc*N + def.RewardType[0];
			crc = crc*N + def.RewardID[0];
			crc = crc*N + def.RewardNum[0];
			crc = crc*N + def.Point;

			if(def.RewardNum[1] > 0)
			{
				crc = crc*N + def.RewardType[1];
				crc = crc*N + def.RewardID[1];
				crc = crc*N + def.RewardNum[1];
			}
		}
	}
	else if(t == CRCCODE_ITEMS)
	{
        OPTICK_EVENT("CRCCODE_ITEMS");
        auto& table = ItemDefCsv::getInstance()->m_ItemTable;
        struct TableCache {
            typedef decltype(ItemDefCsv::getInstance()->m_ItemTable) InputTableType;
            typedef decltype(ItemDef::ID) ItemType;
            typedef dynamic_array<ItemType> CacheArray;
            CacheArray cache;
            TableCache(InputTableType* table) {
                for (size_t i = 0; i < table->size(); i++) {
                    const ItemDef* def = table->at(i);
                    if (def) {
                        cache.push_back(def->ID);
                    }
                }
            }
            ItemType operator[](size_t index) const {
                return cache[index];
            }
            size_t size() const {
                return cache.size();
            }
        };
        static TableCache cache(&table);
        size_t count = cache.size();
        register size_t i = 0;
        for (; i < cache.size(); i++) {
            crc = (crc <<5) - crc + cache[i];
        }
        
        LogStringMsg("CRCCODE_ITEMS size:%d cacche:%d", table.size(), cache.size());
	}
	else if(t == CRCCODE_MOBSPAWNER)
	{
        OPTICK_EVENT("CRCCODE_MOBSPAWNER");
		for(auto iter=m_MobSpawnerTable.m_Records.begin(); iter!=m_MobSpawnerTable.m_Records.end(); iter++)
		{
			MobSpawnerDef &def = iter->second;
			crc = crc*N + def.ID;
			crc = crc*N + def.MobResID;
		}
	}
	else if(t == CRCCODE_MONSTER)
	{
        OPTICK_EVENT("CRCCODE_MONSTER");
		for(auto iter=MonsterCsv::getInstance()->m_Monsters.m_Records.begin(); iter!=MonsterCsv::getInstance()->m_Monsters.m_Records.end(); iter++)
		{
			MonsterDef &def = iter->second;
			crc = crc*N + def.ID;
			crc = crc*N + def.BabyID;

			for(int i=0; i<MAX_MONSTER_DROPITEM; i++)
			{
				if(def.DropItem[i] > 0) crc = crc*N + def.DropItem[i];
			}
		}
	}
	else if(t == CRCCODE_HORSEEGG)
	{
        OPTICK_EVENT("CRCCODE_HORSEEGG");
		for(auto iter= HorseEggCsv::getInstance()->m_HorseEggTable.m_Records.begin(); iter!= HorseEggCsv::getInstance()->m_HorseEggTable.m_Records.end(); iter++)
		{
			HorseEggDef &def = iter->second;
			crc = crc*N + def.ID;

			for(int i=0; i<MAX_EGG_HORSES; i++)
			{
				if(def.Horse[i] > 0) crc = crc*N + def.Horse[i];
			}
		}
	}
	else if(t == CRCCODE_FURNACE)
	{
        OPTICK_EVENT("CRCCODE_FURNACE");
		for(auto iter=m_FurnaceTable.m_Records.begin(); iter!=m_FurnaceTable.m_Records.end(); iter++)
		{
			FurnaceDef &def = iter->second;
			crc = crc*N + def.ID;
			crc = crc*N + def.Heat;
			crc = crc*N + def.Result;
			crc = crc*N + def.Exp;
		}
	}

	return crc;
}

char *s_CrashPtr = 0;
bool DefManager::checkCrcCode(int t)
{
    //根据讨论结果不用crc方式做外挂检查
    return true;
    /*
	if(m_CrcCode[t] != calCrcCode(t))
	{
		//memcpy(s_CrashPtr, this, 10); //crash
		return false;
	}
	else return true;
    */
}

void DefManager::resetCrcCode(int type)
{
	//m_CrcCode[type] = calCrcCode(type);
}

static bool IsCraftingFit(const CraftingDef &def, int gridx, int gridy, const int *ids, const int *counts, int &nresult)
{
	if(def.GridX!=gridx || def.GridY!=gridy) return false;

	int minrate = 10000000;
	for(int y=0; y<gridy; y++)
	{
		for(int x=0; x<gridx; x++)
		{
			int index = y*gridx + x;
			int defmtlid = def.MaterialID[index];
			int srcmtlid = ids[index];

			if(defmtlid==0)
			{
				if(srcmtlid != 0) return false;
				else continue;
			}
			if(def.MaterialCount[index] > counts[index]) return false;

			const ItemDef *itemdef = ItemDefCsv::getInstance()->get(defmtlid);
			if(itemdef == NULL)
			{
				//LOG_SEVERE("合成公式%d有材料%d, 但找不到材料的物品表项", def.ID, defmtlid); 
				LOG_SEVERE("composite formula[%d] has mat [%d], but can not find mat id", def.ID, defmtlid); 
				return false;
			}
			if(itemdef->ItemGroup > 0) //组物品
			{
				const ItemDef *srcitemdef = ItemDefCsv::getInstance()->get(srcmtlid);
				if(srcitemdef->ItemGroup != itemdef->ItemGroup) return false;
			}
			else
			{
				if(srcmtlid != defmtlid) return false;
			}

			assert(def.MaterialCount[index] > 0);
			int rate = counts[index] / def.MaterialCount[index];
			if(rate < minrate) minrate = rate;
		}
	}

	nresult = minrate;
	return true;
}

int DefManager::getCraftingDefNum()
{
	loadCraftingCSV();

	return (int)m_Craftings.size();
}

const CraftingDef *DefManager::findCrafting(int gridx, int gridy, const int *ids, const int *counts, int &nresult)
{	
	loadCraftingCSV();

	nresult = 0;
	auto iter = m_CraftingTable.m_Records.begin();
	for(; iter!=m_CraftingTable.m_Records.end(); iter++)
	{
		if(IsCraftingFit(iter->second, gridx, gridy, ids, counts, nresult)) return &iter->second;
	}

	for (auto it = g_ModMgr.m_CurrentCraftingDefs.m_Records.begin(); it != g_ModMgr.m_CurrentCraftingDefs.m_Records.end(); it++)
	{
		if (IsCraftingFit(iter->second, gridx, gridy, ids, counts, nresult)) return &it->second;
	}

	return NULL;
}

CraftingDef *DefManager::findCrafting(int resultId)
{
	loadCraftingCSV();

	for (auto it = g_ModMgr.m_CurrentCraftingDefs.m_Records.begin(); it != g_ModMgr.m_CurrentCraftingDefs.m_Records.end(); it++)
	{
		if (resultId == it->second.ResultID) return &it->second;
	}

	auto iter = m_CraftingTable.m_Records.begin();
	for (; iter != m_CraftingTable.m_Records.end(); iter++)
	{
		if (resultId == iter->second.ResultID) return &iter->second;
	}

	return nullptr;
}

CraftingDef* DefManager::findCraftingByDoubleWeapon(int rightWeaponId, int leftWeaponId)
{
	loadCraftingCSV();

	for (auto it = g_ModMgr.m_CurrentCraftingDefs.m_Records.begin(); it != g_ModMgr.m_CurrentCraftingDefs.m_Records.end(); it++)
	{
		if (rightWeaponId == it->second.MaterialID[0] && leftWeaponId == it->second.MaterialID[1] && it->second.CraftingItemID == BLOCK_FUSION_CAGE) return &it->second;
	}

	auto iter = m_CraftingTable.m_Records.begin();
	for (; iter != m_CraftingTable.m_Records.end(); iter++)
	{
		if (rightWeaponId == iter->second.MaterialID[0] && leftWeaponId == iter->second.MaterialID[1] && iter->second.CraftingItemID == BLOCK_FUSION_CAGE) return &iter->second;
	}

	return nullptr;
}

CraftingDef* DefManager::findCraftingBySort(int resultId,int times)
{
	int count = times;
	loadCraftingCSV();

	for (auto it = g_ModMgr.m_CurrentCraftingDefs.m_Records.begin(); it != g_ModMgr.m_CurrentCraftingDefs.m_Records.end(); it++)
	{
		if (resultId == it->second.ResultID)
		{
			count = count - 1;
			if (count == 0)
			{
				return &it->second;
			}
		}
	}

	auto iter = m_CraftingTable.m_Records.begin();
	for (; iter != m_CraftingTable.m_Records.end(); iter++)
	{
		if (resultId == iter->second.ResultID)
		{
			count = count - 1;
			if (count == 0)
			{
				return &iter->second;
			}
		}
	}

	return nullptr;
}

int DefManager::getCraftingDefNumCount(int resultId)
{
	int count = 0;
	auto iter = m_CraftingTable.m_Records.begin();
	for (; iter != m_CraftingTable.m_Records.end(); iter++)
	{
		if (resultId == iter->second.ResultID) count = count+1;
	}
	return count;
}

CraftingDef *DefManager::getCraftingDef(int id, bool takeplace/* =false */, bool getDefause /*= true*/)
{	
	loadCraftingCSV();

	CraftingDef* def = g_ModMgr.tryGetCraftingDef(id);
	if (def != nullptr)
	{
		return def;
	}

	auto result = m_CraftingTable.GetRecord(id);
	if (result)
	{
		return result;
	}


	if (takeplace)
		return getCraftingDef(CRAFTING_DEFAULT);
	if (getDefause)
		return getCraftingDef(CRAFTING_DEFAULT);
	else
		return NULL;
}

CraftingDef* DefManager::getCraftingDefByCopy(int id, int nCopyId)
{
	if (m_CraftingTable.GetRecord(id) != NULL)
		return nullptr;

	const CraftingDef* pOriDef = m_CraftingTable.GetRecord(nCopyId);
	if (!pOriDef)
		return nullptr;

	CraftingDef& defNew = m_CraftingTable.m_Records[id];
	defNew = *pOriDef;
	defNew.ID = id;
	defNew.CopyID = nCopyId;

	return &defNew;
}

CraftingDef *DefManager::getCraftingDefByIndex(int index)
{	
	loadCraftingCSV();

	assert(index >= 0 && index < getCraftingDefNum());
	return m_Craftings[index];
}

const CraftingDef* DefManager::findDisassemble(int resultId)
{
	return DisassembleCsv::getInstance()->getByItemId(resultId);
}

std::map<int, CraftingDef*> DefManager::getCustomCraftingDefMap()
{
    OPTICK_EVENT();
	std::map<int, CraftingDef*> customCraftDefMap;
	int count = g_ModMgr.getCraftCount();
	for (int i = 0; i < count; i++)
	{
		CraftingDef* crfatDef = g_ModMgr.tryGetCraftingDefByIndex(i);
		if (crfatDef)
		{
			if (crfatDef->SubType.size() == 0)
			{
				crfatDef->SubType.push_back(21558);
				crfatDef->SubTypeOrder.clear();
				crfatDef->SubTypeOrder.push_back(0);
			}
			customCraftDefMap[crfatDef->ID] = crfatDef;
		}
	}

	return customCraftDefMap;
}

std::vector<CraftingDef> DefManager::getCraftingDefByType(int type)
{
    OPTICK_EVENT();
	loadCraftingCSV();
	
	std::map<int, CraftingDef*> customCraftDefMap = getCustomCraftingDefMap();
	std::vector<CraftingDef> result;
	for (auto iter = m_CraftingTable.m_Records.begin(); iter != m_CraftingTable.m_Records.end(); iter++)
	{
		CraftingDef def = iter->second;
		//LOG_WARNING("dCrafting getCraftingDefByType ... %d", def.ID);
		auto findDef = customCraftDefMap.find(def.ID);
		if (findDef != customCraftDefMap.end())
		{
			def = *findDef->second;
			customCraftDefMap.erase(findDef);
		}

		int size = def.Type.size();
		for (size_t i = 0; i < size; i++)
		{
			if (def.Type[i] == type)
			{
				//LOG_WARNING("dCrafting push_back ... %d", def.ID);
				result.push_back(def);
				break;
			}
		}
	}

	for (auto it = customCraftDefMap.begin(); it != customCraftDefMap.end(); ++it) {
		CraftingDef def = *it->second;
		int size = def.Type.size();
		for (size_t i = 0; i < size; i++)
		{
			if (def.Type[i] == type)
			{
				result.push_back(def);
				break;
			}
		}
	}

	return result;
}
std::vector<CraftingDef> DefManager::getCraftingDefBySubType(int type, int subtype)
{
    OPTICK_EVENT();
	loadCraftingCSV();

	std::map<int, CraftingDef*> customCraftDefMap = getCustomCraftingDefMap();
	std::vector<CraftingDef> result;
	for (auto iter = m_CraftingTable.m_Records.begin(); iter != m_CraftingTable.m_Records.end(); iter++)
	{
		CraftingDef def = iter->second;
		auto findDef = customCraftDefMap.find(def.ID);
		if (findDef != customCraftDefMap.end())
		{
			def = *findDef->second;
			customCraftDefMap.erase(findDef);
		}

		int size = def.Type.size();
		for (size_t i = 0; i < size; i++)
		{
			if (def.Type[i] == type)
			{
				if (def.SubType.size() == 0)
				{
					break;
				}
				if (def.SubType.size() == size)
				{
					if (def.SubType[i] == subtype)
					{
						result.push_back(def);
					}
				}
				else
				{
					if (def.SubType[0] == subtype)
					{
						result.push_back(def);
					}
				}
			}
		}
	}

	for (auto it = customCraftDefMap.begin(); it != customCraftDefMap.end(); ++it) {
		CraftingDef def = *it->second;
		int size = def.Type.size();
		for (size_t i = 0; i < size; i++)
		{
			if (def.Type[i] == type)
			{
				if (def.SubType.size() == 0)
				{
					break;
				}
				if (def.SubType.size() == size)
				{
					if (def.SubType[i] == subtype)
					{
						result.push_back(def);
					}
				}
				else
				{
					if (def.SubType[0] == subtype)
					{
						result.push_back(def);
					}
				}
			}
		}
	}

	return result;
}

bool DefManager::isCraftingType(int resultId, int tabIndex)
{
	CraftingDef* def = findCrafting(resultId);
	if (def == nullptr)
	{
		return false;
	}
	if (def->Type.size() > 0)
	{
		for (unsigned int i = 0; i < def->Type.size(); ++i)
		{
			if (tabIndex == def->Type[i])
			{
				return true;
			}
		}
	}
	return false;
}
int DefManager::getCookbookNum()
{
	loadCraftingCSV();
	return m_CookbookTable.size();
}

CraftingDef* DefManager::getCookbookDef(int id, bool takeplace ,bool getDefause)
{
	loadCraftingCSV();
	return getCraftingDef(id, takeplace, getDefause);
}

CraftingDef* DefManager::getCookbookByIndex(int index)
{
	loadCraftingCSV();
	return m_CookbookTable.at(index);
}

CraftingDef* DefManager::findCookbookByResultId(int resultId)
{
	loadCraftingCSV();
	auto iter = m_CookbookTable.begin();
	for (; iter != m_CookbookTable.end(); iter++)
	{
		if (resultId == (*iter)->ResultID)
			return (*iter);
	}
	return NULL;
}
bool DefManager::isCookbookType(int resultId)
{
	loadCraftingCSV();
	auto iter = m_CookbookTable.begin();
	for (; iter != m_CookbookTable.end(); iter++)
	{
		if (resultId == (*iter)->ResultID) 
			return true;
	}

	return false;
}

CraftingDef* DefManager::getCookBookByDef(std::unordered_map<int, int>& materialMap)
{
	loadCraftingCSV();
	auto iter = std::find_if(m_CookbookTable.begin(), m_CookbookTable.end(), [&materialMap](CraftingDef* otherdef)-> bool {
			//用于匹配配方是否一致 主要用于石锅生成
			std::unordered_map<int, int> formulaMap; // 配方表
			int nMaterialID = 0; // 材料id
			int nMaterialCount = 0; // 材料数量
			for (int i = 0; i < 9; i++)
			{
				nMaterialID = otherdef->MaterialID[i];
				if (nMaterialID > 0)
				{
					nMaterialCount = otherdef->MaterialCount[i];
					if (formulaMap[nMaterialID])
					{
						formulaMap[nMaterialID] = formulaMap[nMaterialID] + nMaterialCount;
					}
					else
					{
						formulaMap[nMaterialID] = nMaterialCount;
					}
				}
				else
				{
					break;
				}
			}

			// 调试代码方便找到对应配方
			/*std::unordered_map<int, int> testMap = {
				{12513, 1},
			};
			if (testMap == formulaMap)
			{
				LOG_INFO("test");
			}*/

			//用于匹配组的配方
			if (otherdef->IsGroup)
			{
				// 需先把不是组的材料id消掉，再把剩下的材料id变成组id去匹配。
				std::unordered_map<int, int> tampMaterialMap(materialMap.begin(), materialMap.end());
				int decNum = 1; // 一个格子只能放一个数量的道具
				for (auto item = materialMap.begin(); item != materialMap.end(); ++item)
				{
					int id = item->first;
					int count = item->second;
					auto formulaMapIt = formulaMap.find(id);
					if (formulaMapIt != formulaMap.end())
					{
						for (int i = 0; i < count; i++)
						{
							tampMaterialMap[id] = tampMaterialMap[id] - decNum;
							if (tampMaterialMap[id] <= 0)
							{
								auto it = tampMaterialMap.find(id);
								if (it != tampMaterialMap.end())
								{
									tampMaterialMap.erase(it);
								}
							}

							formulaMap[id] = formulaMap[id] - decNum;
							if (formulaMap[id] <= 0)
							{
								auto it = formulaMap.find(id);
								if (it != formulaMap.end())
								{
									formulaMap.erase(it);
								}
								break;
							}
						}
					}
					else
					{
						ItemDef* def = GetDefManagerProxy()->getItemDef(id);
						if (!def)
						{
							return false;
						}
						if (def->ItemGroup > 0 && def->ItemGroup != id)
						{
							if (tampMaterialMap[def->ItemGroup])
							{
								tampMaterialMap[def->ItemGroup] = tampMaterialMap[def->ItemGroup] + count;
							}
							else
							{
								tampMaterialMap[def->ItemGroup] = count;
							}
							auto it = tampMaterialMap.find(id);
							if (it != tampMaterialMap.end())
							{
								tampMaterialMap.erase(it);
							}
						}
					}
				}
				if (tampMaterialMap.size() == formulaMap.size())
				{
					if (tampMaterialMap == formulaMap)
					{
						return true;
					}
				}
			}
			else //用于非匹配组的配方
			{
				if (materialMap.size() == formulaMap.size())
				{
					if (materialMap == formulaMap)
					{
						return true;
					}
				}
			}
			return false;
	});


	/*auto iter = std::find(m_CookbookTable.begin(), m_CookbookTable.end(),(*def));*/
	if (iter != m_CookbookTable.end())
	{
		return (*iter);
	}
	return nullptr;
}


const SprayPaintDef* DefManager::getSprayPaintDef(int id)
{
	return SprayPaintDefCsv::getInstance()->get(id);
}

std::string DefManager::getRandomName(int sex)
{	
	loadRandomNames();

	int nSize = m_RandomSurnames.size();
	//std::string name = m_RandomSurnames[m_RandGen->get(m_RandomSurnames.size())];
	std::string name = "";

	if(nSize > 0)
	{
		name = m_RandomSurnames[m_RandGen->get(m_RandomSurnames.size())];
	}

	int r = m_RandGen->get(2);
	if(sex == 1) r = 0;
	else if(sex == 2) r = 1;

	if(r == 0)
	{
		//LLDO:排除0的情况, 因为x%0, 对0取模崩溃
		int nSize = m_RandomMaleNames.size();
		if(nSize > 0)
		{
			name += m_RandomMaleNames[m_RandGen->get(m_RandomMaleNames.size())];
		}		
	}
	else
	{
		int nSize = m_RandomFemaleNames.size();
		if(nSize > 0)
		{
			name += m_RandomFemaleNames[m_RandGen->get(m_RandomFemaleNames.size())];
		}
	}
	return name;
}

void DefManager::toLowerCaseMultilingual(core::string &msg)
{
	if (m_CurLanguage  == 11)
	{
		int letterlen = strlen(letterUpperUtf8) / 2;
		int contentlen = msg.size();
		for (int i = 0; i<(contentlen -1); i++)
		{
			for (int j = 0; j<letterlen; j++)
			{
				if (letterUpperUtf8[j*2] == msg[i] && letterUpperUtf8[j*2+1] == msg[i+1])
				{
					msg[i] = letterLowerUtf8[j];
					msg[i+1] = letterLowerUtf8[j+1];
					i++;
					break;
				}
			}
		}
	}
	StringUtil::toLowerCase(msg);
}

void DefManager::addCannotReadPath(std::string &path)
{
	//加入被禁止加载的资源
	GetFileManagerWeb().AddCannotReadPath(path);
	//FileManager::getSingleton().addCannotReadPath(path);
}

void DefManager::setCannotReadPathReady(bool ready)
{
	//FileManager::getSingleton().setCannotReadPathReady(ready);
}

/*
std::string DefManager::filterStringOld(char *content)
{
	std::string destcontent = content;
	String msg = content;
	toLowerCaseMultilingual(msg);
	for(size_t i=0; i<m_FilterStrings.size(); i++)
	{
		std::string src = m_FilterStrings[i];
		int idx = msg.find(src);
		while(idx != -1)
		{
			destcontent = destcontent.replace(idx, src.length(), "***");
			msg = msg.replace(idx, src.length(), "***");
			idx = msg.find(src);
		}
	}

	return destcontent;
}
*/

std::string DefManager::filterString(char *content, bool checknum)
{
	if(content == NULL)
	{
		return "";
	}

	core::string destcontent = content;
	//std::string msg = content;
	toLowerCaseMultilingual(destcontent);
	//目标字符串是否是纯英文
	bool isAllEnglish_ = true;
	for (size_t j = 0; j<destcontent.length(); j++)
	{
		if ((unsigned char)destcontent[j] > 127)
		{
			isAllEnglish_ = false;
			break;
		}
	}
	int env = GetIWorldConfigProxy()->getGameData("game_env");
	if (env >= 10 || env == 1)
	{
		checknum = false;
	}
	MNSandbox::GetGlobalEvent().Emit<core::string &, bool, bool>("Filter_Filter", destcontent, isAllEnglish_, checknum);

	char* dest = (char*)destcontent.c_str();
	while (*content && *dest)
	{
		if (*dest != '*')
		{
			*dest =	*content;
		}
		content++;
		dest++;
	}
	return destcontent;
}

void DefManager::filterStringDirect(char *content)
{
	if (!content)
		return;
	core::string destcontent = content;
	//std::string msg = content;
	toLowerCaseMultilingual(destcontent);
	//目标字符串是否是纯英文
	bool isAllEnglish_ = true;
	for (size_t j = 0; j<destcontent.length(); j++)
	{
		if ((unsigned char)destcontent[j] > 127)
		{
			isAllEnglish_ = false;
			break;
		}
	}
	int env = GetIWorldConfigProxy()->getGameData("game_env");
	bool oversea = false;
	if (env >= 10 || env == 1)
	{
		oversea = true;
	}
	MNSandbox::GetGlobalEvent().Emit<core::string &, bool, bool>("Filter_Filter", destcontent, isAllEnglish_, !oversea);
	char* dest = (char*)destcontent.c_str();
	while (*content && *dest)
	{
		if (*dest == '*')
		{
			*content = '*';
		}
		content++;
		dest++;
	}
}


void DefManager::addFilterString(char* content, unsigned int type1, unsigned int type2, unsigned int score)   //动态增加一个过滤词
{
#ifdef IWORLD_SERVER_BUILD 
	  return;
#endif
	int len = strlen(content);
	char* content_ = new char[strlen(content) + 2];
	int c = 0;
	for (int i = 0; i<len; i++)
	{
		char temp = content[i];
		if (temp >= 'A' && temp<='Z')
        {
            temp = temp + 32;
        }
		if ((i == 0 || i == (len -1)) && (temp == '%' || temp == '.'))
		{
			continue;
		}
		content_[c++] = temp;
	}
	content_[c] = 0;

	if (GetClientApp().checkIsGMWhiteListMember() && score == 0)
	{
		score = 10;
	}
	//if (!m_pFilter.isFilter(content_, true))

	MNSandbox::GetGlobalEvent().Emit<char* &, int, int, int>("Filter_addOneFilterString2", content_, type1, type2, score);
	OGRE_DELETE_ARRAY(content_); 
}


void DefManager::delFilterString(char* content)   //动态删除一个过滤词
{
/*	vector<char*>::iterator itc = m_FilterStrings.begin();
	for (; itc != m_FilterStrings.end(); itc++) {
		if (strcmp(*itc, content) == 0 ) {
			m_FilterStrings.erase(itc);
			LOG_INFO(" del m_FilterStringsWhole[%s]", content);
			break;
		}
	}

	std::string tmp = content;
	//是否敏感词是纯英文
	bool isAllEnglish_ = true;
	for (int j = 0; j<tmp.length(); j++)
	{
		if ((unsigned char)tmp[j] > 127) {
			isAllEnglish_ = false;
			break;
		}
	}
	if (isAllEnglish_)
	{
		tmp = String(" ") + tmp + " ";
		vector<char*>::iterator itc = m_FilterStringsWhole.begin();
		for (; itc != m_FilterStringsWhole.end(); itc++) {
			if (strcmp(*itc, tmp.c_str() ) == 0) {
				m_FilterStringsWhole.erase(itc);
				LOG_INFO("del m_FilterStringsWhole[%s]", tmp.c_str());
				break;
			}
		}
	}*/
}

void DefManager::addInvalidCode(char * content)
{
	MNSandbox::GetGlobalEvent().Emit<char* &>("Filter_setInvalidCode", content);
}

void DefManager::translateLetter(std::string title, std::string content, std::string& transTitle, std::string& transContent)
{
	WorldStringTranslateMgr::getInstance()->translateLetter(title, content, transTitle, transContent);
}

void DefManager::translateBookCover(std::string title, std::string author, std::string& transTitle, std::string& transAuthor)
{
	WorldStringTranslateMgr::getInstance()->translateBookCover(title, author, transTitle, transAuthor);
}

std::string DefManager::getTransStrByKey(TRANSLATE_TYPE type, std::string key, std::string oldVal, std::string oldMultiLanVal)
{
	return WorldStringTranslateMgr::getInstance()->getTransStrByKey(type, key, oldVal, oldMultiLanVal);
}

void DefManager::getS2(std::string& s2_, std::string& s2t_)
{
	OWorldList::GetInstance().m_CSOWorld->getS2(s2_, s2t_);
}

bool DefManager::checkFilterString(const char *content)
{
	if (content == NULL) return false;
	
	core::string msg = content;
	toLowerCaseMultilingual(msg);

	//目标字符串是否是纯英文
	bool isAllEnglish_ = true;
	for (size_t j = 0; j<msg.length(); j++ )
	{
		if ((unsigned char)msg[j] > 127) 
		{
			isAllEnglish_ = false;
			break;
		}
	}
	bool ret = false;
	MNSandbox::GetGlobalEvent().Emit<bool &, core::string &, bool>("Filter_isFilter", ret, msg, isAllEnglish_);
	return ret;
}
BlockDef *DefManager::getBlockDef(int id, bool takeplace/* = false*/)
{
	return BlockDefCsv::getInstance()->get(id, takeplace);
}

BlockDef* DefManager::addBlockDef(int id, int type, const std::string& model, const std::string& name, const std::string& desc)
{
	return BlockDefCsv::getInstance()->add(id, type, model, name, desc);
}

BlockDef* DefManager::addBlockDefByCopy(int id, int type, int copyId)
{
	return BlockDefCsv::getInstance()->addByCopy(id, type, copyId);
}

void DefManager::clearTempBlockDef()
{
	BlockDefCsv::getInstance()->clearTempDef();
}

int DefManager::getMaxID()
{
	return BlockDefCsv::getInstance()->getMaxID();
}

std::vector<BlockDef*>& DefManager::getBlockDefTable()
{
	return BlockDefCsv::getInstance()->m_BlockDefTable;
}

const BlockEffectDef* DefManager::getBlockEffectDef(const char* blockTypeName)
{
	return BlockEffectDefCsv::getInstance()->getBlockEffectDef(blockTypeName);
}



const FoodDef *DefManager::getFoodDef(int id)
{
	return FoodDefCsv::getInstance()->get(id);
}

int DefManager::getFoodNum()
{
	return FoodDefCsv::getInstance()->getNum();
}

const FoodDef *DefManager::getFoodDefByIndex(int index)
{
	return FoodDefCsv::getInstance()->getByIndex(index);
}

bool DefManager::isFood(int id)
{
	const FoodDef* def = FoodDefCsv::getInstance()->get(id);
	if (def)
	{
		return true;
	}
	return false;
}

FoodDef* DefManager::addFoodDefByCopy(int id, int copyId)
{
	return FoodDefCsv::getInstance()->addByCopy(id, copyId);
}

FoodDef* DefManager::getOriginalFoodDef(int id)
{
	return FoodDefCsv::getInstance()->getOriginal(id);
}

void DefManager::removeFoodDef(int id)
{
	FoodDefCsv::getInstance()->remove(id);
}

ItemEquipDef* DefManager::addEquipDef(int id)
{
	return EquipDefCsv::getInstance()->add(id);
}

ItemEquipDef* DefManager::getEquipDef(int id)
{
	return EquipDefCsv::getInstance()->get(id);
}

void DefManager::removeEquipDef(int id)
{
	EquipDefCsv::getInstance()->remove(id);
}

BiomeGroupDef* DefManager::getBiomeGroupDef(int id)
{
	return BiomeGroupDefCsv::getInstance()->get(id);
}

int DefManager::getBiomeGroupNum()
{
	return BiomeGroupDefCsv::getInstance()->getNum();
}

BiomeGroupDef* DefManager::getBiomeGroupDefByIndex(int index)
{
	return BiomeGroupDefCsv::getInstance()->getByIndex(index);
}

void DefManager::ParseWCoord(const std::string& str, WCoord& posData)
{
	std::vector<std::string> vDepth;
	Rainbow::StringUtil::split(vDepth, str, "|");
	if (vDepth.size() == 3)
	{
		posData.x = static_cast<short>(atoi(vDepth[0].c_str()));
		posData.y = static_cast<short>(atoi(vDepth[1].c_str()));
		posData.z = static_cast<short>(atoi(vDepth[2].c_str()));
	}
	else
	{
		posData = WCoord(0, 0, 0);
	}
}

void DefManager::ParseItemPosData(const std::string& str, ItemPosDataDef& posData)
{
	vector<string> vDepth;
	Rainbow::StringUtil::split(vDepth, str, "|");
	// if (vDepth.size() > 0 && vDepth.size() != 6)
	// 	LOG_WARNING("DefManager::ParseItemPosData parse error str = %s", str.c_str());
	if (vDepth.size() == 6)
	{
		posData.Pos.x = static_cast<short>(atoi(vDepth[0].c_str()));
		posData.Pos.y = static_cast<short>(atoi(vDepth[1].c_str()));
		posData.Pos.z = static_cast<short>(atoi(vDepth[2].c_str()));
		posData.Rot.x = static_cast<short>(atoi(vDepth[3].c_str()));
		posData.Rot.y = static_cast<short>(atoi(vDepth[4].c_str()));
		posData.Rot.z = static_cast<short>(atoi(vDepth[5].c_str()));
	}
	else
	{
		posData.Pos = WCoord(0, 0, 0);
		posData.Rot = WCoord(0, 0, 0);
	}
}

ItemInHandDef* DefManager::getItemInHandDef(int id)
{
	return ItemInHandDefCsv::getInstance()->get(id);
}

const ItemSkillDef* DefManager::getItemSkillDef(int id)
{
	return ItemSkillDefCsv::getInstance()->get(id);
}

DefDataTable<AchievementDef> &DefManager::getAchievementTable()
{	
	loadAchievementCSV();

	return m_AchievementTable;
}

const AchievementDef *DefManager::getAchievementDef(int id)
{	
	loadAchievementCSV();

	return m_AchievementTable.GetRecord(id);
}

int DefManager::getAchievementDefNum()
{	
	loadAchievementCSV();

	return m_AchievementTable.GetRecordSize();
}

int DefManager::getTreeDefID(const char *name)
{	
	if(name[0] == 0) return 0;

	auto iter = m_TreeTable.m_Records.begin();
	for(; iter!= m_TreeTable.m_Records.end(); iter++)
	{
		if(strcmp(name, iter->second.Name) == 0) return iter->second.ID;
	}
	assert(0);
	return 0;
}

const TreeDef *DefManager::getTreeDef(int id)
{
	return m_TreeTable.GetRecord(id);
}

ItemDef *DefManager::getItemDef(int id, bool takeplace/* =false */)
{
	return ItemDefCsv::getInstance()->get(id, takeplace);
}

ItemDef* DefManager::addItemDef(int id, int type, std::string model, std::string name, std::string desc, short involvedid)
{
	return ItemDefCsv::getInstance()->add(id, type, model, name, desc, involvedid);
}

ItemDef* DefManager::addItemDefByCopy(int id, int type, int copyId)
{
	return ItemDefCsv::getInstance()->addByCopy(id, type, copyId);
}

bool DefManager::addModItemDef(int id, ItemDef* def)
{
	return ItemDefCsv::getInstance()->addModItemDef(id, def);
}

bool DefManager::removeModItemDef(int id)
{
	return ItemDefCsv::getInstance()->removeModItemDef(id);
}

bool DefManager::addModBlockDef(int id, BlockDef* def)
{
	return BlockDefCsv::getInstance()->addModBlockDef(id, def);
}

bool DefManager::removeModBlockDef(int id)
{
	return BlockDefCsv::getInstance()->removeModBlockDef(id);
}

int DefManager::getItemNum()
{	
	return ItemDefCsv::getInstance()->getNum();
}

int DefManager::getToolNum()
{
	return ToolDefCsv::getInstance()->getNum();
}

ItemDef* DefManager::getAutoUseForeignID(int id)
{
	return ItemDefCsv::getInstance()->getAutoUseForeignID(id);
}

int DefManager::GetPlayerBuffAtkType(int buff)
{
	return PlayerAttribCsv::getInstance()->GetPlayerBuffAtkType(buff);;
}

Rainbow::SharePtr<Rainbow::Texture2D> DefManager::getItemTexByItemId(int itemid)
{
	ItemDef* itemDef = getItemDef(itemid);
	if (itemDef)
	{
		bool isGetItemBymode = false;
		int modetype = -1;

		if (itemDef->Icon == "customblock" || itemDef->Icon == "fullycustomblock" || itemDef->Icon == "importcustomblock")
		{
			BlockDef* bdef = g_ModEditorMgr.getBlockDefById(itemid);
			if (!bdef)
			{
				bdef = BlockDefCsv::getInstance()->get(itemid);
			}
			if (bdef)
			{
				if (itemDef->Icon == "fullycustomblock"|| itemDef->Icon == "importcustomblock")
				{
					std::map<std::string, int> strToEnumMap = { {"fullycustomblock",FULLY_BLOCK_MODEL},{"importcustomblock",IMPORT_BLOCK_MODEL} };
					auto iter = strToEnumMap.find(itemDef->Icon.c_str());
					if (iter != strToEnumMap.end())
					{
						modetype = iter->second;
						isGetItemBymode = true;
					}
				}
				else
				{
				}

			}
		}
		else
		{
			if (itemDef->Icon == "customitem" || itemDef->Icon == "fullycustomitem" || itemDef->Icon == "fullycustompacking"
				|| itemDef->Icon == "customegg" || itemDef->Icon == "fullycustomegg" || itemDef->Icon == "vehicleitem"
				|| itemDef->Icon == "importcustommodel")
			{
				std::map<std::string, int> strToEnumMap = { {"customitem",WEAPON_MODEL},{"fullycustomitem",FULLY_ITEM_MODEL}, {"fullycustompacking",FULLY_PACKING_CUSTOM_MODEL},
															{"customegg",ACTOR_MODEL},{"fullycustomegg",FULLY_ACTOR_MODEL},{"vehicleitem",VEHICLE_MODEL}, {"importcustommodel",IMPORT_ACTOR_MODEL} };
				auto iter = strToEnumMap.find(itemDef->Icon.c_str());
				if (iter != strToEnumMap.end())
				{
					isGetItemBymode = true;
					modetype = iter->second;
				}
				
			}
		}
		if (isGetItemBymode)
		{
			int u, v, width, height, r, g, b;
			width = 0;
			Rainbow::SharePtr<Rainbow::Texture2D> tex;
			if (modetype >= IMPORT_BLOCK_MODEL && modetype <= IMPORT_MODEL_MAX)
			{
				tex = ImportCustomModelMgr::GetInstancePtr()->getModelIcon(itemDef->Model.c_str(), u, v, width, height, r, g, b);
			}
			else if (modetype >= FULLY_BLOCK_MODEL || modetype == -1)
			{
				tex = FullyCustomModelMgr::GetInstancePtr()->getModelIcon(itemDef->Model.c_str(), modetype, u, v, width, height, r, g, b);
			}

			if (modetype < FULLY_BLOCK_MODEL && width == 0)
			{
				tex = CustomModelMgr::GetInstancePtr()->getModelIcon(itemDef->Model.c_str(), modetype, u, v, width, height, r, g, b);
			}

			return tex;
		}
		else
		{
			Rainbow::RectInt rect;
			Rainbow::ColorRGBA32 color;
			return ItemIconManager::GetInstance().getIconTexture(itemid, rect, color);
		}
		
	}
	

	return NULL;
}


bool DefManager::checkItemCrc(int itemid)
{
	return ItemDefCsv::getInstance()->checkItemCrc(itemid);
}

void DefManager::removeCustom(int id, int type, bool needresetcrc, int involvedid)
{
	ItemDefCsv::getInstance()->removeCustom(id, type, needresetcrc, involvedid);
}

void DefManager::clearAllCustomItem()
{
	if (ItemDefCsv::getInstance())
		ItemDefCsv::getInstance()->clearAllCustomDef();
}

bool DefManager::getItemsByGroup(int id, std::vector<int>& out)
{
	return ItemDefCsv::getInstance()->getItemsByGroup(id, out);
}

PackGiftDef* DefManager::getPackGiftDef(int iPackID)
{
	return g_ModMgr.tryGetPackGiftDef(iPackID);
}

PackGiftDef* DefManager::getPackGiftDefByItemID(int iItemID)
{
	ItemDef* def = ItemDefCsv::getInstance()->getAutoUseForeignID(iItemID);
	if (!def) { return NULL; }

	return getPackGiftDef(def->ID);
}

int DefManager::getFurnaceDefNum()
{	
	loadFurnaceCSV();

	return (int)m_Furnaces.size();
}

const FurnaceDef *DefManager::getFurnaceDef(int id)
{	
	loadFurnaceCSV();

	FurnaceDef* def = g_ModMgr.tryGetFurnaceDef(id);
	if (def != nullptr)
	{
		return def;
	}

	return m_FurnaceTable.GetRecord(id);
}

const FurnaceDef *DefManager::getFurnaceDefByIndex(int index)
{	
	loadFurnaceCSV();

	assert(index >= 0 && index < getFurnaceDefNum());
	return m_Furnaces[index];
}

const FurnaceDef *DefManager::getFurnaceDefByMaterialID(int materialid, bool takeplace /* = false */)
{	
	loadFurnaceCSV();

	if (materialid <= 0) return nullptr;

	for (auto it = g_ModMgr.m_CurrentFurnaceDefs.m_Records.begin(); it != g_ModMgr.m_CurrentFurnaceDefs.m_Records.end(); it++)
	{
		if (materialid == it->second.MaterialID) return &it->second;
	}

	for (auto iter = m_Furnaces.begin(); iter != m_Furnaces.end(); iter++)
	{
		if ((*iter)->MaterialID == materialid) return *iter;
	}

	if (takeplace)
	{
		return m_FurnaceTable.GetRecord(FURNACE_DEFAULT);
	}
	return nullptr;
}

const FurnaceDef * DefManager::getFurnaceDefByMaterialIDWithType(int materialid, bool takeplace, int type)
{
	loadFurnaceCSV();

	if (materialid <= 0) return nullptr;

	for (auto it = g_ModMgr.m_CurrentFurnaceDefs.m_Records.begin(); it != g_ModMgr.m_CurrentFurnaceDefs.m_Records.end(); it++)
	{
		if (materialid == it->second.MaterialID && type == it->second.Type) return &it->second;
	}

	for (auto iter = m_Furnaces.begin(); iter != m_Furnaces.end(); iter++)
	{
		if ((*iter)->MaterialID == materialid && type == (*iter)->Type) return *iter;
	}

	if (takeplace)
	{
		return m_FurnaceTable.GetRecord(FURNACE_DEFAULT);
	}
	return nullptr;
}

const FurnaceDef* DefManager::getFurnaceDefByResult(int resultid)
{
	loadFurnaceCSV();

	if (resultid <= 0) return nullptr;

	for (auto it = g_ModMgr.m_CurrentFurnaceDefs.m_Records.begin(); it != g_ModMgr.m_CurrentFurnaceDefs.m_Records.end(); it++)
	{
		if (resultid == it->second.Result) return &it->second;
	}

	for (auto iter = m_Furnaces.begin(); iter != m_Furnaces.end(); iter++)
	{
		if ((*iter)->Result == resultid) return *iter;
	}
	return nullptr;
}

const ToolDef *DefManager::getToolDef(int id)
{
	return ToolDefCsv::getInstance()->get(id);
}

ToolDef* DefManager::addToolDefByCopy(int id, int copyId)
{
	return ToolDefCsv::getInstance()->addByCopy(id, copyId);
}

ToolDef* DefManager::getOriginalToolDef(int id)
{
	return ToolDefCsv::getInstance()->getOriginal(id);
}

DefDataTable<ToolDef>& DefManager::getToolTable()
{
	return ToolDefCsv::getInstance()->m_ToolTable;
}

std::map<int, int>& DefManager::getMineToolIcon()
{
	return ToolDefCsv::getInstance()->getMineToolIcon();
}

int DefManager::getMonsterDefNum()
{
	return MonsterCsv::getInstance()->getNum();
}

int DefManager::getTriggerActDefNum()
{	
	loadTriggerActDef();

	return (int)m_TriggerActDefMap.size();
}

MonsterDef *DefManager::getMonsterDef(int id, bool takeplace/* =false */, bool bUseOne/* = false*/)
{	
	return MonsterCsv::getInstance()->get(id, takeplace, bUseOne);
}

MonsterDef* DefManager::addMonsterDef(int id, int type, const std::string& model /* = "" */, const std::string& name /* = "" */)
{
	return MonsterCsv::getInstance()->add(id, model, name, type);
}

MonsterDef* DefManager::addMonsterDefByCopy(int id, int copyId, int type, const std::string& model /* = "" */, const std::string& name /* = "" */)
{
	return MonsterCsv::getInstance()->addByCopy(id, model, name, type, copyId);
}

const MonsterDef *DefManager::getMonsterDefByIndex(int index)
{	
	return MonsterCsv::getInstance()->getByIndex(index);
}

DefDataTable<MonsterDef>& DefManager::getMonsters()
{
	return MonsterCsv::getInstance()->getMonsters();
}

MonsterDef* DefManager::getOriginalMonsterDef(int id)
{
	return MonsterCsv::getInstance()->getOriginal(id);
}

MonsterDef* DefManager::getIgnoreEditPlugin(int id, bool takeplace/* = false*/)
{
	return MonsterCsv::getInstance()->getIgnoreEditPlugin(id, takeplace);
}

ProjectileDef* DefManager::addProjectileDefByCopy(int id, int copyId)
{
	return ProjectileDefCsv::getInstance()->addByCopy(id, copyId);
}

ProjectileDef* DefManager::getProjectileDef(int id, bool takeplace)
{
	return ProjectileDefCsv::getInstance()->get(id, takeplace);
}

ProjectileDef* DefManager::getOriginalProjectileDef(int id)
{
	return ProjectileDefCsv::getInstance()->getOriginal(id);
}

MonsterSpawnDef* DefManager::getMonsterSpawnDef(int type)
{
	return m_MonsterSpawnTable.GetRecord(type);
}

DefDataTable<HorseEggDef> &DefManager::getHorseEggTable()
{
	return HorseEggCsv::getInstance()->getHorseEggTable();
}

DefDataTable<HorseDef> DefManager::getHorses()
{
	loadHorseCSV();

	return m_Horses;
}

const HorseDef *DefManager::getHorseDef(int id)
{
	loadHorseCSV();

	HorseDef *horseDef = g_ModMgr.tryGetHorseDef(id);
	if (horseDef != nullptr)
	{
		return horseDef;
	}

	return m_Horses.GetRecord(id);
}

const char *DefManager::getStringDef(int id)
{	
	return StringDefCsv::getInstance()->get(id);
}

RoleDef *DefManager::getRoleDef(char model, int geniuslv)
{	
	loadRoleDef();

	auto iter = m_RoleTable.m_Records.begin();
	for(;iter!=m_RoleTable.m_Records.end(); iter++)
	{
		if(model == iter->second.Model && geniuslv == iter->second.GeniusLv) return &iter->second;
	}
	return NULL;
}

RoleDef* DefManager::getRoleDef(int roleID)
{
	loadRoleDef();

	std::unordered_map<int, RoleDef>::iterator iter = m_RoleTable.m_Records.begin();
	for (; iter != m_RoleTable.m_Records.end(); iter++)
	{
		if (roleID == iter->second.ID) return &iter->second;
	}
	return NULL;
}

int DefManager::getRoleDefNum()
{	
	loadRoleDef();

	return m_RoleTable.GetRecordSize();
}

RoleSkinDef *DefManager::getRoleSkinDef(int id)
{	
	return RoleSkinCsv::getInstance()->get(id);
}

RoleSkinDef* DefManager::GetRoleSkinByIndex(int index)
{
	return RoleSkinCsv::getInstance()->getByIndex(index);
}

RoleSkinDef* DefManager::GetRoleSkinByName(const std::string& name)
{
	return RoleSkinCsv::getInstance()->findByName(name);
}

int DefManager::GetRoleSkinCount()
{
	return RoleSkinCsv::getInstance()->getNum();
}

AvatarModelDef *DefManager::getAvatarSkinDef(char uin[MAX_BLOCK_NAME], int modelId)
{
	for(size_t i=0; i<m_AvatarSkinArray.size(); i++)
	{
		if(m_AvatarSkinArray[i].ModelID == modelId && strcmp(m_AvatarSkinArray[i].UIN, uin) == 0) return &m_AvatarSkinArray[i];
	}
	return NULL;
}

AvatarModelDef *DefManager::getAvatarSkinByIndex(int index)
{
	assert(index>=0 && index<getAvatarSkinNum());
	return &m_AvatarSkinArray[index];
}

int DefManager::getAvatarSkinNum()
{
	return int(m_AvatarSkinArray.size());
}

const StorePropDef *DefManager::getStorePropByIndex(int i)
{	
	loadStorePropDef();

	assert(i>=0 && i<getStorePropNum());
	return &m_StorePropArray[i];
}

StorePropDef *DefManager::getStorePropByID(int id)
{	
	loadStorePropDef();

	for(size_t i=0; i<m_StorePropArray.size(); i++)
	{
		if(m_StorePropArray[i].ProductID == id) return &m_StorePropArray[i];
	}
	return NULL;
}

int DefManager::getStorePropNum()
{
	loadStorePropDef();

	return int(m_StorePropArray.size());
}

//const HomeTraderDef *DefManager::getHomeTraderByIndex(int i)
//{
//	assert(i >= 0 && i < getHomeTraderNum());
//	return m_HomeTraderArray[i];
//}
//
//HomeTraderDef * DefManager::getHomeTraderByItemID(int id)
//{
//	for (size_t i = 0; i < m_HomeTraderArray.size(); i++)
//	{
//		if (m_HomeTraderArray[i]->ItemID == id)
//		{
//			return m_HomeTraderArray[i];
//		}
//	}
//	return NULL;
//}
//
//int DefManager::getHomeTraderNum()
//{
//	return int(m_HomeTraderArray.size());
//}

//20210913：修复迷你币配置表删除部分档位其他档位无法显示的BUG  codeby：范伊蒙
MiniCoinDef *DefManager::getMiniCoinDefByIndex(int index)
{	
	loadMiniCoinDef();
	assert(index >= 0 && index <= getMiniCoinNum());
	return m_MiniCoinTable.GetRecordByIndex(index);
}

MiniCoinDef *DefManager::getMiniCoinDef(int id)
{	
	loadMiniCoinDef();

	return m_MiniCoinTable.GetRecord(id);
}

int DefManager::getMiniCoinNum()
{	
	loadMiniCoinDef();

	return m_MiniCoinTable.GetRecordSize();
}

StoreHorseDef *DefManager::getStoreHorseByIndex(int i)
{
	loadStoreHorseDef();

	assert(i>=0 && i<getStoreHorseNum());
	return &m_StoreHorseArray[i];
}

StoreHorseDef *DefManager::getStoreHorseByID(int id)
{	
	loadStoreHorseDef();

	for(size_t i=0; i<m_StoreHorseArray.size(); i++)
	{
		if(m_StoreHorseArray[i].BaseHorseID+m_StoreHorseArray[i].Level == id) return &m_StoreHorseArray[i];
	}
	return NULL;
}

int DefManager::getStoreHorseNum()
{	
	loadStoreHorseDef();

	return int(m_StoreHorseArray.size());
}

bool DefManager::isStoreHorseById(int id)
{
	loadStoreHorseDef();
	if (m_StoreHorseIdToHave.find(id) == m_StoreHorseIdToHave.end())
	{
		return false;
	}
	return m_StoreHorseIdToHave[id];
}

const HorseAbilityDef *DefManager::getHorseAbilityDef(int id)
{	
	loadHorseAblityDef();

	return m_HorseAbilityTable.GetRecord(id);
}

DefDataTable<ChestDef> &DefManager::getChestDefTable()
{
	loadChestDef();

	return m_ChestDefTable;
}

const ChestDef *DefManager::getChestDef(int id)
{
	loadChestDef();

	return m_ChestDefTable.GetRecord(id);
}

DefDataTable<ChestSpawnDef>& DefManager::getChestSpawnTable()
{
	loadChestSpawnDef();

	return m_ChestSpawnTable;
}

DefDataTable<SkinningToolDef>& DefManager::getSkinningToolTable()
{
	loadSkinningToolDef();

	return m_SkinningToolTable;
}

bool DefManager::loadSkinningToolDef()
{
	static NoFreeFixedString filename = "skinning_tool";
	if (hasLoaded(filename))
	{
		return true;
	}

	m_SkinningToolTable.clear();

	auto* skinningToolCsv = SkinningToolCsv::getInstance();
	int numDefs = skinningToolCsv->getSkinningToolDefNum();
	
	for (int i = 0; i < numDefs; i++) {
		const SkinningToolDef* def = skinningToolCsv->getSkinningToolDefByIndex(i);
		if (!def) continue;

		SkinningToolDef tmpDef = *def;
		m_SkinningToolTable.AddRecord(def->ToolID, tmpDef);
	}
	return true;
}

bool DefManager::loadChestSpawnDef()
{
    static NoFreeFixedString filename = "chest_spawn";
    if (hasLoaded(filename))
    {
        return true;
    }

    m_ChestSpawnTable.clear();
    
    // 从ChestSpawnCsv获取数据
    auto* chestSpawnCsv = ChestSpawnCsv::getInstance();
    int numDefs = chestSpawnCsv->getChestSpawnDefNum();
    
    for (int i = 0; i < numDefs; i++) {
        const ChestSpawnDef* def = chestSpawnCsv->getChestSpawnDefByIndex(i);
        if (!def) continue;
        
        // 使用id作为键存储到DefDataTable中
		ChestSpawnDef tmpDef = *def;
        m_ChestSpawnTable.AddRecord(def->id, tmpDef);
    }

    return true;
}

const NpcTradeDef *DefManager::getNpcTradeDef(int id)
{
	loadNpcTradeDef();

	return m_NpcTradeTable.GetRecord(id);
}

int DefManager::getNpcTradeNum()
{	
	loadNpcTradeDef();

	return m_NpcTradeTable.GetRecordSize();
}

bool DefManager::isNpcTrade(int monsterid)
{	
	loadNpcTradeDef();

	for (int i = 0, _num = g_DefMgr.getNpcTradeNum(); i < _num; ++i)
	{
		const NpcTradeDef *def = g_DefMgr.getNpcTradeDef(i + 1);
		if (!def)
			continue;

		if (def->NpcID == monsterid)
			return true;
	}

	return false;
}

SignInDef *DefManager::getSignInDef(int signintype, int whatday)
{	
	loadSignInDef();

	int id = signintype*100+whatday;
	return m_SignInTable.GetRecord(id);
}

const ExtremityScoreDef *DefManager::getExtremityScoreDef(int Type, int GoalID/* =0 */)
{
	loadExtremityScoreDef();

	int id = Type*10000+GoalID;
	return m_ExtremityScoreTable.GetRecord(id);
}

const HeadIconDef *DefManager::getHeadIconDef(int id)
{	
	loadHeadIconDef();

	return m_HeadIconTable.GetRecord(id);
}

int DefManager::getHeadIconNum()
{	
	loadHeadIconDef();

	return m_HeadIconTable.GetRecordSize();
}

PlantDef *DefManager::getPlantDef(int level)
{	
	loadPlantDef();

	return m_PlantTable.GetRecord(level);
}

FruitDef *DefManager::getFruitDef(int id)
{	
	loadFruitDef();

	return m_FruitTable.GetRecord(id);
}

const BookDef *DefManager::getBookDefByID(int id)
{	
	loadBookDef();

	assert(id>=0 && id<getBookNum());
	return &m_BookArray[id];
}

const BookDef *DefManager::getBookDefByItemID(int id)
{	
	loadBookDef();

	for(size_t i=0; i<m_BookArray.size(); i++)
	{
		if(m_BookArray[i].ItemID == id) return &m_BookArray[i];
	}
	return NULL;
}

int DefManager::getBookNum()
{	
	loadBookDef();

	return (int)m_BookArray.size();
}

const BookSeriesDef *DefManager::getBookSeriesDef(int id)
{	
	loadBookSeriesDef();

	return m_BookSeriesTable.GetRecord(id);
}

const RecycleDef *DefManager::getRecycleDef(int id)
{
	loadRecycleDef();

	return m_RecycleTable.GetRecord(id);
}


DefDataTable<GameRuleDef>& DefManager::getGameRuleTable()
{
	loadGameRuleDef();

	return m_GameRuleTable;
}

int DefManager::getGameRuleNum()
{	
	loadGameRuleDef();

	return m_GameRuleTable.GetRecordSize();
}

const GameRuleDef *DefManager::getGameRuleDef(int id)
{	
	loadGameRuleDef();

	return m_GameRuleTable.GetRecord(id);
}

const RuleOptionDef *DefManager::getRuleOptionDef(int id)
{	
	//20211015 如果已经在m_RuleOptionTable，无需loadGameRuleDef codeby:liushuxin
	return RuleOptionCsv::getInstance()->getRuleOptionDef(id);
}

FuncSwitchDef *DefManager::getFuncSwitchDef(int id)
{
	loadFuncSwitchDef();

	return m_FuncSwitchTable.GetRecord(id);
}

TaskDef *DefManager::getTaskDef(int id)
{	
	loadTaskDef();

	return m_TaskDefTable.GetRecord(id);
}

PlotDef *DefManager::getPlotDef(int id)
{
	loadPlotDef();

	return m_PlotDefTable.GetRecord(id);
}

int DefManager::getHotkeyNum()
{	
	loadHotkeyDef();

	return m_HotkeyDefTable.GetRecordSize();
}

HotkeyDef *DefManager::getHotkeyDef(int id)
{	
	loadHotkeyDef();

	return m_HotkeyDefTable.GetRecord(id);
}

HotkeyDef *DefManager::getHotkeyDefByKey(const char *key)
{	
	loadHotkeyDef();

	auto iter = m_HotkeyDefTable.m_Records.begin();
	for(;iter!=m_HotkeyDefTable.m_Records.end(); iter++)
	{
		if(strcmp(iter->second.FuncName,key) == 0)
			return &iter->second;
	}

	return NULL;
}

const char *DefManager::getKeyName(int code)
{	
	loadKeyDef();

	const KeyDef *def = m_KeyDefTable.GetRecord(code);
	if(def)
	{
		return def->Name;
	}
	else return "";
}

const char *DefManager::getWildmanNameDef(int id)
{	
	loadWildmanNameDef();

	auto def = m_WildmanNameTable.GetRecord(id);
	if(def)
	{
		return def->str.c_str();
	}
	else return "";
}

int DefManager::getWildmanNameDefNum()
{
	loadWildmanNameDef();

	return m_WildmanNameTable.GetRecordSize();
}

bool DefManager::loadNpcPlotCSV()
{
    static NoFreeFixedString filename = "npcplotdef";
	//const char* filename = "npcplotdef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_NpcPlots.clear();
	m_NpcPlotConfigurables.clear();
	m_NpcPlotConfigurablesIds.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["PlotID"].Int();
		if (id == 0) continue;
		NpcPlotDefUseType useType = (NpcPlotDefUseType)parser[i]["IsTemplate"].Int();
		switch (useType)
		{
			case TEMPLATE_NPCPLPTUSETYPE:
			{
				NpcPlotDef def;
				parseNpcPlotTemplateDate(def, parser[i]);
				m_NpcPlots.AddRecord(def.ID, def);
				break;
			}
			case DIRECT_NPCPLPTUSETYPE:
			{
				NpcPlotDef def;
				parseNpcPlotTemplateDate(def, parser[i]);
				m_NpcPlotConfigurables.AddRecord(def.ID, def);
				m_NpcPlotConfigurablesIds.push_back(def.ID);
				break;
			}
			default:
				// 表格中存在没填IsTemplate字段，默认同TEMPLATE_NPCPLPTUSETYPE
				NpcPlotDef def;
				parseNpcPlotTemplateDate(def, parser[i]);
				m_NpcPlots.AddRecord(def.ID, def);
				break;
		}
		
	}

	return true;
}

void DefManager::parseNpcPlotTemplateDate(NpcPlotDef &def, const MINIW::CSVParser::TableLine parserLine)
{
	def.ID = parserLine["PlotID"].Int();
	def.CopyID = 0;
	def.Name = parserLine["PlotName"].Str();
	def.Icon = parserLine["PlotIcon"].Str();
	def.InteractID = parserLine["InteractID"].Int();
	def.EditType = parserLine["EditType"].Int();
	def.IsTemplate = parserLine["IsTemplate"].Int() == 1;
	def.UseType = (NpcPlotDefUseType)parserLine["IsTemplate"].Int();
	def.InteractType = parserLine["InteractType"].Int();
	def.ExtraType = parserLine["ExtraType"].Int();

	jsonxx::Value jsonConditions;
	if (jsonConditions.parse((char*)parserLine["Condition"].Str()))
	{
		const jsonxx::Array& array = jsonConditions.get<jsonxx::Array>();
		for (int j = 0; j < (int)array.size(); j++)
		{
			NpcPlotDef::ConditionDef conditionDef;
			const jsonxx::Object& obj = array.get<jsonxx::Object>(j);

			conditionDef.Type = (int)obj.get<jsonxx::Number>("Type");
			if (obj.has<jsonxx::Number>("TaskID1"))
			{
				conditionDef.TaskIDs.push_back((int)obj.get<jsonxx::Number>("TaskID1"));
			}
			if (obj.has<jsonxx::Number>("TaskID2"))
			{
				conditionDef.TaskIDs.push_back((int)obj.get<jsonxx::Number>("TaskID2"));
			}
			if (obj.has<jsonxx::Number>("TaskID3"))
			{
				conditionDef.TaskIDs.push_back((int)obj.get<jsonxx::Number>("TaskID3"));
			}

			if (obj.has<jsonxx::Number>("StartTime"))
			{
				conditionDef.StartTime = (int)obj.get<jsonxx::Number>("StartTime");
			}
			else
				conditionDef.StartTime = -1;

			if (obj.has<jsonxx::Number>("EndTime"))
			{
				conditionDef.EndTime = (int)obj.get<jsonxx::Number>("EndTime");
			}
			else
				conditionDef.EndTime = -1;

			if (obj.has<jsonxx::Number>("ItemID"))
			{
				conditionDef.ItemID = (int)obj.get<jsonxx::Number>("ItemID");
			}
			else
				conditionDef.ItemID = -1;

			if (obj.has<jsonxx::Number>("Num"))
			{
				conditionDef.ItemNum = (int)obj.get<jsonxx::Number>("Num");
			}
			else
				conditionDef.ItemNum = 1;

			def.Conditions.push_back(conditionDef);
		}
	}

	jsonxx::Value jsonDialogues;
	if (jsonDialogues.parse((char*)parserLine["Content"].Str()))
	{
		const jsonxx::Array& array = jsonDialogues.get<jsonxx::Array>();
		for (int j = 0; j < (int)array.size(); j++)
		{
			DialogueDef dialogueDef;
			const jsonxx::Object& obj = array.get<jsonxx::Object>(j);
			dialogueDef.ID = (int)obj.get<jsonxx::Number>("ID");
			dialogueDef.Text = obj.get<jsonxx::String>("Text");

			if (obj.has<jsonxx::Number>("Action"))
				dialogueDef.Action = (int)obj.get<jsonxx::Number>("Action");
			else
				dialogueDef.Action = 0;

			if (obj.has<jsonxx::String>("Sound"))
				dialogueDef.Sound = obj.get<jsonxx::String>("Sound");
			else
				dialogueDef.Sound = "";

			if (obj.has<jsonxx::String>("Effect"))
				dialogueDef.Effect = obj.get<jsonxx::String>("Effect");
			else
				dialogueDef.Effect = "";

			if (obj.has<jsonxx::Array>("Answer"))
			{
				const jsonxx::Array& answersArray = obj.get<jsonxx::Array>("Answer");
				for (int k = 0; k < (int)answersArray.size(); k++)
				{
					AnswerDef answerDef;
					const jsonxx::Object& answerbj = answersArray.get<jsonxx::Object>(k);
					answerDef.Text = answerbj.get<jsonxx::String>("Text");
					answerDef.FuncType = (int)answerbj.get<jsonxx::Number>("FuncType");
					if (answerbj.has<jsonxx::Number>("Val"))
					{
						answerDef.Val = (int)answerbj.get<jsonxx::Number>("Val");
					}
					else
					{
						answerDef.Val = 0;
					}
					if (answerbj.has<jsonxx::String>("ScriptName"))
					{
						answerDef.ScriptName = answerbj.get<jsonxx::String>("ScriptName");
					}
					else
					{
						answerDef.ScriptName = "";
					}
					
					dialogueDef.Answers.push_back(answerDef);
				}
			}

			def.Dialogues.push_back(dialogueDef);
		}
	}

	def.CopyID = 0;
}

bool DefManager::loadNpcTaskCSV()
{
    static NoFreeFixedString filename = "npctaskdef";
	//const char* filename = "npctaskdef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_NpcTasks.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["TaskID"].Int();
		if (id == 0) continue;

		NpcTaskDef def;
		def.ID = id;
		def.Name = parser[i]["TaskName"].Str();
		def.IsDeliver = parser[i]["IsDeliver"].Int() > 0;
		def.ShowInNote = parser[i]["ShowInNote"].Int() > 0;
		def.InteractID = parser[i]["InteractID"].Int();
		def.IsRepeat = parser[i]["IsRepeat"].Int() > 0;
		def.UseInteract = parser[i]["UseInteract"].Int() > 0;

		//任务内容json
		jsonxx::Value jsonTaskContents;
		if (jsonTaskContents.parse((char*)parser[i]["TaskContent"].Str()))
		{
			jsonxx::Array array = jsonTaskContents.get<jsonxx::Array>();
			for (int j = 0; j < (int)array.size(); j++)
			{
				NpcTaskDef::TaskContentDef taskContentDef;
				taskContentDef.Type = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Type");
				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("ID"))
				{
					taskContentDef.ID = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("ID");
				}
				else
					taskContentDef.ID = -1;

				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("Num"))
				{
					taskContentDef.Num = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Num");
				}
				else
					taskContentDef.Num = -1;
				

				def.TaskContents.push_back(taskContentDef);
			}
		}

		//任务奖励json
		jsonxx::Value jsonTaskRewards;
		if (jsonTaskRewards.parse((char*)parser[i]["TaskReward"].Str()))
		{
			jsonxx::Array array = jsonTaskRewards.get<jsonxx::Array>();
			for (int j = 0; j < (int)array.size(); j++)
			{
				NpcTaskDef::TaskRewardDef taskRewardDef;
				taskRewardDef.Type = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Type");
				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("ItemId"))
				{
					taskRewardDef.ID = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("ItemId");
				}
				else
					taskRewardDef.ID = 0;

				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("Num"))
				{
					taskRewardDef.Num = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Num");
				}
				else
					taskRewardDef.Num = 0;


				def.TaskRewards.push_back(taskRewardDef);
			}
		}

		//接任务剧情对话json
		jsonxx::Value jsonPlots;
		if (jsonPlots.parse((char*)parser[i]["PlotDialogue"].Str()))
		{
			jsonxx::Array array = jsonPlots.get<jsonxx::Array>();
			for (int j = 0; j < (int)array.size(); j++)
			{
				DialogueDef dialogueDef;
				dialogueDef.ID = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("ID");
				dialogueDef.Text = array.get<jsonxx::Object>(j).get<jsonxx::String>("Text");

				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("Action"))
					dialogueDef.Action = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Action");
				else
					dialogueDef.Action = 0;

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Sound"))
					dialogueDef.Sound = array.get<jsonxx::Object>(j).get<jsonxx::String>("Sound");
				else
					dialogueDef.Sound = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Effect"))
					dialogueDef.Effect = array.get<jsonxx::Object>(j).get<jsonxx::String>("Effect");
				else
					dialogueDef.Effect = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::Array>("Answer"))
				{
					jsonxx::Array answersArray = array.get<jsonxx::Object>(j).get<jsonxx::Array>("Answer");
					for (int k = 0; k < (int)answersArray.size(); k++)
					{
						AnswerDef answerDef;
						answerDef.Text = answersArray.get<jsonxx::Object>(k).get<jsonxx::String>("Text");
						answerDef.FuncType = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("FuncType");
						if (answersArray.get<jsonxx::Object>(k).has<jsonxx::Number>("Val"))
						{
							answerDef.Val = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("Val");
						}
						else
						{
							answerDef.Val = 0;
						}

						dialogueDef.Answers.push_back(answerDef);
					}
				}

				def.Plots.push_back(dialogueDef);
			}
		}

		//未完成任务对话json
		jsonxx::Value jsonUnCompleteds;
		if (jsonUnCompleteds.parse((char*)parser[i]["UnCompletedDialogue"].Str()))
		{
			jsonxx::Array array = jsonUnCompleteds.get<jsonxx::Array>();
			for (int j = 0; j < (int)array.size(); j++)
			{
				DialogueDef dialogueDef;
				dialogueDef.ID = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("ID");
				dialogueDef.Text = array.get<jsonxx::Object>(j).get<jsonxx::String>("Text");

				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("Action"))
					dialogueDef.Action = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Action");
				else
					dialogueDef.Action = 0;

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Sound"))
					dialogueDef.Sound = array.get<jsonxx::Object>(j).get<jsonxx::String>("Sound");
				else
					dialogueDef.Sound = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Effect"))
					dialogueDef.Effect = array.get<jsonxx::Object>(j).get<jsonxx::String>("Effect");
				else
					dialogueDef.Effect = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::Array>("Answer"))
				{
					jsonxx::Array answersArray = array.get<jsonxx::Object>(j).get<jsonxx::Array>("Answer");
					for (int k = 0; k < (int)answersArray.size(); k++)
					{
						AnswerDef answerDef;
						answerDef.Text = answersArray.get<jsonxx::Object>(k).get<jsonxx::String>("Text");
						answerDef.FuncType = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("FuncType");
						if (answersArray.get<jsonxx::Object>(k).has<jsonxx::Number>("Val"))
						{
							answerDef.Val = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("Val");
						}
						else
						{
							answerDef.Val = 0;
						}

						dialogueDef.Answers.push_back(answerDef);
					}
				}

				def.UnCompleteds.push_back(dialogueDef);
			}
		}

		//完成任务剧情对话json
		jsonxx::Value jsonCompleteds;
		if (jsonCompleteds.parse((char*)parser[i]["CompletedDialogue"].Str()))
		{
			jsonxx::Array array = jsonCompleteds.get<jsonxx::Array>();
			for (int j = 0; j < (int)array.size(); j++)
			{
				DialogueDef dialogueDef;
				dialogueDef.ID = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("ID");
				dialogueDef.Text = array.get<jsonxx::Object>(j).get<jsonxx::String>("Text");

				if (array.get<jsonxx::Object>(j).has<jsonxx::Number>("Action"))
					dialogueDef.Action = (int)array.get<jsonxx::Object>(j).get<jsonxx::Number>("Action");
				else
					dialogueDef.Action = 0;

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Sound"))
					dialogueDef.Sound = array.get<jsonxx::Object>(j).get<jsonxx::String>("Sound");
				else
					dialogueDef.Sound = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::String>("Effect"))
					dialogueDef.Effect = array.get<jsonxx::Object>(j).get<jsonxx::String>("Effect");
				else
					dialogueDef.Effect = "";

				if (array.get<jsonxx::Object>(j).has<jsonxx::Array>("Answer"))
				{
					jsonxx::Array answersArray = array.get<jsonxx::Object>(j).get<jsonxx::Array>("Answer");
					for (int k = 0; k < (int)answersArray.size(); k++)
					{
						AnswerDef answerDef;
						answerDef.Text = answersArray.get<jsonxx::Object>(k).get<jsonxx::String>("Text");
						answerDef.FuncType = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("FuncType");
						if (answersArray.get<jsonxx::Object>(k).has<jsonxx::Number>("Val"))
						{
							answerDef.Val = (int)answersArray.get<jsonxx::Object>(k).get<jsonxx::Number>("Val");
						}
						else
						{
							answerDef.Val = 0;
						}

						dialogueDef.Answers.push_back(answerDef);
					}
				}

				def.Completeds.push_back(dialogueDef);
			}
		}

		def.CopyID = 0;
		m_NpcTasks.AddRecord(def.ID, def);
	}

	return true;
}

std::vector<int> &DefManager::getNpcPlotIds()
{
	loadNpcPlotCSV();

	return m_NpcPlotIds;
}

int DefManager::getNpcPlotDefNum()
{	
	loadNpcPlotCSV();

	m_NpcPlotIds.clear();
	std::set<int> plotIds;
	m_NpcPlots.GetIDsAll(plotIds);
	//TODO
	//插件库的id;
	g_ModMgr.getNpcPlotIDsAll(plotIds);

	for (set<int>::iterator iter = plotIds.begin(); iter != plotIds.end(); ++iter)
	{
		m_NpcPlotIds.push_back(*iter);
	}

	return m_NpcPlotIds.size();
}

NpcPlotDef *DefManager::getNpcPlotDef(int id)
{	
	loadNpcPlotCSV();

	NpcPlotDef* def = g_ModMgr.tryGetNpcPlotDef(id);
	if (def != nullptr)
	{
		return def;
	}

	return m_NpcPlots.GetRecord(id);
}

NpcPlotDef *DefManager::getNpcPlotDefByIndex(int index)
{	
	loadNpcPlotCSV();

	return getNpcPlotDef(m_NpcPlotIds[index]);
}

int DefManager::getNpcPlotConfigurableDefNum()
{
	loadNpcPlotCSV();

	return m_NpcPlotConfigurablesIds.size();
}

NpcPlotDef *DefManager::getNpcPlotConfigurableDefByIndex(int index)
{
	loadNpcPlotCSV();
	if (index >= (int)m_NpcPlotConfigurablesIds.size()) return NULL;

	return getNpcPlotConfigurableDefByID(m_NpcPlotConfigurablesIds[index]);
}

NpcPlotDef *DefManager::getNpcPlotConfigurableDefByID(int id)
{
	loadNpcPlotCSV();
	return m_NpcPlotConfigurables.GetRecord(id);
}

std::vector<int> &DefManager::getNpcTaskIds()
{
	loadNpcTaskCSV();

	return m_NpcTaskIds;
}

int DefManager::getNpcTaskDefNum()
{	
	loadNpcTaskCSV();

	m_NpcTaskIds.clear();
	std::set<int> taskIds;
	m_NpcTasks.GetIDsAll(taskIds);
	//TODO
	//插件库的id;
	g_ModMgr.getNpcTaskIDsAll(taskIds);

	for (set<int>::iterator iter = taskIds.begin(); iter != taskIds.end(); ++iter)
	{
		m_NpcTaskIds.push_back(*iter);
	}

	return m_NpcTaskIds.size();
}

NpcTaskDef *DefManager::getNpcTaskDef(int id)
{	
	loadNpcTaskCSV();

	NpcTaskDef* def = g_ModMgr.tryGetNpcTaskDef(id);
	if (def != nullptr)
	{
		return def;
	}

	return m_NpcTasks.GetRecord(id);
}

NpcTaskDef *DefManager::getNpcTaskDefByIndex(int index)
{	
	loadNpcTaskCSV();

	return getNpcTaskDef(m_NpcTaskIds[index]);
}

NpcTaskDef *DefManager::getNpcTaskDefByIndexAnyway(int index)
{	
	loadNpcTaskCSV();

	int id = m_NpcTaskIds[index];
	NpcTaskDef* def = g_ModMgr.tryGetNpcTaskDefAnyway(id);

	if (def != nullptr)
	{
		return def;
	}

	return m_NpcTasks.GetRecord(id);
}

std::vector<int> DefManager::getNpcShopIds()
{
	return m_NpcShopIds;
}

int DefManager::getNpcShopDefNum()
{	
	m_NpcShopIds.clear();
	std::set<int> shopIds;
	g_ModMgr.getNpcShopIDsAll(shopIds);
	for (set<int>::iterator iter = shopIds.begin(); iter != shopIds.end(); ++iter)
	{
		m_NpcShopIds.push_back(*iter);
	}

	return m_NpcShopIds.size();
}

NpcShopDef *DefManager::getNpcShopDef(int id)
{
	return g_ModMgr.tryGetNpcShopDef(id);
}

NpcShopDef* DefManager::getNpcShopDefByIndex(int index)
{
	if (index < 0 || index >= (int)m_NpcShopIds.size())
		return NULL;

	return getNpcShopDef(m_NpcShopIds[index]);
}

int DefManager::getNpcShopNpcInnerId(std::string key, unsigned int foreign_id, int default_value)
{
	return g_ModMgr.getActorIdInnerId(key, foreign_id, default_value);
}

int DefManager::getNpcShopNpcRealId(std::string key, int default_value)
{
	return g_ModMgr.getNpcShopRealId(key, default_value);
}

int DefManager::getDialogueNum(int type, int id, int taskstate/* =0 */)
{
	if (type == PLOT_INTERACT)
	{
		auto def = getNpcPlotDef(id);
		if (def)
			return (int)def->Dialogues.size();
	}
	else if (type == TASK_INTERACT)
	{
		auto def = getNpcTaskDef(id);
		if (def)
		{
			if (taskstate == 0)	//接任务后
				return (int)def->Plots.size();
			else if (taskstate == 1)	//任务未完成
				return (int)def->UnCompleteds.size();
			else if (taskstate == 2)	//任务已完成
				return (int)def->Completeds.size();
		}
	}
	else if(type == DIRECT_INTERACT)
	{
		auto def = getNpcPlotConfigurableDefByID(id);
		if (def)
			return (int)def->Dialogues.size();
	}
	return 0;
}

const DialogueDef *DefManager::getDialogueDef(int type, int id, int index, int taskstate/* =0 */)
{
	if (type == PLOT_INTERACT)
	{
		auto def = getNpcPlotDef(id);
		if (def)
			return &def->Dialogues[index];
	}
	else if (type == TASK_INTERACT)
	{
		auto def = getNpcTaskDef(id);
		if (def)
		{
			if (taskstate == 0)	//接任务后
				return &def->Plots[index];
			else if (taskstate == 1)	//任务未完成
				return &def->UnCompleteds[index];
			else if (taskstate == 2)	//任务已完成
				return &def->Completeds[index];
		}
	}
	else if (type == DIRECT_INTERACT)
	{
		auto def = getNpcPlotConfigurableDefByID(id);
		if (def)
			return &def->Dialogues[index];
	}
	return NULL;
}

const AnswerDef *DefManager::getAnswerDefByDialogue(const DialogueDef *def, int index)
{
	if (index >= 0 && index < (int)def->Answers.size())
	{
		return &def->Answers[index];
	}
	return NULL;
}

bool DefManager::loadPlayActDef()
{
    static NoFreeFixedString filename = "animact";
	//const char* filename = "animact";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		PlayActDef def;
		def.ID = parser[i]["ID"].Int();
		def.ActID = parser[i]["ActID"].Int();
		def.Name = parser[i]["Name"].Str();
		def.Effect = parser[i]["Effect"].Str();
		def.icon = parser[i]["icon"].Str();
		def.Face = parser[i]["FaceID"].Str();
		def.InGame = parser[i]["InGame"].Int();
		def.SkinType = parser[i]["SkinType"].Int();
		def.SkinID = parser[i]["SkinID"].Int();
		//20210909 codeby：chenwei 解析装扮互动字段
		def.SkinID2 = parser[i]["SkinID2"].Int();
		def.UnlockActSkinID = parser[i]["UnlockActSkinID"].Int();
		def.SortWeight = parser[i]["SortWeight"].Int();
		def.PosDirect = parser[i]["PosDirect"].Int();
		//20210927 codeyby:wangyu 装扮解析字段新增
		def.EffectSkinID = parser[i]["EffectSkinID"].Int();
		def.SkinID3 = parser[i]["SkinID3"].Int();
		def.SkinID4 = parser[i]["SkinID4"].Int();
		def.PosDirect2 = parser[i]["PosDirect2"].Int();
		def.SkinOffset = parser[i]["SkinOffset"].Int();
		def.Distance2 = parser[i]["Distance2"].Float();
		def.SkinScale = parser[i]["SkinScale"].Float();
		def.Distance = parser[i]["Distance"].Float();
		def.Desc = parser[i]["Desc"].Str();
		def.Sound = parser[i]["Sound"].Str();

		m_PlayAct.push_back(def);
	}

	return true;
}

const PlayActDef *DefManager::getPlayActDef(int id)
{
	loadPlayActDef();

	for (int i = 0; i<(int)m_PlayAct.size(); i++)
	{
		if (m_PlayAct[i].ID == id)
			return &m_PlayAct[i];
	}
	return NULL;
}

int DefManager::getPlayActDefNum()
{	
	loadPlayActDef();

	return m_PlayAct.size();
}

const PlayActDef *DefManager::getPlayActDefByIndex(int index)
{	
	loadPlayActDef();

	return &m_PlayAct[index];
}


bool DefManager::reloadPhysicsMaterialCSV()
{
	m_PhysicsMaterial.clear();
	if (!loadPhysicsMaterialCSV())
	{
		LOG_SEVERE("load PhysicsMaterial.csv failed");
		return false;
	}
	return true;
}

bool DefManager::loadPhysicsMaterialCSV()
{
    static NoFreeFixedString filename = "PhysicsMaterial";
	//const char* filename = "PhysicsMaterial";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		PhysicsMaterialDef def;

		def.MaterialID = parser[i]["MaterialID"].Int();
		def.DynamicFriction = parser[i]["DynamicFriction"].Float();
		def.StaticFriction = parser[i]["StaticFriction"].Float();
		def.Bouncyness = parser[i]["Bouncyness"].Float();
		def.Hardness = parser[i]["Hardness"].Float();
		def.DragScale = parser[i]["DragScale"].Int();
		def.Sound = parser[i]["Sound"].Str();
		def.Selectable = parser[i]["Selectable"].Int();
		def.IsTemplate = parser[i]["IsTemplate"].Int();
		def.NameStringID = parser[i]["NameStringID"].Int();
		def.Name = DefManager::getStringDef(def.NameStringID);
		def.CopyID = 0;

		m_PhysicsMaterial.push_back(def);
	}

	return true;
}

const PhysicsMaterialDef *DefManager::getPhysicsMaterialDef(int id)
{	
	loadPhysicsMaterialCSV();

	PhysicsMaterialDef* def = g_ModMgr.tryGetPhysicsMaterialDef(id);
	if (def != nullptr)
	{
		return def;
	}
	for (int i = 0; i<(int)m_PhysicsMaterial.size(); i++)
	{
		if (m_PhysicsMaterial[i].MaterialID == id)
			return &m_PhysicsMaterial[i];
	}
	return NULL;
}

int DefManager::getPhysicsMaterialDefNum()
{	
	loadPhysicsMaterialCSV();

	return m_PhysicsMaterial.size();
}

const PhysicsMaterialDef *DefManager::getPhysicsMaterialDefByIndex(int index)
{	
	loadPhysicsMaterialCSV();

	return &m_PhysicsMaterial[index];
}

bool DefManager::reloadPhysicsPartsCSV()
{
	m_PhysicsParts.clear();
	m_PhysicsPartsIds.clear();
	if (!loadPhysicsPartsCSV())
	{
		LOG_SEVERE("load PhysicsParts.csv failed");
		return false;
	}
	return true;
}

bool DefManager::loadPhysicsPartsCSV()
{
    static NoFreeFixedString filename = "PhysicsParts";
	//const char* filename = "PhysicsParts";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		PhysicsPartsDef def;

		def.PartsID = parser[i]["PartsID"].Int();
		def.IsCore = parser[i]["IsCore"].Int();
		def.Life = parser[i]["Life"].Int();
		def.PartsType = parser[i]["PartsType"].Int();
		def.SubType = parser[i]["SubType"].Int();
		def.IsCoreArmor = parser[i]["IsCoreArmor"].Int();
		def.AddPower = parser[i]["AddPower"].Int();
		def.UsePower = parser[i]["UsePower"].Int();
		def.UseType = parser[i]["UseType"].Int();
		def.IsEnergy = parser[i]["IsEnergy"].Int();
		def.BlockNum = parser[i]["BlockNum"].Int();
		def.BlockNum = def.BlockNum > 0 ? def.BlockNum : 1;
		def.PortTriggerType = parser[i]["PortTriggerType"].Int();
		def.CannotTrigger = parser[i]["CanTrigger"].Int();
		def.CostValue = parser[i]["CostValue"].Int();
		def.CostInterval = parser[i]["CostInterval"].Int();

		jsonxx::Value jsonFunctions;
		if (jsonFunctions.parse((char*)parser[i]["Functions"].Str()))
		{
			jsonxx::Array functionArray = jsonFunctions.get<jsonxx::Array>();
			for(int j = 0; j<(int)functionArray.size(); j++)
			{
				PhysicsPartsDef::EffectFunctionsDef functiondef;
				functiondef.func_id = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("FunctionID");
				AddPhysicsPartKeyDefFromFunc(def.Keys, functionArray.get<jsonxx::Object>(j));
				if(functiondef.func_id == 1)
				{
					functiondef.func.wheelfun.DriveSwitch =	(int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("DriveSwitch");
					functiondef.func.wheelfun.DriveDirection =	(int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("DriveDirection");
					functiondef.func.wheelfun.SteeringSwitch =	(int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SteeringSwitch");
					functiondef.func.wheelfun.SteeringDirection = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SteeringDirection");
					functiondef.func.wheelfun.SteeringRange = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SteeringRange");
					functiondef.func.wheelfun.SteeringRate = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SteeringRate");
					functiondef.func.wheelfun.RecoverMode = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("RecoverMode");
					//functiondef.func.wheelfun.LeftKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("LeftKey");
					//functiondef.func.wheelfun.RightKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("RightKey");
					functiondef.func.wheelfun.BrakeTorque = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("BrakeTorque");
					functiondef.func.wheelfun.Icon = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Icon");
					functiondef.func.wheelfun.MOI = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MOI");
					functiondef.func.wheelfun.DampingRate = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("DampingRate");
					functiondef.func.wheelfun.MaxCompression = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxCompression");
					functiondef.func.wheelfun.MaxDroop = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxDroop");
					functiondef.func.wheelfun.SpringStrength = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SpringStrength");
					functiondef.func.wheelfun.SpringDamperRate = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SpringDamperRate");
				}
				else if(functiondef.func_id == 2)
				{
					functiondef.func.enginefun.MaxTorque = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxTorque");
					functiondef.func.enginefun.MaxSpeed = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxSpeed");
					functiondef.func.enginefun.ForwardKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ForwardKey");
					functiondef.func.enginefun.BackwardKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("BackwardKey");
					functiondef.func.enginefun.Icon = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Icon");
				}
				else if(functiondef.func_id == 3)
				{
					functiondef.func.seatfun.SeatType = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SeatType");
					functiondef.func.seatfun.IsControl = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("IsControl");
					functiondef.func.seatfun.LeaveKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("LeaveKey");
					functiondef.func.seatfun.Icon = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Icon");
					if (functionArray.get<jsonxx::Object>(j).has<jsonxx::Number>("AddHeight"))
					{
						functiondef.func.seatfun.AddHeight = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("AddHeight");
					}
					else
					{
						functiondef.func.seatfun.AddHeight = 0;
					}
				}
				else if(functiondef.func_id == 4)
				{
					functiondef.func.resetfun.ResetKey = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ResetKey");
					functiondef.func.resetfun.Icon = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Icon");
				}
				else if(functiondef.func_id == 5)
				{
					functiondef.func.costfun.CostType = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("CostType");
					functiondef.func.costfun.CostVal = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("CostVal");
					functiondef.func.costfun.CostInterval = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("CostInterval");
					functiondef.func.costfun.MaxCost = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxCost");
					functiondef.func.costfun.UIDisplay = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("UIDisplay");
					functiondef.func.costfun.ButtonDisplay = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ButtonDisplay");
					functiondef.func.costfun.ModelDisplay = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ModelDisplay");
					functiondef.func.costfun.SupplyWay = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SupplyWay");
					functiondef.func.costfun.SupplyItem = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SupplyItem");
					functiondef.func.costfun.SupplyVal = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SupplyVal");
				}
				else if (functiondef.func_id == 6)
				{
					functiondef.func.overheatfun.HeatPerUnit=(int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("HeatPerUnit");
					functiondef.func.overheatfun.HeatDownTime=(int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("HeatDownTime");
					functiondef.func.overheatfun.HeatReduce = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("HeatReduce");
					functiondef.func.overheatfun.MaxHeat = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxHeat");
					functiondef.func.overheatfun.MaxHeatReduce = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxHeatReduce");

				}
				else if (functiondef.func_id == 9)
				{
					functiondef.func.propellerfun.Thruster = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Thrust");
					functiondef.func.propellerfun.Effect = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Effect");
					functiondef.func.propellerfun.Sound = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Sound");
					functiondef.func.propellerfun.SoundVolume = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("SoundVolume");
					functiondef.func.propellerfun.ThrusterForce = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ThrusterForce");
					functiondef.func.propellerfun.ThrusterType = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ThrusterType");
					functiondef.func.propellerfun.ThrusterStableType = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ThrusterStableType");
					functiondef.func.propellerfun.RigidForce = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("RigidForce");
					//functiondef.func.propellerfun.KeyType = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("KeyType");
					//functiondef.func.propellerfun.Key = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Key");
					//functiondef.func.propellerfun.KeyIcon = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("KeyIcon");
				}
				else if (11 == functiondef.func_id)
				{
					functiondef.func.sthrusterfun.PowerChangeValue = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("PowerChangeVal");
					functiondef.func.sthrusterfun.PowerChangeInterval = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("PowerChangeInterval");
					functiondef.func.sthrusterfun.CostInterval = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("CostInterval");
					functiondef.func.sthrusterfun.PowerMax = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("PowerMax");
					functiondef.func.sthrusterfun.Level1TopLimit = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level1TopLimit");
					functiondef.func.sthrusterfun.Level1CostValue = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level1CostVal");
					functiondef.func.sthrusterfun.Level2TopLimit = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level2TopLimit");
					functiondef.func.sthrusterfun.Level2CostValue = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level2CostVal");
					functiondef.func.sthrusterfun.Level3TopLimit = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level3TopLimit");
					functiondef.func.sthrusterfun.Level3CostValue = (int)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Level3CostVal");
					functiondef.func.sthrusterfun.ThrusterForce1 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ThrusterForce");
					functiondef.func.sthrusterfun.ThrusterForce2 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("RigidForce");
				}
				else if (12 == functiondef.func_id)
				{
					functiondef.func.suspensionfun.Stiffness1 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness1");
					functiondef.func.suspensionfun.Damping1 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping1");
					functiondef.func.suspensionfun.MaxForce1 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce1");
					functiondef.func.suspensionfun.Stiffness2 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness2");
					functiondef.func.suspensionfun.Damping2 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping2");
					functiondef.func.suspensionfun.MaxForce2 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce2");
					functiondef.func.suspensionfun.Stiffness3 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness3");
					functiondef.func.suspensionfun.Damping3 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping3");
					functiondef.func.suspensionfun.MaxForce3 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce3");
					functiondef.func.suspensionfun.Stiffness4 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness4");
					functiondef.func.suspensionfun.Damping4 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping4");
					functiondef.func.suspensionfun.MaxForce4 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce4");
					functiondef.func.suspensionfun.Stiffness5 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness5");
					functiondef.func.suspensionfun.Damping5 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping5");
					functiondef.func.suspensionfun.MaxForce5 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce5");
					functiondef.func.suspensionfun.Stiffness6 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Stiffness6");
					functiondef.func.suspensionfun.Damping6 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("Damping6");
					functiondef.func.suspensionfun.MaxForce6 = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxForce6");
				}
				else if (13 == functiondef.func_id)
				{
					functiondef.func.boatPropellerfun.Thruster = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("ThrusterForce");
					functiondef.func.boatPropellerfun.maxUpAngle = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxUpAngle");
					functiondef.func.boatPropellerfun.maxDownAngle = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("MaxDownAngle");
				}
				else if (14 == functiondef.func_id)
				{
					functiondef.func.boatBucketfun.floatForce = (float)functionArray.get<jsonxx::Object>(j).get<jsonxx::Number>("FloatForce");
				}
				def.EffectFunctions.push_back(functiondef);
			}
		}

		m_PhysicsParts.AddRecord(def.PartsID, def);
		m_PhysicsPartsIds.push_back(def.PartsID);
	}

	return true;
}

std::vector<int> &DefManager::getPhysicsPartIds()
{
	return m_PhysicsPartsIds;
}

const PhysicsPartsDef *DefManager::getPhysicsPartsDef(int id)
{
	BlockDef *def = getBlockDef(id);
	if (!def) { return NULL; }
	//微雕方块当作id为2000的方块处理，自定义方块取其copyid
	if (def->CopyID > 0)
	{
		id = def->CopyID;
	}
	else if(def->Type == "custombasic")
	{
		if (id > (int)BlockDefCsv::getInstance()->getNum() || BlockDefCsv::getInstance()->getOrigin(id)->Type == "custombasic")
			id = 2000;
	}
	
	return m_PhysicsParts.GetRecord(id);
}

int DefManager::getPhysicsPartsDefNum()
{	
	return m_PhysicsPartsIds.size();
}

const PhysicsPartsDef *DefManager::getPhysicsPartsDefByIndex(int index)
{	
	if ((int)m_PhysicsPartsIds.size() <= index) { return NULL; }
	return getPhysicsPartsDef(m_PhysicsPartsIds[index]);
}

bool DefManager::reloadPhysicsPartsTypeCSV()
{
	if (!loadPhysicsPartsTypeCSV())
	{
		LOG_SEVERE("load loadPhysicsPartsTypeCSV.csv failed");
		return false;
	}
	return true;
}

bool DefManager::loadPhysicsPartsTypeCSV()
{
    static NoFreeFixedString filename = "PhysicsPartsType";
	//const char* filename = "PhysicsPartsType";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);

	m_PhysicsPartsTypeDef.clear();
	int iType = -1;
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		iType = parser[i]["PartsType"].Int();
		PhysicsPartsTypeInfoDef stInfoDef;
		stInfoDef.iSubType = parser[i]["SubType"].Int();
		stInfoDef.iLimitNum = parser[i]["NumLimit"].Int();
		if (parser[i]["NameID"].IsValid()) { stInfoDef.iStringId = parser[i]["NameID"].Int(); }
		if (parser[i]["Icon"].IsValid()) { stInfoDef.sPicUrl = parser[i]["Icon"].String(); }
		stInfoDef.iMaxOutNum = parser[i]["MaxOutNum"].Int();
		stInfoDef.iMaxInNum = parser[i]["MaxInNum"].Int();

		auto def = m_PhysicsPartsTypeDef.GetRecord(iType);
		if (def) {
			def->mPartsTypeInfo[stInfoDef.iSubType] = stInfoDef;
		}
		else {
			PhysicsPartsTypeDef stDef;
			stDef.iPartsType = iType;
			stDef.mPartsTypeInfo[stInfoDef.iSubType] = stInfoDef;

			m_PhysicsPartsTypeDef.AddRecord(iType, stDef);
		}
	}

	return true;
}

PhysicsPartsTypeInfoDef* DefManager::getPhysicsPartsTypeDef(int iType, int iSubType)
{
	loadPhysicsPartsTypeCSV();

	auto def = m_PhysicsPartsTypeDef.GetRecord(iType);
	if (!def) { return NULL; }

	auto it = def->mPartsTypeInfo.find(iSubType);
	if (it == def->mPartsTypeInfo.end()) { return NULL; }

	return &it->second;
}

int DefManager::getPhysicsPartsConnectDefNum()
{
	loadPhysicsPartsConnectCSV();

	return m_PhysicsPartsConnectDef.GetRecordSize();
}

bool DefManager::loadPhysicsPartsConnectCSV()
{
    static NoFreeFixedString filename = "PhysicsPartsConnect";
	//const char* filename = "PhysicsPartsConnect";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);

	m_PhysicsPartsConnectDef.clear();

	int numLines = (int)parser.GetNumLines();
	int recordIndex = 1;
	for (int i = 2; i < numLines; ++i)
	{
		PhysicsPartsConnectDef connectDef;

		connectDef.ID				= recordIndex;
		connectDef.ControlType		= parser[i]["ControlPortPartsType"].Int();
		connectDef.ControlSubType	= parser[i]["ControlPortSubType"].Int();
		connectDef.WorkType			= parser[i]["WorkPortPartsType"].Int();
		connectDef.WorkSubType		= parser[i]["WorkPortSubType"].Int();

		connectDef.ControlRelationType = parser[i]["ControlRelationType"].Int();
		connectDef.DefaultConnect = parser[i]["DefaultConnect"].Char();
		connectDef.CanEdit = parser[i]["CanEdit"].Char();

		m_PhysicsPartsConnectDef.AddRecord(recordIndex, connectDef);
		recordIndex++;
	}
	return true;
}

PhysicsPartsConnectDef* DefManager::getPhysicsPartsConnectDef(int controlType, int controlSubType,int workType, int workSubType)
{	
	loadPhysicsPartsConnectCSV();

	for (int i = 1; i <= getPhysicsPartsConnectDefNum(); i++ )
	{
		auto def = m_PhysicsPartsConnectDef.GetRecord(i);
		if (!def) 
			continue;

		if (def->ControlType == controlType && 
			def->ControlSubType == controlSubType &&
			def->WorkType == workType &&
			def->WorkSubType == workSubType)
		{
			return def;
		}
	}
	return NULL;
}

PhysicsPartsConnectDef* DefManager::getPhysicsPartsConnectDefByIndex(int index)
{
	loadPhysicsPartsConnectCSV();

	return m_PhysicsPartsConnectDef.GetRecord(index);
}

PhysicsPartsTypeInfoDef* DefManager::getPhysicsPartsTypeDefWithPartsId(int iPartsId)
{	
	loadPhysicsPartsTypeCSV();

	auto def = getPhysicsPartsDef(iPartsId);
	if (!def) { return NULL; }

	return getPhysicsPartsTypeDef(def->PartsType, def->SubType);
}

bool DefManager::loadRecordEffectCSV()
{
    static NoFreeFixedString filename = "RecordEffect";
	//const char* filename = "RecordEffect";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		RecordEffectDef def;
		def.ID = parser[i]["ID"].Int();
		def.Path = parser[i]["Path"].Str();
		m_RecordEffectDef.push_back(def);
	}

	return true;
}

PhysicsActorDef* DefManager::getPhysicsActorDef(int id)
{
	return PhysicsActorCsv::getInstance()->get(id);
}

PhysicsActorDef* DefManager::addPhysicsActorDefByCopy(int id, int copyId)
{
	return PhysicsActorCsv::getInstance()->addByCopy(id, copyId);
}

const RecordEffectDef *DefManager::getRecordEffectDef(int id)
{	
	loadRecordEffectCSV();

	 for (int i = 0; i<(int)m_RecordEffectDef.size(); i++)
	{
		if (m_RecordEffectDef[i].ID == id)
			return &m_RecordEffectDef[i];
	}
	return NULL;
}

int DefManager::getRecordEffectDefNum()
{	
	loadRecordEffectCSV();

	return m_RecordEffectDef.size();
}

const RecordEffectDef *DefManager::getRecordEffectDefByIndex(int index)
{	
	loadRecordEffectCSV();

	if (index >= (int)m_RecordEffectDef.size())
	{
		return &m_RecordEffectDef[index];
	}
	return NULL;
}

void DefManager::addDefByCustomModel(int id, int eFcmSaveType, std::string filename/* ="" */, std::string name /* = "" */, std::string desc /* = "" */, Rainbow::Vector3f box /* = Rainbow::Vector3f(0, 0, 0) */, short involvedId/* =0 */)
{
	if (eFcmSaveType == BLOCK_MODEL)
	{
		if (BlockDefCsv::getInstance()->get(id, false))
			return;

		BlockDefCsv::getInstance()->add(id, eFcmSaveType, filename, name, desc);
	}
	else if (eFcmSaveType > BLOCK_MODEL && eFcmSaveType <= BOW_MODEL)
	{
		if (ItemDefCsv::getInstance()->get(id, false))
			return;

		if (eFcmSaveType == WEAPON_MODEL || eFcmSaveType == PROJECTILE_MODEL || eFcmSaveType == BOW_MODEL)
			ToolDefCsv::getInstance()->add(id, eFcmSaveType);

		if (eFcmSaveType == GUN_MODEL)
			addGunDef(id);

		if (eFcmSaveType == PROJECTILE_MODEL)
		{
			ProjectileDefCsv::getInstance()->add(id, filename);
			PhysicsActorCsv::getInstance()->add(id, box);
		}
			

		ItemDefCsv::getInstance()->add(id, eFcmSaveType, filename, name, desc);
	}
	else if (eFcmSaveType == ACTOR_MODEL)
	{
		if (MonsterCsv::getInstance()->get(id))
			return;

		MonsterCsv::getInstance()->add(id, filename, name, eFcmSaveType);
		if(involvedId > 0)
			ItemDefCsv::getInstance()->add(involvedId, eFcmSaveType, filename, name, "", id);
	}
	else if (eFcmSaveType == FULLY_BLOCK_MODEL)
	{
		if (BlockDefCsv::getInstance()->get(id, false))
			return;

		BlockDefCsv::getInstance()->add(id, eFcmSaveType, filename, name, desc);
	}
	else if (eFcmSaveType == FULLY_ITEM_MODEL)
	{
		if (ItemDefCsv::getInstance()->get(id, false))
			return;

		ToolDefCsv::getInstance()->add(id, FULLY_ITEM_MODEL-100);
		ItemDefCsv::getInstance()->add(id, FULLY_ITEM_MODEL, filename, name, desc);
	}
	else if (eFcmSaveType == FULLY_ACTOR_MODEL)
	{
		if (MonsterCsv::getInstance()->get(id))
			return;

		MonsterCsv::getInstance()->add(id, filename, name, eFcmSaveType);
		if (involvedId > 0)
			ItemDefCsv::getInstance()->add(involvedId, eFcmSaveType, filename, name, desc, id);
	}
	else if (eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		if (ItemDefCsv::getInstance()->get(id, false))
			return;

		ToolDefCsv::getInstance()->add(id, FULLY_PACKING_CUSTOM_MODEL - 100);
		ItemDefCsv::getInstance()->add(id, FULLY_PACKING_CUSTOM_MODEL, filename, name, desc);
	}
	else if (eFcmSaveType == IMPORT_BLOCK_MODEL)
	{
		if (BlockDefCsv::getInstance()->get(id, false))
			return;

		BlockDefCsv::getInstance()->add(id, eFcmSaveType, filename, name, desc);
	}
	else if (eFcmSaveType == IMPORT_ITEM_MODEL)
	{
		if (ItemDefCsv::getInstance()->get(id, false))
			return;

		ToolDefCsv::getInstance()->add(id, IMPORT_ITEM_MODEL - 200);
		ItemDefCsv::getInstance()->add(id, eFcmSaveType, filename, name, desc);
	}
	else if (eFcmSaveType == IMPORT_ACTOR_MODEL)
	{
		if (MonsterCsv::getInstance()->get(id))
			return;

		MonsterCsv::getInstance()->add(id, filename, name, eFcmSaveType);
		if (involvedId > 0)
			ItemDefCsv::getInstance()->add(involvedId, eFcmSaveType, filename, name, desc, id);
	}

}

GunDef* DefManager::addGunDef(int id)
{	
	loadGunDef();
	auto *templateGunDef = getGunDef(9998);
	return addGunDef(id, templateGunDef);
}

GunDef* DefManager::addGunDefByCopy(int id, int copyId)
{
	loadGunDef();
	auto *templateGunDef = getGunDef(copyId);
	return addGunDef(id, templateGunDef);
}

GunDef* DefManager::addGunDef(int id, const GunDef* templateGunDef)
{
	if(templateGunDef == NULL) return NULL;
	GunDef tmpGunDef;
	tmpGunDef.ID = id;

	MyStringCpy(tmpGunDef.Name, sizeof(tmpGunDef.Name), templateGunDef->Name);
	MyStringCpy(tmpGunDef.ShootEffect, sizeof(tmpGunDef.ShootEffect), templateGunDef->ShootEffect);
	MyStringCpy(tmpGunDef.ShootSound, sizeof(tmpGunDef.ShootSound), templateGunDef->ShootSound);
	MyStringCpy(tmpGunDef.ReloadSound, sizeof(tmpGunDef.ReloadSound), templateGunDef->ReloadSound);
	MyStringCpy(tmpGunDef.EmptyShootSound, sizeof(tmpGunDef.EmptyShootSound), templateGunDef->EmptyShootSound);

	tmpGunDef.Attack = templateGunDef->Attack;
	tmpGunDef.FireInterval = templateGunDef->FireInterval;
	tmpGunDef.Magazines = templateGunDef->Magazines;
	tmpGunDef.InitSpread = templateGunDef->InitSpread;
	tmpGunDef.MaxSpread = templateGunDef->MaxSpread;
	tmpGunDef.SpreadSpeed = templateGunDef->SpreadSpeed;
	tmpGunDef.SpreadRecoverySpeed = templateGunDef->SpreadRecoverySpeed;
	tmpGunDef.MaxRecoil = templateGunDef->MaxRecoil;
	tmpGunDef.RecoilSpeed = templateGunDef->RecoilSpeed;
	tmpGunDef.RecoilRecoverySpeed = templateGunDef->RecoilRecoverySpeed;
	tmpGunDef.Aim = templateGunDef->Aim;
	tmpGunDef.Crosshair = templateGunDef->Crosshair;
	tmpGunDef.BulletID = templateGunDef->BulletID;
	tmpGunDef.NeedBullet = templateGunDef->NeedBullet;
	tmpGunDef.ContinuousFire = templateGunDef->ContinuousFire;
	//拉栓的动作和音效和时间
	tmpGunDef.ManualAnimFps = templateGunDef->ManualAnimFps;
	MyStringCpy(tmpGunDef.ManualSound, sizeof(tmpGunDef.ManualSound), templateGunDef->ManualSound);
	tmpGunDef.ManualTime = templateGunDef->ManualTime;
	tmpGunDef.ManualDelayTime = templateGunDef->ManualDelayTime;
	tmpGunDef.ReloadTime = templateGunDef->ReloadTime;
	tmpGunDef.Weight = templateGunDef->Weight;
	tmpGunDef.IdleAnimFps = templateGunDef->IdleAnimFps;
	tmpGunDef.ShootAnimFps = templateGunDef->ShootAnimFps;
	tmpGunDef.ReloadAnimFps = templateGunDef->ReloadAnimFps;
	tmpGunDef.DrawAnimFps = templateGunDef->DrawAnimFps;
	tmpGunDef.AimAnimFps = templateGunDef->AimAnimFps;
	tmpGunDef.AimShootAnimFps = templateGunDef->AimShootAnimFps;
	tmpGunDef.IdleAnimTps = templateGunDef->IdleAnimTps;
	tmpGunDef.ShootAnimTps = templateGunDef->ShootAnimTps;
	tmpGunDef.ReloadAnimTps = templateGunDef->ReloadAnimTps;
	tmpGunDef.SpeedAdd = templateGunDef->SpeedAdd;
	tmpGunDef.GunType = templateGunDef->GunType;

	m_GunTable.AddRecord(tmpGunDef.ID, tmpGunDef);
	return m_GunTable.GetRecord(tmpGunDef.ID);
}

void DefManager::getNpcShopChangeConfigStatus(const char* path, std::map<int, std::vector<int> >& mNpcShopChangeInfo)
{
	g_ModMgr.getNpcShopChangeConfigStatus(path, mNpcShopChangeInfo);
}

void DefManager::setNpcShopChangeConfigStatus(const char* path, const std::map<int, std::vector<int> >& mNpcShopChangeInfo)
{
	g_ModMgr.setNpcShopChangeConfigStatus(path, mNpcShopChangeInfo);
}

bool DefManager::loadTriggerActDef()
{
    static NoFreeFixedString filename = "triggeract";
	//const char* filename = "triggeract";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_TriggerActDefMap.clear();
	m_TriggerActLinkMap.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		TriggerActDef def;
		def.ID = parser[i]["ID"].Int();
		if(def.ID == 0) continue;

		def.Name = parser[i]["Name"].Str();
		def.ActID = parser[i]["ActID"].Int();
		def.LinkID = parser[i]["LinkID"].Int();
		def.Icon = parser[i]["icon"].Str();
		def.Effect = parser[i]["Effect"].Str();
		def.Face = parser[i]["FaceID"].Str();
		def.TriggerType = parser[i]["TriggerType"].Int();
		def.stringID = parser[i]["stringID"].Int();
		def.FpsActID = parser[i]["FpsActID"].Int();

		m_TriggerActDefMap.insert(std::make_pair(def.ID, def));
		m_TriggerActLinkMap.insert(std::make_pair(def.LinkID, def.ID));
	}

	return true;
}

void DefManager::linkTriggerAct2AnimAct(TriggerActDef* def)
{
	// 如果是link到animact.csv，填充数据
	if (def && def->LinkID > 0)
	{
		auto playactdef = getPlayActDef(def->LinkID);
		if (playactdef)
		{
			def->ActID = playactdef->ActID;
			def->Effect = playactdef->Effect;
			def->Face = playactdef->Face;
		}
		else
		{
			assert(false && "can not found animact.csv data!");
		}

		def->LinkID = -def->LinkID;
	}

}

int DefManager::addEquipStatus(int id)
{
	return g_ModMgr.addEquipStatus(id);
}

int DefManager::getStatusIdByEquipId(int id)
{
	return g_ModMgr.getStatusIdByEquipId(id);
}

std::vector<CharacterDef> &DefManager::getCharDefTable()
{
	loadCharacterDef();

	return m_CharDefTable;
}

ChunkRandGen *DefManager::getRandGen()
{
	return m_RandGen;
}

std::vector<int> &DefManager::getMonsterBiomesIDs()
{
	loadMonsterBiomeCSV();

	return monsterBiomesIDs;
}

BiomePlantTryDef& DefManager::getBiomePlantTries()
{
	return m_BiomePlantTries;
}

TriggerActDef* DefManager::getTriggerActDef(int id)
{
	loadTriggerActDef();

	auto iter = m_TriggerActDefMap.find(id);
	if (iter == m_TriggerActDefMap.end())
		return NULL;
	
	// link
	auto def = &iter->second;
	linkTriggerAct2AnimAct(def);

	return def;
}

//index: 从0开始
TriggerActDef* DefManager::getTriggerActDefByIndex(int index)
{	
	loadTriggerActDef();

	if(index < 0 || index >= (int)m_TriggerActDefMap.size())
		return NULL;

	auto iter = m_TriggerActDefMap.begin();
	std::advance(iter, index);
	
	// link
	auto def = &iter->second;
	linkTriggerAct2AnimAct(def);

	return def;
}

int DefManager::getTriggerActBySrcAct(int act)
{	
	loadTriggerActDef();

	auto iter = m_TriggerActLinkMap.find(act);
	if (iter == m_TriggerActLinkMap.end())
		return 0;

	return iter->second;
}

void DefManager::AddPhysicsPartKeyDefFromFunc(std::vector<PhysicsPartKeyDef>&Keys, jsonxx::Object FuncDef)
{
	int index = 1;
	std::stringstream ss;
	ss << index;
	while (FuncDef.has<jsonxx::Number>("Key" + ss.str()))
	{
		PhysicsPartKeyDef def;
		def.FuncID=FuncDef.get<jsonxx::Number>("FunctionID");
		def.KeyVal= FuncDef.get<jsonxx::Number>("Key" + ss.str());
		if (FuncDef.has<jsonxx::Number>("KeyType" + ss.str()))
			def.KeyType = FuncDef.get<jsonxx::Number>("KeyType" + ss.str());
		if (FuncDef.has<jsonxx::String>("KeyIcon" + ss.str()))
			def.KeyIcon = FuncDef.get<jsonxx::String>("KeyIcon" + ss.str());

		Keys.push_back(def);
		ss.str("");
		ss << (++index);
	}
}

int DefManager::getPhysicsPartKeysNum(int PartsId)
{	
	const PhysicsPartsDef* pDef = getPhysicsPartsDef(PartsId);
	if (pDef)
	{
		return (pDef->Keys).size();
	}
	return 0;
}

const PhysicsPartKeyDef* DefManager::getPhysicsPartKeyDefByIndex(int PartsId, int index)
{	
	const PhysicsPartsDef* pDef = getPhysicsPartsDef(PartsId);
	if (pDef)
	{
		if (index >= (int)pDef->Keys.size())
			return NULL;
		return &(pDef->Keys[index]);
	}
	return NULL;
}

bool DefManager::loadWildmanNameDef( )
{
    static NoFreeFixedString filename = "random_names_wildman";
	//const char* filename = "random_names_wildman";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser(false, true);
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_WildmanNameTable.clear();

	parser.SetTitleLine(1);
	int id = 1;
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		const char* str = ColumnLang(parser[i],"Name",m_CurLanguage);
		if (str != NULL && str[0] != '\0')
		{
			StringDef def;
			def.ID = id;
			def.str = str;
			m_WildmanNameTable.AddRecord(id, def);
			id++;
		}
	}

	return true;
}
bool DefManager::loadRoleActionDef( ) 
{
    static NoFreeFixedString filename = "roleActions";
	//const char* filename = "roleActions";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_RoleActionTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i<numLines; ++i)
	{
		//RoleActionDef *def = ENG_NEW(RoleActionDef);
		RoleActionDef def;
		def.ID = parser[i]["ID"].Int();
		if (def.ID == 0) continue;
		def.SkinID = parser[i]["SkinID"].Int();
		def.Name = parser[i]["Name"].Str();
		def.StringID = parser[i]["StringID"].Int();
		def.CharacterID = parser[i]["CharacterID"].Int();
		def.ActionID = parser[i]["ActionID"].Int();
		def.Toaction = parser[i]["Toaction"].Int();
		def.Tomodel = parser[i]["Tomodel"].Int();
		def.Close = parser[i]["Close"].Int();
		def.Type = parser[i]["Type"].Int();
		m_RoleActionTable.push_back(def);
	}
	return true;
}

bool DefManager::isSpecialUin(int uin)
{
	if (m_SpecialUinDefTable.find(uin) != m_SpecialUinDefTable.end())
	{
		return true;
	}
	return false;
}

bool DefManager::loadOverseasGrayCSV( ) 
{
    static NoFreeFixedString filename = "overseascustom";
	//const char* filename = "overseascustom";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	
	m_OverseasGrayTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i<numLines; ++i)
	{
		//OverseasGrayDef *def = ENG_NEW(OverseasGrayDef)();
		OverseasGrayDef def;
		def.ApiID = parser[i]["ApiID"].Int();
		def.Name = parser[i]["Name"].Str();
		def.MinVersion = parser[i]["MinVersion"].Str();
		def.MaxVersion = parser[i]["MaxVersion"].Str();
		def.CusLanguger = atoi(ColumnLang(parser[i], "Language", m_CurLanguage));
		m_OverseasGrayTable.push_back(def);
	}
	return true;
}

bool DefManager::loadWikiConfigCSV()
{
    static NoFreeFixedString filename = "WikiConfig";
	//const char* filename = "WikiConfig";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_wikiConfigTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		//OverseasGrayDef *def = ENG_NEW(OverseasGrayDef)();
		WikiConfigDef def;
		def.id = parser[i]["ID"].Int();
		def.desc = parser[i]["Desc"].Str();
		def.isShow = parser[i]["IsShow"].Int();
		def.openWith = parser[i]["OpenWith"].Int();
		def.address = parser[i]["Address"].Str();
		m_wikiConfigTable.push_back(def);
	}
	return true;
}

int DefManager::getOverseasGrayNum() 
{
	loadOverseasGrayCSV();
	return  m_OverseasGrayTable.size();
}

WikiConfigDef* DefManager::getWikiConfigDef(int index)
{
	loadWikiConfigCSV();
	if (index < m_wikiConfigTable.size())
	{
		return &m_wikiConfigTable[index];
	}
	return nullptr;
}

OverseasGrayDef* DefManager::getOverseasGrayDef(int index) 
{
	loadOverseasGrayCSV();
	return &m_OverseasGrayTable[index];
}

bool DefManager::loadBPSettingCSV()
{
    static NoFreeFixedString filename = "BPSetting";
	//const char* filename = "BPSetting";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BPSettingTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 3; i < numLines; ++i)
	{
		//BPSettingDef *def = ENG_NEW(BPSettingDef)();
		BPSettingDef def;
		def.ID = parser[i]["ID"].Short();
		def.Name = parser[i]["Name"].Str();
		std::vector<core::string> vTemp;
		vTemp.clear();
		Rainbow::StringUtil::split(vTemp, parser[i]["Mission"].String(), ",");
		for (auto it = vTemp.begin(); it != vTemp.end(); it++)
			def.Mission.push_back(atoi(it->c_str()));

		vTemp.clear();
		Rainbow::StringUtil::split(vTemp, parser[i]["MaxIntegral"].String(), ",");
		for (auto it = vTemp.begin(); it != vTemp.end(); it++)
			def.MaxIntegral[it - vTemp.begin()] = atoi(it->c_str());
		
		vTemp.clear();
		Rainbow::StringUtil::split(vTemp, parser[i]["GeneralReward"].String(), ",");
		for (auto it = vTemp.begin(); it != vTemp.end(); it++)
		{
			if (it->size() == 0)
				def.GeneralReward.push_back(-1);
			else
				def.GeneralReward.push_back(atoi(it->c_str()));
		}

		vTemp.clear();
		std::vector<core::string> wTemp;
		wTemp.clear();
		StringUtil::split(vTemp, parser[i]["BattlePassReward"].String(), "{");
		int tIndex = 0;
		for (auto it = vTemp.begin(); it != vTemp.end(); it++)
		{
			if (it->length() != 0)
			{
				auto ts = *it;
				ts.erase(ts.rfind('}'));
				StringUtil::split(wTemp, ts, ",");
				std::vector<short> tempVec;
				for (auto iy = wTemp.begin(); iy != wTemp.end(); iy++)
				{
					if (iy->size() == 0)
						def.BattlePassReward[tIndex].push_back(-1);
					else
						def.BattlePassReward[tIndex].push_back(atoi(iy->c_str()));
				}
				++tIndex;
			}
		}
		def.OpenTime = parser[i]["OpenTime"].String();
		def.CloseTime = parser[i]["CloseTime"].String();
		def.Background1 = parser[i]["Background1"].String();
		def.Background2 = parser[i]["Background2"].String();
		def.Background3 = parser[i]["Background3"].String();
		def.Modle = parser[i]["Modle"].String();
		//def->Uilocation = parser[i]["Uilocation"].String();
		def.Sound = parser[i]["Sound"].String();
		def.Rule = parser[i]["Rule"].Short();
		def.Propaganda = parser[i]["Propaganda"].Short();

		m_BPSettingTable.push_back(def);
	}
	return true;
}

bool DefManager::loadBPDrawCSV()
{
    static NoFreeFixedString filename = "BPDraw";
	//const char* filename = "BPDraw";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BPDrawTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 3; i < numLines; ++i)
	{
		//BPDrawDef *def = ENG_NEW(BPDrawDef)();
		BPDrawDef def;
		def.ID = parser[i]["ID"].Short();
		def.Name = parser[i]["Name"].Str();
		def.Type[0] = parser[i]["Type"].Str()[0] - '0';
		def.Drawui1 = parser[i]["Drawui1"].Str();
		def.Drawui2 = parser[i]["Drawui2"].Str();
		def.Drawui3 = parser[i]["Drawui3"].Str();
		def.Rule = parser[i]["Ruler"].Short();
		def.Sound = parser[i]["Sound"].Str();

		m_BPDrawTable.push_back(def);
	}
	return true;
}

bool DefManager::loadBPMissionCSV()
{
    static NoFreeFixedString filename = "BPMission";
	//const char* filename = "BPMission";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BPMissionTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 3; i < numLines; ++i)
	{
		//BPMissionDef *def = ENG_NEW(BPMissionDef)();
		BPMissionDef def;
		def.ID = parser[i]["ID"].Short();
		def.Name = parser[i]["Title"].Int();
		def.Mission = parser[i]["Mission"].Int();
		def.Jump = parser[i]["Jump"].Int();
		def.Icon = parser[i]["Icon"].Int();
		def.Integral = parser[i]["Integral"].Short();
		def.FuctionDec = parser[i]["FuctionDec"].Short();
		def.Times = parser[i]["Times"].Short();

		m_BPMissionTable.push_back(def);
	}
	return true;
}

bool DefManager::loadBPRewardCSV() 
{
    static NoFreeFixedString filename = "BPReward";
	//const char* filename = "BPReward";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_BPRewardTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 3; i < numLines; ++i)
	{
		//BPRewardDef *def = ENG_NEW(BPRewardDef)();
		BPRewardDef def;
		def.ID = parser[i]["ID"].Short();
		def.Item = parser[i]["Item"].Short();
		def.Count = parser[i]["Count"].Short();

		m_BPRewardTable.push_back(def);
	}
	return true;
}

bool DefManager::loadDriftBottleOfficialText()
{
    static NoFreeFixedString filename = "bottle";
	//const char* filename = "bottle";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_DriftBottleOfficialText.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		DriftBottleOfficialText* def = ENG_NEW(DriftBottleOfficialText);
		def->id = parser[i]["Id"].UShort();
		def->title = parser[i]["Title"].String();
		def->context = parser[i]["Context"].String();
		def->author = parser[i]["Author"].String();
		def->shouldAnonymity = parser[i]["ShouldAnonymity"].UShort() == 1;
		m_DriftBottleOfficialText.push_back(def);
	}
	return true;
}

bool DefManager::loadLettersOfficialText()
{
    static NoFreeFixedString filename = "letters";
	//const char* filename = "letters";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	for (auto p : m_LettersOfficialText)
	{
		ENG_DELETE(p);
	}
	m_LettersOfficialText.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		LettersOfficialText* def = ENG_NEW(LettersOfficialText);
		def->id = parser[i]["Id"].UShort();
		def->title = parser[i]["Title"].String();
		def->context = parser[i]["Context"].String();
		def->author = parser[i]["Author"].String();
		def->shouldAnonymity = parser[i]["ShouldAnonymity"].UShort() == 1;
		m_LettersOfficialText.push_back(def);
	}
	return true;
}

bool DefManager::copyLettersOfficialText(std::vector<LettersOfficialText*> value)
{
	if (value.empty()) return false;
	for (auto p : m_LettersOfficialText)
	{
		ENG_DELETE(p);
	}
	m_LettersOfficialText.clear();
	m_LettersOfficialText = value;
	return true;
}

bool DefManager::loadDungeonsDef()
{
    static NoFreeFixedString filename = "dungeons";
	//const char* filename = "dungeons";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename.c_str(), filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_DungeonsTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		DungeonsDef def;
		def.ID = parser[i]["ID"].Int();
		if (def.ID == 0) continue;
		def.Mob1ResID = parser[i]["Mob1ResID"].Int();
		def.Mob1Num = parser[i]["Mob1Num"].Int();
		def.Mob2ResID = parser[i]["Mob2ResID"].Int();
		def.Mob2Num = parser[i]["Mob2Num"].Int();
		def.Mob3ResID = parser[i]["Mob3ResID"].Int();
		def.Mob3Num = parser[i]["Mob3Num"].Int();
		m_DungeonsTable.AddRecord(def.ID, def);
	}
	return true;
}

bool DefManager::loadMonsterStatueDef()
{
    static NoFreeFixedString filename = "monsterstatue";
	//const char* filename = "monsterstatue";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename.c_str(), filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_MonsterStatueDefTable.clear();
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		MonsterStatueDef def;
		def.ID = parser[i]["ID"].Int();
		if (def.ID == 0) continue;
		def.Name = parser[i]["Name"].Str();
		def.AnimID = parser[i]["CorrespondingAction"].Int();
		def.AnimTick = parser[i]["CorrespondingPoseTick"].Int();
		m_MonsterStatueDefTable.AddRecord(def.ID, def);
	}
	return true;
}

BuildReplaceDef *DefManager::getBuildReplaceDef(int id)
{
	auto it = m_BuildReplaceTable.find(id);
	if (it == m_BuildReplaceTable.end())
	{
		return nullptr;
	}
	return &it->second;
}

WorkbenchTechCsvDef* DefManager::findItembenchTech(int itemid)
{
	return WorkbenchTechCsv::getInstance()->findItembenchTech(itemid);
}

TechTree* DefManager::getWorkbenchTechTree(int level)
{
	return WorkbenchTechCsv::getInstance()->getWorkbenchTechTree(level);
}

bool DefManager::loadBuildReplaceCSV()
{
	LOG_INFO("Load BuildingSpecialBlock_new ");

	static NoFreeFixedString filename = "BuildingSpecialBlock_new";
	//const char* filename = "BuildReplace";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename.c_str(), filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	
	m_BuildReplaceTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		BuildReplaceDef def;
		def.BlockId = parser[i]["ID"].Int();
		if (def.BlockId == 0)
			continue;
		def.ReplaceType = (BuildReplaceType)parser[i]["Type"].Int();
		std::string ids = parser[i]["Ids"].Str();

		std::vector<std::string> vTemp;
		StringUtil::split(vTemp, ids, "|");

		int totalWeight = 0;
		for (auto it = vTemp.begin(); it != vTemp.end(); it++)
		{
			std::vector<std::string> vTemp2;
			StringUtil::split(vTemp2, *it, "-");
			if (vTemp2.size() != 2)
			{
				LOG_WARNING("BuildingSpecialBlock ID:%d Ids:%s is invalid", def.BlockId, ids.c_str());
				continue;
			}
			int id = atoi(vTemp2[0].c_str());
			int weight = atoi(vTemp2[1].c_str());
			def.ReplaceIds[id] = weight;
			totalWeight += weight;
		}
		def.TotalWeight = totalWeight;
		m_BuildReplaceTable[def.BlockId] = def;	
	}
	return true;
}

int DefManager::getDriftBottleOfficalTextNum()
{
	loadDriftBottleOfficialText();
	return m_DriftBottleOfficialText.size();
}

const DriftBottleOfficialText* DefManager::getDriftBottleOfficialTextByIndex(int index)
{
	loadDriftBottleOfficialText();
	if (index < 0 || index > m_DriftBottleOfficialText.size())
	{
		return NULL;
	}
	return m_DriftBottleOfficialText[index];
}

int DefManager::getLettersOfficalTextNum()
{
	loadLettersOfficialText();
	return m_LettersOfficialText.size();
}

const LettersOfficialText* DefManager::getLettersOfficialTextByIndex(int index)
{
	loadLettersOfficialText();
	if (index < 0 || index > m_LettersOfficialText.size())
	{
		return NULL;
	}
	return m_LettersOfficialText[index];
}

int DefManager::getVoiceZone(const char* country)
{
	return GameZoneCsv::getInstance()->getVoiceZone(country);
}
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
int DefManager::getHomeProducerDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeProducerDefNum();
}

HomeProducerDef *DefManager::getHomeProducerDef(int id)
{
	return GetHomeCsvManagerProxy()->getHomeProducerDef(id);
}

HomeProducerDef *DefManager::getHomeProducerDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeProducerDefByIndex(index);
}

int DefManager::getHomeTabDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeTabDefNum();
}

const HomeTabDef* DefManager::getHomeTabDefById(int id)
{
	return GetHomeCsvManagerProxy()->getHomeTabDefById(id);
}

const HomeTabDef* DefManager::getHomeTabDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeTabDefByIndex(index);
}

//家园表
int DefManager::getHomeItemDefDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeItemDefDefNum();
}
const HomeItemDef *DefManager::getHomeItemDef(int itemid)
{
	return GetHomeCsvManagerProxy()->getHomeItemDef(itemid);
}

const HomeItemDef *DefManager::getHomeItemDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeItemDefByIndex(index);
}

bool DefManager::findHomeItemDefFunctionId(int itemid, int functionid)
{
	return GetHomeCsvManagerProxy()->findHomeItemDefFunctionId(itemid, functionid);
}
//家园Npc
int DefManager::getHomeNpcDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeNpcDefNum();
}
bool DefManager::getHomeNpcDef(std::vector<HomeNpcInfo> &vec,int type)
{
	return GetHomeCsvManagerProxy()->getHomeNpcDef(vec, type);
}
const HomeNpcInfo *DefManager::getHomeNpcDef(int type,int id)
{
	return GetHomeCsvManagerProxy()->getHomeNpcDef(type, id);
}

const HomeNpcInfo *DefManager::getHomeNpcDefByIndex(int type, int index)
{
	return GetHomeCsvManagerProxy()->getHomeNpcDefByIndex(type, index);
}


//宠物表
bool DefManager::loadPetDef()
{
    static NoFreeFixedString filename = "PetDef";
	//const char* filename = "PetDef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename.c_str(), filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	m_PetTable.clear();
	int petid = -1;
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		petid = parser[i]["PetID"].Int();
		PetInfoDef petInfoDef;
		petInfoDef.PetID = petid;
		petInfoDef.MonsterID = parser[i]["MonsterID"].Int();
		petInfoDef.Stage = parser[i]["Stage"].Int();
		petInfoDef.HeadIcon = parser[i]["HeadIcon"].Str();
		petInfoDef.PetQuality = parser[i]["PetQuality"].Int();
		petInfoDef.BaseStaminaMin = parser[i]["BaseStaminaMin"].Int();
		petInfoDef.BaseStaminaMax = parser[i]["BaseStaminaMax"].Int();
		petInfoDef.BaseSpeedMin = parser[i]["BaseSpeedMin"].Int();
		petInfoDef.BaseSpeedMax = parser[i]["BaseSpeedMax"].Int();
		petInfoDef.BaseLuckMin = parser[i]["BaseLuckMin"].Int();
		petInfoDef.BaseLuckMax = parser[i]["BaseLuckMax"].Int();
		petInfoDef.SkillPackID = parser[i]["GetSkillPack"].Int();
		petInfoDef.SpecialEffect = parser[i]["SpecialEffects"].Str();
		petInfoDef.FootPrint = parser[i]["Footprint"].Str();
		petInfoDef.HandBookScore = parser[i]["HandBookScore"].Int();
		petInfoDef.StaminaMax = parser[i]["MaxStamina"].Int(); // 表格  -- 2021/08/05 codeby wudeshen
		petInfoDef.LuckMax = parser[i]["MaxLuck"].Int();
		petInfoDef.SpeedMax = parser[i]["MaxSpeed"].Int();
		auto def = m_PetTable.GetRecord(petid);
		if (def) {
			def->mPetInfo[petInfoDef.Stage][petInfoDef.PetQuality] = petInfoDef;
		}
		else {
			PetDef petDef;
			petDef.PetID = petid;
			petDef.mPetInfo[petInfoDef.Stage][petInfoDef.PetQuality] = petInfoDef;
			m_PetTable.AddRecord(petid, petDef);
		}
	}

	return true;
}


int DefManager::getPetDefNum()
{
	loadPetDef();
	return m_PetTable.GetRecordSize();
}

const PetInfoDef *DefManager::getPetDef(int id,int stage, int quality)
{
	loadPetDef();
	auto def = m_PetTable.GetRecord(id);
	if (!def) { return NULL; }

	auto it = def->mPetInfo.find(stage);
	if (it == def->mPetInfo.end()) { return NULL; }

	auto iter = def->mPetInfo[stage].find(quality);
	if (iter == def->mPetInfo[stage].end()) { return NULL; }

	return &iter->second;
}

const PetInfoDef * DefManager::getPetDefByIndex(int index, int stage, int quality)
{
	PetDef* petDef = m_PetTable.GetRecordByIndex(index);
	if (petDef)
	{
		auto it = petDef->mPetInfo.find(stage);
		if (it == petDef->mPetInfo.end()) { return NULL; }

		auto iter = petDef->mPetInfo[stage].find(quality);
		if (iter == petDef->mPetInfo[stage].end()) { return NULL; }

		return &iter->second;
	}
	return NULL;
}


//宠物技能表
bool DefManager::loadPetSkillsDef()
{
    static NoFreeFixedString filename = "PetSkillDef";
	//const char* filename = "PetSkillDef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename.c_str(), filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	m_PetSkillsTable.clear();
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		PetSkillsDef def;
		def.SkillID = parser[i]["SkillID"].Int();
		def.SkillName = parser[i]["SkillName"].Int();
		def.Icon = parser[i]["Icon"].Str();
		def.Desc = parser[i]["Desc"].Int();
		m_PetSkillsTable.AddRecord(def.SkillID, def);
	}
	return true;
}


int DefManager::getPetSkillsDefNum()
{
	loadPetSkillsDef();
	return m_PetSkillsTable.GetRecordSize();
}
const PetSkillsDef *DefManager::getPetSkillsDef(int id)
{
	loadPetSkillsDef();
	return m_PetSkillsTable.GetRecord(id);
}

const PetSkillsDef *DefManager::getPetSkillsDefByIndex(int index)
{
	loadPetSkillsDef();
	return m_PetSkillsTable.GetRecordByIndex(index);
}

bool DefManager::loadFarmInfoDef()
{
	return true;
}

const FarmInfoDef* DefManager::getFarmInfoDef(int farmlandID)
{
	return GetHomeCsvManagerProxy()->getFarmInfoDef(farmlandID);
}

int DefManager::getFamrInfoDefNum()
{
	return GetHomeCsvManagerProxy()->getFamrInfoDefNum();
}

bool DefManager::loadPetExploreDef()
{
    static NoFreeFixedString filename = "PetExploreDef";
	//const char* filename = "PetExploreDef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	m_PetExploreTable.clear();
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		PetExploreDef def;
		def.ExploreMapID = parser[i]["ExploreMapID"].Int();
		def.MapName = parser[i]["MapName"].Int();
		def.NeedHomeLv = parser[i]["NeedHomeLv"].Int();
		def.NeedMapID = parser[i]["NeedMapID"].Int();
		def.BaseTime = parser[i]["BaseTime"].Int();
		def.SpecialSkill = parser[i]["SpecialSkill"].Int();
		def.MapIcon = parser[i]["MapIcon"].Str();
		def.MaxPetNum = parser[i]["MaxPetNum"].Int();
		def.SpecialEventID = parser[i]["SpecialEventID"].Int();
		def.Milepost1_Distance = parser[i]["Milepost1_Distance"].Int();
		def.Milepost2_Distance = parser[i]["Milepost2_Distance"].Int();
		def.Milepost3_Distance = parser[i]["Milepost3_Distance"].Int();
		def.Milepost4_Distance = parser[i]["Milepost4_Distance"].Int();
		def.Milepost5_Distance = parser[i]["Milepost5_Distance"].Int();
		def.Milepost1_AwardPackID = parser[i]["Milepost1_AwardPackID"].Int();
		def.Milepost2_AwardPackID = parser[i]["Milepost2_AwardPackID"].Int();
		def.Milepost3_AwardPackID = parser[i]["Milepost3_AwardPackID"].Int();
		def.Milepost4_AwardPackID = parser[i]["Milepost4_AwardPackID"].Int();
		def.Milepost5_AwardPackID = parser[i]["Milepost5_AwardPackID"].Int();
		def.Milepost1_PowerValue = parser[i]["Milepost1_PowerValue"].Int();
		def.Milepost2_PowerValue = parser[i]["Milepost2_PowerValue"].Int();
		def.Milepost3_PowerValue = parser[i]["Milepost3_PowerValue"].Int();
		def.Milepost4_PowerValue = parser[i]["Milepost4_PowerValue"].Int();
		def.Milepost5_PowerValue = parser[i]["Milepost5_PowerValue"].Int();
		m_PetExploreTable.AddRecord(def.ExploreMapID, def);
	}
	return true;
}

int DefManager::getPetExploreDefNum()
{
	loadPetExploreDef();
	return m_PetExploreTable.GetRecordSize();
}

const PetExploreDef *DefManager::getPetExploreDef(int id)
{
	loadPetExploreDef();
	return m_PetExploreTable.GetRecord(id);
}

const PetExploreDef *DefManager::getPetExploreDefByIndex(int index)
{
	loadPetExploreDef();
	return m_PetExploreTable.GetRecordByIndex(index);
}

bool DefManager::loadPetEventDef()
{
    static NoFreeFixedString filename = "PetEventDef";
	//const char* filename = "PetEventDef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	m_PetEventTable.clear();
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		PetEventDef def;
		def.EventID = parser[i]["EventID"].Int();
		def.Icon = parser[i]["Icon"].Str();
		def.Icon1 = parser[i]["Icon1"].Str();
		def.Desc = parser[i]["Desc"].Int();
		def.MissionFunction1_ID = parser[i]["MissionFunction1_ID"].Int();
		def.MissionFunction1_Val1 = parser[i]["MissionFunction1_Val1"].Int();
		def.MissionFunction1_Val2 = parser[i]["MissionFunction1_Val2"].Int();
		def.MissionFunction1_Val3 = parser[i]["MissionFunction1_Val3"].Int();
		def.MissionFunction1_Val4 = parser[i]["MissionFunction1_Val4"].Int();
		m_PetEventTable.AddRecord(def.EventID, def);
	}
	return true;
}

const PetEventDef *DefManager::getPetEventDef(int id)
{
	loadPetEventDef();
	return m_PetEventTable.GetRecord(id);
}

const HomeChunkInfoDef* DefManager::getHomeChunkDef(int chunx, int chunz)
{
	return GetHomeCsvManagerProxy()->getHomeChunkDef(chunx, chunz);
}

/*
		获取家园地图size(通过HomeChunkDef表配置的最大值最小值换算得来)
	*/
int DefManager::getHomeLandMaxX()
{ 
	return GetHomeCsvManagerProxy()->getHomeLandMaxX();
}
int DefManager::getHomeLandMaxZ()
{
	return GetHomeCsvManagerProxy()->getHomeLandMaxZ();
}

const HomeAnimalDef* DefManager::getHomeAnimalDef(int animalseedid)
{
	return GetHomeCsvManagerProxy()->getHomeAnimalDef(animalseedid);
}

int DefManager::getPetSkillPackDefNum()
{
	loadPetSkillPackDef();
	return m_PetSkillPackTable.GetRecordSize();
}

const PetSkillPackDef * DefManager::getPetSkillPackDefById(int id)
{
	loadPetSkillPackDef();
	return m_PetSkillPackTable.GetRecord(id);
}

int DefManager::getHomeDrawDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeDrawDefNum();
}

const HomeDrawDef *DefManager::getHomeDrawDefById(int seriesId)
{
	return GetHomeCsvManagerProxy()->getHomeDrawDefById(seriesId);
}

const HomeDrawDef *DefManager::getHomeDrawDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeDrawDefByIndex(index);
}

int DefManager::getHomeBuildDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeBuildDefNum();
}

const HomeBuildDef *DefManager::getHomeBuildDefById(int buildID)
{
	return GetHomeCsvManagerProxy()->getHomeBuildDefById(buildID);
}

const HomeBuildDef *DefManager::getHomeBuildDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeBuildDefByIndex(index);
}

int DefManager::getHomeTraderDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeTraderDefNum();
}

const HomeTraderDef *DefManager::getHomeTraderDefById(int tradeID)
{
	return GetHomeCsvManagerProxy()->getHomeTraderDefById(tradeID);
}

const HomeTraderDef *DefManager::getHomeTraderDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeTraderDefByIndex(index);
}


bool DefManager::loadPetSkillPackDef()
{
    static NoFreeFixedString filename = "PetSkillPackDef";
	//const char* filename = "PetSkillPackDef";
	if (hasLoaded(filename))
	{
		return true;
	}
	MultiLanCSVParser parser;
	char filepath[64] = {0};
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
 		LOG_WARNING("%s loading failed", filepath);
		return false;
	}
	parser.SetTitleLine(1);
	m_PetSkillPackTable.clear();
	int numLines = (int)parser.GetNumLines();
	char tmpname[64];
	for (int i = 2; i < numLines; ++i)
	{
		PetSkillPackDef def;
		def.PackID = parser[i]["PackID"].Int();
		for (int j = 0; j < MAX_PET_SKILL; j++)
		{
			sprintf(tmpname, "Skill%d_ID", j + 1);
			def.SkillID[j] = parser[i][tmpname].Int();
			sprintf(tmpname, "Skill%d_Lv", j + 1);
			def.SkillLv[j] = parser[i][tmpname].Int();
			sprintf(tmpname, "Skill%d_Prob", j + 1);
			def.SkillProb[j] = parser[i][tmpname].Int();
		}
		m_PetSkillPackTable.AddRecord(def.PackID, def);
	}
	return true;
}

int DefManager::getHomeItemUnlockDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeItemUnlockDefNum();
}

const HomeItemUnlockDef *DefManager::getHomeItemUnlockDefById(int itemid)
{
	return GetHomeCsvManagerProxy()->getHomeItemUnlockDefById(itemid);
}

const HomeItemUnlockDef *DefManager::getHomeItemUnlockDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeItemUnlockDefByIndex(index);
}


bool DefManager::getHomeRegionChunkPos(int regionType, int& chunkx, int& chunkz)
{
	return GetHomeCsvManagerProxy()->getHomeRegionChunkPos(regionType, chunkx, chunkz);
}

#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
void DefManager::getHomeChunksByChunkID(int regionType, int chunkfunction, std::vector<HomeChunkListInfo>& chunklists, std::vector<HomeChunkListInfo>* disableChunklist)
{
	GetHomeCsvManagerProxy()->getHomeChunksByChunkID(regionType, chunkfunction, chunklists, disableChunklist);
}
#endif

const HomeCropsSeedDef* DefManager::getHomeCropsSeedDef(int cropsSeedId)
{
	return GetHomeCsvManagerProxy()->getHomeCropsSeedDef(cropsSeedId);
}

const HomeLvDef *DefManager::getHomeLvDef(int id)
{
	return GetHomeCsvManagerProxy()->getHomeLvDef(id);
}

int DefManager::getHomeLevelDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeLevelDefNum();
}
int DefManager::getHomePackDef(int id)
{
	return GetHomeCsvManagerProxy()->getHomePackDef(id);
}

const AwardPackDef *DefManager::getHomePackAwardDefByIndex(int id, int index)
{
	return GetHomeCsvManagerProxy()->getHomePackAwardDefByIndex(id, index);
}

const HomeRanchDef* DefManager::getHomeRanchDef(int PastureLv)
{
	return GetHomeCsvManagerProxy()->getHomeRanchDef(PastureLv);
}

const HomeMysticalDef* DefManager::getHomeMystiaclDef(int MysticalLv)
{
	return GetHomeCsvManagerProxy()->getHomeMystiaclDef(MysticalLv);
}

int DefManager::getHomeTaskDefNum()
{
	return GetHomeCsvManagerProxy()->getHomeTaskDefNum();
}

HomeTaskDef *DefManager::getHomeTaskDef(int id)
{
	return GetHomeCsvManagerProxy()->getHomeTaskDef(id);
}

HomeTaskDef *DefManager::getHomeTaskDefByIndex(int index)
{
	return GetHomeCsvManagerProxy()->getHomeTaskDefByIndex(index);
}

ScoreDef *DefManager::getScoreDefByIndex(int index)
{
	return ScoreCsv::getInstance()->getScoreDefByIndex(index);
}

int DefManager::getScoreTableSize()
{
	loadScoreCSV();
	return (int)m_ScoreTable.size();
}

bool DefManager::loadScoreCSV()
{
    static NoFreeFixedString filename = "score";
	//const char* filename = "score";
	if (hasLoaded(filename))
	{
		return true;
	}
     MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	m_ScoreTable.clear();

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["Id"].Int();
		if (id == 0) continue;

		ScoreDef def;
		def.Id = id;
		def.Name = parser[i]["Name"].Str();
		def.Location = parser[i]["Location"].Str();

		//m_ScoreTable.AddRecord(id, def);
		m_ScoreTable.push_back(def);
	}

	return true;
}

const MusicalDef *DefManager::getMusicalDef(int id)
{
	return nullptr;
}
const MusicalDef *DefManager::getMusicalDefByCode(int code)
{
	return nullptr;
}
bool DefManager::loadInstrumentDef()
{
    static NoFreeFixedString filename = "MidiAllInstruments";
	//const char* filename = "MidiAllInstruments";
	if (hasLoaded(filename))
	{
		return true;
	}
     MultiLanCSVParser parser;
	char filepath[64] = { 0 };
	m_CsvLoadConfig->getPath(filename, filepath);
	if (!parser.Load(filepath, m_CsvLoadConfig->withUTF8()))
	{
		LOG_WARNING("%s loading failed", filepath);
		return false;
	}

	parser.SetTitleLine(1);
	m_MidiInstrumentTable.clear();

	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{

		InstrumentDef instrumentDef;
		instrumentDef.ID = parser[i]["id"].Int();
		instrumentDef.Name = parser[i]["Name"].Str();
		instrumentDef.Icon = parser[i]["Icon"].Str();
		m_MidiInstrumentTable[instrumentDef.ID] = instrumentDef;
	}

	return true;
}

InstrumentDef *DefManager::getInstrumentDef(int ID)
{
	loadInstrumentDef();
	auto iter = m_MidiInstrumentTable.find(ID);
	if (iter == m_MidiInstrumentTable.end())
	{
		return NULL;
	}
	return &iter->second;
}

int DefManager::GetDyeColor(int id)
{
	return ColorMixDefCsv::getInstance()->getColor(id);
}

int DefManager::getBotConversationsDefNum()
{
	return BotConversationsDefCsv::getInstance()->getBotConversationsDefNum();
}

const BotConversationsDef* DefManager::getBotConversationsDefIndex(unsigned int index)
{
	return BotConversationsDefCsv::getInstance()->getBotConversationsDefByIndex(index);
}

const BotConversationsDef* DefManager::getBotConversationsDef(unsigned int id)
{
	return BotConversationsDefCsv::getInstance()->get(id);
}

ENCHANT_TYPE DefManager::Name2EnchantTypeWrapper(const char* name)
{
	return DefManager::Name2EnchantType(name);
}

const SurviveObjectiveDef* DefManager::getSurviveObjectiveDef(unsigned int id)
{
	return SurviveObjectiveDefCsv::getInstance()->get(id);
}

int DefManager::getSurviveObjectiveDefCsvNum()
{
	return SurviveObjectiveDefCsv::getInstance()->getSurviveObjectiveDefCsvNum();
}

const SurviveObjectiveDef* DefManager::getSurviveObjectiveDefByIndex(int index)
{
	return SurviveObjectiveDefCsv::getInstance()->getSurviveObjectiveDefByIndex(index);
}

int DefManager::getSurviveObjectiveFrontTaskNum(unsigned int taskID)
{
	return SurviveObjectiveDefCsv::getInstance()->getFrontTaskNum(taskID);
}

int DefManager::getSurviveObjectiveFrontSize()
{
	return SurviveObjectiveDefCsv::getInstance()->getFrontSize();
}

const SurviveTaskDef* DefManager::getSurviveTaskDef(unsigned int taskID)
{
    return SurviveTaskDefCsv::getInstance()->get(taskID);
}

std::vector<int> DefManager::getSurviveTaskListByObjectiveID(int objectiveID) const
{
	return SurviveTaskDefCsv::getInstance()->getTaskListByObjectiveID(objectiveID);
}

int DefManager::getSurviveTaskDefCsvNum()
{
	return SurviveTaskDefCsv::getInstance()->getSurviveTaskDefCsvNum();
}

int DefManager::getSurviveTaskFrontSize()
{
	return SurviveTaskDefCsv::getInstance()->getFrontSize();
}

const SurviveTaskDef* DefManager::getSurviveTaskDefByIndex(int index)
{
	return SurviveTaskDefCsv::getInstance()->getSurviveTaskDefByIndex(index);
}

void DefManager::ImportTaskDef(std::unordered_map<int, SurviveTaskDef*>& in)
{
	SurviveTaskDefCsv::getInstance()->ImportTaskDef(in);
}

void DefManager::ExportTaskObjective(std::unordered_map<int, std::vector<int>>& out)
{
	SurviveTaskDefCsv::getInstance()->ExportTaskObjective(out);
}

void DefManager::ImportTaskObjective(std::unordered_map<int, std::vector<int>>& in)
{
	SurviveTaskDefCsv::getInstance()->ImportTaskObjective(in);
}

void DefManager::Import(std::unordered_map<int, SurviveObjectiveDef*>& in)
{
	SurviveObjectiveDefCsv::getInstance()->Import(in);
}

void DefManager::onParseTask(CSVParser& parser, std::unordered_map<int, SurviveTaskDef*>& out)
{
	SurviveTaskDefCsv::getInstance()->onModParse(parser, out);
}

int DefManager::getFrontTaskNum(int taskid)
{
	return SurviveTaskDefCsv::getInstance()->getFrontTaskNum(taskid);
}

void DefManager::onParseObjective(CSVParser& parser, std::unordered_map<int, SurviveObjectiveDef*>& out)
{
	SurviveObjectiveDefCsv::getInstance()->onModParse(parser, out);
}

void DefManager::SetClientActorPetTamedOwnerUin(ClientMob* mob)
{
    ClientActorPet* pActorPet = dynamic_cast<ClientActorPet*>(mob);
    if (pActorPet != nullptr)
    {
        pActorPet->resetNeedClear(); //家园宠物设计有问题
        pActorPet->setTamedOwnerUin(GetClientAccountMgr().getUin());
    }
}

DevUIResourceDef* DefManager::GetDevUIResourceDef(int id, bool takeplace)
{
	return DevUIResourceCsv::getInstance()->get(id, takeplace);
}

UgcModelDef* DefManager::getUgcModelDef(int id)
{
	return UgcModelCsv::getInstance()->get(id);
}

UgcMaterialDef* DefManager::getUgcMaterialDef(int id)
{
	return UgcMaterialCsv::getInstance()->get(id);
}

CustomGunDef* DefManager::addCustomGunDef(int id)
{
	return CustomGunDefCsv::getInstance()->add(id);
}

CustomGunDef* DefManager::getCustomGunDef(int id)
{
	return CustomGunDefCsv::getInstance()->get(id);
}

void DefManager::removeCustomGunDef(int id)
{
	CustomGunDefCsv::getInstance()->remove(id);
}

ItemStatusDef* DefManager::addItemStatusDef(int id)
{
	return ItemStatusDefCsv::getInstance()->add(id);
}

ItemStatusDef* DefManager::getItemStatusDef(int id)
{
	return ItemStatusDefCsv::getInstance()->get(id);
}

bool DefManager::IsShowUseBtnForItemRightUse(int id)
{
	ItemStatusDef* def = getItemStatusDef(id);
	if (def && def->GetNum() > 0)
	{
		return true;
	}
	return false;
}

void DefManager::removeItemStatusDef(int id)
{
	ItemStatusDefCsv::getInstance()->remove(id);
}

CustomPrefabDescInfo* DefManager::getCustomPrefabDescInfo(std::string name)
{
	return CustomPrefabInfoCsv::getInstance()->get(name);
}

CustomPrefabDescInfo* DefManager::addCustomPrefabDescInfo(std::string name)
{
	return CustomPrefabInfoCsv::getInstance()->add(name);
}


int DefManager::setModMainTask(const std::string& taskFilePath, const std::string& objectFilePath, 
	std::unordered_map<int, std::vector<int>>& m_TaskObjectiveTableCache,
	std::unordered_map<int, SurviveTaskDef*>& m_TaskDefTableCache, 
	std::unordered_map<int, SurviveObjectiveDef*>& m_ObjectiveDefTableCache)
{
	//加载文件
	CSVParser taskCSVPaser;
	std::string fileContent;
	std::string desc;
	std::string SurviveFileName = "SurviveTaskDef.csv";
	bool ret = UgcAssetMgr::GetInstancePtr()->ReadString(SurviveFileName, (int)UgcAssetType::STRING_CONFIG, fileContent, desc, taskFilePath);
	if (ret)
	{
		UgcAssetMgr::GetInstancePtr()->ReleaseCache(SurviveFileName);
		if (!taskCSVPaser.LoadBuffer((char*)fileContent.c_str(), fileContent.size(), true))
		{
			assert(false);
			return 0;
		}
	}
	else if (!taskCSVPaser.Load(taskFilePath, true))
	{
		assert(false);
		return 0;
	}

	//标题行
	taskCSVPaser.SetTitleLine(1);
	GetDefManagerProxy()->ExportTaskObjective(m_TaskObjectiveTableCache);
	GetDefManagerProxy()->onParseTask(taskCSVPaser, m_TaskDefTableCache);

	//替换目标配置
	CSVParser objectCSVPaser;
	std::string fileContent2;
	std::string desc2;
	std::string SurviveObjectiveFileName = "SurviveObjectiveDef.csv";
	bool ret2 = UgcAssetMgr::GetInstancePtr()->ReadString(SurviveObjectiveFileName, (int)UgcAssetType::STRING_CONFIG, fileContent2, desc2, objectFilePath);
	if (ret2)
	{
		UgcAssetMgr::GetInstancePtr()->ReleaseCache(SurviveObjectiveFileName);
		if (!objectCSVPaser.LoadBuffer((char*)fileContent2.c_str(), fileContent2.size(), true))
		{
			assert(false);
			return 0;
		}
	}
	else if (!objectCSVPaser.Load(objectFilePath, true))
	{
		assert(false);
		return 0;
	}

	//标题行
	objectCSVPaser.SetTitleLine(1);
	GetDefManagerProxy()->onParseObjective(objectCSVPaser, m_ObjectiveDefTableCache);
	return 1;
}

int DefManager::ModParseBiomeGroupDef(const std::string& filePath, std::unordered_map<int, BiomeGroupDef*>& cache)
{
	//加载文件
	CSVParser csvPaser;
	std::string fileContent;
	std::string desc;
	std::string FileName = "biomegroupdef.csv";
	bool ret = UgcAssetMgr::GetInstancePtr()->ReadString(FileName, (int)UgcAssetType::STRING_CONFIG, fileContent, desc, filePath);
	if (ret)
	{
		UgcAssetMgr::GetInstancePtr()->ReleaseCache(FileName);
		if (!csvPaser.LoadBuffer((char*)fileContent.c_str(), fileContent.size(), true))
		{
			assert(false);
			return 0;
		}
	}
	else if (!csvPaser.Load(filePath, true))
	{
		assert(false);
		return 0;
	}

	BiomeGroupDefCsv::getInstance()->onModParse(csvPaser, cache);
	return 1;
}

void DefManager::ResetBiomeGroupDefFromCache(std::unordered_map<int, BiomeGroupDef*>& cache)
{
	BiomeGroupDefCsv::getInstance()->resetFromCache(cache);
}

const EquipGroupDef* DefManager::getEquipGroupDef(int id)
{
	return EquipGroupDefCsv::getInstance()->get(id);
}

OperateUIData* DefManager::getOperateUIData(int id)
{
	return OperateUIDataCsv::getInstance()->get(id);
}

#endif

