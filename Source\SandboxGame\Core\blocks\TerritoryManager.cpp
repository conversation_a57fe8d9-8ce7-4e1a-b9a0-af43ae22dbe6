#include "TerritoryManager.h"
#include "container_territory.h"

// 初始化单例指针
TerritoryManager* TerritoryManager::s_instance = nullptr;

TerritoryManager* TerritoryManager::GetInstance()
{
    if (!s_instance)
    {
        s_instance = new TerritoryManager();
    }
    return s_instance;
}

void TerritoryManager::DestroyInstance()
{
    if (s_instance)
    {
        delete s_instance;
        s_instance = nullptr;
    }
}

TerritoryManager::TerritoryManager()
    : m_isRegisteredWithWorld(false)  // 添加标志跟踪是否已经向World注册
{
}

TerritoryManager::~TerritoryManager()
{
    Clear();
}

void TerritoryManager::RegisterTerritory(const WCoord& blockPos, TerritoryContainer* container)
{
    if (container)
    {
        m_territories[blockPos] = container;
    }
}

void TerritoryManager::UnregisterTerritory(const WCoord& blockPos)
{
    auto it = m_territories.find(blockPos);
    if (it != m_territories.end())
    {
        m_territories.erase(it);
    }
}

bool TerritoryManager::CanBuildAtPosition(const Rainbow::Vector3f& point, long long playerUin) const
{
    // 如果点不在任何领地内，可以建造
    if (!IsPointInAnyTerritory(point))
    {
        return true;
    }
    
    // 如果在领地内，只有领地所有者可以建造
    return IsPointInPlayerTerritory(point, playerUin);
}

bool TerritoryManager::IsPointInAnyTerritory(const Rainbow::Vector3f& point) const
{
    for (const auto& pair : m_territories)
    {
        if (pair.second)
        {
            TerritoryBoundingBox bounds = pair.second->GetTerritoryBounds();
            if (bounds.IsPointInside(point))
            {
                return true;
            }
        }
    }
    return false;
}

std::vector<TerritoryContainer*> TerritoryManager::GetTerritoriesContainingPoint(const Rainbow::Vector3f& point) const
{
    std::vector<TerritoryContainer*> result;
    
    for (const auto& pair : m_territories)
    {
        if (pair.second)
        {
            TerritoryBoundingBox bounds = pair.second->GetTerritoryBounds();
            if (bounds.IsPointInside(point))
            {
                result.push_back(pair.second);
            }
        }
    }
    
    return result;
}

bool TerritoryManager::IsPointInPlayerTerritory(const Rainbow::Vector3f& point, long long playerUin) const
{
    for (const auto& pair : m_territories)
    {
        if (pair.second && pair.second->GetUin() == playerUin)
        {
            TerritoryBoundingBox bounds = pair.second->GetTerritoryBounds();
            if (bounds.IsPointInside(point))
            {
                return true;
            }
        }
    }
    return false;
}

void TerritoryManager::DebugDrawAllTerritories(World* pworld, const Rainbow::ColourValue& color) const
{
    if (!pworld) return;
    
    for (const auto& pair : m_territories)
    {
        if (pair.second)
        {
            TerritoryBoundingBox bounds = pair.second->GetTerritoryBounds();
            bounds.DrawWireframe(pworld, color);
        }
    }
}

void TerritoryManager::Clear()
{
    m_territories.clear();
}

void TerritoryManager::Tick()
{
    // Update all registered territories
    for (auto& pair : m_territories)
    {
        if (pair.second)
        {
            pair.second->Tick();
        }
    }
    
    // Additional periodic territory management logic can be added here
} 

// 添加一个方法来设置World的建造权限回调
void TerritoryManager::RegisterWithWorld(World* pWorld)
{
    if (!pWorld || m_isRegisteredWithWorld) return;  // 如果已经注册过，直接返回
    
    // 使用lambda表达式设置回调
    pWorld->SetBuildPermissionCallback([this](const Rainbow::Vector3f& point, unsigned int playerUin) -> bool {
        // 调用TerritoryManager自己的方法来检查建造权限
        return this->CanBuildAtPosition(point, playerUin);
    });
    
    m_isRegisteredWithWorld = true;  // 标记为已注册
}

TerritoryContainer* TerritoryManager::GetTerritoryContainingBlock(const Rainbow::Vector3f& point) const
{
    // 遍历所有领地，检查该方块是否在领地范围内
    for (const auto& pair : m_territories)
    {
        if (pair.second)
        {
            TerritoryBoundingBox bounds = pair.second->GetTerritoryBounds();
            if (bounds.IsPointInside(point))
            {
                return pair.second;
            }
        }
    }
    return nullptr;
}