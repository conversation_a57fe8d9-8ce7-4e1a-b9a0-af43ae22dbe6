#include "ActorLocoMotion.h"
#include "world.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "ActorAttrib.h"
//#include "block.h"
#include "chunk.h"
#include "ClientMob.h"
#include "ClientActorManager.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "VehicleWorld.h"
#include "BlockMaterial.h"
#include "BlockMaterialMgr.h"
#include "BlockFluid.h"
#include "PlayerControl.h"
#include "EffectManager.h"
#include "LuaInterfaceProxy.h"
#include "ActorBall.h"
#include "BlockCollider.h"
#include "ClientActorHook.h"

#include "PlayerLocoMotion.h"

#include "ObserverEventManager.h"
#include "PlayerAttrib.h"
#include "ActorVehicleAssemble.h"
#include "RiddenComponent.h"
#include "ActorBindVehicle.h"
#include "CarryComponent.h"
#include "BindActorComponent.h"
#include "FireBurnComponent.h"
#include "AttackedComponent.h"
#include "ClientActorFuncWrapper.h"
#include "FallComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "MobAttrib.h"
#include "ThermalSpringManager.h"
#include "IWorldConfigProxy.h"
#include "ClientItem.h"
#include "DieComponent.h"
#include "Math/LegacyBounding.h"
#include "physicsLocoMotion/PhysicsLocoMotion.h"
#include "ActorVehicleAssemble.h"
#include "ActorBody.h"
#include "FindComponent.h"
#include "ActorVortex.h"
#include "ChargeJumpComponent.h"
#include "VehicleMgr.h"
#include "minisystem/base/Plugin.h"
//#include "Graphics/LegacyGlobalShaderParam.h"

using namespace MINIW;
using namespace MNSandbox;
const float MOTION2VELOCITY = 10.0f;
IMPLEMENT_COMPONENTCLASS(ActorLocoMotion)

void ActorLocoMotion::CheckMotionValid(Vector3f &motion)
{
	const int MAX_MOTION = (int)(100*BLOCK_FSIZE);
	if(((int)motion.x)<-MAX_MOTION || ((int)motion.x)>MAX_MOTION) motion.x = 0;
	if(((int)motion.z)<-MAX_MOTION || ((int)motion.z)>MAX_MOTION) motion.z = 0;
	if(((int)motion.y)<-MAX_MOTION || ((int)motion.y)>MAX_MOTION) motion.y = 0;
}

ActorLocoMotion::ActorLocoMotion()
	: m_bIsAIJumping(false), m_BoundSize(int(BLOCK_SIZE * 0.4f)), m_BoundHeight(int(BLOCK_SIZE * 1.7f)),
	m_HitBoundWidth(int(BLOCK_SIZE * 0.4f)), m_HitBoundHeight(int(BLOCK_SIZE * 1.7f)),
	m_HitBoundThickness(int(BLOCK_SIZE * 0.4f)), m_Position(0, 0, 0), m_OldPosition(0, 0, 0),
	m_TickPosition(), m_Motion(0, 0, 0), m_bMoving(false),
	m_AccumLeftMotion(0, 0, 0), m_pWorld(NULL),  m_CollidedVertically(false), m_bIsBlocked(false),
	m_RotateQuat(0.0f, 0.0f, 0.0f, 1.0f), m_MovementAbility(Movement_Ability_Max), m_UseNoClipMove(false), m_ExtraMotion(0, 0, 0), m_ActorRoleController(nullptr)
{
	m_OnLadder = false;
	m_OnVehicle = false;
	m_IsEnchClimb = false;
	m_IsEnchFall = false;
	m_OnGround = false;
	m_OnBlockFrontEdge = false;
	m_OnBlockLeftEdge = false;
	m_OnBlockRightEdge = false;
	m_FrontBlockLeftIsEmpty = false;
	m_FrontBlockRightIsEmpty = false;
	m_CanDriftWithSkateboard = false;
	m_CollidedHorizontally = m_InLava = m_InWater = m_InHoney = m_InHurt = m_InDriftsand = m_InWaterReal = false;
	m_InRun = false;
	m_yOffset = 0;
	m_RotateYaw = 0.0f;
	m_RotationPitch = 0.0f;
	m_PrevRotateYaw = 0;
	m_PrevRotatePitch = 0;
	m_JumpMovementFactor = 2.0f;
	setNoClip(false);
	m_isJumping = false;
	m_JumpingTicks = 0;
	m_MovementAbility.SetFlag(Movement_Water, true);
	//m_DisableWaterMovement = false;

	m_DistanceWalked = m_NextStepDistance = 0;

	m_NavigationRotateYaw = -1.0f;
	m_NavigationRotationPitch = -1.0f;
	m_IsInBoatWaterMask = false;

	m_RotateRoll = 0.0f;
	m_fSlopeFallSpeed = 0.0f;

	resetVortexMotion();

	m_CurMovementState = MovementState::Other;
	CreateEvent2();
	/*if(g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich())
		m_TickPosition.m_PosStabilizer.SetMaxCount(1);*/
}

ActorLocoMotion::~ActorLocoMotion()
{
	m_OwnerActor = nullptr;
	DestroyEvent2();
}

void ActorLocoMotion::leaveWorld()
{
	for (int i = 0; i < (int)m_preCheckPhy.size(); i++)
	{
		if (m_pWorld)
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
	}
	m_preCheckPhy.clear();
}

void ActorLocoMotion::CreateEvent2()
{
	typedef ListenerFunctionRef<int, float> ListenerBlockCatapult;
	m_listenerBlockCatapulte = SANDBOX_NEW(ListenerBlockCatapult, [&](int placedir, float power) -> void {
		
		ClientActor* owneractor = this->getOwnerActor();
		//if (actors[i]->getLocoMotion())
		{
			// 这里判断是否为物理actor
			// 标准：标准质量定1000g，
			// y轴角度固定45度
			// 注：PhysicsActorDef->Mass 单位：g
			PhysicsLocoMotion* physxMotion = dynamic_cast<PhysicsLocoMotion*>(this);
			VehicleAssembleLocoMotion* vehicleLoco = dynamic_cast<VehicleAssembleLocoMotion*>(this);
			if (physxMotion && physxMotion->m_PhysActor)
			{
				float momentum = 7500.0f;
				int mass = 0;
				if (vehicleLoco)
					mass = owneractor->getMass();
				else
				{
					const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(owneractor->GetItemId());
					if (physicsActorDef != NULL)
					{
						mass = physicsActorDef->Mass;
					}
				}

				if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig != NULL && mass > 0)
				{
					momentum = GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.catapult_velocity / mass;
				}
				momentum = momentum < 7500.0f ? momentum : 7500.0f;

				static float yaw[4] = { 90.0f, -90.0f, 0, 180.0f };
				Rainbow::Vector3f flyDir = Yaw2FowardDir(yaw[placedir]) + Rainbow::Vector3f(0, Rainbow::SinByAngle(45.0f), 0);
				Rainbow::Vector3f motion = flyDir * momentum * (power / 15.0f);
				physxMotion->m_PhysActor->SetLinearVelocity(motion);
			}
			else
			{
				static float yaw[4] = { 90.0f, -90.0f, 0, 180.0f };
				Rainbow::Vector3f motion = Yaw2FowardDir(yaw[placedir]) * 400.0f * (power / 15.0f);

				Rainbow::Vector3f pushDir = Yaw2FowardDir(yaw[placedir]);

				Rainbow::Vector3f  amotion = this->m_Motion;

				motion.y += 300.0f * (power / 15.0f);
				owneractor->setMotionChange(motion);

				Rainbow::Vector3f  bmotion = this->m_Motion;

				WCoord actorPos = owneractor->getPosition();

				LOG_INFO("pushDir: %f %f %f motion: %f %f %f  amotion: %f %f %f bmotion: %f %f %f actorPos: %d %d %d",
					pushDir.x, pushDir.y, pushDir.z, motion.x, motion.y, motion.z,
					amotion.x, amotion.y, amotion.z, bmotion.x, bmotion.y, bmotion.z,
					actorPos.x, actorPos.y, actorPos.z);

				ClientPlayer* player = dynamic_cast<ClientPlayer*>(owneractor);
				if (player)
				{
					player->resetCheckClientInputVariable();
					player->setGuardSafeTick(12000);
				}
			}

		}
	});
	Event2().Subscribe("BlockCatapult_LocomotionNotify", m_listenerBlockCatapulte);
}
void ActorLocoMotion::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerBlockCatapulte);
}
void ActorLocoMotion::setPosition(int x, int y, int z)
{
	setPosition(WCoord(x, y, z));
}

void ActorLocoMotion::setPosition(const WCoord pos)
{
	m_OldPosition = m_Position;
	m_Position = pos;

	syncPhysActorPosByLocPos();
}
void ActorLocoMotion::setJumping(bool b)
{

	if (m_isJumping == b)
		return;
	//if (m_OwnerActor)
	//{
	//	ClientMob* mob = m_OwnerActor->ToCast<ClientMob>();
	//	if (mob)
	//	{
	//		int k = 9;
	//	}
	//}
	//
	m_isJumping = b;
}


void ActorLocoMotion::setMoveDir(const Rainbow::Vector3f &dir)
{
	float yaw;
	Direction2PitchYaw(&yaw, NULL, dir);
	float limitAngle = m_OwnerActor->getRotaionLimitAngle();
	m_RotateYaw = LimitAngle(m_RotateYaw, yaw, limitAngle);
	ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
	if (player && player->isNewMoveSyncSwitchOn())
	{
		player->setMoveControlYaw(m_RotateYaw);
	}
}

void ActorLocoMotion::setRotateRaw(float r)
{
	m_RotateYaw = r;
}

void ActorLocoMotion::setRotatePitch(float p)
{
	m_RotationPitch = p;
}

void ActorLocoMotion::onEnterWorld(World *pworld)
{
	assert(pworld != NULL);
	m_pWorld = pworld;

	m_CollidedHorizontally = false;
	m_CollidedVertically = false;

	//??冗余代码, marked by David 20230807
	//CollideAABB box;
	//m_OwnerActor->getCollideBox(box);
	//??

	//m_OnGround = (m_pWorld->moveBox(box, WCoord(0,-1,0)).y == 0);
	setOnGround(false);
}

void ActorLocoMotion::gotoPosition(const WCoord &pos, float yaw, float pitch)
{
	m_OldPosition = m_Position;
	//m_Position = pos;
	setPosition(pos.x, pos.y, pos.z);
	m_TickPosition.clear();
	m_TickPosition.beginTick(pos);
	
	m_RotateYaw = yaw;
	m_RotationPitch = pitch;

	setOnGround(false);
	m_InLava = m_InWater = m_InHoney = m_InHurt = m_InDriftsand = false;
	setInWater(false);

	if (getOwnerActor()) {
		if (getOwnerActor()->getBody()) {
			getOwnerActor()->getBody()->resetPos();
		}
	}
}

void ActorLocoMotion::initPosition(const WCoord& pos)
{

}

void ActorLocoMotion::onDie()
{
	gotoPosition(m_Position, m_RotateYaw, m_RotationPitch);
}

void ActorLocoMotion::updateRidden()
{
	auto RidComp = m_OwnerActor->getRiddenComponent();
	if(RidComp && RidComp->isRiding())
	{
		m_Motion.Set(0,0,0);
		
		ClientActor *riding = RidComp->getRidingActor();
		if(riding)
		{
			WCoord pos(0,0,0);
			auto ridingComp = riding->getRiddenComponent();
			if (ridingComp)
			{
				pos = ridingComp->getRiderPosition(m_OwnerActor);
			}
			if (pos.y > -10000000)
			setPosition(pos.x, pos.y, pos.z);
		}
	}
	auto vehicleComponent = m_OwnerActor->m_pActorBindVehicle;
	if (vehicleComponent)
	{
		vehicleComponent->updatePosByVehicle();
	}

	auto CarryComp = m_OwnerActor->getCarryComponent();
	if (CarryComp && CarryComp->isCarried())
	{
		m_Motion.Set(0, 0, 0);
		auto *carried = CarryComp->getCarriedActor();
		if (carried)
		{
			auto CarriedComp = carried->getCarryComponent();
			if (CarriedComp)
			{
				WCoord pos = CarriedComp->getCarryingBindPos();
				if (pos.y > -10000000)
					setPosition(pos.x, pos.y, pos.z);
			}

		}	
	}
}

void ActorLocoMotion::updateBindActor()
{
	auto bindAComponent = m_OwnerActor->m_pBindActorComponent;
	if (bindAComponent)
	{
		bindAComponent->updatePosByBindActor();
	}
}

void ActorLocoMotion::prepareTick()
{
	m_TickPosition.beginTick(m_Position);
	m_PrevRotateYaw = m_RotateYaw;
	m_PrevRotatePitch = m_RotationPitch;

	if (m_OwnerActor->IsObject() && m_pWorld && m_pWorld->isRemoteMode())
	{
		m_PrevRotateQuat = m_RotateQuat;
	}

	//if (m_OwnerActor)
	//	m_OwnerActor->prepareTick();
}

void ActorLocoMotion::tick()
{
	if (m_OwnerActor->IsObject() && m_pWorld && m_pWorld->isRemoteMode())
	{
		if (m_PosRotationIncrements > 0)
		{
			m_Position = m_Position + (m_ServerPos - m_Position) / m_PosRotationIncrements;
			//m_RotateQuat.slerp(m_RotateQuat, m_ServerRot, 1.0f/m_PosRotationIncrements);
			m_RotateQuat = Slerp(m_RotateQuat, m_ServerRot, 1.0f / m_PosRotationIncrements);

			Direction2PitchYaw(&m_RotateYaw, &m_RotationPitch, -m_RotateQuat.GetAxisZ());

			m_PosRotationIncrements--;
		}
		return;
	}

	if(m_OwnerActor->needClear()) return;

	/*
    if(isOnThicket()){//荆棘上不能跳
        setJumping(false);
        m_OwnerActor->setCanFly(false);
        m_OwnerActor->setFlying(false);
        if(m_Motion.y > 0)
            m_Motion.y = 0.0f;
    }*/

	updateRidden();
	updateBindActor();
	if (!m_pWorld){
		return;
	}
	handleBlockMovement();
	handleWaterMovement();
	//handleHoneyMovement();
	handleDriftsandMovement();
	//判断脚下是否站在带伤害的方块上
	m_InHurt = IsHurtBlock(m_pWorld->getBlockID(CoordDivBlock(getPosition())));
	if(m_InLava && !m_OwnerActor->isDead())
	{
		ActorAttrib *attrib = m_OwnerActor->getAttrib();
		if(attrib)
		{
			if (attrib->immuneToFire() <= 0)
			{
				auto FireBurnComp = m_OwnerActor->sureFireBurnComponent();
				if (FireBurnComp)
				{
					FireBurnComp->setFire(100, 1);
				}

			}
			if (attrib->immuneToFire() <= 1)
			{
				auto component = m_OwnerActor->getAttackedComponent();
				if (component)
				{
					component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); //modify by null,岩浆的伤害翻5倍
				}
			}
		}
		//m_OwnerActor->m_FallDistance *= 0.5f;
		auto functionWrapper = m_OwnerActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
		}
	}
	if (m_InPoison && !m_OwnerActor->isDead())
	{
		ActorAttrib *attrib = m_OwnerActor->getAttrib();
		if (attrib && !attrib->hasImmuneType(attrib->getImmuneTypeByAttackType(ATTACK_POISON)))
		{
			auto component = m_OwnerActor->getAttackedComponent();
			if (component)
			{
				component->attackedFromType_Base(ATTACK_POISON, 1 * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv);
			}
		}
	}
	if (handleVenomMovement() && !m_OwnerActor->isDead()) {

		LivingAttrib* attrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());
		if (attrib == NULL || m_OwnerActor->getDefID() == 3416) return;
		if (!attrib->hasBuff(1016))
		{
			attrib->addBuff(1016, 1);
		}
	}
	if(!m_pWorld->isRemoteMode() && m_Position.y < -64*BLOCK_SIZE && !m_OwnerActor->isDead())
	{
		m_OwnerActor->kill();
	}

	WCoord coordBlock = CoordDivBlock(m_Position);
	WCoord m_OldBlock= CoordDivBlock(m_OldPosition);
	if (m_OldBlock != coordBlock)
	{
		m_OldPosition = m_Position;
		WCoord curPos;
		int blockid;
		WorldBells* container;
		for (int i = 0; i < 5; i++)
		{
			curPos = coordBlock + WCoord(0, i, 0);
			blockid = m_pWorld->getBlockID(curPos);
			if (blockid == 1243)
			{
				container = dynamic_cast<WorldBells*>(m_pWorld->getContainerMgr()->getContainer(curPos));
				if (container)
				{
					container->playAnim(100923);
					container->playSound(m_pWorld, ("item.1243.aeolianbells1"));
				}
			}
		}
		if (m_OnGround)
		{
			WCoord pos = CoordDivBlock(m_Position);
			BlockMaterial* mtl = m_pWorld->getBlockMaterial(pos);
			if (mtl)
			{
				mtl->DoOnActorMoving(m_pWorld, pos, m_OwnerActor);
			}
		}


	}
}

void ActorLocoMotion::update(float dtime)
{
	m_TickPosition.update(dtime);

	if (m_OwnerActor->IsObject() && m_pWorld && m_pWorld->isRemoteMode())
	{
		m_UpdateRot = Slerp(m_PrevRotateQuat, m_RotateQuat, m_TickPosition.m_TickOffsetTime / GAME_TICK_TIME);
		m_UpdatePos = getFramePosition();
	}
}

void ActorLocoMotion::updatePosition(const WCoord &pos)
{
	m_OldPosition = m_Position;
	m_Position = pos;
	syncPhysActorPosByLocPos();
}

float ActorLocoMotion::getBrightness()
{
	int y = m_Position.y - m_yOffset + int(m_BoundHeight*2.0f/3.0f);

	WCoord pos = CoordDivBlock(WCoord(m_Position.x, y, m_Position.z));
	if (m_pWorld)
	{
		return m_pWorld->getBlockBright(pos);
	}
	return 0.0f;
}

void ActorLocoMotion::getCollideBox(CollideAABB &box)
{
	box.dim = WCoord(m_BoundSize, m_BoundHeight, m_BoundSize);
	box.pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
}

void ActorLocoMotion::getCheatCollideBox(CollideAABB &box, const WCoord &pos)
{
	box.dim = WCoord(m_BoundSize/2, m_BoundHeight/2, m_BoundSize/2);
	box.pos = pos - WCoord(box.dim.x/2, m_yOffset - m_BoundHeight / 4, box.dim.z/2);
}

void ActorLocoMotion::getCollideBox(Rainbow::AABB& box) {
	auto& pos = getPosition();
	box.m_Center.x = float(pos.x);
	box.m_Center.z = float(pos.z);
	box.m_Center.y = float(pos.y + ((m_BoundHeight - m_yOffset) >> 1));
	box.m_Extent = Vector3f(float(m_BoundSize >> 1), float(m_BoundHeight >> 1), float(m_BoundSize >> 1));
}

void ActorLocoMotion::getCollideBoxMatchPhysBox(CollideAABB& box)
{
	getCollideBox(box);
}

void ActorLocoMotion::getBoundingBoxLocal(Rainbow::BoxSphereBound& bound)
{
	bound.m_Center = Vector3f(0, m_yOffset, 0);
	bound.m_Extent = Vector3f(m_BoundSize, m_BoundHeight, m_BoundSize) / 2.0f;
	bound.m_Radius = bound.m_Extent.Length();
}
void ActorLocoMotion::getHitCollideBox(Rainbow::AABB& box) {
	int iBoundZ = m_OwnerActor->IsObject() ? m_HitBoundThickness : m_HitBoundWidth;
	auto& pos = getPosition();
	box.m_Center.x = float(pos.x);
	box.m_Center.z = float(pos.z);
	box.m_Center.y = float(pos.y + ((m_HitBoundHeight - m_yOffset) >> 1));
	box.m_Extent = Vector3f(float(m_HitBoundWidth >> 1), float(m_HitBoundHeight >> 1), float(iBoundZ >> 1));
}

void ActorLocoMotion::setBaseCollideBoxs(const std::vector<TypeCollideAABB>& boxs, const std::vector<TypeCollideAABB>& sneakboxs)
{
	m_BaseCollideBoxs = boxs;
	m_SneakCollideBoxs = sneakboxs;
}


CollideAABB s_TestCollide;

namespace GMParam
{
	extern bool UpdateMonsterCollide;
}

std::vector<TypeCollideAABB>& ActorLocoMotion::getOrInitBaseCollide()
{
	int iBoundZ = m_OwnerActor->IsObject() ? m_HitBoundThickness : m_HitBoundWidth;
	if (m_BaseCollideBoxs.size() == 0 || GMParam::UpdateMonsterCollide)
	{
		// 客机中的mob没有调用mobinit 这里重新检测一次
		auto owner = getOwnerActor();
		auto mob = dynamic_cast<ClientMob*>(owner);
		if (mob)
		{
			auto mobdef = mob->getMonsterDef();
			if (mobdef)
			{
				setBaseCollideBoxs(mobdef->CollideBoxs, mobdef->CollideBoxs);
			}
		}
		if (m_BaseCollideBoxs.size() == 0)
		{
			WCoord startpos = WCoord(0, -m_yOffset, 0);
			TypeCollideAABB box1;
			box1.box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight * 0.35, iBoundZ);
			box1.box.pos = startpos;
			box1.part = "downbody";
			m_BaseCollideBoxs.push_back(box1);
			TypeCollideAABB box2;
			box2.box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight * 0.35, iBoundZ);
			box2.box.pos = startpos + WCoord(0, m_HitBoundHeight * 0.35, 0);
			box2.part = "upbody";
			m_BaseCollideBoxs.push_back(box2);
			TypeCollideAABB box3;
			box3.box.dim = WCoord(m_HitBoundWidth * 0.5f, m_HitBoundHeight * 0.2f, iBoundZ * 0.5f);
			box3.box.pos = startpos + WCoord(m_HitBoundWidth * 0.25f, m_HitBoundHeight * 0.7f, iBoundZ * 0.25f);
			box3.part = "head";
			m_BaseCollideBoxs.push_back(box3);
		}
	}
	return m_BaseCollideBoxs;
}
void ActorLocoMotion::getMultiTypeCollidBoxs(std::vector<TypeCollideAABB>& boxs)
{
	getOrInitBaseCollide();

	// TODO 躺下及潜行需要额外处理
	int iBoundZ = m_OwnerActor->IsObject() ? m_HitBoundThickness : m_HitBoundWidth;
	if (m_OwnerActor->getFlagBit(ACTORFLAG_AI_SLEEP) || m_OwnerActor->getFlagBit(ACTORFLAG_SLEEP)) //躺下的时候都这么处理
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(m_RotateYaw);
		TypeCollideAABB tcbox;
		tcbox.box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
		WCoord pos = getPosition() - WCoord(tcbox.box.dim.x / 2, m_yOffset, tcbox.box.dim.z / 2);
		dir *= -m_HitBoundWidth * 0.65f;
		pos += dir;
		tcbox.box.dim = WCoord(m_HitBoundWidth * 1.3f, m_HitBoundHeight / 2, iBoundZ * 1.3f);
		tcbox.box.pos = pos;
		tcbox.part = "upbody";
		boxs.push_back(tcbox);
	}
	else
	{
		auto rot = Rainbow::XYZAngleToQuat(0, m_RotateYaw + 180, 0);
		std::vector<TypeCollideAABB>& useCollidesBox = m_OwnerActor->getFlagBit(ACTORFLAG_SNEAK) ? m_SneakCollideBoxs : m_BaseCollideBoxs;
		for (auto& box : useCollidesBox)
		{
			TypeCollideAABB tcbox = box;
			Vector3f hdim = tcbox.box.dim.toVector3() * 0.5;
			Vector3f cpos = tcbox.box.pos.toVector3() + hdim;
			auto pos = RotateVectorByQuat(rot, cpos);
			tcbox.box.pos = getPosition() + pos - hdim;
			boxs.push_back(tcbox);
		}
	}
}
// 获取多个分区碰撞盒
void ActorLocoMotion::getCollideBoxs(std::vector<CollideAABB>& boxs)
{
	OPTICK_EVENT();
	getOrInitBaseCollide();

	int iBoundZ = m_OwnerActor->IsObject() ? m_HitBoundThickness : m_HitBoundWidth;
	if (m_OwnerActor->getFlagBit(ACTORFLAG_AI_SLEEP) || m_OwnerActor->getFlagBit(ACTORFLAG_SLEEP)) //躺下的时候都这么处理
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(m_RotateYaw);
		CollideAABB box;
		box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
		WCoord pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
		dir *= -m_HitBoundWidth*0.65f;
		pos += dir;
		box.dim = WCoord(m_HitBoundWidth*1.3f, m_HitBoundHeight/2, iBoundZ *1.3f);
		box.pos = pos;
		boxs.push_back(box);
	}
	else
	{
		auto rot = Rainbow::XYZAngleToQuat(0, m_RotateYaw + 180, 0);
		std::vector<TypeCollideAABB>& useCollidesBox = m_OwnerActor->getFlagBit(ACTORFLAG_SNEAK) ? m_SneakCollideBoxs : m_BaseCollideBoxs;
		for (auto& box : useCollidesBox)
		{
			CollideAABB cbox = box.box;
			Vector3f hdim = cbox.dim.toVector3() * 0.5;
			Vector3f cpos = cbox.pos.toVector3() + hdim;
			auto pos = RotateVectorByQuat(rot, cpos);
			cbox.pos = getPosition() + pos - hdim;
			boxs.push_back(cbox);
		}

		// if (m_OwnerActor->isPlayer())
		// {
		// 	CollideAABB cbox = s_TestCollide;
		// 	auto rot = Rainbow::XYZAngleToQuat(0, m_RotateYaw + 180, 0);
		// 	auto pos = RotateVectorByQuat(rot, s_TestCollide.pos.toVector3());
		// 	cbox.pos = getPosition() + pos + Vector3f(-s_TestCollide.dim.x / 2, 0, -s_TestCollide.dim.z / 2);
		// 	boxs.push_back(cbox);
		// }
	}
}

void ActorLocoMotion::getHitCollideBox(CollideAABB &box)
{
    OPTICK_EVENT();
	int iBoundZ = m_OwnerActor->IsObject() ? m_HitBoundThickness : m_HitBoundWidth;
	//if (m_OwnerActor->getFlagBit(ACTORFLAG_AI_SLEEP) || m_OwnerActor->getFlagBit(ACTORFLAG_SLEEP)) //躺下的时候都这么处理
	//{
	//	Rainbow::Vector3f dir = Yaw2FowardDir(m_RotateYaw);
	//	box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
	//	WCoord pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
	//	dir *= -m_HitBoundWidth*0.65f;
	//	pos += dir;
	//	box.dim = WCoord(m_HitBoundWidth*1.3f, m_HitBoundHeight/2, iBoundZ *1.3f);
	//	box.pos = pos;
	//}
	//else
	//{
		box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
		box.pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
	//}
}

void ActorLocoMotion::combatAttackCollideBox(CollideAABB& box)
{
    OPTICK_EVENT();
	int iBoundZ = m_HitBoundThickness;
	if (m_OwnerActor->getFlagBit(ACTORFLAG_AI_SLEEP) || m_OwnerActor->getFlagBit(ACTORFLAG_SLEEP)) //躺下的时候都这么处理
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(m_RotateYaw);
		box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
		WCoord pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
		dir *= -m_HitBoundWidth * 0.65f;
		pos += dir;
		box.dim = WCoord(m_HitBoundWidth * 1.3f, m_HitBoundHeight / 2, iBoundZ * 1.3f);
		box.pos = pos;
	}
	else
	{
		box.dim = WCoord(m_HitBoundWidth, m_HitBoundHeight, iBoundZ);
		box.pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
	}
}

void ActorLocoMotion::hitBack(const WCoord &from)
{
    OPTICK_EVENT();
	Vector3f dir = (m_Position - from).toVector3();
	dir.y = 0;
	dir.Normalize();
	dir *= BLOCK_SIZE*1.5f;
	dir.y = BLOCK_SIZE*0.7f;

	WCoord mvec(dir);

	CollideAABB box;
	getCollideBox(box);
	mvec = m_pWorld->moveBox(box, mvec);

	m_OldPosition = m_Position;
	m_Position += mvec;
}

void ActorLocoMotion::addMotion(float dx, float dy, float dz)
{
	m_Motion.x += dx;
	m_Motion.y += dy;
	m_Motion.z += dz;

	//doPickThrough();
}

Rainbow::Vector3f ActorLocoMotion::getLookDir()
{
	Vector3f dir;
	PitchYaw2Direction(dir, m_RotateYaw, m_RotationPitch);

	return dir;
}

void ActorLocoMotion::setLookDir(float pitch, float yaw)
{
	m_RotationPitch = pitch;
	m_RotateYaw = yaw;
}

bool ActorLocoMotion::handleWaterMovement()
{
    OPTICK_EVENT();
	WCoord contract = WCoord(1, 40, 1);
	if(contract.y > m_BoundHeight/2-1) contract.y = m_BoundHeight/2 - 1;

	// 20210826: 优化除法计算 codeby:liusijia
	int halfSize = m_BoundSize >> 1;
	WCoord minpos = m_Position - WCoord(halfSize, 0, halfSize) + contract;
	WCoord maxpos = m_Position + WCoord(halfSize, m_BoundHeight, halfSize) - contract;


	Vector3f flowmotion;
	bool tmpInWater = false;
	if (m_MovementAbility.CheckFlag(Movement_Water) && m_pWorld->getFluidFlowMotion(minpos, maxpos, flowmotion))
	{
		m_Motion += flowmotion;
		if (m_InWater)
		{
			auto functionWrapper = m_OwnerActor->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}
		}
		tmpInWater = true;

		auto FireBurnComp = m_OwnerActor->getFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(0, 0);
		}
		
	}

	if (!tmpInWater && m_InWaterReal)
	{
		tmpInWater = true;
	}
	if (tmpInWater)
	{
		//添加是否站在载具上的判断
		Rainbow::RaycastHit out;
		auto physScene = m_pWorld->m_PhysScene;
		if (physScene && physScene->RayCast(m_Position.toVector3() + Rainbow::Vector3f(0, 30, 0), Rainbow::Vector3f(0, -1, 0), 400, out, 0, true))
		{
			if (out.collider && out.collider->GetUserData() && dynamic_cast<ActorVehicleAssemble*>((SandboxNode*)out.collider->GetUserData()))
			{
				tmpInWater = false;
			}
		}
	}

	if (tmpInWater != m_InWater)
		setInWater(tmpInWater && !m_IsInBoatWaterMask);
	return m_InWater;
}
void ActorLocoMotion::setInWater(bool inwater){
	m_InWater = inwater;
}

bool ActorLocoMotion::hasCollisionBetweenPlayers()
{
	float val = 0.0f;
	int optId = 0;
	if (g_WorldMgr)
	{
		g_WorldMgr->getRuleOptionID(GMRULE_COLLISIONPLAYER, optId, val);
	}

	return optId != 2;
}

void ActorLocoMotion::syncPhysActorPosByLocPos()
{
#ifdef USE_ACTOR_ROLECONTROLLER
	if (m_ActorRoleController)
		m_ActorRoleController->SetPosition(m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_BoundHeight / 2 - 2.0f, 0.0f));
#endif
}

bool ActorLocoMotion::handleDriftsandMovement()
{
	WCoord contract = WCoord(1, 40, 1);
	if (contract.y > (m_BoundHeight >> 1)) contract.y = m_BoundHeight >> 1;

	// 20210826: 优化除法计算 codeby:liusijia
	int halfSize = m_BoundSize >> 1;
	WCoord minpos = m_Position - WCoord(halfSize, 0, halfSize) + contract;
	WCoord maxpos = m_Position + WCoord(halfSize, m_BoundHeight, halfSize) - contract;

	Vector3f flowmotion;
	bool tmpInDriftsand = false;
	//当存在在流沙中不受影响移动的生物加入 流沙属性判断
	if (/*m_MovementAbility.CheckFlag(Movement_Water) && */m_pWorld->getSandFluidFlowMotion(minpos, maxpos, flowmotion))
	{
		m_Motion += flowmotion;
		if (m_InDriftsand)
		{
			auto functionWrapper = m_OwnerActor->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0.0f);
			}
			//m_OwnerActor->m_FallDistance = 0;
		}
		tmpInDriftsand = true;
		auto FireBurnComp = m_OwnerActor->getFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(0, 0);
		}
		//m_OwnerActor->setFire(0, 0);
	}

	if (!tmpInDriftsand && m_InWaterReal)
	{
		tmpInDriftsand = true;
	}
	m_InDriftsand = tmpInDriftsand;

	return m_InDriftsand;
}

bool ActorLocoMotion::handleVenomMovement() {
	CollideAABB box;
	getCollideBox(box);
	bool isVenom = m_pWorld->isAnyVenom(box.minPos(), box.maxPos());
	if (isVenom)
	{
		auto FireBurnComp = m_OwnerActor->getFireBurnComponent();
		if (FireBurnComp)
		{
			FireBurnComp->setFire(0, 0);
		}
		return true;
	}
	return false;
}

bool ActorLocoMotion::handleLavaMovement()
{
	CollideAABB box;
	getCollideBox(box);

	int dxz = 10;
	int dy = 40;
	if(dxz > m_BoundSize/2) dxz = m_BoundSize/2;
	if(dy > m_BoundHeight/2) dy = m_BoundHeight/2;

	box.expand(-dxz, -dy, -dxz);

	
	m_InLava =  m_pWorld->hasBlocksInCoordRange(box.minPos(), box.maxPos(), BLOCK_STILL_LAVA, BLOCK_FLOW_LAVA);
	return m_InLava;
}

//bool ActorLocoMotion::handleHoneyMovement()
//{
//	CollideAABB box;
//	getCollideBox(box);
//
//	int dxz = 10;
//	int dy = 40;
//	if(dxz > m_BoundSize/2) dxz = m_BoundSize/2;
//	if(dy > m_BoundHeight/2) dy = m_BoundHeight/2;
//
//	box.expand(-dxz, -dy, -dxz);
//
//	m_InHoney =  m_pWorld->hasBlocksInCoordRange(box.minPos(), box.maxPos(), BLOCK_STILL_HONEY, BLOCK_FLOW_HONEY);
//	return m_InHoney;
//}

void ActorLocoMotion::handleBlockMovement()
{
	static int resid_to_check[] = {BLOCK_STILL_HONEY, BLOCK_FLOW_HONEY,BLOCK_STILL_LAVA, BLOCK_FLOW_LAVA ,
	BLOCK_POISON};

	CollideAABB box;
	getCollideBox(box);

	int dxz = 10;
	int dy = 40;
	if (dxz > m_BoundSize / 2) dxz = m_BoundSize / 2;
	if (dy > m_BoundHeight / 2) dy = m_BoundHeight / 2;

	box.expand(-dxz, -dy, -dxz);

	m_InHoney = false;
	m_InLava = false;
	m_InPoison = false;
	m_InWaterReal = false;

	unsigned int checkres = m_pWorld->hasBlocksInCoordRange(box.minPos(), box.maxPos(), resid_to_check, 5);
	if (checkres & ((1 << 0) | (1 << 1)))
	{
		m_InHoney = true;
	}
	if (checkres & ((1 << 2) | (1 << 3)))
	{
		m_InLava = true;
	}
	if (checkres & ((1 << 4)))
	{
		m_InPoison = true;
	}

	int water = -1;
	WCoord contract = WCoord(1, 40, 1);
	if (contract.y > (m_BoundHeight >> 1)) contract.y = m_BoundHeight >> 1;

	// 20210826: 优化除法计算 codeby:liusijia
	int halfSize = m_BoundSize >> 1;
	WCoord minpos = m_Position - WCoord(halfSize, 0, halfSize) + contract;
	WCoord maxpos = m_Position + WCoord(halfSize, m_BoundHeight, halfSize) - contract;
	checkres = m_pWorld->hasBlocksInCoordRange(minpos, maxpos, &water, 1);
	if (checkres & 1)
	{
		m_InWaterReal = true;
	}
}

//bool ActorLocoMotion::isOnThicket()
//{
//	//actor->EXEC_USEMODULE(queryBlockEnvStatus, params...);
//    WCoord pos(m_Position.x, m_Position.y, m_Position.z);
//    int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
//    return blockid == BLOCK_THICKET;
//}

//bool ActorLocoMotion::handlePoisonMovement()
//{
//	CollideAABB box;
//	getCollideBox(box);
//
//	int dxz = 10;
//	int dy = 40;
//	if (dxz > m_BoundSize / 2) dxz = m_BoundSize / 2;
//	if (dy > m_BoundHeight / 2) dy = m_BoundHeight / 2;
//
//	box.expand(-dxz, -dy, -dxz);
//
//	m_InPoison = m_pWorld->hasBlocksInCoordRange(box.minPos(), box.maxPos(), BLOCK_POISON);
//	return m_InPoison;
//}

bool ActorLocoMotion::isOnLadder()
{
	return false;
}

bool ActorLocoMotion::isOnAirWall()
{
	int blockid;
	WCoord blockpos = CoordDivBlock(m_Position+WCoord(0,m_yOffset,0));

	for(int i=0; i<4; i++)
	{
		WCoord ng = NeighborCoord(blockpos, i);
		blockid = m_pWorld->getBlockID(ng);
		if(!IsAirBlockID(blockid) && blockid!= BLOCK_AIRWALL && blockid!= BLOCK_AIRWALL_PASS)
			return false;
	}
	return true;
}

bool ActorLocoMotion::isInLiquid()
{
	return (m_InWater || m_InLava || m_InHoney) && !m_OwnerActor->getFlying();
}

/*
void ActorLocoMotion::updateGravity()
{
	m_Gravity = -m_pWorld->getGravity();

	WCoord footgrid = CoordDivBlock(m_Position);
	Chunk *pchunk = m_pWorld->getChunk(footgrid);
	if(pchunk == NULL) return;

	Block *footblock = pchunk->getBlock(footgrid-pchunk->m_Origin);
	Block *headblock = pchunk->getBlock(footgrid+WCoord(0,1,0)-pchunk->m_Origin);

	if(m_OnGround) //看是否改变位置导致下方不是地
	{
		WCoord downpos = m_Position + WCoord(0, -1, 0);
		if(!m_pWorld->isBlockNormalCube(CoordDivBlock(downpos)))
		{
			m_OnGround = false; //脚下不是地，会往下掉
		}
	}

	if(m_InWater || m_InLava) m_Gravity *= 0.5f;

	if(!m_OnGround) m_Motion.y += m_Gravity*(0.5f*GAME_TICK_TIME*GAME_TICK_TIME);
}*/

bool ActorLocoMotion::isMovementBlocked()
{
	return m_OwnerActor ? m_OwnerActor->isDead() : true;
}

//speed = 200.0f * GAME_TICK_TIME * MOTION_TICK_DECAY
void ActorLocoMotion::moveFlying(float strafing, float forward, float speed)
{
	float r = strafing*strafing + forward*forward;
	if(r <= 0.0001f)
	{
		return;
	}

	r = Sqrt(r);
	if(r < 1.0f) r = 1.0f;
	r = speed/r;
	strafing *= r;
	forward *= r;

	Vector3f fdir = Yaw2FowardDir(m_RotateYaw);
	Vector3f sdir = Yaw2StrafingDir(m_RotateYaw);

	if(m_NavigationRotateYaw >= 0)
		fdir = Yaw2FowardDir(m_NavigationRotateYaw);

	if (m_NavigationRotationPitch >= 0)
		sdir = Yaw2StrafingDir(m_NavigationRotationPitch);

	m_Motion.x += forward*fdir.x + strafing*sdir.x;
	m_Motion.z += forward*fdir.z + strafing*sdir.z;

}

//函数名和函数的意思是相反的 - -！
bool ActorLocoMotion::isOffsetPositionInLiquid(float x, float y, float z)
{
	CollideAABB box;
	m_OwnerActor->getCollideBox(box);
	box.pos += Vector3f(x,y,z);

	if(m_pWorld->isBoxCollide(box)) return false;
	else return !m_pWorld->isAnyLiquid(box.minPos(), box.maxPos());
}

bool ActorLocoMotion::isBoundsAllInLiquid()
{
	CollideAABB box;
	m_OwnerActor->getCollideBox(box);

	return m_pWorld->isAllWater(box.minPos(), box.maxPos());
}

bool ActorLocoMotion::isOffsetBoundsAllInLiquid(float x, float y, float z)
{
	CollideAABB box;
	m_OwnerActor->getCollideBox(box);
	box.pos += Vector3f(x, y, z);

	return m_pWorld->isAllWater(box.minPos(), box.maxPos());
}

const int MAX_MOTION = (int)(100*BLOCK_FSIZE);
WCoord ActorLocoMotion::getIntegerMotion(const Rainbow::Vector3f &motion)
{
	Vector3f &accumleft = m_AccumLeftMotion;
	WCoord coord = motion;

	//计算误差累积
	accumleft.x += motion.x - coord.x;
	accumleft.y += motion.y - coord.y;
	accumleft.z += motion.z - coord.z;

	//将误差添加到位移中去
	int x = int(accumleft.x);
	int y = int(accumleft.y);
	int z = int(accumleft.z);
	coord.x += x;
	coord.y += y;
	coord.z += z;

	accumleft.x -= x;
	accumleft.y -= y;
	accumleft.z -= z;
	if(coord.x<-MAX_MOTION || coord.x>MAX_MOTION) coord.x = 0;
	if(coord.z<-MAX_MOTION || coord.z>MAX_MOTION) coord.z = 0;
	if(coord.y<-MAX_MOTION || coord.y>MAX_MOTION) coord.y = 0;
	return coord;
}

bool ActorLocoMotion::onTheBlock(int blockID)
{
	WCoord blockpos = CoordDivBlock(m_Position);
	blockpos.y--;
	
	if (m_OnGround 
	&& m_pWorld->getBlock(blockpos).getResID() == blockID)
	{
		if(BLOCK_HOTCRYSTAL == blockID	&& ((m_pWorld->getBlock(blockpos).getData() & 7) - 1) == DIR_POS_Y)
			return true;
		else if(BLOCK_HOTCRYSTAL != blockID) 
		    return true;
	}

	return false;
}

// 20210926：检测离地面距离够不够  codeby： keguanqiang
bool ActorLocoMotion::checkDistByGround(int dist)
{
	WCoord blockpos = CoordDivBlock(m_Position + WCoord(0, -dist, 0));
	if (m_pWorld && m_pWorld->getBlockMaterial(blockpos) && !m_pWorld->getBlockMaterial(blockpos)->isSolid())
	{
		return true;
	}

	return false;
}


bool ActorLocoMotion::findBottomBlock(int blockID)
{
	WCoord blockpos = CoordDivBlock(m_Position);
	while(blockpos.y > 0)
	{
		blockpos.y--;
		int nextBlockID = m_pWorld->getBlock(blockpos).getResID();
		if (nextBlockID == blockID)
		{
			if(BLOCK_HOTCRYSTAL == blockID	&& ((m_pWorld->getBlock(blockpos).getData() & 7) - 1) == DIR_POS_Y)
				return true;
			else if(BLOCK_HOTCRYSTAL != blockID) 
				return true;
		}
		else if(!IsLavaBlock(nextBlockID) && !IsWaterBlockID(nextBlockID))
		{
			break;
		}
	}
	return false;
}

bool ActorLocoMotion::findBottomBlock(int blockID, WCoord& blockpos, WORLD_ID &vehicleID)
{
	blockpos = CoordDivBlock(m_Position);
	int yOffset = 0;
	while(blockpos.y > 0)
	{
		blockpos.y--;
		yOffset++;
		int nextBlockID = m_pWorld->getBlock(blockpos).getResID();
		if (nextBlockID == blockID)
		{
			return true;
		}
		if(yOffset > 1)
		{
			return false;
		}
	}
	return false;
}
bool ActorLocoMotion::findBottomLeafBlock()
{
	WCoord blockpos = CoordDivBlock(m_Position - Rainbow::Vector3f(0.0f, 10.0f, 0.0f));
	while(blockpos.y > 0)
	{
		blockpos.y--;
		int nextBlockID = m_pWorld->getBlock(blockpos).getResID();
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(nextBlockID);
		if (mtl && mtl->m_TypeName.compare("modelleaf") == 0)
		{
			return true;
		}
		else if(!IsLavaBlock(nextBlockID) && !IsWaterBlockID(nextBlockID))
		{
			break;
		}
	}
	return false;
}

bool ActorLocoMotion::onThePhysicsActor()
{
	WCoord blockpos = CoordDivBlock(m_Position);

	//查找以玩家为中心, 脚下3格范围内的物理物体(---joker 修改为1格，测试)
	WCoord pos = m_Position;
	CollideAABB box;
	//WCoord endPos = pos + WCoord(0, 10, 0);   
	//box.pos.x = Rainbow::Min(pos.x, endPos.x);
	//box.pos.y = Rainbow::Min(pos.y, endPos.y);
	//box.pos.z = Rainbow::Min(pos.z, endPos.z);
	//box.dim.x = Rainbow::Max(pos.x, endPos.x) - pos.x;
	//box.dim.y = Rainbow::Max(pos.y, endPos.y) - pos.y;
	//box.dim.z = Rainbow::Max(pos.z, endPos.z) - pos.z;
	box.pos = pos;
	box.dim = WCoord(0, 10, 0);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsInBox(actors, box);
	for (size_t i=0; i<actors.size(); i++)
	{
		PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actors[i]->GetActor()->getLocoMotion());
		if (loc && loc->m_hasPhysActor)
		{
			ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble*>(actors[i]);
			if (vehicle)
			{
				int blockid, x, y, z;
				CollideAABB box;
				m_OwnerActor->getCollideBox(box);
				if (vehicle->intersectBox(box, blockid, x, y, z))
				{
					//做下特殊处理，避免人物在接触触碰方块以后，在半空中直接下坠的情况
					if (blockid == BLOCK_COLLIDER)
						return false;
				}
			}
			return true;
		}
	}

	return false;
}

static float vel_limit_xz = 5.0f; // 跟LivingLocoMotion统一
int ActorLocoMotion::doMoveStep(const Rainbow::Vector3f &argMotion)
{
	//using Rainbow::Timer;
	//uint64_t t1 = Timer::getTimeUS();
	Rainbow::Vector3f motion = argMotion; //m_noMoveDecay ? m_networkMotion : argMotion;

	bool onVehicle = false;
	ClientPlayer* pPlayer = nullptr;
	PlayerLocoMotion* pPlayerLoc = nullptr;

	int objType = m_OwnerActor->getObjType();
	if (objType == OBJ_TYPE_ROLE)
	{
		pPlayer = static_cast<ClientPlayer*>(m_OwnerActor);
		if (pPlayer)
		{
			pPlayerLoc = static_cast<PlayerLocoMotion*>(pPlayer->getLocoMotion());
			PlayerAttrib* playerAttrib = pPlayer->getPlayerAttrib();
			if (playerAttrib && playerAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				if (m_CollideMoveTicks)
				{
					m_CollideMoveTicks--;
				}
				else
				{
					motion.x = 0;//存在飞行击落
					motion.z = 0;
				}
				//return 0;
			}
		}
	}
	else if (objType == OBJ_TYPE_MONSTER||
		//m_OwnerActor->getObjType() == OBJ_TYPE_CREATURE || 
		objType == OBJ_TYPE_NPC ||
		objType == OBJ_TYPE_VILLAGER ||
		objType == OBJ_TYPE_FLYSNAKEGOD ||
		objType == OBJ_TYPE_SKIN_NPC ||
		objType == OBJ_TYPE_DESERTBUSINESSMAN ||
		objType == OBJ_TYPE_DESERTBUSINESSMANGUARD ||
		objType == OBJ_TYPE_DESERTVILLAGER ||
		objType == OBJ_TYPE_PACKHORSE ||
		objType == OBJ_TYPE_SANDMAN ||
		objType == OBJ_TYPE_SEASPIRITGUARDING ||
		objType == OBJ_TYPE_FISHINGVILLAGER ||
		objType == OBJ_TYPE_SNOWMAN ||
		objType == OBJ_TYPE_PACKHORSE ||
		objType == OBJ_TYPE_SHAPESHIFT_HORSE||
		objType == OBJ_TYPE_HORSE ||
		objType == OBJ_TYPE_TRAVELING_TRADER_NPC)
	{

		ClientMob* _mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		if (_mob)
		{
			MobAttrib* mobAttrib = _mob->getMobAttrib();
			if (mobAttrib && mobAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
				if (mobAttrib->hasStatusEffect(STATUS_EFFECT_DROP) || mobAttrib->hasStatusEffect(STATUS_EFFECT_PERCIPIENCE))
				{

				}
				else if (m_CollideMoveTicks)
				{
					m_CollideMoveTicks--;
				}
				else if (motion.x == 0 && motion.y < 0 && motion.z == 0) //下落
				{
				}
				else
					return 0;
			}
		}
	}
	m_MotionStatus = motion;
	m_bMoving = false;

	WCoord mvec = getIntegerMotion(motion);
	if(mvec.isZero())
	{
		return 0;
	}
	m_UseNoClipMove = false;
	if(noClip())
	{
		WCoord move_vec = mvec * (m_IsCollideBackward ? -1 : 1);
		setPosition(m_Position.x + move_vec.x, m_Position.y + move_vec.y, m_Position.z + move_vec.z);
		m_UseNoClipMove = true;
		return 0;
	}

	GetIWorldConfigProxy()->VmpBeginUltra("locl_domovestep");
	CollideAABB box;
	getCollideBoxMatchPhysBox(box);

	/*if (pPlayerLoc)
		pPlayerLoc->reviseCheckAutoJumpBox(box);*/
	
	//如果连续4格都是空的就反转
	if(!m_pWorld->isRemoteMode() && m_OwnerActor->getReverse())
	{
		if(m_pWorld->moveBox(box, WCoord(0,400,0)).y == 400)
		{
			m_OwnerActor->setReverse(false);
		}
	} 

	float fWalkUpLadderHeight = 0.0f;
	Vector3f vSlopeNormal;
	bool onRebound = false;
	
	if (!m_OwnerActor->getReverse())
	{
		//从原逻辑不能向下移动1像素为在地上，/改为
		// 		m_OnGround = (m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		//往下移动10像素~但是却不能完全下移10像素判断为在地上（以解决如铲草后，角色可以下移草皮的厚度小于10像素，也是在地上）
		if (pPlayer)
		{
			int newMvecY = 0;
			if (m_pWorld->isBlockNormalCube(CoordDivBlock(m_Position)) && !m_pWorld->isBlockNormalCube(CoordDivBlock(WCoord(m_Position.x, m_Position.y + 10, m_Position.z))))
			{
				//TODO 这里去获取BlockDef的方式去判断是否微缩方块，性能不大好，待优化
				int blockid = m_pWorld->getBlockID(CoordDivBlock(m_Position));
				BlockDef* pDef = GetDefManagerProxy()->getBlockDef(blockid);
				if (pDef && pDef->Type && pDef->Type == "custombasic")
				{
					//微缩方块不处理，否则会抖动
				}
				else
				{
					newMvecY = (CoordDivBlock(WCoord(m_Position.x, m_Position.y + 10, m_Position.z))*BLOCK_SIZE).y - m_Position.y;
				}
			}

			int iRet = pPlayerLoc->sweepMovePos(mvec, vSlopeNormal, fWalkUpLadderHeight, onRebound);
			if (m_CurMovementState == MovementState::Other && newMvecY != 0)
				mvec.y = newMvecY;

			if (iRet < 0)
			{
				setOnGround(false);
			}
			else
			{
				setOnGround(true);
			}

			if (!m_OnGround)
			{
				auto functionWrapper = m_OwnerActor->getFuncWrapper();
				if (functionWrapper && functionWrapper->getFallDistance() > 0)
				{
					//掉落的时候沿用老的判断，不然6格这个高度往下掉的时候，会因为这里导致没有伤害 code_by:huangfubin
					setOnGround(m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
				}
				else
				{
					int y = m_pWorld->moveBox(box, WCoord(0, -10, 0)).y;
					setOnGround(y != -10);
				}

				//判断是否在载具上
				if (pPlayer->m_UsePhysics)
				{
					if (onThePhysicsActor())
					{
						onVehicle = true;
					}
				}
			}
		}
		else
		{
			setOnGround(m_pWorld->moveBox(box, WCoord(0, -1, 0)).y == 0);
		}
	}
	else
		setOnGround(m_pWorld->moveBox(box, WCoord(0,1,0)).y == 0);

	if (m_CurMovementState != MovementState::OnSlopeFall && m_OnGround)
		m_fSlopeFallSpeed = 0.0f;

	if (pPlayer)
	{
		// 审查模式,观战模式
		if (pPlayer->IsInPierceMode() || pPlayer->getSpectatorMode() == PLAYER_SPECTATOR_MODE::SPECTATOR_MODE_FAILURE)
		{
			setOnGround(false);
		}

		bool dosneak = m_OnGround && m_OwnerActor->getSneaking();
		if (dosneak)
		{
			int minstep = 5;
			int mx = mvec.x;
			int mz = mvec.z;
			int offsety = -BLOCK_SIZE;
			while (mx != 0 && m_pWorld->checkNoCollisionBoundBox(box.getOffsetBox(mx, offsety, 0), m_OwnerActor))
			{
				if (mx > -minstep && mx < minstep)
				{
					mx = 0;
				}
				else if (mx > 0) mx -= minstep;
				else mx += minstep;
			}

			while (mz != 0 && m_pWorld->checkNoCollisionBoundBox(box.getOffsetBox(0, offsety, mz), m_OwnerActor))
			{
				if (mz > -minstep && mz < minstep)
				{
					mz = 0;
				}
				else if (mz > 0) mz -= minstep;
				else mz += minstep;
			}

			while (mx != 0 && mz != 0 && m_pWorld->checkNoCollisionBoundBox(box.getOffsetBox(mx, offsety, mz), m_OwnerActor))
			{
				if (mx > -minstep && mx < minstep)
				{
					mx = 0;
				}
				else if (mx > 0) mx -= minstep;
				else mx += minstep;

				if (mz > -minstep && mz < minstep)
				{
					mz = 0;
				}
				else if (mz > 0) mz -= minstep;
				else mz += minstep;
			}
			mvec.x = mx;
			mvec.z = mz;
		}
	}

	WCoord realmov;
	bool onladder_pre = isOnLadder();
	m_UseOnGround.Set(0);
	bool groundTemp = m_OnGround;
	bool waterTemp = m_InWater;
	bool laveTemp = m_InLava;
	if(m_OnGround && !(onladder_pre&&m_CollidedHorizontally) || m_InWater&&canWalkOnLiquid(true) || m_InLava&&canWalkOnLiquid(false))
	{
		if (m_CurMovementState != MovementState::AdjustLandingOffset && ((mvec.y < 0 && !m_OwnerActor->getReverse())
		|| (mvec.y > 0 && m_OwnerActor->getReverse()))) 
		{
			m_Motion.y = 0;
			mvec.y = 0;
			m_AccumLeftMotion.y = 0;
		}
		//m_OnGround = true;
		if (mvec.isZero() && m_CurMovementState != MovementState::OnSlopeFall)
			return -1;

		if (!mvec.isZero())
		{
			int stepheight = m_OwnerActor->getStepHeight();
			if (stepheight == 0)
				realmov = m_pWorld->moveBox(box, mvec);
			else
			{
				if (m_CurMovementState == MovementState::OnSlope || m_CurMovementState == MovementState::OnSlopeFall || m_CurMovementState == WalkUpHalfBlockHeight || m_CurMovementState == StopUphill)
					realmov = mvec;
				else
					realmov = m_pWorld->moveBoxWalk(box, mvec, stepheight);
			}
			m_UseOnGround.Set(1);
		}
	}
	else 
	{
		realmov = m_pWorld->moveBox(box, mvec);
	}

	m_bIsBlocked = false;
	if (realmov.x == 0 && realmov.y == 0 && realmov.z == 0 && !mvec.isZero())
	{
		m_bIsBlocked = true;
	}
	//只对都是阻挡状态的角色进行碰撞处理
	bool isDealtSpecialMove = false;
	if (pPlayer /*&& player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT*/ && (realmov.x != 0 || realmov.z != 0 || m_CurMovementState == MovementState::OnSlopeFall))
	{
		if(hasCollisionBetweenPlayers())
			m_pWorld->checkPlayerCollisionPlayerBoundBox(box, pPlayer, realmov);

		isDealtSpecialMove = pPlayerLoc->specialMove(realmov,m_fSlopeFallSpeed, fWalkUpLadderHeight, vSlopeNormal);
	}
	if (!isDealtSpecialMove && realmov.isZero())
	{
		return -1;
	}

	int flags = 0;											 
	m_CollidedHorizontally = false;
	m_CollidedVertically = false;

	if (!isDealtSpecialMove)
		addRealMove(box, realmov);
		

	if(realmov.y != mvec.y)
	{
		if((mvec.y > 0 && m_OwnerActor->getReverse())
		|| (mvec.y < 0 && !m_OwnerActor->getReverse()))
		{
			//LOG_INFO("m_OnGround2: %d   position: %d %d %d", m_OnGround, m_Position.x, m_Position.y, m_Position.z);
		   	setOnGround(true);
			m_Motion.y = 0;
			m_AccumLeftMotion.y = 0;
		}
		m_CollidedVertically = true;
		flags |= 2;
	}
	else if ((realmov.z && realmov.z != mvec.z) || (realmov.x && realmov.x != mvec.x))
	{  // 横行一段距离受阻, 为支持靠墙多段跳 2024.09.19 by huanglin
		setOnGround(true);
	}
	else if(onVehicle && !onladder_pre)
	{
		// 如果在载具上，同样设置m_OnGround为true（投掷器在载具上和在地面，CalMoveDecay函数计算结果一样，对人物推力差别很大）
		setOnGround(true);
	}
	// 2021/12/01 codeby:liusijia 如果原来在地上，往上移动，则需要修正一下，否则人在空中，onground还是true
	if (!(onVehicle && !onladder_pre) && (m_CurMovementState != MovementState::OnSlope && m_CurMovementState != MovementState::WalkUpHalfBlockHeight)
		&& m_OnGround && pPlayer && ((realmov.y > 0 && !m_OwnerActor->getReverse()) || (realmov.y < 0 && m_OwnerActor->getReverse())))
	{
		setOnGround(false);
	}//这里会影响角色上坡判断
	m_OnLadder = onladder_pre;
	m_OnVehicle = onVehicle;

	bool obstruct = false;
	if(mvec.x!=0 && (realmov.x==0 || (onVehicle && abs(realmov.x) < vel_limit_xz)))
	{
		m_Motion.x = 0;
		m_AccumLeftMotion.x = 0;
		m_CollidedHorizontally = true;
		flags |= 1;
		obstruct = true;
	}
	if(mvec.z!=0 && (realmov.z==0 || (onVehicle && abs(realmov.z) < vel_limit_xz)))
	{
		m_Motion.z = 0;
		m_AccumLeftMotion.z = 0;
		m_CollidedHorizontally = true;
		flags |= 1;
		obstruct = true;
	}

	if(obstruct && pPlayer)
	{
		if(pPlayer && pPlayer->getHookObj())
		{
			ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
			if (!actorMgr) return flags;
			ClientActor *actor = actorMgr->findActorByWID(pPlayer->getHookObj());
			if (actor)
			{
				ClientActorHook* hook = dynamic_cast<ClientActorHook*>(actor);
				if (hook && hook->getState() == 2)
				{
					pPlayer->setHookObj(0);
				}
			}
		}
	}

	fallMotion((float)realmov.y, (float)mvec.y, onRebound);

	bool onladder = isOnLadder();
	auto RidComp = m_OwnerActor->getRiddenComponent();
	if(!m_pWorld->isRemoteMode() && m_OwnerActor->needWalkEffects() && !(RidComp && RidComp->isRiding()))
	{
		int distsqr = realmov.x*realmov.x + realmov.z*realmov.z;
		if(onladder || m_IsEnchClimb) distsqr += realmov.y*realmov.y;

		float walkdist = Sqrt(distsqr)*0.6f;
		m_DistanceWalked += walkdist;
		if(m_DistanceWalked > m_NextStepDistance)
		{
			m_NextStepDistance = m_DistanceWalked + BLOCK_FSIZE;
			m_bMoving = true;
			if (m_InWater || m_InLava || m_InHoney)
			{
				if (canWalkOnLiquid(m_InWater))
					playWalkOnLiquidEffect(m_InWater);
			}
			else if(m_OnGround || onladder || m_IsEnchClimb)
			{
				ActorLiving *living = dynamic_cast<ActorLiving *>(m_OwnerActor);
				if(living)
				{
					if (onladder)
					{
						auto sound = living->getSoundComponent();
						if (sound)
						{
							sound->playSound("blocks.ladder", 1.0f, 1.0f);
						}
					}
					else if (m_IsEnchClimb) living->playClimbStepSound();
					// else living->playStepSound();
				}

				WCoord blockpos = CoordDivBlock(m_Position);
				blockpos.y--;
				BlockMaterial *mtl = m_pWorld->getBlockMaterial(blockpos);
				if (mtl)
				{
					mtl->DoOnActorWalking(m_pWorld, blockpos, m_OwnerActor);
				}
			}
		}
	}
	GetIWorldConfigProxy()->VmpEnd();

	//uint64_t t2 = Timer::getTimeUS();
	//LogStringMsg("ActorLocoMotion  domovestep Costtime:[%d]", t2 - t1);

	return flags;
}

bool ActorLocoMotion::needFullRotation()
{
	return m_OwnerActor && (m_OwnerActor->getObjType() == OBJ_TYPE_GAMEOBJECT);
}

void ActorLocoMotion::addRealMove(const CollideAABB &box, WCoord &realmov)
{
	m_OldPosition = m_Position;
	if (m_ActorRoleController)
	{
		bool xchange = false;
		bool ychange = false;
		bool zchange = false;

		Rainbow::Vector3f prePhyPos = m_ActorRoleController->GetPosition();
		Rainbow::Vector3f motion((float)realmov.x, (float)realmov.y, (float)realmov.z);
		// m_ActorRoleController->Move(motion, 0, false);
		m_ActorRoleController->SetPosition(prePhyPos + motion);
		Rainbow::Vector3f nowPhyPos = m_ActorRoleController->GetPosition();

		if (abs(nowPhyPos.x - prePhyPos.x - float(realmov.x)) >= 1)
		{
			xchange = true;
		}
		if (abs(nowPhyPos.y - prePhyPos.y - float(realmov.y)) >= 1)
		{
			ychange = true;
		}
		if (abs(nowPhyPos.z - prePhyPos.z - float(realmov.z)) >= 1)
		{
			zchange = true;
		}

		m_OldPosition = m_Position;
		WCoord positionPre = m_Position;
		//初始化物理胶囊体是y为m_BoundHeight中间，所以每次计算时不需要偏移值
		m_Position = nowPhyPos - Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2)/* - 2.0f*/, 0.0f);
		WCoord realmovPre = realmov;
		if (!xchange)
		{
			m_Position.x = positionPre.x + realmov.x;
		}
		if (!ychange)
		{
			m_Position.y = positionPre.y + realmov.y;
		}
		if (!zchange)
		{
			m_Position.z = positionPre.z + realmov.z;
		}

		if (m_OnGround && realmov.y < 0 && ychange)	//保持角色控制器比m_Position高2个单位的距离
		{
			m_Position.y = nowPhyPos.y - (float)(m_BoundHeight / 2) + 2;
		}

	}
	else
	{
		if (m_OwnerActor->getReverse())
			m_Position = box.pos + realmov + WCoord(m_BoundSize / 2, -m_yOffset, m_BoundSize / 2);
		else
			m_Position = box.pos + realmov + WCoord(m_BoundSize / 2, m_yOffset, m_BoundSize / 2);
	}
}

void ActorLocoMotion::doBlockCollision()
{
    OPTICK_EVENT();
	if(m_pWorld == NULL) return;
	if(m_OwnerActor == NULL) return;
	if (m_pWorld->isRemoteMode())
	{
		return;
	}
	if(m_OwnerActor->isDead()) return;

	CollideAABB box;
	getCollideBox(box);
	box.expand(-1, -1, -1);

	WCoord mingrid = CoordDivBlock(box.minPos());
	WCoord maxgrid = CoordDivBlock(box.maxPos());

	World *pworld = m_OwnerActor->getWorld();
	if(nullptr != pworld && pworld->checkChunksExist(mingrid, maxgrid))
	{
		Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_woord(16);

		for (int x = mingrid.x; x <= maxgrid.x; x++)
		{
			for (int z = mingrid.z; z <= maxgrid.z; z++)
			{
				for (int y = mingrid.y; y <= maxgrid.y; y++)
				{
					WCoord wcoordcollide(x, y, z);
					int blockid = pworld->getBlockID(wcoordcollide);
					if (blockid > 0)
					{
						auto material = g_BlockMtlMgr.getMaterial(blockid);
						if (BLOCK_PHYSXCOLLIDER == blockid)
						{
							if (check_woord.find(wcoordcollide) == NULL && material
								&& material->DoOnActorCollidedWithBlock(pworld, wcoordcollide, m_OwnerActor))
							{
								std::list<WCoord> wcoordlist;
								wcoordlist.push_back(wcoordcollide);
								check_woord[wcoordcollide] = 1;
								while (wcoordlist.size())
								{
									WCoord postemp = *wcoordlist.begin();
									wcoordlist.pop_front();
									for (int i = 0; i < 6; i++)
									{
										WCoord coord = NeighborCoord(postemp, i);
										if (check_woord.find(coord) == NULL)
										{
											int NeighborBlockID = m_OwnerActor->getWorld()->getBlockID(coord);
											if (NeighborBlockID == BLOCK_PHYSXCOLLIDER)
											{
												check_woord[coord] = NeighborBlockID;
												wcoordlist.push_back(coord);
											}
										}
									}
								}
							}
						}
						else
						{
							if (material && material->DoOnActorCollidedWithBlock(pworld, wcoordcollide, m_OwnerActor))
							{
								check_woord[wcoordcollide] = blockid;
							}
						}
					}
				}
			}
		}

		// 减少对象构造析构。 20210831 codeby:liusijia
		static std::map<WCoord, ActorVehicleAssemble*> vehicleList;
		vehicleList.clear();
		GET_SUB_SYSTEM(VehicleMgr)->getActorVehiclesAndCollidePosInBox(pworld,vehicleList, box);

		for (auto iter = vehicleList.begin(); iter != vehicleList.end(); iter++)
		{
			ActorVehicleAssemble* pVehicle = iter->second;

			//碰撞前，先重置碰撞信息
			pVehicle->getMGraph()->ResetNode();

			int blockid = pVehicle->getBlockID(iter->first);

			if (blockid > 0)
			{
				auto material = g_BlockMtlMgr.getMaterial(blockid);

				WCoord wcoordcollide = pVehicle->convertWcoord(iter->first);//世界坐标

				wcoordcollide.x = (int)floor(float(wcoordcollide.x) / 100.f);
				wcoordcollide.y = (int)floor(float(wcoordcollide.y - 50) / 100.f);
				wcoordcollide.z = (int)floor(float(wcoordcollide.z) / 100.f);

				{
					if (BLOCK_PHYSXCOLLIDER == blockid)
					{
						if (check_woord.find(wcoordcollide) == NULL && material
							&& material->DoOnActorCollidedWithBlock( pVehicle->getVehicleWorld(), iter->first, m_OwnerActor))
						{
							std::list<WCoord> wcoordlist;
							wcoordlist.push_back(wcoordcollide);
							check_woord[wcoordcollide] = 1;
							while (wcoordlist.size())
							{
								WCoord postemp = *wcoordlist.begin();
								wcoordlist.pop_front();
								for (int i = 0; i < 6; i++)
								{
									WCoord coord = NeighborCoord(postemp, i);
									if (check_woord.find(coord) == NULL)
									{
										int NeighborBlockID = m_OwnerActor->getWorld()->getBlockID(coord);
										if (NeighborBlockID == BLOCK_PHYSXCOLLIDER)
										{
											check_woord[coord] = NeighborBlockID;
											wcoordlist.push_back(coord);
										}
									}
								}
							}
						}
					}
					else
					{
						if (material && material->DoOnActorCollidedWithBlock( pVehicle->getVehicleWorld(), iter->first, m_OwnerActor))
						{
							check_woord[wcoordcollide] = blockid;
						}
					}
				}
			}
			
		}

		m_OwnerActor->clearCollideBlock();
		Rainbow::HashTable<WCoord, int, WCoordHashCoder>::Element *ele = check_woord.iterate(NULL);
		while(ele)
		{
			m_OwnerActor->setCollideBlockState(ele->key, ele->value);
			ele = check_woord.iterate(ele);
		}
	}

	if (m_OnGround && !m_OwnerActor->getFlying())
	{
		WCoord pos = CoordDivBlock(m_Position);
		BlockMaterial *mtl = m_pWorld->getBlockMaterial(pos);
		if (mtl)
		{
			if (mtl->m_TypeName == "air")
			{

				WCoord down_pos = CoordDivBlock(m_Position) + WCoord(0, -1, 0);
				BlockMaterial* dmtl = m_pWorld->getBlockMaterial(down_pos);
				if (dmtl)
				{
					dmtl->DoOnActorWalking(m_pWorld, down_pos, m_OwnerActor);
				}
				// 观察者事件接口
				//ObserverEvent_Block obevent(down_pos.x, down_pos.y, down_pos.z, mtl->getBlockResID(), (long long)m_OwnerActor->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorWalking", &obevent);
			}
			else
			{
				mtl->DoOnActorWalking(m_pWorld, pos, m_OwnerActor);
			}
		}
		
		/*ActorLiving* living = dynamic_cast<ActorLiving*>(m_OwnerActor);
		if (living != NULL) { // 非投射物类型
			WCoord curPos = CoordDivBlock(m_Position);
			BlockMaterial *curMtl = m_pWorld->getBlockMaterial(curPos);
			if (curMtl != NULL) {// 观察者事件接口/碰撞事件
				//ObserverEvent_Block obevent(curPos.x, curPos.y, curPos.z, curMtl->getBlockResID(), m_OwnerActor->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
			}
		}*/
	}
}

void ActorLocoMotion::doNearBlockCollision()
{
	/*
	if (m_pWorld->isRemoteMode()) return;

	World *pworld = m_OwnerActor->getWorld();
	for (int i = 0; i < 6; i++)
	{
		switch (i)
		{
		case DIR_NEG_X:
		{
			if (abs((m_Position.x - 50) % 100) <= 20)
			{
				WCoord grid = NeighborCoord(CoordDivBlock(m_Position), DIR_NEG_X);
				int blockid = pworld->getBlockID(grid);
				if (blockid > 0 )
				{

					g_BlockMtlMgr.getMaterial(blockid)->onActorMoveCollidedWithBlock(pworld, DIR_POS_X, grid, m_OwnerActor);
				}
			}

		}
		break;
		case DIR_POS_X:
		{
			if (abs((m_Position.x + 50) % 100) <= 20)
			{
				WCoord grid = NeighborCoord(CoordDivBlock(m_Position), DIR_POS_X);
				int blockid = pworld->getBlockID(grid);
				if (blockid > 0)
				{
					g_BlockMtlMgr.getMaterial(blockid)->onActorMoveCollidedWithBlock(pworld, DIR_NEG_X, grid, m_OwnerActor);
				}
			}

		}
		break;
		}
	}
	*/
}
extern void CheckMotionValid(Vector3f &motion);
void ActorLocoMotion::doPickThrough(ClientActor *excludesactor/* =nullptr */)
{
	if (m_pWorld->isRemoteMode()) return;

	CheckMotionValid(m_Motion);
	WCoord mvec = getIntegerMotion(m_Motion);
	if(m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}

	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActorWithRiding(m_OwnerActor);

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::BLOCK) //block
	{
		int blockID = m_pWorld->getBlockID(presult.block);
		
		WCoord pos = (m_Position + mvec)/BLOCK_SIZE;
			
		if (presult.block != pos)
		{
			if (blockID == BLOCK_COLLIDER || blockID == BLOCK_MOBCOLLIDER)
			{
				g_BlockMtlMgr.getMaterial(blockID)->DoOnActorCollidedWithBlock(m_OwnerActor->getWorld(), presult.block, m_OwnerActor);
				// 观察者事件接口
				//ObserverEvent_Block obevent(presult.block.x, presult.block.y, presult.block.z, blockID, (long long)m_OwnerActor->getObjId());
				//ObserverEventManager::getSingleton().OnTriggerEvent("Block.ActorCollide", &obevent);
			}
		}
	}
}

void ActorLocoMotion::getRotation(Rainbow::Quaternionf &quat)
{
	//quat.setEulerAngle(m_RotateYaw, -m_RotationPitch, 0);
	if (m_OwnerActor && (m_OwnerActor->getObjType() == OBJ_TYPE_GAMEOBJECT))
	{
		quat = m_RotateQuat;
	}
	else
	{
		quat = AngleEulerToQuaternionf(Vector3f(-m_RotationPitch, m_RotateYaw, 0));
	}

}
bool ActorLocoMotion::findRandTargetBlock(float& x, float& y, float& z, int range, int yrange, const WCoord* pvec, int nTryTimes /*= 10*/)
{
	auto pos = WCoord(0, 0, 0);
	bool ret = findRandTargetBlock(pos, range, yrange, pvec, nTryTimes);

	x = pos.x;
	y = pos.y;
	z = pos.z;
	return ret;
}
bool ActorLocoMotion::findRandTargetBlock(WCoord &retpos, int range, int yrange, const WCoord *pvec, int nTryTimes /*= 10*/)
{
	bool inhomedist;
	bool found = false;
	float maxweight = -99999.0;
	retpos = WCoord(0,0,0);

	ClientMob *pMob = dynamic_cast<ClientMob *>(m_OwnerActor);
	//if (NULL == pMob) return false;

	if(pMob && -1 != pMob->getHomeDist())
	{
		WCoord vec = m_HomePosition - m_Position;
		int r = pMob->getHomeDist()+range*BLOCK_SIZE;
		inhomedist = (vec.lengthSquared()+4*BLOCK_SIZE*BLOCK_SIZE) < r*r;
	}
	else
	{
		inhomedist = false;
	}

	if(m_Position.y < 0) return false;
	
	for(int i = 0; i < nTryTimes; i++)
	{
		WCoord pos;
		pos.x = GenRandomInt(-range, range);
		pos.z = GenRandomInt(-range, range);
		pos.y = GenRandomInt(-yrange, yrange);
		
		if(NULL == pvec || pos.x*pvec->x + pos.z*pvec->z >= 0)
		{
			pos += CoordDivBlock(m_Position);

			if(!inhomedist || (pMob && pMob->isInHomeDist(pos.x*BLOCK_SIZE,pos.y*BLOCK_SIZE, pos.z*BLOCK_SIZE)))
			{
				if (pMob)
				{
					float tmp = pMob->getBlockPathWeight(pos);
					if(tmp > maxweight)
					{
						maxweight = tmp;
						found = true;
						retpos = pos;
					}
				}
				else
				{
					found = true;
					retpos = pos;
					break;
				}
			}
		}
	}
	
	if (pMob && pMob->m_Def->Type == MOB_WATER)
	{
		//retpos = BlockCenterCoord(retpos);
		retpos = BlockBottomCenter(retpos);
		retpos.y += GenRandomInt(10, 20);
		return found;
	}

	retpos = BlockBottomCenter(retpos);
	return found;
}

bool ActorLocoMotion::findRandTargetBlockTowards(WCoord &pos, int range, int yrange, const WCoord &vec)
{
	WCoord vec1 = vec - m_Position; 

	return findRandTargetBlock(pos, range, yrange, &vec1);
}

bool ActorLocoMotion::pushOutOfBlocks(const WCoord &pos)
{
	WCoord grid = CoordDivBlock(pos);
	WCoord offset = pos - grid*BLOCK_SIZE;
	CollideAABB box;
	getCollideBox(box);

	if(!m_pWorld->isBoxCollide(box))
	{
		return false;
	}
	else
	{
		int dir = DIR_POS_Y;
		int maxoffset = 1000000;
		bool fullcube[6];
		
		for(int i=0; i<6; i++) fullcube[i] = !m_pWorld->isBlockNormalCube(NeighborCoord(grid,i));
			
		if(fullcube[0] && offset.x<maxoffset)
		{
			maxoffset = offset.x;
			dir = 0;
		}

		if(fullcube[1] && BLOCK_SIZE-offset.x<maxoffset)
		{
			maxoffset = BLOCK_SIZE-offset.x;
			dir = 1;
		}

		if(fullcube[2] && offset.z<maxoffset)
		{
			maxoffset = offset.z;
			dir = 2;
		}

		if(fullcube[3] && BLOCK_SIZE-offset.z<maxoffset)
		{
			maxoffset = BLOCK_SIZE-offset.z;
			dir = 3;
		}

		if(fullcube[4] && offset.y<maxoffset)
		{
			maxoffset = offset.y;
			dir = 4;
		}

		if(fullcube[5] && BLOCK_SIZE-offset.y<maxoffset)
		{
			maxoffset = BLOCK_SIZE-offset.y;
			dir = 5;
		}

		float movestep = GenRandomFloat()*20.0f + 10.0f;
		m_Motion += g_DirectionCoord[dir].toVector3() * movestep * (m_IsCollideBackward ? -1.f : 1.f);

		return true;
	}
}

bool ActorLocoMotion::isInsideOpaqueBlock(const WCoord &pos)
{
	 for(int i = 0; i < 8; ++i)
	{
		int x = int( (((i >> 0) % 2) - 0.5f) * m_BoundSize * 0.8f );
		int y = int( (((i >> 1) % 2) - 0.5f) * 10.0f );
		int z = int( (((i >> 2) % 2) - 0.5f) * m_BoundSize * 0.8f );

		WCoord blockpos = pos + WCoord(x, y+m_OwnerActor->getEyeHeight(), z);

		if(m_pWorld->isBlockNormalCube(CoordDivBlock(blockpos)))
		{
			return true;
		}
	}
	return false;
}

bool ActorLocoMotion::isInsideOpaqueBlock()
{
	float scale = 1.0f;
	ClientMob *mob = dynamic_cast<ClientMob*>(m_OwnerActor);
	if (mob) scale = mob->m_Def->ModelScale;

	for(int i = 0; i < 8; ++i)
	{
		int x = int( (((i >> 0) % 2) - 0.5f) * m_BoundSize * 0.8f );
		int y = int( (((i >> 1) % 2) - 0.5f) * 10.0f*scale );
		int z = int( (((i >> 2) % 2) - 0.5f) * m_BoundSize * 0.8f );

		WCoord blockpos = m_Position + WCoord(x, y+m_OwnerActor->getEyeHeight(), z);

		if(m_pWorld->isBlockNormalCube(CoordDivBlock(blockpos)))
		{
			return true;
		}
	}
	return false;
}

bool ActorLocoMotion::getOneNoOpaqueBlock(WCoord* pos)
{
    for(int i=0; i<=4; i++)
	{
		WCoord blockpos = m_Position + WCoord(0, 0, -m_BoundSize + (-i*25));
		if(m_pWorld->isAirBlock(CoordDivBlock(blockpos)) && !isInsideOpaqueBlock(blockpos))
		{
			*pos = blockpos;
			return true;
		}

		blockpos = m_Position + WCoord(0, 0, m_BoundSize + i*25);
		if(m_pWorld->isAirBlock(CoordDivBlock(blockpos)) && !isInsideOpaqueBlock(blockpos))
		{
			*pos = blockpos;
			return true;
		}


		blockpos = m_Position + WCoord(-m_BoundSize + (-i*25), 0, 0);
		if(m_pWorld->isAirBlock(CoordDivBlock(blockpos)) && !isInsideOpaqueBlock(blockpos))
		{
			*pos = blockpos;
			return true;
		}

		blockpos = m_Position + WCoord(m_BoundSize + i*25, 0, 0);
		if(m_pWorld->isAirBlock(CoordDivBlock(blockpos)) && !isInsideOpaqueBlock(blockpos))
		{
			*pos = blockpos;
			return true;
		}
	}
       return false;
} 

bool ActorLocoMotion::isInsideNoOxygenBlock()
{
	WCoord eyepos = m_OwnerActor->getEyePosition();
	WCoord blockpos = CoordDivBlock(eyepos);
	int blockid = m_pWorld->getBlockID(blockpos);

	if(m_pWorld->getCurMapID()>=MAPID_MENGYANSTAR)
	{
		if(blockid == BLOCK_PLANTSPACE_OXYGEN || isBlockInsideOxygen(blockid, blockpos))
		{
			return false;
		}
		else
			return true;
	}
	else if(IsWaterBlockID(blockid) || isWaterPlantID(blockid) || isDriftsandBlockID(blockid))
	{
		float percent = FluidBlockMaterial::getFluidHeightPercent(m_pWorld->getBlockData(blockpos), blockid) - 0.11111111f;
		int watereheight = (blockpos.y + 1)*BLOCK_SIZE - int(percent*BLOCK_SIZE);
		return eyepos.y < watereheight;
	}
	else
	{
		return false;
	}
}

//方块是否在氧气中
bool ActorLocoMotion::isBlockInsideOxygen(int blockid, WCoord blockpos)
{
	if(!BlockMaterial::isNormalCube(blockid))
	{
		for(int i=0; i<4; i++)
		{
			WCoord ng = NeighborCoord(blockpos, i);
			int blockid = m_pWorld->getBlockID(ng);

			if(blockid == BLOCK_PLANTSPACE_OXYGEN)
				return true;
		}
	}

	return false;
}


//是否在外星空气中
bool ActorLocoMotion::isInSpaceAirBlock()
{
	WCoord eyepos = m_OwnerActor->getEyePosition();
	WCoord blockpos = CoordDivBlock(eyepos);
	int blockid = m_pWorld->getBlockID(blockpos);

	if (m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if (blockid == BLOCK_PLANTSPACE_OXYGEN || isBlockInsideOxygen(blockid, blockpos))
		{
			return false;
		}
		else
			return true;
	}
	return false;
}

ClientActor * ActorLocoMotion::getOwnerActor()
{
	return m_OwnerActor;
}

void ActorLocoMotion::playWalkOnLiquidEffect(bool iswater)
{
	if (!m_pWorld || !m_pWorld->getEffectMgr())
		return;

	if (iswater)
	{
		m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/horse_3432.ent", getPosition() + WCoord(0, 20, 0), 20);
	}
	else
	{
		m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/horse_3437.ent", getPosition() + WCoord(0, 40, 0), 20);
	}
}
namespace AntiSetting{
	unsigned GetRuntimeUnsigned();
}
// 随机保护数
void ActorLocoMotion::setNoClip(bool clip)
{
	m_noClip = clip;
	m_clipProtected = AntiSetting::GetRuntimeUnsigned() + m_noClip;
}

bool ActorLocoMotion::noClip()
{
	// 如果这两个值相等，说明被外部修改或锁定了，直接返回false
	if ((m_noClip + AntiSetting::GetRuntimeUnsigned()) != m_clipProtected)
	{
		if (m_OwnerActor->getObjType() == OBJ_TYPE_ROLE && g_pPlayerCtrl == m_OwnerActor)
		{
			jsonxx::Object log;
			log << "client_no_clip" << m_noClip;
			log << "client_no_clip_protected" << m_clipProtected;
			g_pPlayerCtrl->SendActionLog2Host(true, "cheat_client_noclip", log.json_nospace());
		}
		m_noClip = m_clipProtected - AntiSetting::GetRuntimeUnsigned();   // 重置
		// 上报客户端作弊
		return false;
	}
	return m_noClip;
}
void ActorLocoMotion::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_OwnerActor = dynamic_cast<ClientActor*>(owner);
	if (m_OwnerActor)
	{
		m_OwnerActor->BindLocoMotion(this);
	}
	Super::OnEnterOwner(owner);
}
void ActorLocoMotion::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	if (m_OwnerActor)
	{
		m_OwnerActor->BindLocoMotion(NULL);
	}
	m_OwnerActor = nullptr;
	Super::OnLeaveOwner(owner);
}

static bool findBottomThermalSpring(const WCoord& position, World* world)
{
	WCoord blockpos = CoordDivBlock(position);
	//int yOffset = 0;
	//int nextBlockID;
	return GetThermalSpringMgr().haveThermalSpring(blockpos, true);
}


int ActorLocoMotion::checkActorInHotZone()
{
	if (m_InWater)
	{
		ActorLiving* living = dynamic_cast<ActorLiving*>(m_OwnerActor);
		ConstAtLua* lua = GetLuaInterfaceProxy().get_lua_const();
		if (lua == nullptr)
		{
			return 0;
		}
		if (GetThermalSpringMgr().checkIsInHotZone(m_Position))
		{
			m_Motion.y = lua->hotZoneJetSpeed;
			if (living && living->getLivingAttrib())
			{
				living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
				int equipItemId = living->getLivingAttrib()->getEquipItemWithType(EQUIP_BREAST);
				if (equipItemId != 11643 && equipItemId != 11646)
				{
					living->getLivingAttrib()->addBuff(SCALD_BUFF, 1);
				}
			}
			m_IsSlamByThermalSpring = true;
			return 1;
		}
		else if (m_IsSlamByThermalSpring && findBottomThermalSpring(m_Position, m_pWorld))
		{
			//上面不是水，就停止.
			int blockId = m_pWorld->getBlockID(CoordDivBlock(m_Position) + WCoord(0, 2, 0));
			if (!BlockMaterialMgr::isWater(blockId))//(blockId != BLOCK_STILL_WATER && blockId != BLOCK_FLOW_WATER)
			{
				m_IsSlamByThermalSpring = false;
				return 0;
			}
			else
			{
				m_Motion.y = lua->hotZoneJetSpeed;
				if (living && living->getLivingAttrib())
				{
					living->getLivingAttrib()->addBuff(BOUND_BUFF, 1);
				}
				return 2;
			}
		}
		else
		{
			m_IsSlamByThermalSpring = false;
		}
	}
	else
	{
		m_IsSlamByThermalSpring = false;
	}
	return 0;
}

Rainbow::Vector3f ActorLocoMotion::getWorldDirection(float yawdeg, float pitchdeg, const Rainbow::Vector3f& localdir)
{
	// 旋转矩阵
	float yaw = Rainbow::Deg2Rad(yawdeg);
	float pitch = Rainbow::Deg2Rad(pitchdeg);
	Rainbow::Matrix3x3f transMat;

	if (true)
	{
		// 手动求旋转矩阵
		Rainbow::Matrix3x3f matX(
			1, 0, 0,
			0, cos(pitch), sin(pitch),
			0, -sin(pitch), cos(pitch)
		);
		/*float mx[9] = {
			1, 0, 0,
			0, cos(pitch), sin(pitch),
			0, -sin(pitch), cos(pitch)
		};
		memcpy(matX.m, mx, sizeof(mx));*/

		Rainbow::Matrix3x3f matY(
			cos(yaw), 0, -sin(yaw),
			0, 1, 0,
			sin(yaw), 0, cos(yaw)
		);
		/*float my[9] = {
			cos(yaw), 0, -sin(yaw),
			0, 1, 0,
			sin(yaw), 0, cos(yaw)
		};
		memcpy(matY.m, my, sizeof(my));*/
		transMat = matX * matY;
	}
	else
	{
		// 已有函数求旋转矩阵
		Rainbow::Vector3f euler(pitch, yaw, 0.0f);
		//Rainbow::Matrix3x3f::EulerToMatrix(euler, transMat);
		EulerToMatrix(euler, transMat);
	}

	// 矩阵变换
	//Rainbow::Vector3f dir = localdir * transMat;
	Rainbow::Vector3f dir = transMat.MultiplyPoint3(localdir);
	return dir;
}

float ActorLocoMotion::localYawToGlobalYaw(float localyaw)
{
	return m_RotateYaw + localyaw + 180.0f;
}

float ActorLocoMotion::localPitchToGlobalPitch(float localpitch)
{
	return m_RotationPitch - localpitch;
}

void ActorLocoMotion::getCircularActors(std::vector<ClientActor*>& actors, int radius)
{
	CollideAABB box;
	GetOwnerActor()->getCollideBox(box);
	box.expand(radius * 2, 5, radius * 2);

	std::vector<IClientActor*>tmpactors;
	if (GetOwnerActor()->getWorld())
	{
		GetOwnerActor()->getWorld()->getActorsInBox(tmpactors, box);
	}

	WCoord origin = GetOwnerActor()->getPosition();

	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		ClientActor* actor = tmpactors[i]->GetActor();
		Vector3f dp = (actor->getPosition() - origin).toVector3();
		if (dp.LengthSqr() < (radius * radius))
		{
			actors.push_back(actor);
		}
	}
}


bool ActorLocoMotion::getWorldDirectionByDir(float& x, float& y, float& z)
{
	float yaw = localYawToGlobalYaw(0.0f);
	float pitch = localPitchToGlobalPitch(0.0f);
	Rainbow::Vector3f dir = getWorldDirection(yaw, pitch, Rainbow::Vector3f(x, y, z));
	x = dir.x;
	y = dir.y;
	z = dir.z;
	return true;
}

bool ActorLocoMotion::getWorldDirectionByAngle(float localyaw, float localpitch, float& x, float& y, float& z)
{
	float yaw = localYawToGlobalYaw(localyaw);
	float pitch = localPitchToGlobalPitch(localpitch);
	Rainbow::Vector3f dir = getWorldDirection(yaw, pitch, Rainbow::Vector3f(0.0f, 0.0f, 1.0f));
	x = dir.x;
	y = dir.y;
	z = dir.z;
	return true;
}

bool ActorLocoMotion::getWorldDirectionByYawDir(float& x, float& y, float& z)
{
	float yaw = localYawToGlobalYaw(0.0f);
	float pitch = 0.0f;
	Rainbow::Vector3f dir = getWorldDirection(yaw, pitch, Rainbow::Vector3f(x, y, z));
	x = dir.x;
	y = dir.y;
	z = dir.z;
	return true;
}

bool ActorLocoMotion::getWorldDirectionByYawAngle(float localyaw, float localpitch, float& x, float& y, float& z)
{
	float yaw = -localYawToGlobalYaw(localyaw);
	float pitch = -localpitch;
	Rainbow::Vector3f dir = getWorldDirection(yaw, pitch, Rainbow::Vector3f(0.0f, 0.0f, 1.0f));
	x = dir.x;
	y = dir.y;
	z = dir.z;
	return true;
}

void ActorLocoMotion::getFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int width)
{
	CollideAABB box;
	GetOwnerActor()->getCollideBox(box);
	box.expand(range, 0, range);

	std::vector<IClientActor*>tmpactors;
	if (GetOwnerActor()->getWorld())
	{
		GetOwnerActor()->getWorld()->getActorsInBox(tmpactors, box);
	}

	WCoord origin = GetOwnerActor()->getPosition();

	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		ClientActor* actor = tmpactors[i]->GetActor();
		Vector3f dp = (actor->getPosition() - origin).toVector3();
		if (dp.x == 0.0f && dp.y == 0.0f && dp.z == 0.0f)
		{
			actors.push_back(actor);
			continue;
		}
		float t = DotProduct(dp, dir);
		if (t > 0 && t < range)
		{
			Vector3f tmp = CrossProduct(dp, dir);
			if (tmp.LengthSqr() < width * width) actors.push_back(actor);
		}
	}
}

void ActorLocoMotion::getFanShapedAreaFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int height, int angle, const WCoord* pOrigin /*= NULL*/)
{
	CollideAABB box;
	GetOwnerActor()->getCollideBox(box);
	box.expand(range, 0, range);

	std::vector<IClientActor*>tmpactors;
	if (GetOwnerActor()->getWorld())
	{
		GetOwnerActor()->getWorld()->getActorsInBox(tmpactors, box);
	}

	WCoord origin = pOrigin != NULL ? *pOrigin : GetOwnerActor()->getPosition();
	//if(angle >= 180)
	//{
	//	angle = 180; //扇形区域最大为180度
	//}

	angle /= 2;   //扇形区域左右两边可取一半
	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		ClientActor* actor = tmpactors[i]->GetActor();
		Rainbow::Vector3f dp = (actor->getPosition() - origin).toVector3();
		Rainbow::Vector3f dpTemp = dp;
		dpTemp.NormalizeSafe();
		Rainbow::Vector3f dirTemp = dir;
		dirTemp.Normalize();
		float t = DotProduct(dpTemp, dirTemp);
		float magnitude = dp.Length();
		float ang = Rainbow::ACosToAngle(t);
		if (ang <= angle && magnitude > 0 && magnitude < range)
		{
			if (dp.y * dp.y < height * height) actors.push_back(actor);
		}
	}
}

void ActorLocoMotion::checkVortex()
{
	m_Motion += m_vortexMotion;
	resetVortexMotion();
}

void ActorLocoMotion::updateTick()
{
	updateRidden();
	updateBindActor();
	if (!m_pWorld){
		return;
	}
	handleBlockMovement();
	handleWaterMovement();
	//handleHoneyMovement();
	handleDriftsandMovement();

	m_InHurt = IsHurtBlock(m_pWorld->getBlockID(CoordDivBlock(getPosition())));
	if(m_InLava && !m_OwnerActor->isDead())
	{
		ActorAttrib *attrib = m_OwnerActor->getAttrib();
		if(attrib)
		{
			if (attrib->immuneToFire() <= 0)
			{
				auto FireBurnComp = m_OwnerActor->sureFireBurnComponent();
				if (FireBurnComp)
				{
					FireBurnComp->setFire(100, 1);
				}

			}
			if (attrib->immuneToFire() <= 1)
			{
				auto component = m_OwnerActor->getAttackedComponent();
				if (component)
				{
					component->attackedFromType_Base(ATTACK_FIRE, 4.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); //modify by null,岩浆的伤害翻5倍
				}
			}
		}
		//m_OwnerActor->m_FallDistance *= 0.5f;
		auto functionWrapper = m_OwnerActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(functionWrapper->getFallDistance() * 0.5f);
		}
	}

	WCoord coordBlock = CoordDivBlock(m_Position) + WCoord(0, 0, 0);
	WCoord m_OldBlock= CoordDivBlock(m_OldPosition) + WCoord(0, 0, 0);
	if (m_OldBlock != coordBlock)
	{
		m_OldPosition = m_Position;
		if (m_OnGround)
		{
			WCoord pos = CoordDivBlock(m_Position) + WCoord(0, 0, 0);
			BlockMaterial* mtl = m_pWorld->getBlockMaterial(pos);
			if (mtl)
			{
				mtl->DoOnActorMoving(m_pWorld, pos, m_OwnerActor);
			}
		}


	}
}

void ActorLocoMotion::fallMotion(float realMovY, float movY, bool onRebound)
{
	bool bCanRebounce = (onTheBlock(BLOCK_VOID_MUSHROOM) || onTheBlock(BLOCK_VOID_MUSHROOM_CAP));
	if (!onTheBlock(BLOCK_HOTCRYSTAL) && !bCanRebounce && !onRebound)
	{
		bool isPlayerRididBody = false;
		PlayerLocoMotion* playerloco = nullptr;
		ClientPlayer* pPlayer = nullptr;
		if (m_OwnerActor && m_OwnerActor->getObjType() == OBJ_TYPE_ROLE)
		{
			pPlayer = static_cast<ClientPlayer*>(m_OwnerActor);
			if (pPlayer != nullptr)
			{
				playerloco = static_cast<PlayerLocoMotion*>(pPlayer->getLocoMotion());
				isPlayerRididBody = playerloco->getPhysType() == RolePhysType::PHYS_RIGIDBODY;
			}
		}

		if (isPlayerRididBody)
		{
			playerloco->checkFallByRigidType();
		}
		else
		{
			FallComponent::calFallMotion(m_OwnerActor, realMovY, m_OnGround);
		}

		if (pPlayer)
		{
			if (m_IsEnchFall)
			{
				auto effectComponent = pPlayer->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect(BODYFX_ENCH_FALL);
				}
			}
		}
	}
	else
	{
		auto functionWrapper = m_OwnerActor->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
	}
	
	if (m_OwnerActor)
	{
		auto chargeJumpComp = m_OwnerActor->getChargeJumpComponent();
		if (chargeJumpComp )
		{
			if (bCanRebounce && movY < 0)
			{
				float realFallDist = m_OwnerActor->getFuncWrapper() ? m_OwnerActor->getFuncWrapper()->getFallDistance() : 0;
				chargeJumpComp->rebounce(movY, realFallDist);
			}
		}
	}
}

bool ActorLocoMotion::IsPhysActor()
{
	auto physxMotion = dynamic_cast<PhysicsLocoMotion*>(this);
	if (physxMotion && physxMotion->m_PhysActor)
	{
		return true;
	}
	return false;
}

MNSandbox::EventObjectManager* ActorLocoMotion::GetEvent2()
{
	return &Event2();
}