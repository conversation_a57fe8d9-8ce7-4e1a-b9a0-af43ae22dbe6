#pragma once

#include "TerritoryBoundingBox.h"
#include <vector>
#include <unordered_map>
#include "world.h"

class TerritoryContainer;

/**
 * 领地管理器 - 管理领地范围并控制建造权限
 */
class TerritoryManager
{
public:
    // 单例访问
    static TerritoryManager* GetInstance();
    static void DestroyInstance();

    // 修改：注册和注销领地都使用 WCoord 和容器指针
    void RegisterTerritory(const WCoord& blockPos, TerritoryContainer* container);
    void UnregisterTerritory(const WCoord& blockPos);
    
    // 权限检查
    bool CanBuildAtPosition(const Rainbow::Vector3f& point, long long playerUin) const;
    bool IsPointInAnyTerritory(const Rainbow::Vector3f& point) const;
    
    // 获取包含某点的所有领地容器
    std::vector<TerritoryContainer*> GetTerritoriesContainingPoint(const Rainbow::Vector3f& point) const;
    
    // 判断领地所有权
    bool IsPointInPlayerTerritory(const Rainbow::Vector3f& point, long long playerUin) const;
    
    // 调试功能
    void DebugDrawAllTerritories(World* pworld, const Rainbow::ColourValue& color = Rainbow::ColourValue(1.0f, 0.0f, 1.0f, 1.0f)) const;
    
    // 清除所有数据
    void Clear();

    void Tick();

    // 注册到World实例
    void RegisterWithWorld(World* pWorld);

    // 根据方块的坐标查找领地，已经是传入参数是blockpos*100
    TerritoryContainer* GetTerritoryContainingBlock(const Rainbow::Vector3f& point) const;

private:
    TerritoryManager();
    ~TerritoryManager();
    
    static TerritoryManager* s_instance;
    
    // 映射区块坐标到领地容器
    std::unordered_map<WCoord, TerritoryContainer*, WCoordHashCoder> m_territories;

    bool m_isRegisteredWithWorld;  // 标记是否已向World注册
}; 