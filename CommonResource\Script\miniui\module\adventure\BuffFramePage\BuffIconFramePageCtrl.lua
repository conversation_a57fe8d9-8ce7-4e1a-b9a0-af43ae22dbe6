--声明
local BuffIconFramePageCtrl = Class("BuffIconFramePageCtrl",ClassList["UIBaseCtrl"])

--创建
function BuffIconFramePageCtrl:Create(param)
	return ClassList["BuffIconFramePageCtrl"].new(param)
end

--初始化
function BuffIconFramePageCtrl:Init(param)
	self.super:Init(param)

end

--启动
function BuffIconFramePageCtrl:Start()
	self.super:Start()
	self.view:InitView()
end

function BuffIconFramePageCtrl:HideAllBuffBtn()
	for i=1, 5 do
		local buffBtn = self.view.widgets["PlayerBuff"..i];
		if buffBtn:isVisible() then
			buffBtn:setVisible(false);
		end
	end
	if self.view.widgets.PlayerBuffMore:isVisible() then
		self.view.widgets.PlayerBuffMore:setVisible(false);
	end
end

--刷新
function BuffIconFramePageCtrl:Refresh()
	self.super:Refresh()
	self:HideAllBuffBtn()

	if self.schId then
		GetInst("MiniUIScheduler"):unreg(self.schId)
		self.schId = nil
	end
	self.schId = GetInst("MiniUIScheduler"):regPrivate(self.view.root:getChild("PlayerBuff1") ,function()
		self:PlayerBuff_OnUpdate()
	end,0.05,-1)
	self.tBuffBtnIconSetInfo = {}
end

--隐藏
function BuffIconFramePageCtrl:Reset()
	self.super:Reset()
	if self.schId then
		GetInst("MiniUIScheduler"):unreg(self.schId)
		self.schId = nil
	end
	self:HideAllBuffBtn()
end

--关闭
function BuffIconFramePageCtrl:Remove()
	self.super:Reset()

end

--消息处理
function BuffIconFramePageCtrl:FGUIHandleEvent(eventName)

end

function BuffIconFramePageCtrl:PlayerBuff_OnUpdate()
	local hideBufUICallback = function()
		self:HideAllBuffBtn()
		--	getglobal("PlayerHungerBar"):ShowSpecialIcon(false);
		StarveBuffChange(false);
	end
	--@kent 先回退 这里会有hide的操作（切换玩法模式，或者冒险模式出来进编辑模式会隐藏不掉）
	if UGCModeMgr and UGCModeMgr:IsEditing() then
		hideBufUICallback();
		return 
	end	
	--[[
		策划改动@韩浩东
		在坐骑上面只显示坐骑buff
		code-by:liya
	]]

	local ride = CurMainPlayer and CurMainPlayer:getRidingHorse();
	local buffnum = 0;
	local attrib = nil;
	-- local ridebuffnum = 0;
	-- local rideAttrib = nil;
	if MainPlayerAttrib and MainPlayerAttrib:getBuffNum() > 0 then
		buffnum = MainPlayerAttrib:getBuffNum();
		attrib = MainPlayerAttrib;
	end

	if ride and ride:getLivingAttrib() --[[and ride:getLivingAttrib():getBuffNum()>0]] then
		-- ridebuffnum = ride:getLivingAttrib():getBuffNum();
		-- rideAttrib = ride:getLivingAttrib();
		buffnum = ride:getLivingAttrib():getBuffNum();
		attrib = ride:getLivingAttrib();
	end
	-- if (buffnum+ridebuffnum) < 1 then
	if buffnum < 1 then
		hideBufUICallback();
	 	return;
	end

	local t_buff = {};
	local t_buffScript = {};
	for i=1, buffnum do
		if attrib then
			local info = attrib:getBuffInfo(i-1);
			--装备的buff不显示
			if info and info.def and info.def.BuffType == 1 and not info.def.Hide then
				local time = math.ceil( info.ticks*0.05 );
				table.insert(t_buff, {iconName=info.def.IconName, remaintime=time, buffid = info.def.ID})

				if info.def.ScriptName ~= nil and info.def.ScriptName ~= "" then
					table.insert(t_buffScript, info.def.ScriptName);
				end
			end
		end
	end

	for i=1, 5 do
		local btName = "PlayerBuff"..i
		local buffBtn = self.view.widgets[btName];
		if i<=#(t_buff) then
			if t_buff[i].iconName ~= '' or SingleEditorFrame_Switch_New then
				--https://www.tapd.cn/22897851/bugtrace/bugs/view/1122897851001106344
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.BUFF) then--xyang自定义UI
					buffBtn:setVisible(true);
				else
					buffBtn:setVisible(false);
				end
				local icon = buffBtn:getChild("ld_BuffIcon");
				local time  = buffBtn:getChild("lbl_BuffTime");

				local timetext = t_buff[i].remaintime.."s";
				if t_buff[i].remaintime > 60 then
					if t_buff[i].remaintime >= 9999 then
						timetext = GetS(1350);
					else
						timetext = math.floor(t_buff[i].remaintime/60).."m";
					end
				end
				time:setText(timetext);
				if not self.tBuffBtnIconSetInfo[btName] or self.tBuffBtnIconSetInfo[btName] ~= t_buff[i].buffid then
					self.tBuffBtnIconSetInfo[btName] = t_buff[i].buffid
					local def = DefMgr:getStatusDef(t_buff[i].buffid)
					if def and icon then
						local strIconId = def.Status.strIconID
						if strIconId ~= "" then
							GetInst("UIEditorPicSelectorMgr"):setCellIconById(strIconId, icon);
						else
							local iconId = def.Status.IconID
							if iconId < 10000 then
							-- IconBank.csv 中的图标
								iconId = iconId + DEVUI_ICONRES_TYPE_ICONBANK * 1000000
							end
							GetInst("UIEditorPicSelectorMgr"):setCellIconById(iconId,icon);
						end
					end
				end
			else
				buffBtn:setVisible(false);
			end
		else
			buffBtn:setVisible(false);
		end
	end

	--创造模式下隐藏buff按钮
	if CurWorld:isCreativeMode() then
		self:HideAllBuffBtn()
	end

	if #(t_buff) > 5 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.BUFF) then
		self.view.widgets.PlayerBuffMore:setVisible(true);
	else
		self.view.widgets.PlayerBuffMore:setVisible(false);
	end
end

function BuffIconFramePageCtrl:PlayerBuffMoreClick(obj, context)
	if not GetInst("MiniUIManager"):IsShown("BuffFramePageAutoGen") then
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/c_login"}, "BuffFramePageAutoGen")
		GetInst("MiniUIManager"):OpenUI("BuffFramePage", "miniui/miniworld/adventure/", "BuffFramePageAutoGen");
	else
		GetInst("MiniUIManager"):CloseUI("BuffFramePageAutoGen",true)
	end
end
