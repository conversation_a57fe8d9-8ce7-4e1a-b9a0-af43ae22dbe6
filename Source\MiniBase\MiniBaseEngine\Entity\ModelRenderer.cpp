#include "ModelRenderer.h"
#include "Common/LegacyClassTypeIds.h"
#include "Components/Renderer.h"
#include "Components/Transform.h"
#include "Components/SkeletonAnimation.h"
#include "Allocator/MemoryMacros.h"
#include "Components/SkinMeshRenderer.h"
#include "Render/SceneObjects/SkinMeshRenderObject.h"
#include "Core/GameObject.h"
#include "Graphics/LegacyGlobalShaderParam.h"
#include "Render/LOD/MeshRendererLOD.h"
#include "Components/MeshRenderer.h"
namespace Rainbow
{

	//----------------------------------------------------------------------------------

	IMPLEMENT_CLASS(ModelRenderer)
	ModelRenderer::~ModelRenderer()
	{
		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& renderers = itor.second;
			for (auto renderer : renderers)
			{
				ENG_DELETE_LABEL(renderer, kMemRenderer);
			}
		}
		m_mapAvatarVec.clear();

		for (auto renderer : m_modelMeshRenderers)
		{
			ENG_DELETE_LABEL(renderer, kMemRenderer);
		}
	}

	void ModelRenderer::SetInstanceAmbient(const ColorRGBAf& color)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetInstanceAmbient(color);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetInstanceAmbient(color);
			}
		}
	}

	void ModelRenderer::SetInstanceData(const Vector4f& instanceData)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetInstanceData(instanceData);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetInstanceData(instanceData);
			}
		}
	}

	void ModelRenderer::SetColor(const ColorRGBAf& color)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetColor(color);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetColor(color);
			}
		}
	}

	void ModelRenderer::SetPartColor(int partIdx, const ColorRGBAf& color)
	{
		std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator it = m_mapAvatarVec.find(partIdx);
		if (it != m_mapAvatarVec.end())
		{
			if (it->second.size() > 0)
			{
				for (int i = 0; i < it->second.size(); i++)
				{
					if (it->second[i])
					{
						it->second[i]->SetColor(color);
					}
				}
			}
		}
	}

	void ModelRenderer::SetOverlayColor(const ColorRGBAf& color)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetOverlayColor(color);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetOverlayColor(color);
			}
		}
	}

	void ModelRenderer::SetRimColor(bool enable, const ColorRGBAf& color, float power)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetRimColor(enable, color, power);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetRimColor(enable, color, power);
			}
		}
	}

	void ModelRenderer::SetTransparentRGB(float transparentValue)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetTransparentRGB(transparentValue);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetTransparentRGB(transparentValue);
			}
		}
	}

	void ModelRenderer::SetTransparentValue(float transparentValue)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetTransparentValue(transparentValue);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetTransparentValue(transparentValue);
			}
		}
	}

	void ModelRenderer::SetFrozenEffectEnable(bool value, float frozen_amount)
	{
		this->ResetLocalAABB();
		float height = m_LocalBounds.GetExtent().y * 2.0f;

		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetFrozenEffectEnable(value, height, frozen_amount);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetFrozenEffectEnable(value, height, frozen_amount);
			}
		}
	}

	void ModelRenderer::SetRenderGroup(UInt8 value, bool onlyTransparent)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetRenderGroup(value, onlyTransparent);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetRenderGroup(value, onlyTransparent);
			}
		}
	}

	void ModelRenderer::SetSubMeshOverlayColor(const FixedString& name, const ColorRGBAf& color)
	{
		ModelMeshRenderers* meshIns = FindMesh(name);
		if (meshIns)
		{
			meshIns->SetOverlayColor(color);
		}
	}

	void ModelRenderer::SetOverlayMask(const SharePtr<Texture2D>& tex, const ColorRGBAf& color, const FixedString& exceptName)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			if (m_modelMeshRenderers[i]->GetName() == exceptName)//exceptName���ų���Ӧ����ģ�Ͳ��ı���ɫ������������ɫʱҪ�󲻸ı��۾�����ģ����ɫ��
			{
				continue;
			}
			m_modelMeshRenderers[i]->SetOverlayMask(tex, color);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				vec[i]->SetOverlayMask(tex, color);
			}
		}
	}

	void ModelRenderer::SetEmissiveTex(const SharePtr<Texture2D>& tex)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetEmissiveTex(tex);
		}
	}

	void ModelRenderer::SetNextPass(const SharePtr<MaterialInstance>& nextPass)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetNextPass(nextPass);
		}
		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetNextPass(nextPass);
			}
		}
	}

	void ModelRenderer::SetStencilTest(UInt8 refValue, Rainbow::CompareFunction compareFunc, Rainbow::StencilOp pass, Rainbow::StencilOp fail, Rainbow::StencilOp zFail, UInt8 readMask, UInt8 writeMask)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetStencilTest(refValue, compareFunc, pass, fail, zFail, readMask, writeMask);
		}
		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetStencilTest(refValue, compareFunc, pass, fail, zFail, readMask, writeMask);
			}
		}
	}

	void  ModelRenderer::SetSelfEmissive(const ColorRGBAf& color)
	{
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetSelfEmissive(color);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetSelfEmissive(color);
			}
		}
	}

	void ModelRenderer::SetOutline(UInt32 value)
	{
		m_Outline = value;
		for (int i = 0; i < m_modelMeshRenderers.size(); i++)
		{
			m_modelMeshRenderers[i]->SetOutline(value);
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& vec = itor.second;
			size_t vecSize = vec.size();
			for (int i = 0; i < vecSize; i++)
			{
				ModelMeshRenderers* meshInstance = vec[i];
				meshInstance->SetOutline(value);
			}
		}
	}

	UInt32 ModelRenderer::GetOutline() const
	{
		return m_Outline;
	}

	ModelMeshRenderers* ModelRenderer::GetSubmesh(const FixedString& submeshName)
	{
		if (submeshName.empty())
		{
			return nullptr;
		}

		for (auto var : m_modelMeshRenderers)
		{
			if (var->GetName() == submeshName)
			{
				return var;
			}
		}
		return nullptr;
	}

	void ModelRenderer::ShowSkin(const FixedString& name, bool b)
	{
		ModelMeshRenderers* m = FindMesh(name);
		if (m)
		{
			m->show(b);
		}
	}

	void ModelRenderer::ShowSkins(bool b)
	{
		for (auto var : m_modelMeshRenderers)
		{
			var->show(b);
		}
	}

	void ModelRenderer::UpdateSkinsShow()
	{
		for (auto var : m_modelMeshRenderers)
		{
			if (m_Skeletion)
				var->SetShow(m_IsShow && var->GetShow(), m_Skeletion);
			else
				var->SetShow(m_IsShow && var->GetShow());
		}
	}

	void ModelRenderer::Show(bool b)
	{
		if (b != m_IsShow && m_Skeletion!=nullptr)
		{
			auto  attachments = m_Skeletion->GetBoneAttachments();
			for (auto& attach : attachments)
			{
				if (attach.m_GameObject != nullptr)
				{
					attach.m_GameObject->SetActive(b);
				}
			}
		}

		m_IsShow = b;

		UpdateSkinsShow();

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& renderers = itor.second;

			for (auto renderer : renderers)
			{
				renderer->SetShow(renderer->GetShow() && m_IsShow, m_Skeletion);
			}
		}
	}

	bool ModelRenderer::IsSubMeshNameExist(const FixedString& name)
	{
		if (name.empty())
		{
			return false;
		}

		for (auto var : m_modelMeshRenderers)
		{
			if (var->GetName() == name)
			{
				return true;
			}
		}

		return false;
	}

	ModelMeshRenderers* ModelRenderer::FindMesh(const FixedString& name)
	{
		if (name.empty())
		{
			return nullptr;
		}

		for (auto var : m_modelMeshRenderers)
		{
			if (var->GetName() == name)
			{
				return var;
			}
		}

		if (!m_mapAvatarVec.empty())
		{
			for (auto& itor : m_mapAvatarVec)
			{
				dynamic_array<ModelMeshRenderers*>& renderers = itor.second;
				for (auto renderer : renderers)
				{
					if (renderer->GetName() == name)
					{
						return renderer;
					}
				}
			}
		}

		return nullptr;
	}

	void ModelRenderer::SetTexture(const ShaderLab::FastPropertyName& textureName, Rainbow::SharePtr<Rainbow::Texture2D> ptexture, const char* meshname)
	{
		for (auto var : m_modelMeshRenderers)
		{
			if ((meshname && strcmp(meshname, var->GetName().c_str()) == 0) || !meshname)
			{
				var->SetTexture(textureName, ptexture);
			}
		}
	}

	SharePtr<Texture2D> ModelRenderer::GetAvatarTex(const FixedString& texture_name, int parts)
	{
		SharePtr<Texture2D> tex;
		if (!m_mapAvatarVec.empty())
		{
			std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator it = m_mapAvatarVec.find(parts);
			if (it != m_mapAvatarVec.end())
			{
				if (!it->second.empty())
				{
					for (size_t jj = 0; jj < it->second.size(); jj++)
					{
						if (it->second[jj] == NULL)
							return tex;
						return it->second[jj]->GetTexture(texture_name.c_str());

					}
				}
			}
		}
		return tex;
	}

	void ModelRenderer::SetAvatarTexture(const FixedString& texture_name, SharePtr<Texture2D> texture, int parts)
	{
		if (!m_mapAvatarVec.empty())
		{
			std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator it = m_mapAvatarVec.find(parts);
			if (it != m_mapAvatarVec.end())
			{
				if (!it->second.empty())
				{
					for (size_t jj = 0; jj < it->second.size(); jj++)
					{
						it->second[jj]->SetTexture(texture_name.c_str(), texture);
					}
				}
			}
		}
	}

	size_t ModelRenderer::GetNumSkin()
	{
		return m_modelMeshRenderers.size();
	}

	void ModelRenderer::SetVisibleDistance(float distance, UInt32 cullPolicyType)
	{
		for (int j = 0; j < m_modelMeshRenderers.size(); ++j)
		{
			ModelMeshRenderers* meshRenderer = m_modelMeshRenderers[j];
			if (meshRenderer)
			{
				meshRenderer->SetVisibleDistance( distance, cullPolicyType);
			}
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& renderers = itor.second;
			for (auto renderer : renderers)
			{
				if (renderer)
				{
					renderer->SetVisibleDistance(distance, cullPolicyType);
				}
			}
		}
	}

	void ModelRenderer::SetLODForce(int lodIdx)
	{
		//Ŀǰֻ��������lod,
		for (int j = 0; j < m_modelMeshRenderers.size(); ++j)
		{
			ModelMeshRenderers* meshRenderer = m_modelMeshRenderers[j];
			if (meshRenderer)
			{
				meshRenderer->SetLODForce(lodIdx);
			}
		}
	}

	ModelMeshRenderers* ModelRenderer::GetIthSkin(size_t i)
	{
		if (i < 0) return nullptr;
		if (i >= m_modelMeshRenderers.size()) return nullptr;
		return m_modelMeshRenderers[i];
	}

	void ModelRenderer::SetSkeleton(SkinnedSkeleton* skeleton)
	{
		m_Skeletion = skeleton;
	}

	dynamic_array<ModelMeshRenderers*> ModelRenderer::ToModelmeshRenderers(dynamic_array<Renderer*> render)
	{
		dynamic_array<ModelMeshRenderers*> molvec;
		std::map <std::string, dynamic_array<Renderer*>> map;
		for (int i = 0; i < render.size(); i++)
		{
			//�����Ҫ������mesh�����֣���Ҫ����gameobject�����Ӧ������
			const char* name = render[i]->GetGameObject()->GetName();
			if (name)
			{
				std::map < std::string, dynamic_array<Renderer*>>::iterator it = map.find(name);
				if (it != map.end())
				{

					dynamic_array<Renderer*>& r = map[name];
					r.push_back(render[i]);
				}
				else
				{
					dynamic_array<Renderer*> r;
					r.push_back(render[i]);
					map[name] = r;
				}
			}
			else
			{
				assert(false);
			}
		}

		for (auto var : map)
		{
			ModelMeshRenderers* model = ENG_NEW_LABEL(ModelMeshRenderers, kMemRenderer);
			model->m_ModelRenderer = this;
			model->SetModelmeshRenderers(var.second, var.first);
			molvec.push_back(model);
		}

		return molvec;
	}

	void ModelRenderer::ResetLocalAABB()
	{
		Rainbow::MinMaxAABB aabb = Rainbow::MinMaxAABB::invalid;

		for (auto model : m_modelMeshRenderers)
		{
			aabb.m_Min = Minimize(aabb.m_Min, model->GetLocalAABB().CalculateMin());
			aabb.m_Max = Maximize(aabb.m_Max, model->GetLocalAABB().CalculateMax());
		}

		for (auto& itor : m_mapAvatarVec)
		{
			dynamic_array<ModelMeshRenderers*>& renderers = itor.second;
			for (auto renderer : renderers)
			{
				aabb.m_Min = Minimize(aabb.m_Min, renderer->GetLocalAABB().CalculateMin());
				aabb.m_Max = Maximize(aabb.m_Max, renderer->GetLocalAABB().CalculateMax());
			}
		}


		if (aabb.IsValid())
		{
			m_LocalBounds.FromMinMaxAABB(aabb);
		}
	}

	void  ModelRenderer::SetRenderers(dynamic_array<Renderer*> render)
	{
		for (auto renderer : m_modelMeshRenderers)
		{
			ENG_DELETE_LABEL(renderer, kMemRenderer);
		}
		m_modelMeshRenderers = ToModelmeshRenderers(render);

		for (auto renderer : m_modelMeshRenderers)
		{
			renderer->CalculateLocalAABB();
		}
		ResetLocalAABB();
	}

	bool ModelRenderer::IsShowAvatar(int parts)
	{
		std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator it = m_mapAvatarVec.find(parts);
		if (it != m_mapAvatarVec.end())
		{
			if (it->second.size() > 0)
			{
				return it->second[0]->GetShow();

			}
		}
		return false;
	}

	void ModelRenderer::ShowAvatar(int parts, bool show)
	{
		std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator it = m_mapAvatarVec.find(parts);
		if (it != m_mapAvatarVec.end())
		{
			if (it->second.size() > 0)
			{
				for (int i = 0; i < it->second.size(); i++)
				{
					if (it->second[i])
					{
						it->second[i]->show(show);
					}
				}
			}
		}
	}

	void ModelRenderer::AddAvatar(int parts, GameObject* go, SharePtr<Texture2D> partTex, SharePtr<Texture2D> emissiveTex, SharePtr<Prefab> prefab)
	{
		if (!go) return;

		SkinnedSkeleton* sk = go->GetComponent<SkinnedSkeleton>();
		if (sk)
		{
			Rainbow::PPtr<Rainbow::Component> com(sk);
			go->RemoveComponent(com);
		}
		SkeletonAnimation* sa = go->GetComponent<SkeletonAnimation>();
		if (sa)
		{
			Rainbow::PPtr<Rainbow::Component> com(sa);
			go->RemoveComponent(com);
		}

		bool show = false;
		if (!m_mapAvatarVec.empty())
		{
			std::map<int, dynamic_array<ModelMeshRenderers*>>::iterator j = m_mapAvatarVec.find(parts);
			if (j != m_mapAvatarVec.end())
			{
				if (j->second.size() > 0)
				{
					show = j->second[0]->GetShow();
				}

				dynamic_array<ModelMeshRenderers*>& renderers = j->second;
				for (auto renderer : renderers)
				{
					ENG_DELETE_LABEL(renderer, kMemRenderer);
				}
				m_mapAvatarVec.erase(j);
			}
		}

		if (!m_mapAvatarGameobject.empty())
		{
			auto j = m_mapAvatarGameobject.find(parts);
			if (j != m_mapAvatarGameobject.end())
			{
				GameObject::Destroy((j->second).go);
				m_mapAvatarGameobject.erase(j);
			}
		}

		dynamic_array<Renderer*> renderers = go->GetComponentsInChildren<Renderer>();
		dynamic_array<ModelMeshRenderers*> str = ToModelmeshRenderers(renderers);

		auto ske = GetSkinnedSkeleton();

		for (size_t ii = 0; ii < renderers.size(); ii++)
		{
			renderers[ii]->SetOutline(m_Outline);

			if (renderers[ii]->IsKindOf<SkinMeshRenderer>())
			{
				SkinMeshRenderer* skinMeshRenderer = static_cast<SkinMeshRenderer*>(renderers[ii]);
				if (ske)
				{
					skinMeshRenderer->OnSkeletonUnbind(ske);
					skinMeshRenderer->OnSkeletonBind(ske);
					skinMeshRenderer->SetSkeleton(ske);
				}

				// 设置更大的包围盒避免错误剔除
				// 使用无限大包围盒
				 Vector3f minPos = Vector3f(std::numeric_limits<float>::min() / 2.0f, std::numeric_limits<float>::min() / 2.0f, std::numeric_limits<float>::min() / 2.0f);
				 Vector3f maxPos = Vector3f(std::numeric_limits<float>::max() / 2.0f, std::numeric_limits<float>::max() / 2.0f, std::numeric_limits<float>::max() / 2.0f);
				
				skinMeshRenderer->SetLocalAABB(AABB(minPos, maxPos));
			}
			int materialNum = renderers[ii]->GetMaterialCount();
			for (int j = 0; j < materialNum; j++)
			{
				SharePtr<MaterialInstance> material = renderers[ii]->GetMaterial(j).CastTo<MaterialInstance>();

				if (partTex)
				{
					material->SetTexture("g_DiffuseTex", partTex.Get());
				}

				if (emissiveTex)
				{
					float UVMul = 1.0f;
					material->SetFloat("g_fUVMul", UVMul);
					material->SetTexture("g_EmissiveTex", emissiveTex.Get());
					material->EnableKeyword("EMISSIVE");
				}
			}
		}

		for (size_t idx = 0; idx < str.size(); idx++) 
		{
			if (str[idx] != nullptr) 
			{
				str[idx]->CalculateLocalAABB();
			}	
		}

		m_mapAvatarVec[parts] = str;
		m_mapAvatarGameobject[parts] = { go };

		GetTransform()->AddChild(go->GetTransform());

		ResetLocalAABB();
		ShowAvatar(parts, show);
	}

	void ModelRenderer::AddAvatar(int parts, SharePtr<Prefab> prefab, SharePtr<Texture2D> partTex/*=NULL*/, SharePtr<Texture2D> emissiveTex)
	{
		if (!prefab ) return;

		if (!prefab->IsLoaded()) return;

		Object* cloneObject = prefab->CreatObjectInstance();
		GameObject* go = static_cast<GameObject*>(cloneObject);

		AddAvatar(parts, go, partTex, emissiveTex, prefab);
	}

	//---------------------------Modelmeshrender--------------------------------------------
	ModelMeshRenderers::ModelMeshRenderers()
		:m_ModelRenderer(nullptr)
	{
	}

	ModelMeshRenderers::~ModelMeshRenderers()
	{
	}

	void ModelMeshRenderers::OnLODChanged(const EventContent* evt)
	{
		LODExecutor* lod = static_cast<LODExecutor*>(evt->userData);
		SkinMeshRenderer* skinMeshRenderer = lod->GetGameObject()->GetComponent<SkinMeshRenderer>();
		if (skinMeshRenderer)
		{
			int  matsNum = skinMeshRenderer->GetMaterials().size();
			for (int i = 0; i < matsNum; ++i)
			{
				SharePtr<MaterialInterface> newMat = skinMeshRenderer->GetMaterial(i);
				SharedMaterialData& newMaterialData = newMat->GetWritableSharedMaterialData();
				if (m_RenderGroup >= 0)
				{
					newMaterialData.SetRenderGroup(m_RenderGroup);
				}
				m_MatPropertySheet.CopyPropertiesTo(newMat->GetWritableProperties());
				for (auto iter = m_MatKeywords.begin(); iter != m_MatKeywords.end(); ++iter)
				{
					if (iter->second)
					{
						newMat->EnableKeyword(iter->first.c_str());
					}
					else
					{
						newMat->DisableKeyword(iter->first.c_str());
					}
				}
				if (m_TransparentValue > 0.0001f)
				{
					BlendMode mode = newMat->GetMaterial()->GetBlendMode();
					if (mode == kBlendModeOpaque)
					{
						newMat->SetFloat("g_ModelRGBATransparent", m_TransparentValue);
						newMat->SetFloat("g_ModelAlphaTransparent", 1.0f);
					}
					else
					{
						newMat->SetFloat("g_ModelAlphaTransparent", m_TransparentValue);
						newMat->SetFloat("g_ModelRGBATransparent", 1.0f);
					}
				}

				if (m_MatCullMode != CullMode::kCullUnknown)
				{
					newMat->SetCullMode(m_MatCullMode);
				}
			}
		}
	}

	void ModelMeshRenderers::OnSkeletonBind(const EventContent* evt)
	{
		Rainbow::MinMaxAABB aabb = Rainbow::MinMaxAABB::invalid;
		for (size_t idx = 0; idx < m_ModelmeshRenderers.size(); idx++) 
		{
			AABB bound = m_ModelmeshRenderers[idx]->GetWorldBounds();
			aabb.m_Min = Minimize(aabb.m_Min, bound.CalculateMin());
			aabb.m_Max = Maximize(aabb.m_Max, bound.CalculateMax());
		}
		if (aabb.IsValid())
		{
			m_LocalBounds.FromMinMaxAABB(aabb);
		}
	}

	void ModelMeshRenderers::SetVisibleDistance(float distance, UInt32 cullPolicyType)
	{
		const auto& renderers = this->GetModelmeshRenderders();
		for (int i = 0; i < renderers.size(); ++i)
		{
			if (cullPolicyType == Rainbow::CullPolicy::kCullPolicyDistance)
			{
				renderers[i]->SetMaxViewDistanceSqr(distance * distance);
			}
			else if (cullPolicyType == Rainbow::CullPolicy::kCullPolicyScreenSize)
			{
				renderers[i]->SetMinScreenRadiusSqr(distance * distance);
			}
			else
			{
				renderers[i]->DisableCullPolicy();
			}
		}
	}

	void ModelMeshRenderers::SetLODForce(int lodIdx)
	{
		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			if (m_ModelmeshRenderers[i]->IsKindOf<SkinMeshRenderer>())
			{
				SkinMeshRenderer* skinMeshRenderer = static_cast<SkinMeshRenderer*>(m_ModelmeshRenderers[i]);
				MeshRendererLOD* lod = skinMeshRenderer->GetGameObject()->GetComponent<MeshRendererLOD>();
				if (lod)
				{
					lod->OnLODChange(lodIdx);
				}
			}
		}
		
	}

	void ModelMeshRenderers::SetModelmeshRenderers(dynamic_array<Renderer*> rederers, std::string name)
	{
		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			m_ModelmeshRenderers[i]->GetGameObject()->RemoveEvent(Evt_LODChanged, &ModelMeshRenderers::OnLODChanged, this);
			m_ModelmeshRenderers[i]->GetGameObject()->RemoveEvent(Evt_SkeletonBind, &ModelMeshRenderers::OnSkeletonBind, this);
		}

		m_ModelmeshRenderers = rederers;
		m_name = FixedString(name.c_str());

		//MinMaxAABB aabb = MinMaxAABB::invalid;
		std::map<int, Submesh> map;
		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			if (!m_ModelmeshRenderers[i]->IsKindOf<SkinMeshRenderer>())
				continue;

			SkinMeshRenderer* skinMeshRenderer = static_cast<SkinMeshRenderer*>(m_ModelmeshRenderers[i]);
			//AABB localAABB;
			//if (skinMeshRenderer->GetSharedMesh())
			//{
			//	localAABB = skinMeshRenderer->GetSharedMesh()->GetLocalAABB();
			//}
			//else
			//{
			//	skinMeshRenderer->GetSkinnedMeshLocalAABB(localAABB);
			//}

			MeshRendererLOD* lod = skinMeshRenderer->GetGameObject()->GetComponent<MeshRendererLOD>();
			if (lod)
			{
				m_EnableLOD = true;
				lod->GetGameObject()->AddEvent(Evt_LODChanged, &ModelMeshRenderers::OnLODChanged, this);
				lod->SetEnableMeshSetNull(false);
			}
			m_ModelmeshRenderers[i]->GetGameObject()->AddEvent(Evt_SkeletonBind, &ModelMeshRenderers::OnSkeletonBind, this);
			//aabb.m_Min = Minimize(aabb.m_Min, localAABB.CalculateMin());
			//aabb.m_Max = Maximize(aabb.m_Max, localAABB.CalculateMax());
			int useindex = skinMeshRenderer->GetUserId();
			std::map<int, Submesh>::iterator iter = map.find(useindex);
			if (iter != map.end())
			{
				map[useindex].push_back(rederers[i]);
			}
			else
			{
				Submesh sub;
				sub.push_back(rederers[i]);
				map[useindex] = sub;
			}
		}
		//m_LocalBounds.FromMinMaxAABB(aabb);

		for (auto k : map)
		{
			m_submeshs.push_back(k.second);
		}
	}

	const dynamic_array<Renderer*>& ModelMeshRenderers::GetModelmeshRenderders()
	{
		return m_ModelmeshRenderers;
	}

	Submesh ModelMeshRenderers::GetSubmeshs(int i)
	{
		if (i >= m_submeshs.size())
			return dynamic_array<Renderer*>();

		return m_submeshs[i];
	}

	const FixedString& ModelMeshRenderers::GetName()
	{
		return m_name;
	}

	void ModelMeshRenderers::show(bool b)
	{
		m_show = b;
		this->SetShow(b, m_ModelRenderer->GetSkinnedSkeleton());
	}

	void ModelMeshRenderers::SetShow(bool show, SkinnedSkeleton* Skeletion)
	{
		if (Skeletion == nullptr)
			return;

		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			if (m_ModelmeshRenderers[i])
			{
				m_ModelmeshRenderers[i]->SetHide(!show);
			}

			if (m_ModelmeshRenderers[i]->IsKindOf<SkinMeshRenderer>())
			{
				SkinMeshRenderer* skr = static_cast<SkinMeshRenderer*>(m_ModelmeshRenderers[i]);

				if (skr != nullptr)
				{
					int index = skr->GetSkeletonGroupStartIndex();

					if (index != -1)
					{
						Skeletion->PostDataGroupEnable(index, show);
					}
				}
			}
		}
	}

	void ModelMeshRenderers::SetShow(bool show)
	{
		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			if (m_ModelmeshRenderers[i])
			{
				m_ModelmeshRenderers[i]->SetHide(!show);
			}
		}
	}

	void  ModelMeshRenderers::SetOverlayMask(const SharePtr<Texture2D>& tex, const ColorRGBAf& color)
	{
		if (m_EnableLOD)
		{
			m_MatKeywords["OVERLAY_MODE_TEX"] = tex ? true : false;
			m_MatPropertySheet.m_Colors["g_MaskColor"] = color;

			SerializedPropertySheet::SerializedTexEnv texEnv;
			texEnv.m_Texture = tex.CastTo<Texture>();
			m_MatPropertySheet.m_TexEnvs["g_OverlayTex"] = texEnv;
		}

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;
				if (tex)
				{
					material->SetColor("g_MaskColor", color);
					material->SetTexture("g_OverlayTex", tex.Get());
					material->EnableKeyword("OVERLAY_MODE_TEX");
				}
				else
				{
					material->DisableKeyword("OVERLAY_MODE_TEX");
				}
			}
		}
	}

	void ModelMeshRenderers::SetColor(const ColorRGBAf& color)
	{
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;
				material->SetColor("g_Color", color);
			}
		}
	}

	void ModelMeshRenderers::SetOverlayColor(const ColorRGBAf& color)
	{
		bool enable = true;
		if (color.Equals(Rainbow::ColorRGBAf::black))
		{
			enable = false;
		}

		if (m_EnableLOD)
		{
			m_MatPropertySheet.m_Colors["g_OverlayColor"] = color;
			m_MatPropertySheet.m_Floats["g_UseOverlayColor"] = enable ? 1.0f : 0.0f;
		}

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;
				material->SetColor("g_OverlayColor", color);
				material->SetFloat("g_UseOverlayColor", enable ? 1.0f : 0.0f);
			}
		}
	}

	void ModelMeshRenderers::SetTexture(const ShaderLab::FastPropertyName& texture_name, Rainbow::SharePtr<Rainbow::Texture2D> ptexture, InlineSamplerType samplerType/* = InlineSamplerType::kInvalid*/)
	{
		if (m_EnableLOD)
		{ 
			SerializedPropertySheet::SerializedTexEnv texEnv;
			texEnv.m_Texture = ptexture.CastTo<Texture>();
			m_MatPropertySheet.m_TexEnvs[texture_name] = texEnv;
		}

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (material.IsValid())
				{
					material->SetTexture(texture_name, ptexture.Get(), samplerType);
				}
			}
		}
	}

	SharePtr<Texture2D> ModelMeshRenderers::GetTexture(const ShaderLab::FastPropertyName& texture_name)
	{
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (material.IsValid())
				{
					return NativeToSharePtr<Texture2D>(static_cast<Texture2D*>(material->GetTexture(texture_name)));
				}
			}
		}
		return NULL;
	}

	void ModelMeshRenderers::SetTextureWithIndex(const ShaderLab::FastPropertyName& texture_name, Rainbow::SharePtr<Rainbow::Texture2D> ptexture, int index /* = -1 */, InlineSamplerType samplerType/* = InlineSamplerType::kInvalid*/)
	{
		if (index <= -1)
		{
			if (m_EnableLOD)
			{
				SerializedPropertySheet::SerializedTexEnv texEnv;
				texEnv.m_Texture = ptexture.CastTo<Texture>();
				m_MatPropertySheet.m_TexEnvs[texture_name] = texEnv;
			}

			for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
			{
				int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetTexture(texture_name, ptexture.Get(), samplerType);
				}
			}
		}
		else
		{
			if (index < m_ModelmeshRenderers.size())
			{
				int materialNum = m_ModelmeshRenderers[index]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[index]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetTexture(texture_name, ptexture.Get(), samplerType);
				}
			}
		}
	}

	void ModelMeshRenderers::SetFloat(const ShaderLab::FastPropertyName& name, float val, int index /* = -1 */)
	{
		if (index <= -1)
		{
			if (m_EnableLOD)
			{
				m_MatPropertySheet.m_Floats[name] = val;
			}

			for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
			{
				int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetFloat(name, val);
				}
			}
		}
		else
		{
			if (index < m_ModelmeshRenderers.size())
			{
				int materialNum = m_ModelmeshRenderers[index]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[index]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetFloat(name, val);
				}
			}
		}
	}

	void ModelMeshRenderers::SetVector(const ShaderLab::FastPropertyName& name, const Rainbow::Vector4f& vec4, int index /* = -1 */)
	{
		if (index <= -1)
		{
			if (m_EnableLOD)
				m_MatPropertySheet.m_Colors[name] = ColorRGBAf(vec4);

			for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
			{
				int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetVector(name, vec4);
				}
			}
		}
		else
		{
			if (index < m_ModelmeshRenderers.size())
			{
				int materialNum = m_ModelmeshRenderers[index]->GetMaterialCount();
				for (int i = 0; i < materialNum; i++)
				{
					SharePtr<MaterialInstance> material = m_ModelmeshRenderers[index]->GetMaterial(i).CastTo<MaterialInstance>();
					material->SetVector(name, vec4);
				}
			}
		}
	}

	void ModelMeshRenderers::SetKeyword(const ShaderLab::FastPropertyName& name, const bool& b)
	{
		if (m_EnableLOD)
			m_MatKeywords[name.GetName()] = b;
		
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (material->HasKeyword(name.GetName()))
				{
					material->SetKeyword(name.GetName(), b);
				}
			}
		}
	}

	void ModelMeshRenderers::SetCullMode(Rainbow::CullMode mode)
	{
		if (m_EnableLOD)
			m_MatCullMode = mode;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				material->SetCullMode(mode);
			}
		}
	}

	//void ModelMeshRenderers::SetCastShadowAsNonOpacity(bool flag)
	//{
	//	for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
	//	{
	//		int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
	//		for (int i = 0; i < materialNum; i++)
	//		{
	//			SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
	//			if (!material) continue;
	//			material->GetMaterial()->SetCastShadowAsNonOpacity(flag);
	//		}
	//	}
	//}

	void ModelMeshRenderers::SetTransparentRGB(float transparentValue)
	{
		m_TransparentValue = transparentValue;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;

				BlendMode mode = material->GetMaterial()->GetBlendMode();
				if (mode == kBlendModeOpaque)
				{
					material->SetFloat("g_ModelRGBATransparent", transparentValue);
					material->SetFloat("g_ModelAlphaTransparent", 1.0f);
				}
				else
				{
					material->SetFloat("g_ModelAlphaTransparent", transparentValue);
					material->SetFloat("g_ModelRGBATransparent", 1.0f);
				}
			}
		}
	}

	void ModelMeshRenderers::SetTransparentValue(float transparentValue)
	{
		m_TransparentValue = transparentValue;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;

				BlendMode mode = material->GetMaterial()->GetBlendMode();
				if (mode == kBlendModeOpaque)
				{
					material->GetMaterial()->SetBlendMode(kBlendModeTranslucent);
					material->SetFloat("g_ModelRGBATransparent", transparentValue);
					material->SetFloat("g_ModelAlphaTransparent", 1.0f);
				}
				else
				{
					material->SetFloat("g_ModelAlphaTransparent", transparentValue);
					material->SetFloat("g_ModelRGBATransparent", 1.0f);
				}
			}
		}
	}

	void ModelMeshRenderers::SetFrozenEffectEnable(bool value, float model_height, float frozen_amount)
	{
		if (value)
			GetLegacyGlobalShaderParamManager()->LoadFrozenEffectTexture();

		if (m_EnableLOD)
		{
			m_MatKeywords["ENABLE_FROZEN"] = value;
			m_MatPropertySheet.m_Floats["g_FrozenAmount"] = frozen_amount;
			m_MatPropertySheet.m_Floats["g_ModelHeight"] = model_height;
		}

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material || !(material->HasKeyword("ENABLE_FROZEN"))) continue;

				if (value)
				{
					material->EnableKeyword("ENABLE_FROZEN");
					material->SetFloat("g_FrozenAmount", frozen_amount);
					material->SetFloat("g_ModelHeight", model_height);
				}
				else
				{
					material->DisableKeyword("ENABLE_FROZEN");
				}
			}
		}
	}

	void ModelMeshRenderers::SetNextPass(const SharePtr<MaterialInstance>& nextMaterial)
	{
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;
				material->SetNextPass(nextMaterial);
			}
		}
	}
	void ModelMeshRenderers::SetStencilTest(UInt8 refValue, Rainbow::CompareFunction compareFunc, Rainbow::StencilOp pass, Rainbow::StencilOp fail, StencilOp zFail, UInt8 readMask, UInt8 writeMask)
	{
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material) continue;
				material->SetStencilRef(refValue);
				material->SetStencilCompFunc(compareFunc);
				material->SetStencilOpPass(pass);
				material->SetStencilOpFail(fail);
				material->SetStencilOpZFail(zFail);
				material->SetStencilReadMask(readMask);
				material->SetStencilWriteMask(writeMask);
			}
		}
	}


	void ModelMeshRenderers::SetOutline(UInt32 value)
	{
		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			m_ModelmeshRenderers[j]->SetOutline(value);
		}
	}

	void ModelMeshRenderers::SetRenderGroup(UInt8 value, bool onlyTransparent)
	{
		if(m_EnableLOD)
		m_RenderGroup = value;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInstance> material = m_ModelmeshRenderers[j]->GetMaterial(i).CastTo<MaterialInstance>();
				if (!material || material->GetMaterial() == nullptr) continue;
				if (onlyTransparent) 
				{
					if (material->GetMaterial()->GetBlendMode() == kBlendModeTranslucent)
					{
						material->SetRenderGroup(value);
					}
				}
				else 
				{
					material->SetRenderGroup(value);
				}
			}
		}
	}

	void ModelMeshRenderers::SetSelfEmissive(const ColorRGBAf& color)
	{
		if (m_EnableLOD)
			m_MatPropertySheet.m_Colors["g_SelfPower"] = color;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInterface> material = m_ModelmeshRenderers[j]->GetMaterial(i);
				if (!material) continue;
				material->SetColor("g_SelfPower", color);
			}
		}
	}

	void ModelMeshRenderers::SetInstanceAmbient(const ColorRGBAf& color)
	{
		if (m_EnableLOD)
			m_MatPropertySheet.m_Colors["g_InstanceAmbient"] = color;

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInterface> material = m_ModelmeshRenderers[j]->GetMaterial(i);
				if (!material) continue;
				material->SetColor("g_InstanceAmbient", color);
			}
		}
	}

	void ModelMeshRenderers::SetInstanceData(const Vector4f& instanceData)
	{
		if (m_EnableLOD)
			m_MatPropertySheet.m_Colors["g_InstanceData"] = ColorRGBAf(instanceData);

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInterface> material = m_ModelmeshRenderers[j]->GetMaterial(i);
				if (!material) continue;
				material->SetVector("g_InstanceData", instanceData);
			}
		}
	}

	void ModelMeshRenderers::SetEmissiveTex(const SharePtr<Texture2D>& tex)
	{
		if (m_EnableLOD)
		{
			m_MatKeywords["EMISSIVE"] = true;
			m_MatPropertySheet.m_Floats["g_fUVMul"] = 1.0f;
			SerializedPropertySheet::SerializedTexEnv texEnv;
			texEnv.m_Texture = tex.CastTo<Texture>();
			m_MatPropertySheet.m_TexEnvs["g_EmissiveTex"] = texEnv;
		}

		for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		{
			int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
			for (int i = 0; i < materialNum; i++)
			{
				SharePtr<MaterialInterface> material = m_ModelmeshRenderers[j]->GetMaterial(i);
				if (!material) continue;

				material->SetFloat("g_fUVMul", 1.0f);
				material->SetTexture("g_EmissiveTex", tex.Get());
				material->EnableKeyword("EMISSIVE");
			}
		}
	}

	void ModelMeshRenderers::SetRimColor(bool enable, const ColorRGBAf& color, float power)
	{
		
		//if (m_EnableLOD)
		//{
		//	m_MatKeywords["USE_RIM_COLOR"] = enable;
		//	m_MatPropertySheet.m_Colors["g_RimColor"] = color;
		//	m_MatPropertySheet.m_Floats["g_RimPower"] = power;
		//}

		//for (size_t j = 0; j < m_ModelmeshRenderers.size(); j++)
		//{
		//	int materialNum = m_ModelmeshRenderers[j]->GetMaterialCount();
		//	for (int i = 0; i < materialNum; i++)
		//	{
		//		SharePtr<MaterialInterface> material = m_ModelmeshRenderers[j]->GetMaterial(i);
		//		if (!material) continue;
		//		if (enable)
		//		{
		//			material->SetColor("g_RimColor", color);
		//			material->SetFloat("g_RimPower", power);
		//			material->EnableKeyword("USE_RIM_COLOR");
		//		}
		//		else
		//		{
		//			material->DisableKeyword("USE_RIM_COLOR");
		//		}
		//	}
		//}
	}

	MaterialInstance* ModelMeshRenderers::GetSubMeshMaterial(int subMeshIdx)
	{
		Submesh submesh = GetSubmeshs(subMeshIdx);
		if (submesh.size() == 0) return nullptr;
		return submesh[0]->GetMaterial(0).CastTo<MaterialInstance>().Get();
	}

	MaterialInstance* ModelMeshRenderers::GetSubMeshInstMaterial(int subMeshIdx)
	{
		return GetSubMeshMaterial(subMeshIdx);
	}

	void ModelMeshRenderers::CalculateLocalAABB()
	{
		Rainbow::MinMaxAABB aabb = Rainbow::MinMaxAABB::invalid;
		for (int i = 0; i < m_ModelmeshRenderers.size(); i++)
		{
			if (m_ModelmeshRenderers[i]->IsKindOf<SkinMeshRenderer>())
			{
				SkinMeshRenderer* skinMeshRenderer = static_cast<SkinMeshRenderer*>(m_ModelmeshRenderers[i]);
				AABB localAABB;
				//if (skinMeshRenderer->GetSharedMesh().Get() != nullptr)
				//{
				//	localAABB = skinMeshRenderer->GetSharedMesh()->GetLocalAABB();
				//	if (localAABB.m_Center.ContainsNaN() || localAABB.m_Extent.ContainsNaN())
				//	{
				//		skinMeshRenderer->GetSkinnedMeshLocalAABB(localAABB);
				//	}
				//}
				//else
				{
					skinMeshRenderer->GetSkinnedMeshLocalAABB(localAABB);
				}
				aabb.m_Min = Minimize(aabb.m_Min, localAABB.CalculateMin());
				aabb.m_Max = Maximize(aabb.m_Max, localAABB.CalculateMax());
			}
			else if (m_ModelmeshRenderers[i]->IsKindOf<MeshRenderer>())
			{
				// aabb = Rainbow::MinMaxAABB();
				MeshRenderer* meshRender = static_cast<MeshRenderer*>(m_ModelmeshRenderers[i]);
				if (meshRender->GetSharedMesh().Get() != nullptr)
				{
					AABB localAABB = meshRender->GetSharedMesh()->GetLocalAABB();
					aabb.m_Min = Minimize(aabb.m_Min, localAABB.CalculateMin());
					aabb.m_Max = Maximize(aabb.m_Max, localAABB.CalculateMax());
				}
			}
		}

		if (aabb.IsValid())
		{
			m_LocalBounds.FromMinMaxAABB(aabb);
		}
	}

	void ModelMeshRenderers::SetSubMeshMaterial(int subMeshIdx, MaterialInstance* mat)
	{
		Submesh submesh = GetSubmeshs(subMeshIdx);
		if (submesh.size() == 0 || !mat) return;
		SharePtr<MaterialInstance> shareMat = NativeToSharePtr(mat);
		submesh[0]->SetMaterial(shareMat.CastTo<MaterialInterface>(), 0);
	}

	void ModelMeshRenderers::UpdateSubMeshMaterial(int subMeshIdx, MaterialInstance* mat)
	{
		SetSubMeshMaterial(subMeshIdx, mat);
	}

	int ModelMeshRenderers::GetSubMeshNum()
	{
		return m_submeshs.size();
	}

}
