#include "BlockMultiWall.h"
#include "world.h"
//#include "CollisionDetect.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "basesection.h"
#include "container_sandboxGame.h"
#include "section.h"
//#include "OgreVector3.h"
/*
n->2
e->0
s->3
w->1
*/
/*
3 2
0 1
*/
//左下角为核心
// 定义四个方向的墙扩展方块位置
// [方向][扩展块索引][x/z坐标]
const int BlockMultiWall::WallExtendPos[4][3][3] = {

    // 方向0: E向墙 
    {
		{0,0,-1},
		{0,1,-1},
		{0,1,0},
        
    },
    // 方向1: W向墙
    {
        {0, 0, 1},
        {0, 1, 1},
        {0, 1, 0}
    },
	// 方向2: N向墙 
    {
        {1, 0,0},  // 扩展位置1
        {1, 1,0},  // 扩展位置2
        {0, 1,0}   // 扩展位置3
    },
    // 方向3: S向墙 
    {
        {-1, 0, 0},
        {-1, 1, 0},
        {0, 1, 0},
    }
};

IMPLEMENT_BLOCKMATERIAL(BlockMultiWall)

BlockMultiWall::BlockMultiWall()
{
    //setDestroyTime(2.0f);
    //setSoundType(BlockSoundType::WOOD);
    //setMaterialType(BlockMaterialType::WOOD);
    //setBlockBounds(0.0f, 0.0f, 0.0f, 1.0f, 1.0f, 1.0f);
    //setBlockName("multiwall");
    //setDefaultTexture("wall");
}

void BlockMultiWall::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
}


void BlockMultiWall::initGeomName()
{
    m_geomName = m_Def->Texture2.c_str();
}

// 判断是否是核心方块
bool BlockMultiWall::isCoreBlock(int blockdata)
{
    return (blockdata & 4) == 0;
}

// 判断墙是否在近端
bool BlockMultiWall::isNearWall(int blockdata)
{
    return (blockdata & 8) != 0;
}

// 获取核心方块位置
WCoord BlockMultiWall::getCoreBlockPos(World *pworld, const WCoord &blockpos, int blockdata)
{
    if (blockdata == -1)
        blockdata = pworld->getBlockData(blockpos);
    
    // 如果是核心方块，直接返回当前位置
    if (isCoreBlock(blockdata))
        return blockpos;
    
    // 获取墙的方向
    int direction = blockdata & 3;
    
    // 通过检查所有可能的扩展块位置来确定当前块是哪个扩展块
    for (int i = 0; i < 3; i++)
    {
        // 计算如果当前块是第i个扩展块，核心块应该在的位置
        WCoord possibleCorePos = blockpos - WCoord(
            WallExtendPos[direction][i][0],
            WallExtendPos[direction][i][1],
            WallExtendPos[direction][i][2]
        );
        
        // 检查这个位置是否确实是一个核心块
        int coreBlockData = pworld->getBlockData(possibleCorePos);
        if (pworld->getBlockID(possibleCorePos) == getBlockResID() &&
            isCoreBlock(coreBlockData) && 
            (coreBlockData & 3) == direction)
        {
            return possibleCorePos;
        }
    }
    
    // 找不到有效的核心块
    return WCoord(0, -1, 0);
}

// 处理方块交互
bool BlockMultiWall::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
    return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount) > 0;
	//if (pworld->isRemoteMode())
	//{
	//	return true;
	//}
 //   ClientPlayer* playerTmp = player->GetPlayer();
 //   int toolID = playerTmp->getCurToolID();
 //   const ItemDef* def = GetDefManagerProxy()->getItemDef(toolID);
 //   if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
 //   {
 //       int blockdata = pworld->getBlockData(blockpos);
 //       WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
 //       auto orgcontainer = pworld->getContainerMgr()->getContainer(corePos);
 //       containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
 //       if (container)
 //       {
 //           if (container->checkArchitectureResEnough(2, 0, playerTmp, true) >= 0)
 //           {
 //               //todo 维修恢复的hp 材料的扣除
 //               container->addHp(amount);
 //               return true;
 //           }
 //       }
 //   }
 //   // 墙没有特殊交互
 //   return false;
}

bool BlockMultiWall::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
    return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer());
}

bool BlockMultiWall::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage)
{
    return onDamaged(pworld, blockpos, player->GetPlayer(), damage);
	//if (pworld->isRemoteMode())
	//{
	//	return true;
	//}
 //   int blockdata = pworld->getBlockData(blockpos);
	//WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	//containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	//if (container)
	//{
	//	//todo 维修恢复的hp 材料的扣除   
	//	container->addHp(-damage);//负数扣血
 //       //pworld->setBlockAll()
 //       return true;
	//}
 //   return false;
}

void BlockMultiWall::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}

// 方块被破坏时掉落物品
void BlockMultiWall::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance)
{
    // 只有核心方块掉落物品
    if (isCoreBlock(blockdata))
    {
        ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata & 3, droptype, chance);
    }
}

// 创建碰撞数据
void BlockMultiWall::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
    Block pblock = pworld->getBlock(blockpos);
    int blockdata = pblock.getData();
    int dir = blockdata & 3;
    bool isNear = isNearWall(blockdata);
    WCoord origin = blockpos * BLOCK_SIZE;
    
	if (dir == DIR_POS_X) {
		if (isNear) {
			coldetect->addObstacle(origin + WCoord(BLOCK_SIZE - thick, 0, 0), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
		}
		else {
			coldetect->addObstacle(origin, origin + WCoord(thick, BLOCK_SIZE, BLOCK_SIZE));
		}
	}
	else if (dir == DIR_NEG_X) {
		if (isNear) {
			coldetect->addObstacle(origin, origin + WCoord(thick, BLOCK_SIZE, BLOCK_SIZE));
		}
		else {
			coldetect->addObstacle(origin + WCoord(BLOCK_SIZE - thick, 0, 0), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
		}
	}
	else if (dir == DIR_POS_Z) {
		if (isNear) {
			coldetect->addObstacle(origin + WCoord(0, 0, BLOCK_SIZE - thick), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
		}
		else {
			coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, thick));
		}
	}
	else if (dir == DIR_NEG_Z) {
		if (isNear) {
			coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, thick));
		}
		else {
			coldetect->addObstacle(origin + WCoord(0, 0, BLOCK_SIZE - thick), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));

		}
	}
}
/*
* 
*  if (dir == DIR_POS_X)

Top-down view (X-Z plane at Y=0)
Z axis
^
|
100 +---------------+
	|               |
	|    EMPTY      |
	|    SPACE      |
	|               |
	|               |
	|               |
	|               |
  0 +WWWWWWWW-------+
	0       30     100 --> X axis
	 <---->
	  thick

W = Wall collision area


Side view (X-Y plane at Z=0)
Y axis
^
|
100 +---------------+
	|WWWWWWW|       |
	|W      |       |
	|W      |       |
	|W      |       |
	|W      |       |
	|W      |       |
  0 +WWWWWWW+-------+
	0      30      100 --> X axis
	 <---->
	  thick
3D perspective view

	Y
	↑
	|    Z
	|   ↗
	|  /
	| /
	|/
	+--------+--------+
   /|       /|       /|
  / |      / |      / |
 +--+-----+  |     /  |
 |  |XXXXX|  +----+---+
 |  XXXXX/|  /    |  /
 | /XXXXX | /     | /
 |/XXXXX  |/      |/
 +--------+-------+
	thick
	<---->

X = Wall collision volume


minpos = (0, 0, 0)
maxpos = (30, 100, 100)

    Y
    ↑
    |    Z
    |   ↗
    |  /
    | /
    |/
    +--------+--------+ (100,100,100)
   /|       /|       /|
  / |      / |      / |
 +--+-----+  |     /  |
 |  |     |  +----+---+
 |  |     |  /    |  /
 | /      | /     | /
 |/       |/      |/
 +--------+-------+
(0,0,0)   (30,0,0)
*/

extern void setPhyVerts(dynamic_array<Rainbow::Vector3f>& verts, WCoord& minpos, WCoord& maxpos);
void BlockMultiWall::getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas)
{
    WCoord origin = WCoord::zero;//posInSection * BLOCK_SIZE;
	if (m_physDataIdxs.size() == 0)
	{
		UInt16 physidxs[12][3] = { {0,1,2},{0,2,3},{0,5,1},{0,4,5},{1,5,2},{2,5,6},{2,6,3},{3,6,7},{3,4,0},{3,7,4},{4,6,5},{4,7,6} };
		for (int i = 0; i < 12; i++)
		{
			for (int j = 0; j < 3; j++)
			{
				m_physDataIdxs.push_back(physidxs[i][j]);
			}
		}
	}

    int blockdata = psection->getBlock(posInSection).getData();
    int dir = blockdata & 3;
    bool isNear = isNearWall(blockdata);
    WCoord minpos(0, 0, 0);
    WCoord maxpos(0, 0, 0);

    if (dir == DIR_POS_X) {
        if (isNear) {
			minpos = origin + WCoord(BLOCK_SIZE - thick, 0, 0);
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        } else {
			minpos = origin;
			maxpos = origin + WCoord(thick, BLOCK_SIZE, BLOCK_SIZE);
        }
    }
    else if (dir == DIR_NEG_X) {
        if (isNear) {
			minpos = origin;
			maxpos = origin + WCoord(thick, BLOCK_SIZE, BLOCK_SIZE);
        } else {
			minpos = origin + WCoord(BLOCK_SIZE - thick, 0, 0);
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        }
    }
    else if (dir == DIR_POS_Z) {
        if (isNear) {
			minpos = origin + WCoord(0, 0, BLOCK_SIZE - thick);
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        } else {
			minpos = origin;
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, thick);
        }
    }
    else if (dir == DIR_NEG_Z) {
        if (isNear) {
			minpos = origin;
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, thick);
        } else {
			minpos = origin + WCoord(0, 0, BLOCK_SIZE - thick);
			maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        }
    }

	TriangleBlockPhyData phyData;
	phyData.pos = posInSection * BLOCK_SIZE;
	phyData.blockVertIdxList = m_physDataIdxs;
	phyData.triangleFaceCount = 12;
	setPhyVerts(phyData.blockVertList, minpos, maxpos);
	physDatas.push_back(phyData);
}

// 根据玩家朝向获取方块数据
int BlockMultiWall::getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		return playerTmp->getCurPlaceDir();
	}
    return 0;
}

int BlockMultiWall::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
    // 获取墙的基本方向（0-3）
    DirectionType direction =(DirectionType) getPlaceBlockDataByPlayer(pworld, player);
    
    // 根据点击位置确定墙放置在近端还是远端
    bool isNear = false;
    
    // 根据点击的面和坐标来判断
    if (face == DIR_POS_X) {
        isNear = hitptx < 0.5f;
    } else if (face == DIR_NEG_X) {
        isNear = hitptx > 0.5f;
	}
	else if (face == DIR_NEG_Y) {
        if (direction == DIR_POS_X)
		    isNear = hitptx > 0.5f;
        else if (direction == DIR_NEG_X)
        {
			isNear = hitptx < 0.5f;
        }
		else if (direction == DIR_POS_Y)
			isNear = hitpty > 0.5f;
		else if (direction == DIR_NEG_Y)
		{
			isNear = hitpty < 0.5f;
		}
		else if (direction == DIR_POS_Z)
			isNear = hitptz > 0.5f;
		else if (direction == DIR_NEG_Z)
		{
			isNear = hitptz < 0.5f;
		}

    } else if (face == DIR_POS_Z) {
        isNear = hitptz < 0.5f;
    } else if (face == DIR_NEG_Z) {
        isNear = hitptz > 0.5f;
    } else {
        // 如果是上面或下面，根据横向点击位置来判断
        // 这里可以根据实际需求调整逻辑
        if (direction == DIR_POS_X || direction == DIR_NEG_X) {
            isNear = hitptz < 0.5f;
        } else {
            isNear = hitptx < 0.5f;
        }
    }
    
    // 设置方向和位置信息
    int blockdata = direction; // 方向(0-3)
    isNear = false;//只用远离玩家的。
    // 在第4位(3)上记录位置信息：0=远端，1=近端
    if (isNear) {
        blockdata |= 8; // 设置近端标记
    }
    
    return blockdata;
}

int BlockMultiWall::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
    Block pblock = sectionData->getBlock(blockpos);
    int blockdata = pblock.getData();

    idbuf[0] = isNearWall(blockdata) ? 0 : 1; // 0表示近端，1表示远端
    dirbuf[0] = blockdata & 3;

    return 1;
}

WorldContainer* BlockMultiWall::createContainer(World* pworld, const WCoord& blockpos)
{
    int blockdata=  pworld->getBlockData(blockpos);
	// 获取核心方块位置
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos == blockpos)
    {
        int blockId = pworld->getBlockID(blockpos);
        int bpTypeId = 0;
        int bpLevel = 0;
        initBuildData(blockId, bpTypeId, bpLevel);
		containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
		return container;
    }
    return nullptr;
}

int BlockMultiWall::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    if (container)
    {
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
    }
}

WorldContainer* BlockMultiWall::getCoreContainer(World* pworld, const WCoord& blockpos)
{
    return GetArchitecturalCoreContainer(pworld, blockpos);
}

// 当方块被添加到世界
void BlockMultiWall::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    int blockdata = pworld->getBlockData(blockpos);
    // 只有核心方块才创建扩展方块
    if (isCoreBlock(blockdata))
    {
        int direction = blockdata & 3;
        bool isNear = isNearWall(blockdata);

        // 创建扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = blockpos + WCoord(
                WallExtendPos[direction][i][0],
                WallExtendPos[direction][i][1],
                WallExtendPos[direction][i][2]
            );

            // 扩展方块数据:
            // 低2位(0-1): 继承方向 (0-3)
            // 第3位(2): 1表示扩展方块
            // 第4位(3): 继承近端/远端标记
            int extendData = direction | 4;  // 设置方向和扩展标记
            if (isNear) {
                extendData |= 8;  // 保持与核心方块相同的近端/远端标记
            }
            pworld->setBlockAll(extendPos, getBlockResID(), extendData);
        }
    }
}

// 当方块被添加到世界
void BlockMultiWall::onBlockAdded(World *pworld, const WCoord &blockpos)
{
}

bool BlockMultiWall::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
    WCoord corePos = getCoreBlockPos(pworld, blockpos);
    if (corePos != WCoord(0, -1, 0))
    {
        auto blockdata = pworld->getBlockData(corePos);
        int direction = blockdata & 3;
        // 收集所有需要删除的扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = corePos + WCoord(
                WallExtendPos[direction][i][0],
                WallExtendPos[direction][i][1],
                WallExtendPos[direction][i][2]
            );
            blockList.push_back(extendPos);
        }
        if (includeSelf) blockList.push_back(corePos);
        return true;
    }
    return false;
}

// 当方块被移除
void BlockMultiWall::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
    // 临时存储需要删除的方块
    std::vector<WCoord> blocksToRemove;
    
    // 获取核心方块位置
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos.y < 0)
        return; // 无效核心位置
        
    // 获取墙的方向
    int direction = blockdata & 3;
    
    // 收集所有需要删除的扩展方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = corePos + WCoord(
            WallExtendPos[direction][i][0],
            WallExtendPos[direction][i][1],
            WallExtendPos[direction][i][2]
        );
        
        // 检查是否是墙的扩展方块
        if (pworld->getBlockID(extendPos) == getBlockResID() && extendPos != blockpos)
        {
            blocksToRemove.push_back(extendPos);
        }
    }
    
    // 如果当前移除的不是核心方块，将核心方块也加入删除列表
    if (!isCoreBlock(blockdata) && pworld->getBlockID(corePos) == getBlockResID() && corePos != blockpos)
    {
        blocksToRemove.push_back(corePos);
    }
    
    // 批量删除所有收集的方块
    for (const auto& pos : blocksToRemove)
    {
        pworld->setBlockAll(pos, 0, 0);
    }
}

// 检查是否可以放置方块
bool BlockMultiWall::canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
    // 确定墙的朝向
    int direction = getPlaceBlockDataByPlayer(pworld, player);
    
    // 检查所有位置是否能放置方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = blockpos + WCoord(
            WallExtendPos[direction][i][0],
            WallExtendPos[direction][i][1],
			WallExtendPos[direction][i][2]
        );
        auto* mtl = pworld->getBlockMaterial(extendPos);
        
		if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), extendPos))
			return false;
        
        // 检查建造权限
        if (!pworld->CanBuildAtPosition(extendPos, player->getUin()))
            return false;
    }
    
    return pworld->CanBuildAtPosition(blockpos, player->getUin());
}

// 创建方块网格
void BlockMultiWall::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
    // 可能需要根据方向显示不同的模型
    int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
	// 只有核心方块才创建网格
	if (isCoreBlock(blockdata))
	{
		int direction = blockdata & 3;
		// 如果有不同方向的模型，在这里设置模型名称
		ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
	}
} 